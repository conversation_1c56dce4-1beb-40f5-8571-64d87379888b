const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}
    type NavBar {
        home: MdIcon!
        planning: MdIcon!
        guests: MdIcon!
        apps: MdIcon!
        messages: MdIcon!
    }

    type HostLayout {
        id: ID!
        navBar: NavBar!
    }

    type HostLayoutWrapper {
        hostLayout: HostLayout!
    }

    type HostLayoutsWrapper {
        hostLayouts: [HostLayout]!
    }

    type HostLayoutResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostLayoutWrapper!
    }

    type HostLayoutsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostLayoutsWrapper!
        pagination: PaginationInfo!
    }

    type HostLayoutErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union HostLayoutResult = HostLayoutResponse | HostLayoutErrorResponse
    union HostLayoutsResult = HostLayoutsResponse | HostLayoutErrorResponse

    input NavBarInput {
        home: ID!
        planning: ID!
        guests: ID!
        apps: ID!
        messages: ID!
    }

    input NavBarUpdateInput {
        home: ID
        planning: ID
        guests: ID
        apps: ID
        messages: ID
    }

    input CreateHostLayoutInput {
        navBar: NavBarInput!
    }

    input UpdateHostLayoutInput {
        id: ID!
        navBar: NavBarUpdateInput
    }

    type Query {
        getHostLayout: HostLayoutResult!
    }

    type Mutation {
        createHostLayout(input: CreateHostLayoutInput!): HostLayoutResult!
        updateHostLayout(input: UpdateHostLayoutInput!): HostLayoutResult!
        deleteHostLayout(id: ID!): HostLayoutResult!
    }
`


const { model, Schema } = require('mongoose');
// change the collection name to device_tokens

const deviceTokenSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    token: { type: String, required: true },
    deviceId: { type: String, required: true },
    platform: { type: String, enum: ['IOS', 'ANDROID'], required: true }
}, { timestamps: true, collection: 'device_tokens' });
const DeviceToken = model('DeviceToken', deviceTokenSchema);

module.exports = DeviceToken;
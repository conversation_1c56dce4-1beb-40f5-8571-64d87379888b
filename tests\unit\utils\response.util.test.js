const { createResponse } = require('../../../src/utils/response.util');

describe('createResponse', () => {
  it('should create a response object with the given parameters', () => {
    const typeName = 'TestResponse';
    const status = 'SUCCESS';
    const message = 'Test message';
    const data = { key: 'value' };

    const result = createResponse(typeName, status, message, data);

    expect(result).toEqual({
      __typename: typeName,
      status,
      message,
      key: 'value'
    });
  });

  it('should create a response object without additional data', () => {
    const typeName = 'SimpleResponse';
    const status = 'ERROR';
    const message = 'Error message';

    const result = createResponse(typeName, status, message);

    expect(result).toEqual({
      __typename: typeName,
      status,
      message
    });
  });

  it('should spread the data object correctly', () => {
    const typeName = 'ComplexResponse';
    const status = 'PARTIAL';
    const message = 'Partial success';
    const data = {
      items: [1, 2, 3],
      metadata: {
        page: 1,
        totalPages: 5
      }
    };

    const result = createResponse(typeName, status, message, data);

    expect(result).toEqual({
      __typename: typeName,
      status,
      message,
      items: [1, 2, 3],
      metadata: {
        page: 1,
        totalPages: 5
      }
    });
  });

});

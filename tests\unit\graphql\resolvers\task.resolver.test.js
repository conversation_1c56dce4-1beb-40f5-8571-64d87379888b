const taskResolvers = require('../../../../src/graphql/resolvers/task.resolver');
const Task = require('../../../../src/models/Task');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { User } = require('../../../../src/models/User');
const buildTaskQuery = require('../../../../src/graphql/resolvers/filters/task.filter');
const findReferences = require('../../../../src/graphql/resolvers/references/task.references');

jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/Task');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/task.filter');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/references/task.references');

beforeEach(() => {
    jest.clearAllMocks();
})

describe('taskResolvers', () => {
    describe('Task', () => {
        it('should resolve createdBy field', async () => {
            const parent = { createdBy: 'userId' };
            const user = { _id: 'userId', name: 'John Doe' };
            User.findById.mockResolvedValue(user);

            const result = await taskResolvers.Task.createdBy(parent);

            expect(User.findById).toHaveBeenCalledWith('userId');
            expect(result).toEqual(user);
        });

        it('should resolve assignedTo field', async () => {
            const parent = { assignedTo: ['userId1', 'userId2'] };
            const users = [{ _id: 'userId1', name: 'John Doe' }, { _id: 'userId2', name: 'Jane Doe' }];
            User.find.mockResolvedValue(users);

            const result = await taskResolvers.Task.assignedTo(parent);

            expect(User.find).toHaveBeenCalledWith({ _id: { $in: ['userId1', 'userId2'] } });
            expect(result).toEqual(users);
        });

        it('should handle error in createdBy field resolver', async () => {
            const parent = { createdBy: 'userId' };
            const errorMessage = 'Database error';
            User.findById.mockRejectedValue(new Error(errorMessage));
    
            console.error = jest.fn();
    
            await expect(taskResolvers.Task.createdBy(parent)).rejects.toThrow('Error getting created by');
    
            expect(console.error).toHaveBeenCalledWith(expect.any(Error));
            expect(console.error.mock.calls[0][0].message).toBe(errorMessage);
        });

        it('should handle error in assignedTo field resolver', async () => {
            const parent = { assignedTo: ['userId1', 'userId2'] };
            const errorMessage = 'Database error';
            User.find.mockRejectedValue(new Error(errorMessage));
    
            console.error = jest.fn();
    
            await expect(taskResolvers.Task.assignedTo(parent)).rejects.toThrow('Error getting assigned to');
    
            expect(console.error).toHaveBeenCalledWith(expect.any(Error));
            expect(console.error.mock.calls[0][0].message).toBe(errorMessage);
        });
    });

    describe('Query', () => {
        describe('getTasks', () => {
            it('should get tasks with pagination', async () => {
                const filters = { title: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { title: { $regex: 'test', $options: 'i' } };
                const tasks = [{ _id: 'taskId', title: 'test task' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
        
                buildTaskQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                Task.find.mockResolvedValue(tasks);
                createResponse.mockReturnValue({ 
                    status: 'SUCCESS', 
                    message: 'Tasks fetched successfully', 
                    result: { tasks, pagination: paginationInfo } 
                });
        
                const result = await taskResolvers.Query.getTasks(null, { filters, pagination });
        
                expect(buildTaskQuery).toHaveBeenCalledWith(filters);
                expect(getPaginationInfo).toHaveBeenCalledWith(Task, query, pagination.limit, pagination.skip);
                expect(Task.find).toHaveBeenCalledWith(query);
                expect(result).toEqual({ 
                    status: 'SUCCESS', 
                    message: 'Tasks fetched successfully', 
                    result: { tasks, pagination: paginationInfo } 
                });
            });

            it('should return failure response if no tasks found', async () => {
                const filters = { title: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { title: { $regex: 'test', $options: 'i' } };
                const tasks = [];
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
        
                buildTaskQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                Task.find.mockResolvedValue(tasks);
                createResponse.mockReturnValue({ 
                    status: 'FAILURE', 
                    message: 'No tasks found', 
                    result: { tasks, pagination: paginationInfo } 
                });
        
                const result = await taskResolvers.Query.getTasks(null, { filters, pagination });
        
                expect(buildTaskQuery).toHaveBeenCalledWith(filters);
                expect(getPaginationInfo).toHaveBeenCalledWith(Task, query, pagination.limit, pagination.skip);
                expect(Task.find).toHaveBeenCalledWith(query);
                expect(result).toEqual({ 
                    status: 'FAILURE', 
                    message: 'No tasks found', 
                    result: { tasks, pagination: paginationInfo } 
                });
            });

            it('should return error response on exception', async () => {
                const filters = { title: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
        
                buildTaskQuery.mockImplementation(() => { throw error; });
                createResponse.mockReturnValue({ 
                    status: 'FAILURE', 
                    message: 'Error getting tasks', 
                    errors: [{ field: 'getTasks', message: error.message }] 
                });
        
                const result = await taskResolvers.Query.getTasks(null, { filters, pagination });
        
                expect(buildTaskQuery).toHaveBeenCalledWith(filters);
                expect(result).toEqual({ 
                    status: 'FAILURE', 
                    message: 'Error getting tasks', 
                    errors: [{ field: 'getTasks', message: error.message }] 
                });
            });
        });

        describe('getTaskById', () => {
            it('should get task by id', async () => {
                const id = 'taskId';
                const task = {
                    _id: 'taskId',
                    title: 'test task',
                    createdBy: { _id: 'creatorId', name: 'Creator' },
                    assignedTo: [{ _id: 'assigneeId', name: 'Assignee' }]
                };
    
                getByIdCache.mockResolvedValue(null);
                const populateMock = jest.fn().mockResolvedValue(task);
                Task.findById.mockReturnValue({ populate: populateMock });
                setCache.mockResolvedValue();
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'Task fetched successfully', result: { task } });
    
                const result = await taskResolvers.Query.getTaskById(null, { id });
    
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Task.findById).toHaveBeenCalledWith(id);
                expect(populateMock).toHaveBeenCalledWith('createdBy assignedTo');
                expect(setCache).toHaveBeenCalledWith(id, task);
                expect(result).toEqual({ status: 'SUCCESS', message: 'Task fetched successfully', result: { task } });
            });

            it('should return failure response if task not found', async () => {
                const id = 'taskId';
    
                getByIdCache.mockResolvedValue(null);
                const populateMock = jest.fn().mockResolvedValue(null);
                Task.findById.mockReturnValue({ populate: populateMock });
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Task not found', errors: [{ field: 'getTaskById', message: 'Task not found' }] });
    
                const result = await taskResolvers.Query.getTaskById(null, { id });
    
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Task.findById).toHaveBeenCalledWith(id);
                expect(populateMock).toHaveBeenCalledWith('createdBy assignedTo');
                expect(result).toEqual({ status: 'FAILURE', message: 'Task not found', errors: [{ field: 'getTaskById', message: 'Task not found' }] });
            });

            it('should return error response on exception', async () => {
                const id = 'taskId';
                const error = new Error('Database error');
    
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting task', errors: [{ field: 'getTaskById', message: error.message }] });
    
                const result = await taskResolvers.Query.getTaskById(null, { id });
    
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error getting task', errors: [{ field: 'getTaskById', message: error.message }] });
            });

            it('should handle case where task is not found in cache but found in database', async () => {
                const id = 'taskId';
                const task = {
                    _id: 'taskId',
                    title: 'test task',
                    createdBy: { _id: 'creatorId', name: 'Creator' },
                    assignedTo: [{ _id: 'assigneeId', name: 'Assignee' }]
                };
    
                getByIdCache.mockResolvedValue(null);
                const populateMock = jest.fn().mockResolvedValue(task);
                Task.findById.mockReturnValue({ populate: populateMock });
                setCache.mockResolvedValue();
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'Task fetched successfully', result: { task } });
    
                const result = await taskResolvers.Query.getTaskById(null, { id });
    
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Task.findById).toHaveBeenCalledWith(id);
                expect(populateMock).toHaveBeenCalledWith('createdBy assignedTo');
                expect(setCache).toHaveBeenCalledWith(id, task);
                expect(result).toEqual({ status: 'SUCCESS', message: 'Task fetched successfully', result: { task } });
            });

            it('should return task if found in cache', async () => {
                const id = 'taskId';
                const task = {
                    _id: 'taskId',
                    title: 'test task',
                    createdBy: { _id: 'creatorId', name: 'Creator' },
                    assignedTo: [{ _id: 'assigneeId', name: 'Assignee' }]
                };

                getByIdCache.mockResolvedValue(task);

                Task.findById.mockResolvedValue(null);

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Task fetched successfully',
                    result: { task }
                });

                const result = await taskResolvers.Query.getTaskById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Task.findById).not.toHaveBeenCalled();
                expect(setCache).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Task fetched successfully',
                    result: { task }
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createTask', () => {
            it('should create a task and return success response', async () => {
                const input = { title: 'New Task', createdBy: 'user1', assignedTo: ['user2'] };
                const newTask = { id: '1', ...input };
                Task.prototype.save = jest.fn().mockResolvedValue(newTask);
            
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Task created successfully',
                    result: { task: newTask },
                });
            
                const result = await taskResolvers.Mutation.createTask(null, { input });

                expect(Task.prototype.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Task created successfully',
                    result: { task: newTask },
                });
            });

            it('should return error response on exception', async () => {
                const input = { title: 'New Task', createdBy: 'user1', assignedTo: ['user2'] };
                const error = new Error('Database error');
                Task.prototype.save = jest.fn().mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating task',
                    errors: [{ field: 'createTask', message: error.message }],
                });

                const result = await taskResolvers.Mutation.createTask(null, { input });

                expect(Task.prototype.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('TaskErrorResponse', 'FAILURE', 'Error creating task', { errors: [{ field: 'createTask', message: error.message }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating task',
                    errors: [{ field: 'createTask', message: error.message }],
                });
            });

            it('should handle invalid input', async () => {
                const invalidInput = {};
                const error = new Error('Invalid input');
                Task.prototype.save = jest.fn().mockRejectedValue(error);
            
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating task',
                    errors: [{ field: 'createTask', message: 'Invalid input' }],
                });
            
                const result = await taskResolvers.Mutation.createTask(null, { input: invalidInput });
            
                expect(Task.prototype.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('TaskErrorResponse', 'FAILURE', 'Error creating task', { errors: [{ field: 'createTask', message: 'Invalid input' }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating task',
                    errors: [{ field: 'createTask', message: 'Invalid input' }],
                });
            });
        });

        describe('updateTask', () => {
            it('should update task and return success response', async () => {
                const id = '1';
                const input = { title: 'Updated Task' };
                const task = { id, ...input, createdBy: 'user1', assignedTo: ['user2'] };
                Task.findByIdAndUpdate.mockResolvedValue(task);
                setCache.mockResolvedValue();

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Task updated successfully',
                    result: { task },
                });

                const result = await taskResolvers.Mutation.updateTask(null, { id, input });

                expect(Task.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(setCache).toHaveBeenCalledWith(id, task);
                expect(createResponse).toHaveBeenCalledWith('TaskResponse', 'SUCCESS', 'Task updated successfully', { result: { task } });
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Task updated successfully',
                    result: { task },
                });
            });

            it('should return error response if task not found', async () => {
                const id = '1';
                const input = { title: 'Updated Task' };
                Task.findByIdAndUpdate.mockResolvedValue(null);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'updateTask', message: 'Task not found' }],
                });

                const result = await taskResolvers.Mutation.updateTask(null, { id, input });

                expect(Task.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith('TaskErrorResponse', 'FAILURE', 'Task not found', { errors: [{ field: 'updateTask', message: 'Task not found' }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'updateTask', message: 'Task not found' }],
                });
            });

            it('should return error response on exception', async () => {
                const id = '1';
                const input = { title: 'Updated Task' };
                const error = new Error('Database error');
                Task.findByIdAndUpdate.mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error updating task',
                    errors: [{ field: 'updateTask', message: error.message }],
                });

                const result = await taskResolvers.Mutation.updateTask(null, { id, input });

                expect(Task.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith('TaskErrorResponse', 'FAILURE', 'Error updating task', { errors: [{ field: 'updateTask', message: error.message }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error updating task',
                    errors: [{ field: 'updateTask', message: error.message }],
                });
            });

            it('should return an error response if task not found during update', async () => {
                const id = 'nonexistent';
                const input = { title: 'Updated Task' };
                Task.findByIdAndUpdate.mockResolvedValue(null);
            
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'updateTask', message: 'Task not found' }],
                });
            
                const result = await taskResolvers.Mutation.updateTask(null, { id, input });
            
                expect(Task.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith('TaskErrorResponse', 'FAILURE', 'Task not found', { errors: [{ field: 'updateTask', message: 'Task not found' }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'updateTask', message: 'Task not found' }],
                });
            });
        });

        describe('deleteTask', () => {
            it('should check for references before deletion', async () => {
                const id = 'taskId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Task cannot be deleted',
                    errors: [{
                        field: 'deleteTask',
                        message: 'Task cannot be deleted as it has references in the following collections: Collection1, Collection2'
                    }]
                });

                const result = await taskResolvers.Mutation.deleteTask(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Task.findByIdAndDelete).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Task cannot be deleted',
                    errors: [{
                        field: 'deleteTask',
                        message: 'Task cannot be deleted as it has references in the following collections: Collection1, Collection2'
                    }]
                });
            });

            it('should delete task successfully when no references exist', async () => {
                const id = 'taskId';
                const task = { _id: id, title: 'Test Task' };
                
                findReferences.mockResolvedValue([]);
                Task.findByIdAndDelete.mockResolvedValue(task);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Task deleted successfully',
                    result: { task }
                });

                const result = await taskResolvers.Mutation.deleteTask(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Task.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TaskResponse',
                    'SUCCESS',
                    'Task deleted successfully',
                    { result: { task } }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Task deleted successfully',
                    result: { task }
                });
            });

            it('should handle cache clearing error but still delete successfully', async () => {
                const id = 'taskId';
                const task = { _id: id, title: 'Test Task' };
                const cacheError = new Error('Cache clearing failed');
                
                findReferences.mockResolvedValue([]);
                Task.findByIdAndDelete.mockResolvedValue(task);
                clearCacheById.mockRejectedValue(cacheError);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Task deleted successfully, but cache clearing failed',
                    result: { task },
                    errors: [{ field: 'cache', message: cacheError.message }]
                });

                const result = await taskResolvers.Mutation.deleteTask(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Task.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TaskResponse',
                    'SUCCESS',
                    'Task deleted successfully, but cache clearing failed',
                    { result: { task }, errors: [{ field: 'cache', message: cacheError.message }] }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Task deleted successfully, but cache clearing failed',
                    result: { task },
                    errors: [{ field: 'cache', message: cacheError.message }]
                });
            });

            it('should return error response if task not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                Task.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'deleteTask', message: 'Task not found' }]
                });

                const result = await taskResolvers.Mutation.deleteTask(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Task.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TaskErrorResponse',
                    'FAILURE',
                    'Task not found',
                    { errors: [{ field: 'deleteTask', message: 'Task not found' }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Task not found',
                    errors: [{ field: 'deleteTask', message: 'Task not found' }]
                });
            });

            it('should handle deletion errors', async () => {
                const id = 'taskId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Task.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting task',
                    errors: [{ field: 'deleteTask', message: error.message }]
                });

                const result = await taskResolvers.Mutation.deleteTask(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Task.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TaskErrorResponse',
                    'FAILURE',
                    'Error deleting task',
                    { errors: [{ field: 'deleteTask', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting task',
                    errors: [{ field: 'deleteTask', message: error.message }]
                });
            });
        });
    });
});
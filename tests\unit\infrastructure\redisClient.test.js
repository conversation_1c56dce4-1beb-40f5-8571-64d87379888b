const Redis = require("ioredis");
const getRedisClient = require("../../../src/infrastructure/redisClient");

jest.mock('ioredis', () => {
    return jest.fn().mockImplementation(() => {
        return {
            on: jest.fn((event, callback) => {
                if (event === 'error') {
                    this.errorCallback = callback;
                }
                if (event === 'connect') {
                    this.connectCallback = callback;
                }
            }),
            emit: jest.fn((event, error) => {
                if (event === 'error' && this.errorCallback) {
                    this.errorCallback(error);
                }
                if (event === 'connect' && this.connectCallback) {
                    this.connectCallback();
                }
            }),
        };
    });
});

describe("getRedisClient", () => {
    it("should return a new Redis client if not already created", () => {
        const redisClient = getRedisClient();

        expect(Redis).toHaveBeenCalledTimes(1);
        expect(redisClient).toBeDefined();
        expect(typeof redisClient.on).toBe('function');
    });

    it("should return the existing Redis client if already created", () => {
        const redisClient1 = getRedisClient();
        const redisClient2 = getRedisClient();

        expect(redisClient1).toBe(redisClient2);
        expect(Redis).toHaveBeenCalledTimes(1); 
    });

    it('should handle Redis error event', () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
        
        const client = getRedisClient();

        client.emit('error', new Error("Connection error"));

        expect(consoleErrorSpy).toHaveBeenCalledWith("Redis error", expect.any(Error));

        consoleErrorSpy.mockRestore(); 
    });

    it('should handle Redis connect event', () => {
        const redisClient = getRedisClient();
        const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(); 
        
        redisClient.emit('connect');

        expect(consoleLogSpy).toHaveBeenCalledWith("Connected to Redis");

        consoleLogSpy.mockRestore(); 
    });
});

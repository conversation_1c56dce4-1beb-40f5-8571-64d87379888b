const { model, Schema } = require('mongoose');

const guestSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: true },
    additionalGuestsCount: { type: Number, required: false, default: 0 }
}, { timestamps: true, collection: 'guests' });

const Guest = model('Guest', guestSchema);

module.exports = Guest;
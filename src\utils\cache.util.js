const getRedisClient = require('../infrastructure/redisClient');

const TIMEOUT_DURATION = process.env.REDIS_TIMEOUT;
const VARIABLE_EXPIRATION = process.env.REDIS_VARIABLE_EXPIRATION;

async function withTimeout(promise) {
    return Promise.race([
        promise,
        new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Operation timed out')), TIMEOUT_DURATION)
        )
    ]);
}

async function getByIdCache(id) {
    const cacheKey = id;
    const redis = getRedisClient();

    console.log('Fetching from the cache');
    try {
        const cachedData = await withTimeout(redis.get(cacheKey));
        if (cachedData) {
            console.log('Data found in cache');
            const parsedData = JSON.parse(cachedData);
            parsedData.id = parsedData._id;
            return parsedData;
        }
        console.log(`Cache miss for ID ${id}, fetching from database`);
        return null; 
    } catch (error) {
        console.error('Error fetching from cache:', error);
        return null;
    }
}

async function getBySubstring(substring) {
    const redis = getRedisClient();

    console.log('Fetching from the cache');
    try {
        const keys = await redis.keys('*' + substring + '*');
        if (keys.length > 0) {
            const cachedDataPromises = keys.map(key => redis.get(key));
            const cachedDataArray = await Promise.all(cachedDataPromises);
            const parsedDataArray = cachedDataArray.map(cachedData => {
                if (cachedData) {
                    const parsedData = JSON.parse(cachedData);
                    parsedData.id = parsedData._id;
                    return parsedData;
                }
                return null;
            }).filter(data => data !== null);

            console.log('Data found in cache');

            const latestData = parsedDataArray.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

            return latestData;
        }
        console.log(`Cache miss for substring ${substring}, fetching from database`);
        return null; 
    } catch (error) {
        console.error('Error fetching from cache:', error);
        return null;
    }
}

async function setCache(id, data) {
    const cacheKey = id;
    const redis = getRedisClient();

    try {
        await withTimeout(redis.set(cacheKey, JSON.stringify(data), 'EX', VARIABLE_EXPIRATION));
        console.log(`Cache set for ID ${id}`);
    } catch (error) {
        console.error('Error setting cache:', error);
    }
}

const setCachePermanent = async (id, data) => {
    try {
        const redis = getRedisClient();
        await redis.set(id, JSON.stringify(data));
    } catch (error) {
        console.error('Error setting permanent cache:', error);
    }
};

async function clearCacheById(id) {
    const cacheKey = id;
    const redis = getRedisClient();

    console.log(`Clearing cache for ID ${id}`);
    try {
        await withTimeout(redis.del(cacheKey));
        console.log(`Cache cleared for ID ${id}`);
    } catch (error) {
        console.error('Error clearing cache:', error);
    }
}

async function clearCacheBySubstring(substring) {
    const redis = getRedisClient();

    console.log(`Clearing cache for substring ${substring}`);
    try {
        const keys = await redis.keys('*' + substring + '*');
        if (keys.length > 0) {
            await Promise.all(keys.map(key => redis.del(key)));
            console.log(`Cache cleared for keys matching substring ${substring}`);
        } else {
            console.log(`No cache keys found matching substring ${substring}`);
        }
    } catch (error) {
        console.error('Error clearing cache:', error);
    }
}

async function clearAllCache() {
    const redis = getRedisClient();

    console.log('Clearing all cache');
    try {
        await withTimeout(redis.flushall());
        console.log('All cache cleared');
    } catch (error) {
        console.error('Error clearing all cache:', error);
    }
}

module.exports = { 
    getByIdCache,
    setCache,
    clearCacheById,
    clearAllCache,
    getBySubstring,
    clearCacheBySubstring,
    setCachePermanent
};

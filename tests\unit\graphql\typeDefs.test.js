const path = require('path');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');

jest.mock('@graphql-tools/load-files', () => ({
  loadFilesSync: jest.fn(),
}));

jest.mock('@graphql-tools/merge', () => ({
  mergeTypeDefs: jest.fn(),
}));

describe('typeDefs', () => {
  it('should load and merge type definitions correctly', () => {
    const mockTypesArray = [
      `type Query { hello: String }`,
      `type Mutation { sayHello(name: String!): String }`
    ];
    const mockMergedTypeDefs = `type Query { hello: String } type Mutation { sayHello(name: String!): String }`;

    loadFilesSync.mockReturnValue(mockTypesArray);
    mergeTypeDefs.mockReturnValue(mockMergedTypeDefs);

    const typeDefs = require('../../../src/graphql/typeDefs');

    expect(loadFilesSync).toHaveBeenCalledWith(path.join(__dirname, '../../../src/graphql/typedefs'));
    expect(mergeTypeDefs).toHaveBeenCalledWith(mockTypesArray);
    expect(typeDefs).toBe(mockMergedTypeDefs);
  });
});
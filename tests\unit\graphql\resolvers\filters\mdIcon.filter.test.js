const buildMdIconQuery = require('../../../../../src/graphql/resolvers/filters/mdIcon.filter');

describe('buildMdIconQuery', () => {
    it('should return an empty query when no filter is provided', () => {
        const filter = {};
        const query = buildMdIconQuery(filter);
        expect(query).toEqual({});
    });

    it('should return a query with name regex when name filter is provided', () => {
        const filter = { name: 'test' };
        const query = buildMdIconQuery(filter);
        expect(query).toEqual({ name: { $regex: new RegExp('test', 'i') } });
    });

    it('should return a query with id when id filter is provided', () => {
        const filter = { id: '507f1f77bcf86cd799439011' };
        const query = buildMdIconQuery(filter);
        expect(query).toEqual({ _id: filter.id });
    });

    it('should return a query with all fields when all filters are provided', () => {
        const filter = { name: 'test', id: '507f1f77bcf86cd799439011' };
        const query = buildMdIconQuery(filter);
        expect(query).toEqual({
            name: { $regex: new RegExp('test', 'i') },
            _id: filter.id
        });
    });

    it('should throw an error when invalid id is provided', () => {
        const filter = { id: 'invalid-id' };
        expect(() => buildMdIconQuery(filter)).toThrow('Invalid id provided');
    });
});
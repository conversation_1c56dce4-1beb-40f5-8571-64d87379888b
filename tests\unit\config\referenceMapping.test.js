const Party = require('../../../src/models/Party');
const PartyService = require('../../../src/models/PartyService');
const Transaction = require('../../../src/models/Transaction');
const Task = require('../../../src/models/Task');
const referenceMapping = require('../../../src/config/referenceMapping');
const Vendor = require('../../../src/models/Vendor');
const MdFunFact = require('../../../src/models/MdFunFact');
const MdFeature = require('../../../src/models/MdFeature');
const MdPartner = require('../../../src/models/MdPartner');
const MdPartnerCategory = require('../../../src/models/MdPartnerCategory');
const MdVendorType = require('../../../src/models/MdVendorType');
const MdAppSettings = require('../../../src/models/MdAppSettings');
const MdEventType = require('../../../src/models/MdEventType');
const Guest = require('../../../src/models/Guest');
const MdServiceLocation = require('../../../src/models/MdServiceLocation');
const Event = require('../../../src/models/Event');
const VendorService = require('../../../src/models/VendorService');
const MdPartyType = require('../../../src/models/MdPartyType');
const Tag = require('../../../src/models/Tag');
const MdPromotion = require('../../../src/models/MdPromotion');
const Reaction = require('../../../src/models/Reaction');

describe('Reference Mapping Configuration', () => {
    it('should have correct mapping for MdPartyType', () => {
        const mapping = referenceMapping.MdPartyType;
        expect(mapping).toHaveLength(1);
        expect(mapping[0]).toEqual({
            model: Party,
            field: 'partyType'
        });
    });

    it('should have correct mapping for Address', () => {
        const mapping = referenceMapping.Address;
        expect(mapping).toHaveLength(4);
        expect(mapping).toEqual([
            {
                model: Party,
                field: 'address'
            },
            {
                model: PartyService,
                field: 'address'
            },
            {
                model: Vendor,
                field: 'businessAddress'
            },
            {
                model: Event,
                field: 'location'
            }
        ]);
    });

    it('should have correct mapping for Host', () => {
        const mapping = referenceMapping.Host;
        expect(mapping).toHaveLength(3);
        expect(mapping).toEqual([
            {
                model: Party,
                field: 'coHosts',
                isArray: true
            },
            {
                model: Event,
                field: 'mainHost'
            },
            {
                model: Event,
                field: 'coHosts',
                isArray: true
            }
        ]);
    });

    it('should have correct mapping for PartyService', () => {
        const mapping = referenceMapping.PartyService;
        expect(mapping).toHaveLength(3);
        expect(mapping).toEqual([
            {
                model: Party,
                field: 'services',
                isArray: true
            },
            {
                model: Transaction,
                field: 'partyService'
            },
            {
                model: Task,
                field: 'partyService'
            }
        ]);
    });

    it('should have correct mapping for Transaction', () => {
        const mapping = referenceMapping.Transaction;
        expect(mapping).toHaveLength(1);
        expect(mapping[0]).toEqual({
            model: PartyService,
            field: 'transactions',
            isArray: true
        });
    });

    it('should have correct mapping for Task', () => {
        const mapping = referenceMapping.Task;
        expect(mapping).toHaveLength(1);
        expect(mapping[0]).toEqual({
            model: PartyService,
            field: 'tasks',
            isArray: true
        });
    });

    it('should have correct mapping for Vendor', () => {
        const mapping = referenceMapping.Vendor;
        expect(mapping).toHaveLength(1);
        expect(mapping[0]).toEqual({
            model: PartyService,
            field: 'vendor'
        });
    });

    it('should have correct mapping for MdEventType', () => {
        const mapping = referenceMapping.MdEventType;
        expect(mapping).toHaveLength(2);
        expect(mapping).toEqual([
            {
                model: MdFunFact,
                field: 'eventTypes',
                isArray: true
            },
            {
                model: Event,
                field: 'eventType'
            }
        ]);
    });

    it('should have correct mapping for MdIcon', () => {
        const mapping = referenceMapping.MdIcon;
        expect(mapping).toHaveLength(13);
        expect(mapping).toEqual([
            { model: MdFeature, field: 'icon' },
            { model: MdPartner, field: 'icon' },
            { model: MdPartnerCategory, field: 'icon' },
            { model: MdVendorType, field: 'icon' },
            { model: MdAppSettings, field: 'navBars.userDashboard.tabs.home' },
            { model: MdAppSettings, field: 'navBars.userDashboard.tabs.myEvents' },
            { model: MdAppSettings, field: 'navBars.userDashboard.tabs.publicEvents' },
            { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.eventsDb' },
            { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.tasks' },
            { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.guests' },
            { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.apps' },
            { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.messages' },
            { model: Tag, field: 'icon' }
        ]);
    });

    it('should have correct mapping for MdFunFact', () => {
        const mapping = referenceMapping.MdFunFact;
        expect(mapping).toHaveLength(1);
        expect(mapping[0]).toEqual({
            model: MdEventType, field: 'eventTypes', isArray: true
        });
    });

    it('should have correct mapping for MdVendorType', () => {
        const mapping = referenceMapping.MdVendorType;
        expect(mapping).toHaveLength(4);
        expect(mapping).toEqual([
            { model: Party, field: 'vendorTypes', isArray: true },
            { model: VendorService, field: 'vendorTypes', isArray: true },
            { model: MdPartyType, field: 'vendorTypes', isArray: true },
            { model: PartyService, field: 'vendorType' }
        ]);
    });

    it('should have correct mapping for MdPartner', () => {
        const mapping = referenceMapping.MdPartner;
        expect(mapping).toHaveLength(2);
        expect(mapping).toEqual([
            { model: MdPromotion, field: 'partner' },
            { model: MdVendorType, field: 'partners', isArray: true }
        ]);
    });

    it('should have correct mapping for MdReaction', () => {
        const mapping = referenceMapping.MdReaction;
        expect(mapping).toHaveLength(1);
        expect(mapping).toEqual([
            { model: Reaction, field: 'type' }
        ]);
    });

    it('should contain all expected reference types', () => {
        const expectedTypes = [
            'MdPartyType',
            'Address',
            'Host',
            'Guest',
            'PartyService',
            'Transaction',
            'Task',
            'Vendor',
            'MdVendorType',
            'MdEventType',
            'MdIcon',
            'MdFunFact',
            'MdTheme',
            'MdRole',
            'MdServiceLocation',
            'Party',
            'MdVendorSubType',
            'Event',
            'MdChecklist',
            'MdPolicy',
            'Policy',
            'Tag',
            'MdPromotion',
            'MdPartner',
            'MdReaction',
            'Reaction'
        ];

        expect(Object.keys(referenceMapping)).toEqual(expectedTypes);
    });
}); 
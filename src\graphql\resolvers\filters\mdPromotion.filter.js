const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdPartnerQuery = require("./mdPartner.filter");
const buildTagQuery = require("./tag.filter");
const MdPartner = require("../../../models/MdPartner");
const Tag = require("../../../models/Tag");

const buildMdPromotionQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.mediaUrl) {
            query.mediaUrl = { $regex: filter.mediaUrl, $options: 'i' };
        }

        if (filter.ctaLink) {
            query.ctaLink = { $regex: filter.ctaLink, $options: 'i' };
        }

        if (filter.active !== undefined) {
            query.active = filter.active;
        }

        if (filter.dateRange) {
            if (filter.dateRange.start) {
                query.endDate = query.endDate || {};
                query.endDate.$gte = filter.dateRange.start;
            }
            if (filter.dateRange.end) {
                query.startDate = query.startDate || {};
                query.startDate.$lte = filter.dateRange.end;
            }
        }

        if (filter.partner) {
            if (typeof filter.partner === 'string') {
                if (isValidObjectId(filter.partner)) {
                    query.partner = filter.partner;
                } else {
                    throw new Error('Invalid partner ID provided');
                }
            } else if (typeof filter.partner === 'object') {
                const partnerQuery = await buildMdPartnerQuery(filter.partner);
                if (Object.keys(partnerQuery).length > 0) {
                    const matchingPartners = await MdPartner.find(partnerQuery).select('_id');
                    if (matchingPartners.length > 0) {
                        query.partner = { $in: matchingPartners.map(partner => partner._id) };
                    } else {
                        query.partner = { $in: [] };
                    }
                }
            }
        }

        if (filter.tag) {
            if (typeof filter.tag === 'string') {
                if (isValidObjectId(filter.tag)) {
                    query.tags = filter.tag;
                } else {
                    throw new Error('Invalid tag ID provided');
                }
            } else if (typeof filter.tag === 'object') {
                const tagQuery = await buildTagQuery(filter.tag);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.tags = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.tags = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdPromotionQuery; 
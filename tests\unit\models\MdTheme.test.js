const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdTheme = require('../../../src/models/MdTheme');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdTheme Model Test', () => {
    it('should create and save a MdTheme successfully', async () => {
        const validMdTheme = new MdTheme({
            name: 'Summer Theme',
            description: 'A bright and cheerful summer theme',
            primaryColor: '#FF5733',
            secondaryColor: '#33FF57',
            backgroundColor: '#FFFFFF',
            textColor: '#000000'
        });

        const savedMdTheme = await validMdTheme.save();

        expect(savedMdTheme._id).toBeDefined();
        expect(savedMdTheme.name).toBe('Summer Theme');
        expect(savedMdTheme.description).toBe('A bright and cheerful summer theme');
        expect(savedMdTheme.primaryColor).toBe('#FF5733');
        expect(savedMdTheme.secondaryColor).toBe('#33FF57');
        expect(savedMdTheme.backgroundColor).toBe('#FFFFFF');
        expect(savedMdTheme.textColor).toBe('#000000');
        expect(savedMdTheme.createdAt).toBeDefined();
        expect(savedMdTheme.updatedAt).toBeDefined();
    });

    it('should fail to create a MdTheme without required fields', async () => {
        const mdTheme = new MdTheme();

        let err;
        try {
            await mdTheme.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
        expect(err.errors.primaryColor).toBeDefined();
        expect(err.errors.secondaryColor).toBeDefined();
        expect(err.errors.backgroundColor).toBeDefined();
        expect(err.errors.textColor).toBeDefined();
    });

    it('should create a MdTheme with valid hex color codes', async () => {
        const validMdTheme = new MdTheme({
            name: 'Dark Theme',
            description: 'A dark theme for night mode',
            primaryColor: '#121212',
            secondaryColor: '#333333',
            backgroundColor: '#000000',
            textColor: '#FFFFFF'
        });

        const savedMdTheme = await validMdTheme.save();

        expect(savedMdTheme._id).toBeDefined();
        expect(savedMdTheme.primaryColor).toBe('#121212');
        expect(savedMdTheme.secondaryColor).toBe('#333333');
        expect(savedMdTheme.backgroundColor).toBe('#000000');
        expect(savedMdTheme.textColor).toBe('#FFFFFF');
    });
}); 
const { BlobServiceClient } = require('@azure/storage-blob');
const { uploadFileToBlob, deleteFileFromBlob, uploadFilesToBlob, getContainerClient } = require('../../../src/utils/azureStorage.util');

jest.mock('@azure/storage-blob', () => ({
    BlobServiceClient: {
        fromConnectionString: jest.fn().mockReturnValue({
            getContainerClient: jest.fn().mockReturnValue({})
        })
    }
}));

describe('Azure Storage Utility Tests', () => {
    let mockContainerClient;
    let mockBlockBlobClient;
    let mockStream;

    beforeEach(() => {
        jest.clearAllMocks();

        mockStream = {
            on: jest.fn(),
            pipe: jest.fn()
        };

        mockBlockBlobClient = {
            uploadStream: jest.fn().mockResolvedValue({}),
            setHTTPHeaders: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
            url: 'https://test-url.com/test-blob'
        };

        mockContainerClient = {
            getBlockBlobClient: jest.fn().mockReturnValue(mockBlockBlobClient)
        };

        BlobServiceClient.fromConnectionString().getContainerClient.mockReturnValue(mockContainerClient);
    });

    describe('uploadFileToBlob', () => {
        const validFile = {
            createReadStream: jest.fn().mockReturnValue(mockStream),
            filename: 'test.pdf',
            mimetype: 'application/pdf'
        };

        it('should successfully upload a file', async () => {
            const result = await uploadFileToBlob(validFile);

            expect(result).toHaveProperty('url');
            expect(result).toHaveProperty('key');
            expect(result.url).toBe('https://test-url.com/test-blob');
            expect(mockBlockBlobClient.uploadStream).toHaveBeenCalled();
            expect(mockBlockBlobClient.setHTTPHeaders).toHaveBeenCalledWith({
                blobContentType: 'application/pdf'
            });
        });

        it('should handle null/undefined file input', async () => {
            await expect(uploadFileToBlob(null)).rejects.toThrow('No file provided');
            await expect(uploadFileToBlob(undefined)).rejects.toThrow('No file provided');
        });

        it('should handle invalid file format', async () => {
            const invalidFile = 'not-an-object';
            await expect(uploadFileToBlob(invalidFile)).rejects.toThrow('Invalid file format');
        });

        it('should handle missing required properties', async () => {
            const invalidFile = {};
            await expect(uploadFileToBlob(invalidFile)).rejects.toThrow(/Invalid file format/);
        });

        it('should handle Promise-based file input', async () => {
            const filePromise = Promise.resolve(validFile);
            const result = await uploadFileToBlob(filePromise);

            expect(result).toHaveProperty('url');
            expect(result).toHaveProperty('key');
        });

        it('should throw error when fileData is null', async () => {
            const fileData = null;
            
            await expect(uploadFileToBlob(fileData))
                .rejects
                .toThrow('No file provided');
        });

        it('should throw error when fileData is undefined', async () => {
            const fileData = undefined;
            
            await expect(uploadFileToBlob(fileData))
                .rejects
                .toThrow('No file provided');
        });

        it('should throw error when fileData is not an object', async () => {
            const testCases = [
                'string',
                123,
                true,
                [],
                () => {}
            ];

            for (const fileData of testCases) {
                await expect(uploadFileToBlob(fileData))
                    .rejects
                    .toThrow('Invalid file format - file must be an object');
            }
        });

        it('should throw error when fileData object is empty', async () => {
            const fileData = {};
            
            await expect(uploadFileToBlob(fileData))
                .rejects
                .toThrow('Invalid file format: createReadStream is missing, filename is missing, mimetype is missing');
        });

        it('should throw error when fileData is missing required properties', async () => {
            const testCases = [
                { createReadStream: () => {}, filename: 'test.txt' }, 
                { createReadStream: () => {}, mimetype: 'text/plain' }, 
                { filename: 'test.txt', mimetype: 'text/plain' }, 
            ];

            for (const fileData of testCases) {
                await expect(uploadFileToBlob(fileData))
                    .rejects
                    .toThrow(/Invalid file format:/);
            }
        });

        it('should throw error when fileData properties have invalid types', async () => {
            const testCases = [
                { 
                    createReadStream: 'not a function', 
                    filename: 'test.txt', 
                    mimetype: 'text/plain' 
                },
                { 
                    createReadStream: () => {}, 
                    filename: 123,
                    mimetype: 'text/plain' 
                },
                { 
                    createReadStream: () => {}, 
                    filename: 'test.txt', 
                    mimetype: true
                }
            ];

            for (const fileData of testCases) {
                await expect(uploadFileToBlob(fileData))
                    .rejects
                    .toThrow(/Invalid file format:/);
            }
        });

        it('should handle file with promise property successfully', async () => {
            const mockStream = jest.fn();
            const mockFileData = {
                createReadStream: () => mockStream,
                filename: 'test.pdf',
                mimetype: 'application/pdf'
            };

            const fileWithPromise = {
                promise: Promise.resolve(mockFileData)
            };

            const result = await uploadFileToBlob(fileWithPromise);

            expect(result).toEqual({
                url: 'https://test-url.com/test-blob',
                key: expect.stringMatching(/^[0-9a-f-]{36}\.pdf$/)
            });

            expect(mockContainerClient.getBlockBlobClient).toHaveBeenCalledWith(
                expect.stringMatching(/^[0-9a-f-]{36}\.pdf$/)
            );
            expect(mockBlockBlobClient.uploadStream).toHaveBeenCalledWith(mockStream);
            expect(mockBlockBlobClient.setHTTPHeaders).toHaveBeenCalledWith({
                blobContentType: 'application/pdf'
            });
        });

        it('should handle rejected file.promise appropriately', async () => {
            const fileWithPromise = {
                promise: Promise.reject(new Error('Failed to process file'))
            };

            await expect(uploadFileToBlob(fileWithPromise)).rejects.toThrow('Failed to process uploaded file');
        });

        it('should handle null file.promise value', async () => {
            const fileWithPromise = {
                promise: Promise.resolve(null)
            };

            await expect(uploadFileToBlob(fileWithPromise)).rejects.toThrow('Invalid file format - file must be an object');
        });

        it('should handle file.promise resolving to invalid data', async () => {
            const fileWithPromise = {
                promise: Promise.resolve({
                    someOtherProperty: 'test'
                })
            };

            await expect(uploadFileToBlob(fileWithPromise)).rejects.toThrow(/Invalid file format:/);
        });
    });

    describe('uploadFilesToBlob', () => {
        const validFiles = [
            {
                createReadStream: jest.fn().mockReturnValue(mockStream),
                filename: 'test1.pdf',
                mimetype: 'application/pdf'
            },
            {
                createReadStream: jest.fn().mockReturnValue(mockStream),
                filename: 'test2.pdf',
                mimetype: 'application/pdf'
            }
        ];

        it('should successfully upload multiple files', async () => {
            const result = await uploadFilesToBlob(validFiles);

            expect(result.successful).toHaveLength(2);
            expect(result.failures).toHaveLength(0);
            expect(result.totalProcessed).toBe(2);
            expect(result.successCount).toBe(2);
            expect(result.failureCount).toBe(0);
        });

        it('should handle mixed success and failure uploads', async () => {
            const mixedFiles = [
                validFiles[0],
                { invalid: true }
            ];

            const result = await uploadFilesToBlob(mixedFiles);

            expect(result.successful).toHaveLength(1);
            expect(result.failures).toHaveLength(1);
            expect(result.totalProcessed).toBe(2);
            expect(result.successCount).toBe(1);
            expect(result.failureCount).toBe(1);
        });

        it('should handle empty input', async () => {
            await expect(uploadFilesToBlob(null)).rejects.toThrow('No files provided');
        });
    });

    describe('deleteFileFromBlob', () => {
        it('should successfully delete a file', async () => {
            const result = await deleteFileFromBlob('test-key');
            
            expect(result).toBe(true);
            expect(mockBlockBlobClient.delete).toHaveBeenCalled();
        });

        it('should handle deletion errors', async () => {
            mockBlockBlobClient.delete.mockRejectedValue(new Error('Delete failed'));
            
            await expect(deleteFileFromBlob('test-key')).rejects.toThrow('Error deleting file');
        });
    });

    describe('getContainerClient', () => {
        it('should return container client with default container name', () => {
            const client = getContainerClient();
            expect(BlobServiceClient.fromConnectionString().getContainerClient)
                .toHaveBeenCalledWith('documents');
        });

        it('should return container client with custom container name', () => {
            const client = getContainerClient('custom-container');
            expect(BlobServiceClient.fromConnectionString().getContainerClient)
                .toHaveBeenCalledWith('custom-container');
        });
    });
});

describe('azureStorage utils', () => {
    describe('uploadFileToBlob', () => {
        it('should throw error when fileData is null', async () => {
            await expect(uploadFileToBlob(null))
                .rejects
                .toThrow('No file provided');
        });

        it('should throw error when fileData is undefined', async () => {
            await expect(uploadFileToBlob(undefined))
                .rejects
                .toThrow('No file provided');
        });

        it('should throw error when fileData is not an object', async () => {
            const testCases = [
                'string',
                123,
                true,
                [],
                () => {}
            ];

            for (const testCase of testCases) {
                await expect(uploadFileToBlob(testCase))
                .rejects
                .toThrow('Invalid file format - file must be an object');
            }
        });

        it('should throw error when required file properties are missing', async () => {
            const testCases = [
                {},
                { createReadStream: null },
                { createReadStream: 'not-a-function' },
                { createReadStream: () => {}, filename: null },
                { createReadStream: () => {}, filename: 123 },
                { createReadStream: () => {}, filename: 'test.txt', mimetype: null },
                { createReadStream: () => {}, filename: 'test.txt', mimetype: 123 }
            ];

            for (const testCase of testCases) {
                await expect(uploadFileToBlob(testCase))
                .rejects
                .toThrow(/Invalid file format:/);
            }
        });
    });
});

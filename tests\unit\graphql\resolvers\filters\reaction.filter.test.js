const mongoose = require('mongoose');
const buildReactionQuery = require('../../../../../src/graphql/resolvers/filters/reaction.filter');
const buildMdReactionQuery = require('../../../../../src/graphql/resolvers/filters/mdReaction.filter');
const buildUserQuery = require('../../../../../src/graphql/resolvers/filters/user.filter');
const MdReaction = require('../../../../../src/models/MdReaction');
const User = require('../../../../../src/models/User');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/mdReaction.filter');
jest.mock('../../../../../src/graphql/resolvers/filters/user.filter');
jest.mock('../../../../../src/models/MdReaction', () => ({
    find: jest.fn().mockReturnValue({
        select: jest.fn()
    })
}));
jest.mock('../../../../../src/models/User', () => ({
    find: jest.fn().mockReturnValue({
        select: jest.fn()
    })
}));

describe('buildReactionQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', async () => {
        const query = await buildReactionQuery();
        expect(query).toEqual({});
    });

    describe('type filter', () => {
        it('should handle string type ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { type: 'validTypeId' };
            
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                type: 'validTypeId'
            });
        });

        it('should throw error for invalid type ID', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { type: 'invalidTypeId' };
            
            await expect(buildReactionQuery(filter))
                .rejects.toThrow('Invalid type ID provided');
        });

        it('should handle object type filter with results', async () => {
            const typeFilter = { name: 'like' };
            const typeQuery = { name: 'like' };
            const matchingTypes = [{ _id: 'type1' }, { _id: 'type2' }];
            
            buildMdReactionQuery.mockResolvedValue(typeQuery);
            MdReaction.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingTypes)
            });
            
            const filter = { type: typeFilter };
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                type: { $in: ['type1', 'type2'] }
            });
        });

        it('should handle object type filter with no results', async () => {
            buildMdReactionQuery.mockResolvedValue({ name: 'nonexistent' });
            MdReaction.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });
            
            const filter = { type: { name: 'nonexistent' } };
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                type: { $in: [] }
            });
        });
    });

    describe('user filter', () => {
        it('should handle string user ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { user: 'validUserId' };
            
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                user: 'validUserId'
            });
        });

        it('should throw error for invalid user ID', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { user: 'invalidUserId' };
            
            await expect(buildReactionQuery(filter))
                .rejects.toThrow('Invalid user ID provided');
        });

        it('should handle object user filter with results', async () => {
            const userFilter = { firstName: 'John' };
            const userQuery = { firstName: 'John' };
            const matchingUsers = [{ _id: 'user1' }, { _id: 'user2' }];
            
            buildUserQuery.mockResolvedValue(userQuery);
            User.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingUsers)
            });
            
            const filter = { user: userFilter };
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                user: { $in: ['user1', 'user2'] }
            });
        });

        it('should handle object user filter with no results', async () => {
            buildUserQuery.mockResolvedValue({ firstName: 'nonexistent' });
            User.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });
            
            const filter = { user: { firstName: 'nonexistent' } };
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                user: { $in: [] }
            });
        });
    });

    describe('timestamp filter', () => {
        it('should handle timestamp start filter', async () => {
            const startDate = new Date('2024-01-01');
            const filter = { timestamp: { start: startDate } };
            
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                timestamp: { $gte: startDate }
            });
        });

        it('should handle timestamp end filter', async () => {
            const endDate = new Date('2024-12-31');
            const filter = { timestamp: { end: endDate } };
            
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                timestamp: { $lte: endDate }
            });
        });

        it('should handle timestamp range filter', async () => {
            const startDate = new Date('2024-01-01');
            const endDate = new Date('2024-12-31');
            const filter = { timestamp: { start: startDate, end: endDate } };
            
            const query = await buildReactionQuery(filter);
            
            expect(query).toEqual({
                timestamp: { $gte: startDate, $lte: endDate }
            });
        });
    });

    it('should handle multiple filters together', async () => {
        isValidObjectId.mockReturnValue(true);
        const startDate = new Date('2024-01-01');
        const filter = {
            type: 'validTypeId',
            user: 'validUserId',
            timestamp: { start: startDate }
        };
        
        const query = await buildReactionQuery(filter);
        
        expect(query).toEqual({
            type: 'validTypeId',
            user: 'validUserId',
            timestamp: { $gte: startDate }
        });
    });
}); 
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdPartyType = require('../../../src/models/MdPartyType');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdPartyType Model Test', () => {
    it('should create and save a MdPartyType successfully', async () => {
        const validMdPartyType = new MdPartyType({
            name: 'Party Type Name',
            description: 'Party Type Description',
            vendorTypes: [new mongoose.Types.ObjectId(), new mongoose.Types.ObjectId()],
            landscapeImage: 'landscape.jpg',
            portraitImage: 'portrait.jpg'
        });
    
        const savedMdPartyType = await validMdPartyType.save();
    
        expect(savedMdPartyType._id).toBeDefined();
        expect(savedMdPartyType.name).toBe('Party Type Name');
        expect(savedMdPartyType.description).toBe('Party Type Description');
        expect(savedMdPartyType.vendorTypes).toHaveLength(2);
        expect(savedMdPartyType.vendorTypes[0]).toBeInstanceOf(mongoose.Types.ObjectId);
        expect(savedMdPartyType.vendorTypes[1]).toBeInstanceOf(mongoose.Types.ObjectId);
        expect(savedMdPartyType.landscapeImage).toBe('landscape.jpg');
        expect(savedMdPartyType.portraitImage).toBe('portrait.jpg');
    });
    it('should fail to create a MdPartyType without required fields', async () => {
        const mdPartyType = new MdPartyType();

        let err;
        try {
            await mdPartyType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });
});
const Redis = require("ioredis");

let redisInstance;

function getRedisClient() {
    if (!redisInstance) {
        redisInstance = new Redis({
            host: process.env.REDIS_HOST,
            port: process.env.REDIS_PORT,
            password: process.env.REDIS_PASSWORD,
            tls: {
                rejectUnauthorized: process.env.REDIS_TLS === 'true',
            },
            retryStrategy(times) {
                const delay = Math.min(times * 50, 2000);
                return delay;
            },
            maxRetriesPerRequest: 3
        });

        redisInstance.on('connect', () => {
            console.log('Connected to Redis');
        });

        redisInstance.on('ready', () => {
            console.log('Redis client ready');
        });

        redisInstance.on('error', (err) => {
            console.error('Redis error:', err);
        });

        redisInstance.on('close', () => {
            console.log('Redis connection closed');
        });

        redisInstance.on('reconnecting', () => {
            console.log('Redis client reconnecting');
        });

        redisInstance.on('end', () => {
            console.log('Redis connection ended');
        });
    }

    return redisInstance;
}

getRedisClient.healthCheck = async () => {
    try {
        const redis = getRedisClient();
        await redis.ping();
        return true;
    } catch (error) {
        console.error('Redis health check failed:', error);
        return false;
    }
};

module.exports = getRedisClient;
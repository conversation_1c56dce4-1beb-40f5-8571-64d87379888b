const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const articleSchema = new Schema({
    headline: { type: String, required: true },
    text: { type: String, required: true }, // This will be mapped to headline in GraphQL
    image: { type: String },
    source: {
        link: { type: String },
        mainUrl: { type: String }
    },
    date: { type: String }
    }, { 
    timestamps: true,
    collection: 'fastparty_articles'
    });

const Article = model('Article', articleSchema);

module.exports = Article;

const { model, Schema } = require('mongoose');

const addressSchema = new Schema({
    addressType: { type: String, enum: ['HOME', 'WORK', 'OTHER'], required: true },
    street1: { type: String, required: true },
    street2: { type: String },
    city: { type: String, required: true },
    state: { type: String, required: true },
    postalCode: { type: String, required: true },
    country: { type: String, required: true },
    isPrimary: { type: Boolean, required: true, default: false }
}, { timestamps: true, collection: 'addresses' });

const Address = model('Address', addressSchema);

module.exports = Address;
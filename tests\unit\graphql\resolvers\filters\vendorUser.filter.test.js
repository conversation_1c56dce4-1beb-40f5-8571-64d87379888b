const buildVendorUserQuery = require('../../../../../src/graphql/resolvers/filters/vendorUser.filter');
const VendorUser = require('../../../../../src/models/VendorUser');

jest.mock('../../../../../src/models/VendorUser', () => ({
    schema: {
        paths: {
        field1: {},
        field2: {},
        field3: {}
        }
    }
}));

describe('buildVendorUserQuery', () => {
    it('should return an empty pipeline when no filters are provided', () => {
        const result = buildVendorUserQuery();
        expect(result).toEqual([
        {
            $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            field1: 1,
            field2: 1,
            field3: 1
            }
        }
        ]);
    });

    it('should build a pipeline with firstName filter', () => {
        const filters = { vendorFirstName: 'John' };
        const result = buildVendorUserQuery(filters);
        expect(result).toEqual([
        {
            $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
            }
        },
        { $unwind: '$userDetails' },
        {
            $match: {
            'userDetails.firstName': { $regex: /John/i }
            }
        },
        {
            $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            field1: 1,
            field2: 1,
            field3: 1
            }
        }
        ]);
    });

    it('should build a pipeline with lastName filter', () => {
        const filters = { vendorLastName: 'Doe' };
        const result = buildVendorUserQuery(filters);
        expect(result).toEqual([
        {
            $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
            }
        },
        { $unwind: '$userDetails' },
        {
            $match: {
            'userDetails.lastName': { $regex: /Doe/i }
            }
        },
        {
            $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            field1: 1,
            field2: 1,
            field3: 1
            }
        }
        ]);
    });

    it('should build a pipeline with email filter', () => {
        const filters = { vendorEmail: '<EMAIL>' };
        const result = buildVendorUserQuery(filters);
        expect(result).toEqual([
        {
            $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
            }
        },
        { $unwind: '$userDetails' },
        {
            $match: {
            'userDetails.email': { $regex: expect.any(RegExp) }
            }
        },
        {
            $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            field1: 1,
            field2: 1,
            field3: 1
            }
        }
        ]);
        expect(result[2].$match['userDetails.email'].$regex.toString()).toBe('/<EMAIL>/i');
    });

    it('should build a pipeline with multiple filters', () => {
        const filters = {
        vendorFirstName: 'John',
        vendorLastName: 'Doe',
        vendorEmail: '<EMAIL>'
        };
        const result = buildVendorUserQuery(filters);
        expect(result).toEqual([
        {
            $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails'
            }
        },
        { $unwind: '$userDetails' },
        {
            $match: {
            'userDetails.firstName': { $regex: expect.any(RegExp) },
            'userDetails.lastName': { $regex: expect.any(RegExp) },
            'userDetails.email': { $regex: expect.any(RegExp) }
            }
        },
        {
            $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            field1: 1,
            field2: 1,
            field3: 1
            }
        }
        ]);
        expect(result[2].$match['userDetails.firstName'].$regex.toString()).toBe('/John/i');
        expect(result[2].$match['userDetails.lastName'].$regex.toString()).toBe('/Doe/i');
        expect(result[2].$match['userDetails.email'].$regex.toString()).toBe('/<EMAIL>/i');
    });
});
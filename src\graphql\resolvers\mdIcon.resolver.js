const { get, default: mongoose } = require("mongoose");
const MdIcon = require("../../models/MdIcon");
const MdFeature = require("../../models/MdFeature"); 
const MdHostLayout = require("../../models/MdHostLayout");
const { getByIdCache, clearCacheById, setCache } = require("../../utils/cache.util");
const { createResponse } = require("../../utils/response.util");
const { getPaginationInfo } = require("../../utils/paginationInfo.util");
const buildMdIconQuery = require("./filters/mdIcon.filter");
const { findReferences } = require('../../../src/utils/referenceCheck.util');


const mdIconResolvers = {
    Query: {
        getMdIconById: async (_, {id}) => {
            try {
                let mdIcon = await getByIdCache(id) || await MdIcon.findById(id);
                if (mdIcon) {
                    await setCache(id, mdIcon);
                } else {
                    return createResponse('MdIconErrorResponse', 'FAILURE', 'MdIcon not found', { errors: [{ field: 'getMdIconById', message: 'MdIcon not found' }] });
                }
                return createResponse('MdIconResponse', 'SUCCESS', 'MdIcon fetched successfully', { result: { mdIcon } });
            } catch (error) {
                console.error(error);
                return createResponse('MdIconErrorResponse', 'FAILURE', 'Error getting mdIcon', { errors: [{ field: 'getMdIconById', message: error.message }] });
            }
        },
        
        getMdIcons: async (_, {filter, pagination}) => {
            try {
                const query = buildMdIconQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdIcon, query, limit, skip);

                const mdIcons = await MdIcon.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdIcons.length === 0) {
                    return createResponse('MdIconsResponse', 'FAILURE', 'No mdIcons found', { result: { mdIcons }, pagination: paginationInfo });
                }
                return createResponse('MdIconsResponse', 'SUCCESS', 'MdIcons fetched successfully', { result: { mdIcons }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdIconErrorResponse', 'FAILURE', 'Error getting mdIcons', { errors: [{ field: 'getMdIcons', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdIcon: async (_, {name, uiId, iconSrc}) => {
            try {
                const mdIcon = new MdIcon({name, uiId, iconSrc});
                await mdIcon.save();
                return createResponse('MdIconResponse', 'SUCCESS', 'MdIcon created successfully', { result: { mdIcon } });
            } catch (error) {
                console.error(error);
                return createResponse('MdIconErrorResponse', 'FAILURE', 'Error creating mdIcon', { errors: [{ field: 'createMdIcon', message: error.message }] });
            }
        },
        updateMdIcon: async (_, {id, name, uiId, iconSrc}) => {
            try {
                const mdIcon = await MdIcon.findByIdAndUpdate(id, {name, uiId, iconSrc}, {new: true});
                if (!mdIcon) {
                    return createResponse('MdIconErrorResponse', 'FAILURE', 'MdIcon not found', { errors: [{ field: 'updateMdIcon', message: 'MdIcon not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdIconResponse', 'SUCCESS', 'MdIcon updated successfully', { result: { mdIcon } });
            } catch (error) {
                console.error(error);
                return createResponse('MdIconErrorResponse', 'FAILURE', 'Error updating mdIcon', { errors: [{ field: 'updateMdIcon', message: error.message }] });
            }
        },
        deleteMdIcon: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdIcon');
                if (references.length > 0) {
                    return createResponse('MdIconErrorResponse', 'FAILURE', 'MdIcon cannot be deleted', {
                        errors: [{ field: 'deleteMdIcon', message: `MdIcon cannot be deleted as it has references in the following collections: ${references.join(', ')}` }]
                    });
                }

                const mdIcon = await MdIcon.findByIdAndDelete(id);
                if (!mdIcon) {
                    return createResponse('MdIconErrorResponse', 'FAILURE', 'MdIcon not found', {
                        errors: [{ field: 'deleteMdIcon', message: 'MdIcon not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdIconResponse', 'SUCCESS', 'MdIcon deleted successfully', {
                    result: { mdIcon }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdIconErrorResponse', 'FAILURE', 'Error deleting mdIcon', {
                    errors: [{ field: 'deleteMdIcon', message: error.message }]
                });
            }
        }
    }
}

module.exports = mdIconResolvers;
const { isValidObjectId } = require('../../../utils/validation.util');
const buildMdEventTypeQuery = require('../filters/mdEventType.filter');
const buildAddressQuery = require('../filters/address.filter');
const buildPartyQuery = require('../filters/party.filter');
const MdEventType = require('../../../models/MdEventType');
const Party = require('../../../models/Party');
const MdServiceLocation = require('../../../models/MdServiceLocation');
const buildServiceLocationQuery = require('../filters/mdServiceLocation.filter');
const Guest = require('../../../models/Guest');
const Host = require('../../../models/Host');
const Event = require('../../../models/Event');

const buildEventQuery = async (filters, userId) => {
    const query = {};
    let postQueryFilters = {};
    let paginationModifier = null;

    if (filters) {
        if (filters.name) {
            query.name = { $regex: filters.name, $options: 'i' };
        }

        if (filters.description) {
            query.description = { $regex: filters.description, $options: 'i' };
        }

        if (filters.eventType) {
            if (typeof filters.eventType === 'string') {
                if (isValidObjectId(filters.eventType)) {
                    query.eventType = filters.eventType;
                } else {
                    throw new Error('Invalid eventType ID provided');
                }
            } else if (typeof filters.eventType === 'object') {
                const eventTypeQuery = await buildMdEventTypeQuery(filters.eventType);
                if (Object.keys(eventTypeQuery).length > 0) {
                    const matchingEventTypes = await MdEventType.find(eventTypeQuery).select('_id');
                    if (matchingEventTypes.length > 0) {
                        query.eventType = { $in: matchingEventTypes.map(et => et._id) };
                    } else {
                        query.eventType = { $in: [] };
                    }
                }
            }
        }

        if (filters.location) {
            if (typeof filters.location === 'string') {
                if (isValidObjectId(filters.location)) {
                    query.location = filters.location;
                } else {
                    throw new Error('Invalid location ID provided');
                }
            } else if (typeof filters.location === 'object') {
                const locationQuery = await buildServiceLocationQuery(filters.location);
                if (Object.keys(locationQuery).length > 0) {
                    const matchingLocations = await MdServiceLocation.find(locationQuery).select('_id');
                    if (matchingLocations.length > 0) {
                        query.location = { $in: matchingLocations.map(loc => loc._id) };
                    } else {
                        query.location = { $in: [] };
                    }
                }
            }
        }

        if (filters.budget) {
            query.budget = {};
            if (filters.budget.min !== undefined) {
                query.budget.$gte = filters.budget.min;
            }
            if (filters.budget.max !== undefined) {
                query.budget.$lte = filters.budget.max;
            }
        }

        if (filters.parties) {
            const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filters.parties);
            
            if (Object.keys(partyQuery).length > 0) {
                const matchingParties = await Party.find(partyQuery);
                const filteredParties = await applyPostQueryFilters(matchingParties);
                
                if (filteredParties.length > 0) {
                    const eventIds = [...new Set(filteredParties.map(party => party.eventId))];
                    query._id = { $in: eventIds };
                } else {
                    query._id = { $in: [] };
                }
            }
        }

        if (filters.dateRange) {
            const partyQuery = {};
            
            if (filters.dateRange.start) {
                partyQuery.time = partyQuery.time || {};
                partyQuery.time.$gte = filters.dateRange.start;
            }
            
            if (filters.dateRange.end) {
                partyQuery.time = partyQuery.time || {};
                partyQuery.time.$lte = filters.dateRange.end;
            }

            if (Object.keys(partyQuery).length > 0) {
                const matchingParties = await Party.find(partyQuery);
                if (matchingParties.length > 0) {
                    const eventIds = [...new Set(matchingParties.map(party => party.eventId))];
                    query._id = { $in: eventIds };
                } else {
                    query._id = { $in: [] };
                }
            }
        }

        if (filters.presentAndUpcoming !== undefined) {
            const currentDate = new Date();
            const utcDate = new Date(Date.UTC(
                currentDate.getUTCFullYear(),
                currentDate.getUTCMonth(),
                currentDate.getUTCDate(),
                0, 0, 0, 0
            ));

            postQueryFilters.customSort = {
                presentAndUpcoming: filters.presentAndUpcoming,
                currentDate: utcDate
            };

            paginationModifier = async (baseCount, findQuery) => {
                const allEvents = await Event.find(findQuery);
                const eventsWithDates = await Promise.all(allEvents.map(async (event) => {
                    const parties = await Party.find({ eventId: event._id })
                        .select('time')
                        .sort({ time: -1 })
                        .limit(1)
                        .lean();

                    const actualEndDate = parties[0]?.time || event.endDate || null;
                    return { event, actualEndDate };
                }));

                const filteredEvents = eventsWithDates.filter(({ actualEndDate }) => {
                    if (!actualEndDate) {
                        return filters.presentAndUpcoming;
                    }

                    const eventDate = new Date(Date.UTC(
                        actualEndDate.getUTCFullYear(),
                        actualEndDate.getUTCMonth(),
                        actualEndDate.getUTCDate(),
                        0, 0, 0, 0
                    ));

                    if (filters.presentAndUpcoming) {
                        return eventDate >= utcDate;
                    } else {
                        return eventDate < utcDate;
                    }
                });

                return filteredEvents.length;
            };
        }

        if (filters.userType) {
            switch (filters.userType) {
                case 'HOST':
                    const partiesAsCoHost = await Party.find({ 
                        coHosts: userId 
                    }).select('eventId');
                    const eventIdsAsPartyCoHost = [...new Set(partiesAsCoHost.map(p => p.eventId.toString()))];
                    
                    const userHost = await Host.findOne({ userId });
                    
                    if (userHost) {
                        query.$or = [
                            { mainHost: userHost._id },
                            { coHosts: userHost._id },
                            { _id: { $in: eventIdsAsPartyCoHost } }
                        ];
                    } else {
                        query._id = { $in: [] };
                    }
                    break;
        
                case 'GUEST':
                    const userGuests = await Guest.find({ user: userId });
                    
                    if (userGuests.length > 0) {
                        const parties = await Party.find({
                            _id: { $in: userGuests.map(g => g.party) }
                        }).select('eventId');
                        
                        const eventIds = [...new Set(parties.map(p => p.eventId))];
                        
                        if (eventIds.length > 0) {
                            query.$or = [{ _id: { $in: eventIds } }];
                        } else {
                            query._id = { $in: [] };
                        }
                    } else {
                        query._id = { $in: [] };
                    }
                    break;
        
                default:
                    throw new Error('Invalid userType provided');
            }
        
            if (Object.keys(query).length > 1) {
                const otherConditions = { ...query };
                delete otherConditions.$or;
                query = {
                    $and: [
                        { $or: query.$or },
                        otherConditions
                    ]
                };
            }
        }

        if (filters.eventStatus) {
            query.eventStatus = filters.eventStatus;
        }

        if (filters.eventGroupId) {
            query.eventGroupId = filters.eventGroupId;
        }
    }

    return {
        query,
        postQueryFilters,
        paginationModifier,
        applyPostQueryFilters: async (events) => {
            if (Object.keys(postQueryFilters).length === 0) {
                return events;
            }

            let sortedEvents = [...events];

            if (postQueryFilters.customSort) {
                sortedEvents = await Promise.all(sortedEvents.map(async (event) => {
                    const parties = await Party.find({ eventId: event._id })
                        .select('time')
                        .sort({ time: -1 })
                        .limit(1)
                        .lean();

                    const actualEndDate = parties[0]?.time || event.endDate || null;
                    return {
                        ...(event.toObject ? event.toObject() : event),
                        actualEndDate
                    };
                }));

                sortedEvents = sortedEvents.filter(event => {
                    const eventEndDate = event.actualEndDate ? new Date(event.actualEndDate) : null;
                    
                    if (!eventEndDate) {
                        return postQueryFilters.customSort.presentAndUpcoming;
                    }

                    const eventDate = new Date(Date.UTC(
                        eventEndDate.getUTCFullYear(),
                        eventEndDate.getUTCMonth(),
                        eventEndDate.getUTCDate(),
                        0, 0, 0, 0
                    ));

                    const currentDate = postQueryFilters.customSort.currentDate;

                    if (postQueryFilters.customSort.presentAndUpcoming) {
                        return eventDate >= currentDate;
                    } else {
                        return eventDate < currentDate;
                    }
                });

                sortedEvents.sort((a, b) => {
                    if (postQueryFilters.customSort.presentAndUpcoming) {
                        if (!a.actualEndDate || !b.actualEndDate) {
                            if (!a.actualEndDate && !b.actualEndDate) {
                                return new Date(b.createdAt) - new Date(a.createdAt);
                            }
                            if (!a.actualEndDate) return -1;
                            if (!b.actualEndDate) return 1;
                        }
                        
                        return new Date(a.actualEndDate) - new Date(b.actualEndDate);
                    } else {
                        return new Date(b.actualEndDate) - new Date(a.actualEndDate);
                    }
                });
            }

            return sortedEvents.map(({ actualEndDate, ...event }) => event);
        }
    };
};

module.exports = buildEventQuery;
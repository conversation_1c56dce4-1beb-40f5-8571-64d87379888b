const mongoose = require('mongoose');
const MdReaction = require('../../../../src/models/MdReaction');
const MdIcon = require('../../../../src/models/MdIcon');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildMdReactionQuery = require('../../../../src/graphql/resolvers/filters/mdReaction.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const mdReactionResolvers = require('../../../../src/graphql/resolvers/mdReaction.resolver');

const baseMdReactionIdSchema = {
    icon: { type: 'single', model: MdIcon }
};

const mdReactionIdSchema = {
    ...baseMdReactionIdSchema
};

const createMdReactionIdSchema = {
    ...baseMdReactionIdSchema,
    icon: { ...baseMdReactionIdSchema.icon, required: true }
};

jest.mock('../../../../src/models/MdReaction');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdReaction.filter');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util');

console.error = jest.fn();

describe('mdReactionResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        console.error = jest.fn();
    });

    describe('MdReaction Field Resolvers', () => {
        it('should resolve icon field successfully', async () => {
            const parent = { icon: 'iconId' };
            const mockIcon = { _id: 'iconId', name: 'Test Icon' };

            MdIcon.findById.mockResolvedValue(mockIcon);

            const result = await mdReactionResolvers.MdReaction.icon(parent);

            expect(MdIcon.findById).toHaveBeenCalledWith(parent.icon);
            expect(result).toEqual(mockIcon);
        });

        it('should handle error in icon resolver', async () => {
            const parent = { icon: 'iconId' };
            const error = new Error('Database error');

            MdIcon.findById.mockRejectedValue(error);

            await expect(mdReactionResolvers.MdReaction.icon(parent))
                .rejects.toThrow('Error getting icon');
            expect(console.error).toHaveBeenCalledWith(error);
        });
    });

    describe('Query Resolvers', () => {
        describe('getMdReactionById', () => {
            it('should return reaction from cache if available', async () => {
                const id = 'reactionId';
                const cachedReaction = { _id: id, name: 'Cached Reaction' };

                getByIdCache.mockResolvedValue(cachedReaction);
                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Query.getMdReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdReaction.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionResponse',
                    'SUCCESS',
                    'MdReaction fetched successfully',
                    { result: { mdReaction: cachedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache reaction if not in cache', async () => {
                const id = 'reactionId';
                const reaction = { _id: id, name: 'Test Reaction' };

                getByIdCache.mockResolvedValue(null);
                MdReaction.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(reaction)
                });
                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Query.getMdReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdReaction.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, reaction);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionResponse',
                    'SUCCESS',
                    'MdReaction fetched successfully',
                    { result: { mdReaction: reaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when reaction not found', async () => {
                const id = 'nonexistentId';

                getByIdCache.mockResolvedValue(null);
                MdReaction.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(null)
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Query.getMdReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdReaction.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'MdReaction not found',
                    {
                        errors: [{ field: 'id', message: 'MdReaction not found' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors when getting reaction by id', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                
                getByIdCache.mockResolvedValue(null);
                MdReaction.findById.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });
                
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Query.getMdReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdReaction.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Error getting MdReaction',
                    {
                        errors: [{ field: 'getMdReactionById', message: 'Database error' }]
                    }
                );
                expect(result).toBe('errorResponse');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });

        describe('getMdReactions', () => {
            it('should return reactions successfully', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'test', $options: 'i' } };
                const reactions = [{ _id: '1', name: 'Test Reaction' }];
                const paginationInfo = { totalCount: 1, hasNextPage: false };

                buildMdReactionQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(reactions)
                };
                MdReaction.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Query.getMdReactions(null, { filter, pagination });

                expect(buildMdReactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdReaction, query, pagination.limit, pagination.skip);
                expect(MdReaction.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith('icon');
                expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
                expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
                expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionsResponse',
                    'SUCCESS',
                    'Reactions fetched successfully',
                    {
                        result: { mdReactions: reactions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors when getting reactions', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                buildMdReactionQuery.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Query.getMdReactions(null, { filter, pagination });

                expect(buildMdReactionQuery).toHaveBeenCalledWith(filter);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Error getting reactions',
                    {
                        errors: [{ field: 'getMdReactions', message: 'Database error' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return failure response when no reactions found', async () => {
                const filter = { name: 'nonexistent' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'nonexistent', $options: 'i' } };
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
                const mdReactions = [];

                buildMdReactionQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mdReactions),
                };

                MdReaction.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('failureResponse');

                const result = await mdReactionResolvers.Query.getMdReactions(null, { filter, pagination });

                expect(buildMdReactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdReaction, query, pagination.limit, pagination.skip);
                expect(MdReaction.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith('icon');
                expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
                expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
                expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionsResponse',
                    'FAILURE',
                    'No reactions found',
                    {
                        result: { mdReactions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('failureResponse');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createMdReaction', () => {
            it('should create reaction successfully', async () => {
                const input = {
                    name: 'New Reaction',
                    icon: 'iconId',
                    active: true
                };

                validateReferences.mockResolvedValue(null);
                
                const savedReaction = { _id: 'newId', ...input };
                const populatedReaction = { ...savedReaction, icon: { _id: 'iconId' } };

                MdReaction.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(savedReaction)
                }));

                MdReaction.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(populatedReaction)
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Mutation.createMdReaction(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdReaction');
                expect(MdReaction).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionResponse',
                    'SUCCESS',
                    'Reaction created successfully',
                    { result: { mdReaction: populatedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle validation errors', async () => {
                const input = { name: 'Test', icon: 'invalidId' };
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'icon', message: 'Invalid icon reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Mutation.createMdReaction(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle error when creating reaction fails', async () => {
                const input = {
                    name: 'Test Reaction',
                    icon: 'iconId',
                    active: true
                };
                
                const error = new Error('Database error');
                
                validateReferences.mockResolvedValue(null);
                
                MdReaction.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Mutation.createMdReaction(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdReaction');
                expect(MdReaction).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Error creating reaction',
                    { errors: [{ field: 'createMdReaction', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return validation error response when validation fails', async () => {
                const input = {
                    name: 'Test Reaction',
                    icon: 'invalidIconId'
                };

                const validationError = {
                    message: 'Validation failed',
                    errors: [
                        { field: 'icon', message: 'Invalid icon reference' }
                    ]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('validationErrorResponse');

                const result = await mdReactionResolvers.Mutation.createMdReaction(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(
                    input,
                    createMdReactionIdSchema,
                    'MdReaction'
                );
                expect(MdReaction.prototype.save).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('validationErrorResponse');
            });
        });

        describe('updateMdReaction', () => {
            it('should update reaction successfully', async () => {
                const id = 'reactionId';
                const input = { name: 'Updated Reaction' };
                const updatedReaction = { _id: id, ...input };

                validateReferences.mockResolvedValue(null);
                MdReaction.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(updatedReaction)
                });
                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Mutation.updateMdReaction(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdReaction');
                expect(MdReaction.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionResponse',
                    'SUCCESS',
                    'Reaction updated successfully',
                    { result: { mdReaction: updatedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle error when updating reaction fails', async () => {
                const id = 'testId';
                const input = {
                    name: 'Updated Reaction',
                    icon: 'iconId'
                };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                
                MdReaction.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Mutation.updateMdReaction(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdReaction');
                expect(MdReaction.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Error updating reaction',
                    { errors: [{ field: 'updateMdReaction', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdReaction', () => {
            it('should delete reaction successfully', async () => {
                const id = 'reactionId';
                const deletedReaction = { _id: id, name: 'Deleted Reaction' };

                findReferences.mockResolvedValue([]);
                MdReaction.findByIdAndDelete.mockResolvedValue(deletedReaction);
                createResponse.mockReturnValue('successResponse');

                const result = await mdReactionResolvers.Mutation.deleteMdReaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdReaction');
                expect(MdReaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionResponse',
                    'SUCCESS',
                    'Reaction deleted successfully',
                    { result: { mdReaction: deletedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle references preventing deletion', async () => {
                const id = 'reactionId';
                const references = ['Post', 'Comment'];

                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Mutation.deleteMdReaction(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Reaction cannot be deleted',
                    { errors: [{ field: 'id', message: `Reaction cannot be deleted as it is being used in: ${references.join(', ')}` }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors during deletion', async () => {
                const id = 'reactionId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                
                MdReaction.findByIdAndDelete.mockRejectedValue(error);
                
                createResponse.mockReturnValue('errorResponse');

                const result = await mdReactionResolvers.Mutation.deleteMdReaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdReaction');
                expect(MdReaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdReactionErrorResponse',
                    'FAILURE',
                    'Error deleting reaction',
                    {
                        errors: [{ field: 'deleteMdReaction', message: 'Database error' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
const buildMdRoleQuery = require('../../../../../src/graphql/resolvers/filters/mdRole.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdRoleQuery', () => {
    it('should return an empty query object when no filter is provided', () => {
        const result = buildMdRoleQuery();
        expect(result).toEqual({});
    });

    it('should build a query with a valid id', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const result = buildMdRoleQuery({ id: validId });
        expect(result).toEqual({ _id: validId });
    });

    it('should throw an error for an invalid id', () => {
        const invalidId = 'invalid_id';
        isValidObjectId.mockReturnValue(false);
        expect(() => buildMdRoleQuery({ id: invalidId })).toThrow('Invalid id provided');
    });

    it('should build a query with a name filter', () => {
        const nameFilter = 'Admin';
        const result = buildMdRoleQuery({ name: nameFilter });
        expect(result).toEqual({ name: { $regex: new RegExp(nameFilter, 'i') } });
    });

    it('should build a query with both id and name filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        const nameFilter = 'Admin';
        isValidObjectId.mockReturnValue(true);
        const result = buildMdRoleQuery({ id: validId, name: nameFilter });
        expect(result).toEqual({
            _id: validId,
            name: { $regex: new RegExp(nameFilter, 'i') }
        });
    });
});
const tagResolvers = require('../../../../src/graphql/resolvers/tag.resolver');
const Tag = require('../../../../src/models/Tag');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const buildTagQuery = require('../../../../src/graphql/resolvers/filters/tag.filter');
const findReferences = require('../../../../src/graphql/resolvers/references/tag.references');
const MdIcon = require('../../../../src/models/MdIcon');

jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/tag.filter');
jest.mock('../../../../src/graphql/resolvers/references/tag.references');

describe('Tag Resolvers', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('Tag Field Resolvers', () => {
        describe('icon', () => {
            afterEach(() => {
                jest.clearAllMocks();
            });

            it('should return icon when found', async () => {
                const mockIcon = {
                    _id: 'iconId',
                    name: 'Test Icon',
                    iconSrc: '<svg>test</svg>'
                };
                
                const parent = {
                    icon: 'iconId'
                };

                MdIcon.findById.mockResolvedValue(mockIcon);

                const result = await tagResolvers.Tag.icon(parent);

                expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
                expect(result).toEqual(mockIcon);
            });

            it('should throw error when icon lookup fails', async () => {
                const parent = {
                    icon: 'iconId'
                };

                const error = new Error('Database error');
                MdIcon.findById.mockRejectedValue(error);

                await expect(tagResolvers.Tag.icon(parent))
                    .rejects
                    .toThrow('Error getting icon');

                expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
            });
        });
    });

    describe('Query', () => {
        describe('getTagById', () => {
            it('should return tag from cache if available', async () => {
                const mockTag = { id: '1', name: 'Test Tag' };
                getByIdCache.mockResolvedValue(mockTag);
                createResponse.mockReturnValue('success response');

                const result = await tagResolvers.Query.getTagById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(createResponse).toHaveBeenCalledWith('TagResponse', 'SUCCESS', 'Tag fetched successfully', { result: { tag: mockTag } });
                expect(result).toBe('success response');
            });

            it('should fetch tag from database if not in cache', async () => {
                const mockTag = { id: '1', name: 'Test Tag' };
                getByIdCache.mockResolvedValue(null);
                Tag.findById.mockResolvedValue(mockTag);
                createResponse.mockReturnValue('success response');

                const result = await tagResolvers.Query.getTagById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(Tag.findById).toHaveBeenCalledWith('1');
                expect(setCache).toHaveBeenCalledWith('1', mockTag);
                expect(createResponse).toHaveBeenCalledWith('TagResponse', 'SUCCESS', 'Tag fetched successfully', { result: { tag: mockTag } });
                expect(result).toBe('success response');
            });

            it('should return error response if tag not found', async () => {
                getByIdCache.mockResolvedValue(null);
                Tag.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await tagResolvers.Query.getTagById(null, { id: '1' });

                expect(createResponse).toHaveBeenCalledWith('TagErrorResponse', 'FAILURE', 'Tag not found', { 
                    errors: [{ field: 'id', message: 'Tag not found' }] 
                });
                expect(result).toBe('error response');
            });

            it('should return an error response when an unexpected error occurs', async () => {
                const id = 'someTagId';
                const error = new Error('Unexpected error');
                
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                  status: 'FAILURE',
                  message: 'Error retrieving tag',
                  errors: [{ field: 'getTagById', message: error.message }]
                });
        
                const result = await tagResolvers.Query.getTagById(null, { id });
        
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                  'TagErrorResponse',
                  'FAILURE',
                  'Error retrieving tag',
                  { errors: [{ field: 'getTagById', message: error.message }] }
                );
                expect(result).toEqual({
                  status: 'FAILURE',
                  message: 'Error retrieving tag',
                  errors: [{ field: 'getTagById', message: error.message }]
                });
            });
        });

        describe('getTags', () => {
            it('should return tags successfully', async () => {
                const mockTags = [{ id: '1', name: 'Tag 1' }, { id: '2', name: 'Tag 2' }];
                const mockPaginationInfo = { total: 2, limit: 10, skip: 0 };
                buildTagQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                Tag.find.mockReturnValue({
                    limit: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            sort: jest.fn().mockResolvedValue(mockTags)
                        })
                    })
                });
                createResponse.mockReturnValue('success response');

                const result = await tagResolvers.Query.getTags(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

                expect(buildTagQuery).toHaveBeenCalledWith({});
                expect(getPaginationInfo).toHaveBeenCalledWith(Tag, {}, 10, 0);
                expect(Tag.find).toHaveBeenCalledWith({});
                expect(createResponse).toHaveBeenCalledWith('TagsResponse', 'SUCCESS', 'Tags fetched successfully', { result: { tags: mockTags }, pagination: mockPaginationInfo });
                expect(result).toBe('success response');
            });

            it('should return failure response if no tags found', async () => {
                buildTagQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue({ total: 0, limit: 10, skip: 0 });
                Tag.find.mockReturnValue({
                    limit: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            sort: jest.fn().mockResolvedValue([])
                        })
                    })
                });
                createResponse.mockReturnValue('failure response');

                const result = await tagResolvers.Query.getTags(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

                expect(createResponse).toHaveBeenCalledWith('TagsResponse', 'FAILURE', 'No tags found', { result: { tags: [] }, pagination: { total: 0, limit: 10, skip: 0 } });
                expect(result).toBe('failure response');
            });

            it('should return error response when an error occurs', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
          
                buildTagQuery.mockReturnValue({});
                getPaginationInfo.mockRejectedValue(error);
                createResponse.mockReturnValue({
                  status: 'FAILURE',
                  message: 'Error retrieving tags',
                  errors: [{ field: 'getTags', message: error.message }]
                });
          
                const result = await tagResolvers.Query.getTags(null, { filter, pagination });
          
                expect(buildTagQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(Tag, {}, 10, 0);
                expect(createResponse).toHaveBeenCalledWith(
                  'TagErrorResponse',
                  'FAILURE',
                  'Error retrieving tags',
                  { errors: [{ field: 'getTags', message: error.message }] }
                );
                expect(result).toEqual({
                  status: 'FAILURE',
                  message: 'Error retrieving tags',
                  errors: [{ field: 'getTags', message: error.message }]
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createTag', () => {
            it('should create a tag successfully', async () => {
                const mockInput = { name: 'New Tag' };
                const tagMock = {
                    _id: '1',
                    ...mockInput,
                    save: jest.fn().mockResolvedValue(true)
                };
                Tag.mockImplementation(() => tagMock);
                createResponse.mockReturnValue('success response');

                const result = await tagResolvers.Mutation.createTag(null, { input: mockInput });

                expect(Tag).toHaveBeenCalledWith({ name: 'New Tag' });
                expect(tagMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('TagResponse', 'SUCCESS', 'Tag created successfully', { result: { tag: tagMock } });
                expect(result).toBe('success response');
            });

            it('should return error response if tag creation fails', async () => {
                const error = new Error('Creation failed');
                Tag.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue('error response');

                const result = await tagResolvers.Mutation.createTag(null, { input: { name: 'New Tag' } });

                expect(createResponse).toHaveBeenCalledWith('TagErrorResponse', 'FAILURE', 'Error creating tag', { errors: [{ field: 'createTag', message: error.message }] });
                expect(result).toBe('error response');
            });
        });

        describe('deleteTag', () => {
            it('should return an error if the tag has references', async () => {
                const id = 'tagId';
                const references = ['Collection1', 'Collection2'];
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Tag cannot be deleted',
                    errors: [{ field: 'deleteTag', message: `Tag cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                });

                const result = await tagResolvers.Mutation.deleteTag(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('TagErrorResponse', 'FAILURE', 'Tag cannot be deleted', {
                    errors: [{ field: 'deleteTag', message: `Tag cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Tag cannot be deleted',
                    errors: [{ field: 'deleteTag', message: `Tag cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                });
            });

            it('should delete the tag successfully if no references exist', async () => {
                const id = 'tagId';
                const mockTag = { id, name: 'Test Tag' };
                findReferences.mockResolvedValue([]);
                Tag.findByIdAndDelete.mockResolvedValue(mockTag);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Tag deleted successfully',
                    result: { tag: mockTag },
                });

                const result = await tagResolvers.Mutation.deleteTag(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Tag.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('TagResponse', 'SUCCESS', 'Tag deleted successfully', { result: { tag: mockTag } });
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Tag deleted successfully',
                    result: { tag: mockTag },
                });
            });

            it('should return an error if the tag is not found', async () => {
                const id = 'nonExistentTagId';
                findReferences.mockResolvedValue([]);
                Tag.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Tag not found',
                    errors: [{ field: 'deleteTag', message: 'Tag not found' }],
                });

                const result = await tagResolvers.Mutation.deleteTag(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Tag.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('TagErrorResponse', 'FAILURE', 'Tag not found', { errors: [{ field: 'deleteTag', message: 'Tag not found' }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Tag not found',
                    errors: [{ field: 'deleteTag', message: 'Tag not found' }],
                });
            });

            it('should return an error if an exception occurs', async () => {
                const id = 'tagId';
                const error = new Error('Database error');
                findReferences.mockResolvedValue([]);
                Tag.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting tag',
                    errors: [{ field: 'deleteTag', message: error.message }],
                });

                const result = await tagResolvers.Mutation.deleteTag(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Tag.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('TagErrorResponse', 'FAILURE', 'Error deleting tag', { errors: [{ field: 'deleteTag', message: error.message }] });
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting tag',
                    errors: [{ field: 'deleteTag', message: error.message }],
                });
            });
        });

        describe('updateTag', () => {
            it('should update and return the tag successfully', async () => {
                const id = 'validTagId';
                const input = { 
                    name: 'Updated Tag',
                    icon: 'newIconId'
                };
                const updatedTag = { 
                    _id: id,
                    ...input
                };

                Tag.findByIdAndUpdate.mockResolvedValue(updatedTag);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Tag updated successfully',
                    result: { tag: updatedTag }
                });

                const result = await tagResolvers.Mutation.updateTag(null, { id, input });

                expect(Tag.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TagResponse',
                    'SUCCESS',
                    'Tag updated successfully',
                    { result: { tag: updatedTag } }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Tag updated successfully',
                    result: { tag: updatedTag }
                });
            });

            it('should return error response when tag is not found', async () => {
                const id = 'nonExistentTagId';
                const input = { name: 'Updated Tag' };

                Tag.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Tag not found',
                    errors: [{ field: 'id', message: 'Tag not found' }]
                });

                const result = await tagResolvers.Mutation.updateTag(null, { id, input });

                expect(Tag.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'TagErrorResponse',
                    'FAILURE',
                    'Tag not found',
                    { errors: [{ field: 'id', message: 'Tag not found' }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Tag not found',
                    errors: [{ field: 'id', message: 'Tag not found' }]
                });
            });

            it('should handle database errors gracefully', async () => {
                const id = 'validTagId';
                const input = { name: 'Updated Tag' };
                const error = new Error('Database error');

                Tag.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error updating tag',
                    errors: [{ field: 'updateTag', message: error.message }]
                });

                const result = await tagResolvers.Mutation.updateTag(null, { id, input });

                expect(Tag.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'TagErrorResponse',
                    'FAILURE',
                    'Error updating tag',
                    { errors: [{ field: 'updateTag', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error updating tag',
                    errors: [{ field: 'updateTag', message: error.message }]
                });
            });
        });
    });
});
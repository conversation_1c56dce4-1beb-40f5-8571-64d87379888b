const { gql } = require('graphql-tag');
const hostLayoutTypeDef = require('../../../../src/graphql/typedefs/hostLayout.typedef');

describe('HostLayout TypeDef', () => {
  it('should define NavBar type', () => {
    const navBarType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'ObjectTypeDefinition' && def.name.value === 'NavBar'
    );
    expect(navBarType).toBeDefined();
    expect(navBarType.fields).toHaveLength(5);
    expect(navBarType.fields.map(f => f.name.value)).toEqual(['home', 'planning', 'guests', 'apps', 'messages']);
  });

  it('should define HostLayout type', () => {
    const hostLayoutType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'ObjectTypeDefinition' && def.name.value === 'HostLayout'
    );
    expect(hostLayoutType).toBeDefined();
    expect(hostLayoutType.fields).toHaveLength(2);
    expect(hostLayoutType.fields.map(f => f.name.value)).toEqual(['id', 'navBar']);
  });

  it('should define HostLayoutResponse type', () => {
    const responseType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'ObjectTypeDefinition' && def.name.value === 'HostLayoutResponse'
    );
    expect(responseType).toBeDefined();
    expect(responseType.fields).toHaveLength(3);
    expect(responseType.fields.map(f => f.name.value)).toEqual(['status', 'message', 'result']);
  });

  it('should define CreateHostLayoutInput input type', () => {
    const inputType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'InputObjectTypeDefinition' && def.name.value === 'CreateHostLayoutInput'
    );
    expect(inputType).toBeDefined();
    expect(inputType.fields).toHaveLength(1);
    expect(inputType.fields[0].name.value).toBe('navBar');
  });

  it('should define Query type with getHostLayout', () => {
    const queryType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'ObjectTypeDefinition' && def.name.value === 'Query'
    );
    expect(queryType).toBeDefined();
    expect(queryType.fields).toHaveLength(1);
    expect(queryType.fields[0].name.value).toBe('getHostLayout');
  });

  it('should define Mutation type with createHostLayout, updateHostLayout, and deleteHostLayout', () => {
    const mutationType = hostLayoutTypeDef.definitions.find(
      def => def.kind === 'ObjectTypeDefinition' && def.name.value === 'Mutation'
    );
    expect(mutationType).toBeDefined();
    expect(mutationType.fields).toHaveLength(3);
    expect(mutationType.fields.map(f => f.name.value)).toEqual(['createHostLayout', 'updateHostLayout', 'deleteHostLayout']);
  });
});

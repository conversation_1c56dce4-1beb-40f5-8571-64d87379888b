const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const MessageTypeDefs = gql`
    ${sharedTypeDef}
    
    enum MessageType {
        SYSTEM
        USER
    }

    type Message {
        id: ID!
        type: MessageType!
        text: String
        media: [Media!]
        reactions: [Reaction!]
        parentMessageId: ID
        receivers: [User!]
        createdBy: User
        createdAt: DateTime!
        updatedAt: DateTime!
    }

    input MessageFilterInput {
        id: ID
        type: MessageType
        text: String
        parentMessageId: ID
    }

    type MessageWrapper {
        message: Message!
    }

    type MessagesWrapper {
        messages: [Message!]
    }
    
    type MessageResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MessageWrapper!
    }
    
    type MessagesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MessagesWrapper!
        pagination: PaginationInfo!
    }

    type MessageErrorResponse implements Response{
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MessageResult = MessageResponse | MessageErrorResponse
    union MessagesResult = MessagesResponse | MessageErrorResponse

    type Query {
        getMessageById(id: ID!): MessageResult!
        getMessagesByPartyId(partyId: ID!, filter: MessageFilterInput, pagination: PaginationInput): MessagesResult!
    }

    input CreateMessageInput {
        partyId: ID!
        parentMessageId: ID
        type: MessageType!
        text: String
        media: [ID!]
        receivers: [ID!]
    }

    input EditMessageInput {
        text: String
        media: [ID!]
        receivers: [ID!]
    }

    type Mutation {
        createMessage(input: CreateMessageInput!): MessageResult!
        editMessage(id: ID!, input: EditMessageInput!): MessageResult!
        deleteMessage(id: ID!): MessageResult!

        addReactionToMessage(messageId: ID!, reactionId: ID!): MessageResult!
        removeReactionFromMessage(messageId: ID!, reactionId: ID!): MessageResult!
    }
`

module.exports = MessageTypeDefs


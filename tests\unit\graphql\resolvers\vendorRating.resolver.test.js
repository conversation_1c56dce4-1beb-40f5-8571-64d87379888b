const { User } = require('../../../../src/models/User');
const Vendor = require('../../../../src/models/Vendor');
const VendorRating = require('../../../../src/models/VendorRating');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildVendorRatingQuery = require('../../../../src/graphql/resolvers/filters/vendorRating.filter');
const vendorRatingResolvers = require('../../../../src/graphql/resolvers/vendorRating.resolver');

jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/Vendor');
jest.mock('../../../../src/models/VendorRating');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/vendorRating.filter');

describe('vendorRatingResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('VendorRating Field Resolvers', () => {
        describe('user', () => {
            it('should return user for a vendor rating', async () => {
                const parent = { user: 'userId' };
                const mockUser = { _id: 'userId', name: 'John Doe' };

                User.findById.mockResolvedValue(mockUser);

                const result = await vendorRatingResolvers.VendorRating.user(parent);

                expect(User.findById).toHaveBeenCalledWith('userId');
                expect(result).toEqual(mockUser);
            });

            it('should throw error when fetching user fails', async () => {
                const parent = { user: 'userId' };
                const error = new Error('Database error');

                User.findById.mockRejectedValue(error);

                await expect(vendorRatingResolvers.VendorRating.user(parent))
                    .rejects
                    .toThrow('Error getting user');
            });
        });

        describe('vendor', () => {
            it('should return vendor for a vendor rating', async () => {
                const parent = { vendor: 'vendorId' };
                const mockVendor = { _id: 'vendorId', name: 'Test Vendor' };

                Vendor.findById.mockResolvedValue(mockVendor);

                const result = await vendorRatingResolvers.VendorRating.vendor(parent);

                expect(Vendor.findById).toHaveBeenCalledWith('vendorId');
                expect(result).toEqual(mockVendor);
            });

            it('should throw error when fetching vendor fails', async () => {
                const parent = { vendor: 'vendorId' };
                const error = new Error('Database error');

                Vendor.findById.mockRejectedValue(error);

                await expect(vendorRatingResolvers.VendorRating.vendor(parent))
                    .rejects
                    .toThrow('Error getting vendor');
            });
        });
    });

    describe('Query', () => {
        describe('getVendorRatingById', () => {
            it('should return vendor rating from cache if available', async () => {
                const id = 'ratingId';
                const cachedRating = { _id: id, rating: 5 };

                getByIdCache.mockResolvedValue(cachedRating);
                createResponse.mockReturnValue('success response');

                const result = await vendorRatingResolvers.Query.getVendorRatingById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(VendorRating.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingResponse',
                    'SUCCESS',
                    'Vendor rating retrieved successfully',
                    { result: { vendorRating: cachedRating } }
                );
                expect(result).toBe('success response');
            });

            it('should fetch and cache vendor rating if not in cache', async () => {
                const id = 'ratingId';
                const vendorRating = { _id: id, rating: 5 };

                getByIdCache.mockResolvedValue(null);
                VendorRating.findById.mockResolvedValue(vendorRating);
                createResponse.mockReturnValue('success response');

                const result = await vendorRatingResolvers.Query.getVendorRatingById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(VendorRating.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, vendorRating);
                expect(result).toBe('success response');
            });

            it('should return error response if vendor rating not found', async () => {
                const id = 'nonexistentId';

                getByIdCache.mockResolvedValue(null);
                VendorRating.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Query.getVendorRatingById(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Vendor rating not found',
                    { errors: [{ field: 'id', message: 'Vendor rating not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if an error occurs during retrieval', async () => {
                const id = 'ratingId';
                const error = new Error('Database error');

                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Query.getVendorRatingById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Error retrieving vendor rating',
                    { errors: [{ field: 'getVendorRatingById', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('getVendorRatings', () => {
            it('should return vendor ratings with pagination', async () => {
                const filter = { vendorId: 'vendor1' };
                const pagination = { limit: 10, skip: 0 };
                const pipeline = [{ $match: { vendor: 'vendor1' } }];
                const vendorRatings = [
                    { _id: '1', rating: 5, userDetails: { _id: 'user1' }, vendorDetails: { _id: 'vendor1' } }
                ];

                buildVendorRatingQuery.mockReturnValue(pipeline);
                VendorRating.aggregate.mockResolvedValueOnce([{ total: 1 }])
                    .mockResolvedValueOnce(vendorRatings);
                createResponse.mockReturnValue('success response');

                const result = await vendorRatingResolvers.Query.getVendorRatings(null, { filter, pagination });

                expect(buildVendorRatingQuery).toHaveBeenCalledWith(filter);
                expect(VendorRating.aggregate).toHaveBeenCalledTimes(2);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingsResponse',
                    'SUCCESS',
                    'Vendor ratings fetched successfully',
                    expect.any(Object)
                );
                expect(result).toBe('success response');
            });

            it('should return failure response when no ratings found', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const pipeline = [];

                buildVendorRatingQuery.mockReturnValue(pipeline);
                VendorRating.aggregate.mockResolvedValueOnce([{ total: 0 }])
                    .mockResolvedValueOnce([]);
                createResponse.mockReturnValue('failure response');

                const result = await vendorRatingResolvers.Query.getVendorRatings(null, { filter, pagination });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingsResponse',
                    'FAILURE',
                    'No vendor ratings found',
                    expect.any(Object)
                );
                expect(result).toBe('failure response');
            });

            it('should return error response if an error occurs', async () => {
                const filter = { rating: 5 };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                buildVendorRatingQuery.mockImplementation(() => {
                    throw error;
                });
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Query.getVendorRatings(null, { filter, pagination });

                expect(buildVendorRatingQuery).toHaveBeenCalledWith(filter);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Error retrieving vendor ratings',
                    { errors: [{ field: 'getVendorRatings', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });
    });

    describe('Mutation', () => {
        describe('createVendorRating', () => {
            it('should create and return vendor rating', async () => {
                const input = { vendor: 'vendorId', user: 'userId', rating: 5 };
                const vendorRatingMock = {
                    _id: 'newId',
                    ...input,
                    save: jest.fn().mockResolvedValue(true)
                };
                
                VendorRating.mockImplementation(() => vendorRatingMock);
                const createResponseMock = {
                    status: 'SUCCESS',
                    message: 'Vendor rating created successfully',
                    result: { vendorRating: vendorRatingMock }
                };
                createResponse.mockReturnValue(createResponseMock);

                const result = await vendorRatingResolvers.Mutation.createVendorRating(null, { input });

                expect(VendorRating).toHaveBeenCalledWith(input);
                expect(vendorRatingMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingResponse',
                    'SUCCESS',
                    'Vendor rating created successfully',
                    { result: { vendorRating: vendorRatingMock } }
                );
                expect(result).toEqual(createResponseMock);
            });

            it('should return error response if creation fails', async () => {
                const input = { vendor: 'vendorId', user: 'userId', rating: 5 };
                const error = new Error('Database error');

                VendorRating.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Mutation.createVendorRating(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Error creating vendor rating',
                    { errors: [{ field: 'createVendorRating', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('updateVendorRating', () => {
            it('should update and return vendor rating', async () => {
                const id = 'ratingId';
                const input = { rating: 4 };
                const updatedRating = { _id: id, ...input };

                VendorRating.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(updatedRating)
                });
                createResponse.mockReturnValue('success response');

                const result = await vendorRatingResolvers.Mutation.updateVendorRating(null, { id, input });

                expect(VendorRating.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toBe('success response');
            });

            it('should return error response if rating not found', async () => {
                const id = 'nonexistentId';
                const input = { rating: 4 };

                VendorRating.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(null)
                });
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Mutation.updateVendorRating(null, { id, input });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Vendor rating not found',
                    { errors: [{ field: 'id', message: 'Vendor rating not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should handle error during update', async () => {
                const id = 'ratingId';
                const input = { rating: 4 };
                const error = new Error('Database error');

                VendorRating.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Mutation.updateVendorRating(null, { id, input });

                expect(VendorRating.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Error updating vendor rating',
                    { errors: [{ field: 'updateVendorRating', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('deleteVendorRating', () => {
            it('should delete vendor rating successfully', async () => {
                const id = 'ratingId';
                const deletedRating = { _id: id, rating: 5 };

                VendorRating.findByIdAndDelete.mockResolvedValue(deletedRating);
                createResponse.mockReturnValue('success response');

                const result = await vendorRatingResolvers.Mutation.deleteVendorRating(null, { id });

                expect(VendorRating.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toBe('success response');
            });

            it('should return error response if rating not found', async () => {
                const id = 'nonexistentId';

                VendorRating.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Mutation.deleteVendorRating(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorRatingErrorResponse',
                    'FAILURE',
                    'Vendor rating not found',
                    { errors: [{ field: 'id', message: 'Vendor rating not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if an error occurs during deletion', async () => {
                const id = 'ratingId';
                const error = new Error('Database error');
                
                VendorRating.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await vendorRatingResolvers.Mutation.deleteVendorRating(null, { id });

                expect(VendorRating.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                  'VendorRatingErrorResponse',
                  'FAILURE',
                  'Error deleting vendor rating',
                  { errors: [{ field: 'deleteVendorRating', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });
    });
});

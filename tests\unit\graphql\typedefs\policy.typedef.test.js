const { gql } = require('graphql-tag');
const policyTypeDef = require('../../../../src/graphql/typedefs/policy.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('policyTypeDef', () => {
  it('should contain the Policy type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type Policy {
        id: ID!
        vendor: Vendor!
        name: String!
        description: String!
        order: Int!
        tags: [Tag!]
        active: Boolean!
      }

      input PolicyFilterInput {
        id: String
        vendor: VendorFilterInput
        name: String
        description: String
        active: Boolean
        tag: TagFilterInput
      }

      type PolicyWrapper {
        policy: Policy!
      }

      type PoliciesWrapper {
        policies: [Policy]!
      }

      type PolicyResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PolicyWrapper!
      }

      type PoliciesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PoliciesWrapper!
        pagination: PaginationInfo!
      }

      type PolicyErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union PolicyResult = PolicyResponse | PolicyErrorResponse
      union PoliciesResult = PoliciesResponse | PolicyErrorResponse

      type Query {
        getPolicyById(id: ID!): PolicyResult!
        getPolicies(filter: PolicyFilterInput, pagination: PaginationInput): PoliciesResult!
      }

      input PolicyInput {
        vendor: ID!
        name: String!
        description: String!
        order: Int!
        tags: [ID!]
        active: Boolean
      }

      input PolicyUpdateInput {
        vendor: ID
        name: String
        description: String
        order: Int
        tags: [ID!]
        active: Boolean
      }

      type Mutation {
        createPolicy(input: PolicyInput!): PolicyResult!
        updatePolicy(id: ID!, input: PolicyUpdateInput!): PolicyResult!
        deletePolicy(id: ID!): PolicyResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(policyTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
}); 
const { isValidObjectId } = require('../../../utils/validation.util');
const buildUserQuery = require('./user.filter');
const { User } = require('../../../models/User');

const buildEventGroupMemberFilter = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.role) {
            query.role = { $regex: filter.role, $options: 'i' };
        }

        if (filter.user) {
            const userQuery = await buildUserQuery(filter.user);
            if (Object.keys(userQuery).length > 0) {
                query.user = userQuery;
            }
        }
    }

    return query;
};

module.exports = buildEventGroupMemberFilter; 
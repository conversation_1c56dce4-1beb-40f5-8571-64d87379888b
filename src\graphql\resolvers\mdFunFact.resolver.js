const MdFunFact = require('../../models/MdFunFact');
const MdEventType = require('../../models/MdEventType');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMdFunFactQuery = require('./filters/mdFunFact.filter');
const { validateReferences } = require('../../utils/validation.util');

const mdFunFactIdSchema = {
    eventTypes: { type: 'array', required: false, model: MdEventType }
}

const mdFunFactResolvers = {
    MdFunFact: {
        eventTypes: async (parent) => {
            try {
                return await MdEventType.find({ _id: { $in: parent.eventTypes } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event types');
            }
        }
    },

    Query: {
        getMdFunFactById: async (_, { id }) => {
            try {
                const cachedFunFact = await getByIdCache(id);
                if (cachedFunFact) {
                    return createResponse('MdFunFactResponse', 'SUCCESS', 'Fun fact retrieved successfully from cache', { 
                        result: { mdFunFact: cachedFunFact } 
                    });
                }

                const mdFunFact = await MdFunFact.findById(id).populate('eventTypes');
                if (!mdFunFact) {
                    return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Fun fact not found', { 
                        errors: [{ field: 'id', message: 'Fun fact not found' }] 
                    });
                }

                await setCache(id, mdFunFact);
                return createResponse('MdFunFactResponse', 'SUCCESS', 'Fun fact retrieved successfully', { 
                    result: { mdFunFact } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Error retrieving fun fact', { 
                    errors: [{ field: 'getMdFunFactById', message: error.message }] 
                });
            }
        },

        getMdFunFacts: async (_, { filter, pagination }) => {
            try {
                const query = filter ? await buildMdFunFactQuery(filter) : {};
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdFunFact, query, limit, skip);

                const mdFunFacts = await MdFunFact.find(query)
                    .populate('eventTypes')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit);

                return createResponse('MdFunFactsResponse', 'SUCCESS', 'Fun facts fetched successfully', {
                    result: { mdFunFacts },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Error retrieving fun facts', {
                    errors: [{ field: 'getMdFunFacts', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdFunFact: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, mdFunFactIdSchema, 'MdFunFact');
                if (validationError) {
                    return createResponse('MdFunFactErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdFunFact = new MdFunFact(input);
                await mdFunFact.save();
                
                const populatedFunFact = await MdFunFact.findById(mdFunFact._id).populate('eventTypes');

                return createResponse('MdFunFactResponse', 'SUCCESS', 'Fun fact created successfully', { 
                    result: { mdFunFact: populatedFunFact } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Error creating fun fact', { 
                    errors: [{ field: 'createMdFunFact', message: error.message }] 
                });
            }
        },

        updateMdFunFact: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, mdFunFactIdSchema, 'MdFunFact');
                if (validationError) {
                    return createResponse('MdFunFactErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdFunFact = await MdFunFact.findByIdAndUpdate(input.id, input, { new: true }).populate('eventTypes');
                if (!mdFunFact) {
                    return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Fun fact not found', { 
                        errors: [{ field: 'id', message: 'Fun fact not found' }] 
                    });
                }
                await clearCacheById(input.id);

                return createResponse('MdFunFactResponse', 'SUCCESS', 'Fun fact updated successfully', {
                    result: { mdFunFact } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Error updating fun fact', { 
                    errors: [{ field: 'updateMdFunFact', message: error.message }] 
                });
            }
        },

        deleteMdFunFact: async (_, { id }) => {
            try {
                const mdFunFact = await MdFunFact.findByIdAndDelete(id);
                if (!mdFunFact) {
                    return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Fun fact not found', { 
                        errors: [{ field: 'id', message: 'Fun fact not found' }] 
                    });
                }

                await clearCacheById(id);
                return createResponse('MdFunFactResponse', 'SUCCESS', 'Fun fact deleted successfully', { 
                    result: { mdFunFact } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdFunFactErrorResponse', 'FAILURE', 'Error deleting fun fact', { 
                    errors: [{ field: 'deleteMdFunFact', message: error.message }] 
                });
            }
        }
    }
};

module.exports = mdFunFactResolvers;

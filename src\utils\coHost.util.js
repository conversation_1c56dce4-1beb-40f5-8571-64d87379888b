const { User } = require('../models/User');
const Host = require('../models/Host');
const { createResponse } = require('./response.util');
const MdRole = require('../models/MdRole');

const validateCoHosts = (coHosts) => {
    const errors = [];
    for (const coHost of coHosts) {
        if (!coHost.firstName?.trim()) {
            errors.push({ field: 'firstName', message: 'First name is required for co-host' });
        }
        if (!coHost.phone?.trim()) {
            errors.push({ field: 'phone', message: 'Phone number is required for co-host' });
        }
    }
    return errors;
};

const processCoHost = async ({ firstName, lastName, email, phone }) => {
    let user = await User.findOne({ phone });
    
    if (!user) {
        const defaultRole = await MdRole.findOne({ name: 'user' });
        if (!defaultRole) {
            throw new Error('Default user role not found');
        }

        user = new User({
            role: ["user"],
            firstName,
            lastName,
            email,
            phone,
            verified: false
        });
        await user.save();
    }

    let host = await Host.findOne({ userId: user._id });
    if (!host) {
        host = new Host({ userId: user._id });
        await host.save();
    }
    
    return host._id;
};

const processCoHosts = async (coHosts) => {
    if (!coHosts || coHosts.length === 0) {
        return [];
    }

    const coHostErrors = validateCoHosts(coHosts);
    if (coHostErrors.length > 0) {
        return { errors: coHostErrors };
    }

    const createdCoHosts = await Promise.all(
        coHosts.map(coHost => processCoHost(coHost))
    );

    return { coHostIds: createdCoHosts };
};

module.exports = {
    validateCoHosts,
    processCoHost,
    processCoHosts
}; 
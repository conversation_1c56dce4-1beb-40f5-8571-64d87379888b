const mongoose = require('mongoose');
const buildMdPolicyQuery = require('../../../../../src/graphql/resolvers/filters/mdPolicy.filter');
const Tag = require('../../../../../src/models/Tag');
const buildTagQuery = require('../../../../../src/graphql/resolvers/filters/tag.filter');

jest.mock('../../../../../src/utils/validation.util', () => ({
    isValidObjectId: jest.fn()
}));
jest.mock('../../../../../src/models/Tag');
jest.mock('../../../../../src/graphql/resolvers/filters/tag.filter');

describe('buildMdPolicyQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', async () => {
        const result = await buildMdPolicyQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid id filter', async () => {
        const validId = new mongoose.Types.ObjectId().toString();
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(true);

        const result = await buildMdPolicyQuery({ id: validId });

        expect(result).toEqual({
            _id: validId
        });
    });

    it('should throw error with invalid id filter', async () => {
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);

        await expect(buildMdPolicyQuery({ id: 'invalid-id' }))
            .rejects
            .toThrow('Invalid id provided');
    });

    it('should build query with name filter', async () => {
        const result = await buildMdPolicyQuery({ name: 'test' });

        expect(result).toEqual({
            name: { $regex: 'test', $options: 'i' }
        });
    });

    it('should build query with description filter', async () => {
        const result = await buildMdPolicyQuery({ description: 'test description' });

        expect(result).toEqual({
            description: { $regex: 'test description', $options: 'i' }
        });
    });

    it('should build query with valid tag ID', async () => {
        const validTagId = new mongoose.Types.ObjectId().toString();
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(true);

        const result = await buildMdPolicyQuery({ tag: validTagId });

        expect(result).toEqual({
            tags: validTagId
        });
    });

    it('should throw error with invalid tag ID', async () => {
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);

        await expect(buildMdPolicyQuery({ tag: 'invalid-tag-id' }))
            .rejects
            .toThrow('Invalid tag ID provided');
    });

    it('should build query with tag object filter and matching tags', async () => {
        const tagQuery = { name: 'test tag' };
        const matchingTags = [
            { _id: new mongoose.Types.ObjectId() },
            { _id: new mongoose.Types.ObjectId() }
        ];

        buildTagQuery.mockResolvedValue(tagQuery);
        Tag.find.mockReturnValue({
            select: jest.fn().mockResolvedValue(matchingTags)
        });

        const result = await buildMdPolicyQuery({ tag: { name: 'test tag' } });

        expect(buildTagQuery).toHaveBeenCalledWith({ name: 'test tag' });
        expect(Tag.find).toHaveBeenCalledWith(tagQuery);
        expect(result).toEqual({
            tags: { $in: matchingTags.map(tag => tag._id) }
        });
    });

    it('should build query with tag object filter and no matching tags', async () => {
        buildTagQuery.mockResolvedValue({ name: 'test tag' });
        Tag.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([])
        });

        const result = await buildMdPolicyQuery({ tag: { name: 'test tag' } });

        expect(result).toEqual({
            tags: { $in: [] }
        });
    });

    it('should build query with multiple filters', async () => {
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(true);
        const validId = new mongoose.Types.ObjectId().toString();

        const result = await buildMdPolicyQuery({
            id: validId,
            name: 'test',
            description: 'test description'
        });

        expect(result).toEqual({
            _id: validId,
            name: { $regex: 'test', $options: 'i' },
            description: { $regex: 'test description', $options: 'i' }
        });
    });
}); 
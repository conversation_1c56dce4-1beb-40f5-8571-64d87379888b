const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdServiceLocation {
        id: ID!
        name: String!
        city: String!
        state: String!
        areas: [String!]
        createdAt: DateTime!
        updatedAt: DateTime!
    }

    input MdServiceLocationFilterInput {
        id: String
        name: String
        city: String
        state: String
        areas: [String]
    }

    type MdServiceLocationWrapper {
        mdServiceLocation: MdServiceLocation!
    }

    type MdServiceLocationsWrapper {
        mdServiceLocations: [MdServiceLocation]!
    }

    type MdServiceLocationResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdServiceLocationWrapper!
    }

    type MdServiceLocationsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdServiceLocationsWrapper!
        pagination: PaginationInfo!
    }

    type MdServiceLocationErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdServiceLocationResult = MdServiceLocationResponse | MdServiceLocationErrorResponse
    union MdServiceLocationsResult = MdServiceLocationsResponse | MdServiceLocationErrorResponse

    type Query {
        getMdServiceLocationById(id: ID!): MdServiceLocationResult!
        getMdServiceLocations(filter: MdServiceLocationFilterInput, pagination: PaginationInput): MdServiceLocationsResult!
    }

    input MdServiceLocationInput {
        name: String!
        city: String!
        state: String!
        areas: [String!]
    }

    input MdServiceLocationUpdateInput {
        name: String
        city: String
        state: String
        areas: [String!]
    }

    type Mutation {
        createMdServiceLocation(input: MdServiceLocationInput!): MdServiceLocationResult!
        updateMdServiceLocation(id: ID!, input: MdServiceLocationUpdateInput!): MdServiceLocationResult!
        deleteMdServiceLocation(id: ID!): MdServiceLocationResult!
    }
`;
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Party = require('../../../src/models/Party');
const MdPartyType = require('../../../src/models/MdPartyType');
const Host = require('../../../src/models/Host');
const MdServiceLocation = require('../../../src/models/MdServiceLocation');
const PartyService = require('../../../src/models/PartyService');
const MdVendorType = require('../../../src/models/MdVendorType');
const Event = require('../../../src/models/Event');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Party Model Test', () => {
    it('should create and save a Party with all fields successfully', async () => {
        const mockPartyType = new MdPartyType({
            name: 'Test Party Type',
            description: 'Test Description'
        });
        await mockPartyType.save();

        const mockServiceLocation = new MdServiceLocation({
            name: 'Test Location',
            city: 'Test City',
            state: 'Test State',
            address: 'Test Address'
        });
        await mockServiceLocation.save();

        const mockHost = new Host({
            userId: new mongoose.Types.ObjectId()
        });
        await mockHost.save();

        const mockPartyService = new PartyService({
            vendorType: new mongoose.Types.ObjectId(),
            vendor: new mongoose.Types.ObjectId(),
            time: new Date(),
            city: 'Test City',
            address: 'Test Address'
        });
        await mockPartyService.save();

        const mockVendorType = new MdVendorType({
            name: 'Test Vendor Type',
            description: 'Test Description'
        });
        await mockVendorType.save();

        const mockEvent = new Event({
            name: 'Test Event',
            description: 'Test Description',
            mainHost: new mongoose.Types.ObjectId(),
            location: new mongoose.Types.ObjectId()
        });
        await mockEvent.save();

        const validParty = new Party({
            name: 'Birthday Party',
            partyType: mockPartyType._id,
            time: new Date('2024-12-25'),
            serviceLocation: mockServiceLocation._id,
            expectedGuestCount: 50,
            totalBudget: 1000,
            coHosts: [mockHost._id],
            services: [mockPartyService._id],
            vendorTypes: [mockVendorType._id],
            eventId: mockEvent._id
        });

        const savedParty = await validParty.save();

        expect(savedParty._id).toBeDefined();
        expect(savedParty.name).toBe('Birthday Party');
        expect(savedParty.partyType).toEqual(mockPartyType._id);
        expect(savedParty.time).toEqual(new Date('2024-12-25'));
        expect(savedParty.serviceLocation).toEqual(mockServiceLocation._id);
        expect(savedParty.expectedGuestCount).toBe(50);
        expect(savedParty.totalBudget).toBe(1000);
        expect(savedParty.coHosts).toContainEqual(mockHost._id);
        expect(savedParty.services).toContainEqual(mockPartyService._id);
        expect(savedParty.vendorTypes).toContainEqual(mockVendorType._id);
        expect(savedParty.eventId).toEqual(mockEvent._id);
        expect(savedParty.createdAt).toBeDefined();
        expect(savedParty.updatedAt).toBeDefined();
    });

    it('should fail to create a Party without required fields', async () => {
        const party = new Party();

        let err;
        try {
            await party.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.partyType).toBeDefined();
        expect(err.errors.time).toBeDefined();
    });

    it('should create a Party with only required fields and default values', async () => {
        const mockPartyType = new MdPartyType({
            name: 'Test Party Type',
            description: 'Test Description'
        });
        await mockPartyType.save();

        const minimalParty = new Party({
            name: 'Minimal Party',
            partyType: mockPartyType._id,
            time: new Date('2024-12-25')
        });

        const savedParty = await minimalParty.save();

        expect(savedParty._id).toBeDefined();
        expect(savedParty.name).toBe('Minimal Party');
        expect(savedParty.partyType).toEqual(mockPartyType._id);
        expect(savedParty.time).toEqual(new Date('2024-12-25'));
        expect(savedParty.expectedGuestCount).toBe(0);
        expect(savedParty.totalBudget).toBe(0);
        expect(savedParty.coHosts).toEqual([]);
        expect(savedParty.services).toEqual([]);
        expect(savedParty.vendorTypes).toEqual([]);
        expect(savedParty.serviceLocation).toBeUndefined();
        expect(savedParty.eventId).toBeUndefined();
    });

    it('should fail to create a Party with invalid reference IDs', async () => {
        const invalidParty = new Party({
            name: 'Invalid Party',
            partyType: 'invalid_id',
            time: new Date(),
            serviceLocation: 'invalid_id',
            coHosts: ['invalid_id'],
            services: ['invalid_id'],
            vendorTypes: ['invalid_id'],
            eventId: 'invalid_id'
        });

        let err;
        try {
            await invalidParty.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.partyType).toBeDefined();
        expect(err.errors.serviceLocation).toBeDefined();
        expect(err.errors['coHosts.0']).toBeDefined();
        expect(err.errors['services.0']).toBeDefined();
        expect(err.errors['vendorTypes.0']).toBeDefined();
        expect(err.errors.eventId).toBeDefined();
    });
});
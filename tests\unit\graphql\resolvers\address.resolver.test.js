const mongoose = require('mongoose');
const Address = require('../../../../src/models/Address');
const addressResolvers = require('../../../../src/graphql/resolvers/address.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildAddressQuery = require('../../../../src/graphql/resolvers/filters/address.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/models/Address');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/address.filter');
jest.mock('../../../../src/utils/referenceCheck.util', () => ({
    findReferences: jest.fn()
}));

describe('Address Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Query', () => {
        describe('getAddressById', () => {
            it('should return address from cache if available', async () => {
                const address = { id: '1', street: '123 Main St' };
                getByIdCache.mockResolvedValue(address);
                createResponse.mockReturnValue('success response');

                const result = await addressResolvers.Query.getAddressById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(Address.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressResponse',
                    'SUCCESS',
                    'Address retrieved successfully',
                    { result: { address } }
                );
                expect(result).toBe('success response');
            });

            it('should fetch and cache address if not in cache', async () => {
                const address = { id: '1', street: '123 Main St' };
                getByIdCache.mockResolvedValue(null);
                Address.findById.mockResolvedValue(address);
                createResponse.mockReturnValue('success response');

                const result = await addressResolvers.Query.getAddressById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(Address.findById).toHaveBeenCalledWith('1');
                expect(setCache).toHaveBeenCalledWith('1', address);
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressResponse',
                    'SUCCESS',
                    'Address retrieved successfully',
                    { result: { address } }
                );
                expect(result).toBe('success response');
            });

            it('should return error if address not found', async () => {
                getByIdCache.mockResolvedValue(null);
                Address.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await addressResolvers.Query.getAddressById(null, { id: '1' });

                expect(createResponse).toHaveBeenCalledWith(
                    'AddressErrorResponse',
                    'FAILURE',
                    'Address not found',
                    { errors: [{ field: 'id', message: 'Address not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response when an error occurs', async () => {
                const id = 'addressId';
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving address',
                    errors: [{ field: 'getAddressById', message: error.message }]
                });

                const result = await addressResolvers.Query.getAddressById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressErrorResponse',
                    'FAILURE',
                    'Error retrieving address',
                    { errors: [{ field: 'getAddressById', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving address',
                    errors: [{ field: 'getAddressById', message: error.message }]
                });
            });

            it('should return error response if address not found', async () => {
                const id = 'nonexistentId';
                getByIdCache.mockResolvedValue(null);
                Address.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                  status: 'FAILURE',
                  message: 'Address not found',
                  errors: [{ field: 'id', message: 'Address not found' }]
                });
        
                const result = await addressResolvers.Query.getAddressById(null, { id });
        
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Address.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                  'AddressErrorResponse',
                  'FAILURE',
                  'Address not found',
                  {
                    errors: [{ field: 'id', message: 'Address not found' }]
                  }
                );
                expect(result).toEqual({
                  status: 'FAILURE',
                  message: 'Address not found',
                  errors: [{ field: 'id', message: 'Address not found' }]
                });
            });
        });

        describe('getAddresses', () => {
            it('should return addresses successfully', async () => {
                const addresses = [{ id: '1', street: '123 Main St' }];
                const paginationInfo = { total: 1, hasMore: false };
                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(addresses)
                };

                buildAddressQuery.mockReturnValue({});
                Address.find.mockReturnValue(mockFind);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                createResponse.mockReturnValue('success response');

                const result = await addressResolvers.Query.getAddresses(null, {
                    filter: {},
                    pagination: { limit: 10, skip: 0 }
                });

                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressesResponse',
                    'SUCCESS',
                    'Addresses fetched successfully',
                    { result: { addresses }, pagination: paginationInfo }
                );
                expect(result).toBe('success response');
            });

            it('should return error response if address not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                Address.findById.mockResolvedValue(null);
                
                createResponse.mockReturnValue({
                  status: 'FAILURE',
                  message: 'Address not found',
                  errors: [{ field: 'id', message: 'Address not found' }]
                });
        
                const result = await addressResolvers.Query.getAddressById(null, { id });
        
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Address.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                  'AddressErrorResponse',
                  'FAILURE',
                  'Address not found',
                  {
                    errors: [{ field: 'id', message: 'Address not found' }]
                  }
                );
                expect(result).toEqual({
                  status: 'FAILURE',
                  message: 'Address not found',
                  errors: [{ field: 'id', message: 'Address not found' }]
                });
            });
        });

        it('should return failure response if no addresses found', async () => {
            const filter = { city: 'NonexistentCity' };
            const pagination = { limit: 10, skip: 0 };
            const query = { city: { $regex: 'NonexistentCity', $options: 'i' } };
            const addresses = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };
        
            buildAddressQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            
            const mockFind = {
                limit: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                sort: jest.fn().mockResolvedValue(addresses)
            };
        
            Address.find.mockReturnValue(mockFind);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'No addresses found',
                result: { addresses },
                pagination: paginationInfo
            });
        
            const result = await addressResolvers.Query.getAddresses(null, { filter, pagination });
        
            expect(buildAddressQuery).toHaveBeenCalledWith(filter);
            expect(getPaginationInfo).toHaveBeenCalledWith(Address, query, pagination.limit, pagination.skip);
            expect(Address.find).toHaveBeenCalledWith(query);
            expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
            expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
            expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
            expect(createResponse).toHaveBeenCalledWith(
                'AddressesResponse',
                'FAILURE',
                'No addresses found',
                { result: { addresses }, pagination: paginationInfo }
            );
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'No addresses found',
                result: { addresses },
                pagination: paginationInfo
            });
        });

        it('should return error response when an error occurs', async () => {
            const filter = { city: 'Test City' };
            const pagination = { limit: 10, skip: 0 };
            const error = new Error('Database error');

            buildAddressQuery.mockImplementation(() => {
                throw error;
            });
            createResponse.mockReturnValue('error response');

            const result = await addressResolvers.Query.getAddresses(null, { filter, pagination });

            expect(buildAddressQuery).toHaveBeenCalledWith(filter);
            expect(createResponse).toHaveBeenCalledWith(
                'AddressErrorResponse',
                'FAILURE',
                'Error retrieving addresses',
                { errors: [{ field: 'getAddresses', message: error.message }] }
            );
            expect(result).toBe('error response');
        });
    });

    describe('Mutation', () => {
        describe('createAddress', () => {
            it('should create a new address successfully', async () => {
                const mockInput = {
                    addressType: 'HOME',
                    street1: '123 Main St',
                    city: 'New York',
                    state: 'NY',
                    postalCode: '10001',
                    country: 'USA'
                };
                const addressMock = {
                    _id: '123',
                    ...mockInput,
                    save: jest.fn().mockResolvedValue(true)
                };
                Address.mockImplementation(() => addressMock);
                const createResponseMock = {
                    status: 'SUCCESS',
                    message: 'Address created successfully',
                    result: { address: addressMock }
                };
                createResponse.mockReturnValue(createResponseMock);
            
                const result = await addressResolvers.Mutation.createAddress(null, { input: mockInput });
            
                expect(Address).toHaveBeenCalledWith(mockInput);
                expect(addressMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('AddressResponse', 'SUCCESS', 'Address created successfully', { result: { address: addressMock } });
                expect(result).toBe(createResponseMock);
            });

            it('should handle creation error', async () => {
                const input = { street: '123 Main St' };
                const error = new Error('Creation failed');

                Address.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue('error response');

                const result = await addressResolvers.Mutation.createAddress(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'AddressErrorResponse',
                    'FAILURE',
                    'Error creating address',
                    { errors: [{ field: 'createAddress', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('updateAddress', () => {
            it('should update address successfully', async () => {
                const id = '1';
                const input = { street: 'Updated Street' };
                const address = { id, ...input };

                Address.findByIdAndUpdate.mockResolvedValue(address);
                createResponse.mockReturnValue('success response');

                const result = await addressResolvers.Mutation.updateAddress(null, { id, input });

                expect(Address.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressResponse',
                    'SUCCESS',
                    'Address updated successfully',
                    { result: { address } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response when an error occurs during update', async () => {
                const id = 'addressId';
                const input = { street: 'New Street' };
                const error = new Error('Database error');

                Address.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error updating address',
                    errors: [{ field: 'updateAddress', message: error.message }]
                });

                const result = await addressResolvers.Mutation.updateAddress(null, { id, input });

                expect(Address.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith(
                    'AddressErrorResponse',
                    'FAILURE',
                    'Error updating address',
                    { errors: [{ field: 'updateAddress', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error updating address',
                    errors: [{ field: 'updateAddress', message: error.message }]
                });
            });
        });

        describe('deleteAddress', () => {
            const mockId = 'mock-address-id';
        const mockAddress = {
            _id: mockId,
            street: '123 Test St',
            city: 'Test City'
        };

        it('should successfully delete an address when no references exist', async () => {
            findReferences.mockResolvedValue([]);
            Address.findByIdAndDelete.mockResolvedValue(mockAddress);

            const result = await addressResolvers.Mutation.deleteAddress(null, { id: mockId });

            expect(findReferences).toHaveBeenCalledWith(mockId, 'Address');
            expect(Address.findByIdAndDelete).toHaveBeenCalledWith(mockId);
            expect(clearCacheById).toHaveBeenCalledWith(mockId);
            expect(result).toEqual(
                createResponse('AddressResponse', 'SUCCESS', 'Address deleted successfully', {
                    result: { address: mockAddress }
                })
            );
        });

        it('should return error response when address has references', async () => {
            const mockReferences = ['User', 'Organization'];
            findReferences.mockResolvedValue(mockReferences);

            const result = await addressResolvers.Mutation.deleteAddress(null, { id: mockId });

            expect(findReferences).toHaveBeenCalledWith(mockId, 'Address');
            expect(Address.findByIdAndDelete).not.toHaveBeenCalled();
            expect(clearCacheById).not.toHaveBeenCalled();
            expect(result).toEqual(
                createResponse('AddressErrorResponse', 'FAILURE', 'Address cannot be deleted', {
                    errors: [{ 
                        field: 'deleteAddress', 
                        message: `Address cannot be deleted as it is being used in: ${mockReferences.join(', ')}` 
                    }]
                })
            );
        });

        it('should return error response when address is not found', async () => {
            findReferences.mockResolvedValue([]);
            Address.findByIdAndDelete.mockResolvedValue(null);

            const result = await addressResolvers.Mutation.deleteAddress(null, { id: mockId });

            expect(findReferences).toHaveBeenCalledWith(mockId, 'Address');
            expect(Address.findByIdAndDelete).toHaveBeenCalledWith(mockId);
            expect(clearCacheById).not.toHaveBeenCalled();
            expect(result).toEqual(
                createResponse('AddressErrorResponse', 'FAILURE', 'Address not found', {
                    errors: [{ field: 'id', message: 'Address not found' }]
                })
            );
        });

        it('should handle errors during deletion', async () => {
            const error = new Error('Database error');
            findReferences.mockResolvedValue([]);
            Address.findByIdAndDelete.mockRejectedValue(error);

            const result = await addressResolvers.Mutation.deleteAddress(null, { id: mockId });

            expect(findReferences).toHaveBeenCalledWith(mockId, 'Address');
            expect(Address.findByIdAndDelete).toHaveBeenCalledWith(mockId);
            expect(clearCacheById).not.toHaveBeenCalled();
            expect(result).toEqual(
                createResponse('AddressErrorResponse', 'FAILURE', 'Error deleting address', {
                    errors: [{ field: 'deleteAddress', message: error.message }]
                })
            );
        });

        it('should handle errors during reference check', async () => {
            const error = new Error('Reference check failed');
            findReferences.mockRejectedValue(error);

            const result = await addressResolvers.Mutation.deleteAddress(null, { id: mockId });

            expect(findReferences).toHaveBeenCalledWith(mockId, 'Address');
            expect(Address.findByIdAndDelete).not.toHaveBeenCalled();
            expect(clearCacheById).not.toHaveBeenCalled();
            expect(result).toEqual(
                createResponse('AddressErrorResponse', 'FAILURE', 'Error deleting address', {
                        errors: [{ field: 'deleteAddress', message: error.message }]
                    })
                );
            });
        });
    });
});

const Media = require("../../../models/Media");

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];

    const tagReferences = await Media.find({ 'tags': { $in: [id] } });
    if (hasReferences(tagReferences)) {
        references.push('Media');
    }

    return references;
}

module.exports = findReferences;


const { gql } = require('graphql-tag');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('sharedTypeDef', () => {
  it('should contain the shared type definitions', () => {
    const expectedTypeDefs = gql`
      scalar Date

      scalar DateTime

      enum ResponseStatus {
        SUCCESS
        FAILURE
      }

      interface Response {
        status: ResponseStatus!
        message: String!
      }

      type Error {
        field: String!
        message: String!
      }

      input PaginationInput {
        limit: Int
        skip: Int
      }

      type PaginationInfo {
        totalItems: Int!
        totalPages: Int!
        pageSize: Int!
        currentPage: Int!
      }

      input DateRangeInput {
        start: Date
        end: Date
      }

      input DateTimeRangeInput {
        start: DateTime
        end: DateTime
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(sharedTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
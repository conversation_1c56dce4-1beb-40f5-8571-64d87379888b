const { GraphQLScalarType } = require('graphql');

const phoneRegex = /^\+[1-9]\d{1,14}$/;  // Matches +<country code><number>

const PhoneNumberScalar = new GraphQLScalarType({
    name: 'PhoneNumber',
    description: 'Phone number scalar type in E.164 format (e.g., +919015555555)',
    
    parseValue(value) {
        if (!phoneRegex.test(value)) {
            throw new Error('Invalid phone number format. Must be in E.164 format (e.g., +919015555555)');
        }
        return value;
    },
    
    serialize(value) {
        return value;
    },
    
    parseLiteral(ast) {
        if (!phoneRegex.test(ast.value)) {
            throw new Error('Invalid phone number format. Must be in E.164 format (e.g., +919015555555)');
        }
        return ast.value;
    }
});

module.exports = {
    PhoneNumber: PhoneNumberScalar
}; 
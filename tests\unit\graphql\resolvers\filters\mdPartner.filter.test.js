const mongoose = require('mongoose');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');
const buildTagQuery = require('../../../../../src/graphql/resolvers/filters/tag.filter');
const buildMdIconQuery = require('../../../../../src/graphql/resolvers/filters/mdIcon.filter');
const Tag = require('../../../../../src/models/Tag');
const MdIcon = require('../../../../../src/models/MdIcon');
const buildMdPartnerQuery = require('../../../../../src/graphql/resolvers/filters/mdPartner.filter');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/tag.filter');
jest.mock('../../../../../src/graphql/resolvers/filters/mdIcon.filter');
jest.mock('../../../../../src/models/Tag');
jest.mock('../../../../../src/models/MdIcon');

describe('buildMdPartnerQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query object when no filter is provided', async () => {
        const query = await buildMdPartnerQuery();
        expect(query).toEqual({});
    });

    describe('id filter', () => {
        it('should add valid id to query', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { id: 'validObjectId' };
            
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({ _id: 'validObjectId' });
            expect(isValidObjectId).toHaveBeenCalledWith('validObjectId');
        });

        it('should throw error for invalid id', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { id: 'invalidId' };
            
            await expect(buildMdPartnerQuery(filter))
                .rejects.toThrow('Invalid id provided');
        });
    });

    describe('text filters', () => {
        it('should handle name filter', async () => {
            const filter = { name: 'testName' };
            const query = await buildMdPartnerQuery(filter);
            expect(query).toEqual({
                name: { $regex: 'testName', $options: 'i' }
            });
        });

        it('should handle description filter', async () => {
            const filter = { description: 'testDesc' };
            const query = await buildMdPartnerQuery(filter);
            expect(query).toEqual({
                description: { $regex: 'testDesc', $options: 'i' }
            });
        });

        it('should handle banner_image_url filter', async () => {
            const filter = { banner_image_url: 'testUrl' };
            const query = await buildMdPartnerQuery(filter);
            expect(query).toEqual({
                banner_image_url: { $regex: 'testUrl', $options: 'i' }
            });
        });
    });

    describe('categories filter', () => {
        it('should handle categories as string ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { categories: 'validCategoryId' };
            
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({ categories: 'validCategoryId' });
        });

        it('should throw error for invalid categories ID', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { categories: 'invalidId' };
            
            await expect(buildMdPartnerQuery(filter))
                .rejects.toThrow('Invalid categories ID provided');
        });

        it('should handle categories as object filter', async () => {
            const tagQuery = { name: 'testTag' };
            const matchingTags = [{ _id: 'tag1' }, { _id: 'tag2' }];
            
            buildTagQuery.mockResolvedValue(tagQuery);
            Tag.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingTags)
            });
            
            const filter = { categories: { name: 'testTag' } };
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({
                categories: { $in: ['tag1', 'tag2'] }
            });
        });

        it('should handle empty categories results', async () => {
            buildTagQuery.mockResolvedValue({ name: 'nonexistent' });
            Tag.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });
            
            const filter = { categories: { name: 'nonexistent' } };
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({
                categories: { $in: [] }
            });
        });
    });

    describe('icon filter', () => {
        it('should handle icon as string ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { icon: 'validIconId' };
            
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({ icon: 'validIconId' });
        });

        it('should throw error for invalid icon ID', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { icon: 'invalidId' };
            
            await expect(buildMdPartnerQuery(filter))
                .rejects.toThrow('Invalid icon ID provided');
        });

        it('should handle icon as object filter', async () => {
            const iconQuery = { name: 'testIcon' };
            const matchingIcons = [{ _id: 'icon1' }, { _id: 'icon2' }];
            
            buildMdIconQuery.mockResolvedValue(iconQuery);
            MdIcon.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingIcons)
            });
            
            const filter = { icon: { name: 'testIcon' } };
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({
                icon: { $in: ['icon1', 'icon2'] }
            });
        });

        it('should handle empty icon results', async () => {
            buildMdIconQuery.mockResolvedValue({ name: 'nonexistent' });
            MdIcon.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });
            
            const filter = { icon: { name: 'nonexistent' } };
            const query = await buildMdPartnerQuery(filter);
            
            expect(query).toEqual({
                icon: { $in: [] }
            });
        });
    });
});
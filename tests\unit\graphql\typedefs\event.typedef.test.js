const { gql } = require('graphql-tag');
const eventTypeDef = require('../../../../src/graphql/typedefs/event.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('eventTypeDef', () => {
  it('should contain the Event type definitions', () => {
    const expectedTypeDefs = gql`
        ${sharedTypeDef}

        input BudgetRangeInput {
            min: Float
            max: Float
        }

        enum UserRole {
            MAIN_HOST
            CO_HOST
            GUEST
        }

        type Event {
            id: ID!
            eventType: MdEventType
            name: String!
            description: String!
            startDate: DateTime
            endDate: DateTime
            mainHost: Host!
            coHosts: [Host!]
            guests: [Guest!]
            budget: Float!
            location: MdServiceLocation!
            parties: [Party!]
            status: [UserRole]
        }

        input EventFilterInput {
            eventType: MdEventTypeFilterInput
            name: String
            description: String
            theme: MdThemeFilterInput
            budget: BudgetRangeInput
            location: MdServiceLocationFilterInput
            parties: PartyFilterInput
            dateRange: DateTimeRangeInput
            presentAndUpcoming: Boolean
        }

        type EventWrapper {
            event: Event!
        }

        type EventsWrapper {
            events: [Event]!
        }

        type EventResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: EventWrapper!
        }

        type EventsResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: EventsWrapper!
            pagination: PaginationInfo!
        }

        type EventErrorResponse implements Response {
            status: ResponseStatus!
            message: String!
            errors: [Error!]!
        }

        union EventResult = EventResponse | EventErrorResponse
        union EventsResult = EventsResponse | EventErrorResponse

        input CoHostInput {
            name: String!
            phone: String!
            email: String
        }

        input EventInput {
            eventType: ID
            name: String!
            description: String!
            coHosts: [CoHostInput!]
            location: ID!
            startDate: DateTime
            endDate: DateTime
        }

        input EventUpdateInput {
            eventType: ID
            name: String
            description: String
            coHosts: [CoHostInput!]
            location: ID
            startDate: DateTime
            endDate: DateTime
        }

        type Query {
            getEventById(id: ID!): EventResult!
            getEvents(filter: EventFilterInput, pagination: PaginationInput): EventsResult!
            getUserEvents(filter: EventFilterInput, pagination: PaginationInput): EventsResult!
        }

        type Mutation {
            createEvent(input: EventInput!): EventResult!
            updateEvent(id: ID!, input: EventUpdateInput!): EventResult!
            deleteEvent(id: ID!): EventResult!
        }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(eventTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
}); 
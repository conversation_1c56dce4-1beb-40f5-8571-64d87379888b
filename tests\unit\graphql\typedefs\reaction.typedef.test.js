const { gql } = require('graphql-tag');
const reactionTypeDef = require('../../../../src/graphql/typedefs/reaction.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('reactionTypeDef', () => {
    it('should contain the Reaction type definitions', () => {
        const expectedTypeDefs = gql`
            ${sharedTypeDef}

            type Reaction {
                id: ID!
                type: MdReaction!
                user: User!
                timestamp: DateTime!
            }

            input ReactionFilterInput {
                type: MdReactionFilterInput
                user: UserFilterInput
                timestamp: DateTimeRangeInput
            }

            type ReactionWrapper {
                reaction: Reaction!
            }

            type ReactionsWrapper {
                reactions: [Reaction]!
            }

            type ReactionResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: ReactionWrapper!
            }

            type ReactionsResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: ReactionsWrapper!
                pagination: PaginationInfo!
            }

            type ReactionErrorResponse implements Response {
                status: ResponseStatus!
                message: String!
                errors: [Error!]!
            }

            union ReactionResult = ReactionResponse | ReactionErrorResponse
            union ReactionsResult = ReactionsResponse | ReactionErrorResponse

            type Query {
                getReactionById(id: ID!): ReactionResult!
                getReactions(filter: ReactionFilterInput, pagination: PaginationInput): ReactionsResult!
            }

            input ReactionInput {
                type: ID!
                user: ID!
                timestamp: DateTime!
            }

            type Mutation {
                createReaction(input: ReactionInput!): ReactionResult!
                deleteReaction(id: ID!): ReactionResult!
            }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(reactionTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
}); 
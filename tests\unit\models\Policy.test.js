const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Policy = require('../../../src/models/Policy');
const Vendor = require('../../../src/models/Vendor');
const Tag = require('../../../src/models/Tag');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Policy Model Test', () => {
    it('should create and save a Policy successfully', async () => {
        const mockVendor = new Vendor({
            name: 'Test Vendor',
            vendorType: new mongoose.Types.ObjectId(),
            primaryContact: new mongoose.Types.ObjectId(),
            businessAddress: new mongoose.Types.ObjectId(),
            contacts: [new mongoose.Types.ObjectId()],
            serviceLocations: [new mongoose.Types.ObjectId()],
            servicesProvided: [new mongoose.Types.ObjectId()]
        });
        await mockVendor.save();

        const mockTag = new Tag({
            name: 'Test Tag',
            slug: 'test-tag'
        });
        await mockTag.save();

        const validPolicy = new Policy({
            vendor: mockVendor._id,
            name: 'Test Policy',
            description: 'This is a test policy',
            order: 1,
            tags: [mockTag._id],
            active: true
        });

        const savedPolicy = await validPolicy.save();

        expect(savedPolicy._id).toBeDefined();
        expect(savedPolicy.vendor).toEqual(mockVendor._id);
        expect(savedPolicy.name).toBe('Test Policy');
        expect(savedPolicy.description).toBe('This is a test policy');
        expect(savedPolicy.order).toBe(1);
        expect(savedPolicy.tags).toContainEqual(mockTag._id);
        expect(savedPolicy.active).toBe(true);
        expect(savedPolicy.createdAt).toBeDefined();
        expect(savedPolicy.updatedAt).toBeDefined();
    });

    it('should fail to create a Policy without required fields', async () => {
        const policy = new Policy();

        let err;
        try {
            await policy.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.vendor).toBeDefined();
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
        expect(err.errors.order).toBeDefined();
    });

    it('should create a Policy with default active value', async () => {
        const mockVendor = new Vendor({
            name: 'Test Vendor',
            vendorType: new mongoose.Types.ObjectId(),
            primaryContact: new mongoose.Types.ObjectId(),
            businessAddress: new mongoose.Types.ObjectId(),
            contacts: [new mongoose.Types.ObjectId()],
            serviceLocations: [new mongoose.Types.ObjectId()],
            servicesProvided: [new mongoose.Types.ObjectId()]
        });
        await mockVendor.save();

        const minimalPolicy = new Policy({
            vendor: mockVendor._id,
            name: 'Minimal Policy',
            description: 'Minimal policy description',
            order: 1
        });

        const savedPolicy = await minimalPolicy.save();

        expect(savedPolicy._id).toBeDefined();
        expect(savedPolicy.vendor).toEqual(mockVendor._id);
        expect(savedPolicy.name).toBe('Minimal Policy');
        expect(savedPolicy.description).toBe('Minimal policy description');
        expect(savedPolicy.order).toBe(1);
        expect(savedPolicy.active).toBe(false);
        expect(savedPolicy.tags).toEqual([]);
    });

    it('should fail to create a Policy with invalid reference IDs', async () => {
        const invalidPolicy = new Policy({
            vendor: 'invalid_id',
            name: 'Invalid Policy',
            description: 'This should fail',
            order: 1,
            tags: ['invalid_id']
        });

        let err;
        try {
            await invalidPolicy.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.vendor).toBeDefined();
        expect(err.errors['tags.0']).toBeDefined();
    });
}); 
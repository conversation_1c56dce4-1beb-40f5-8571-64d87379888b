const { isValidObjectId } = require('../../../utils/validation.util');

const buildReminderQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (Array.isArray(filter.id)) {
                query._id = { $in: filter.id };
            } else {
                query._id = filter.id;
            }
        }

        if (filter.party) {
            if (isValidObjectId(filter.party)) {
                query.party = filter.party;
            } else {
                throw new Error('Invalid party ID provided');
            }
        }

        if (filter.message) {
            query.message = { $regex: filter.message, $options: 'i' };
        }

        if (filter.datetime) {
            query.datetime = filter.datetime;
        }
    }

    return query;
};

module.exports = buildReminderQuery;

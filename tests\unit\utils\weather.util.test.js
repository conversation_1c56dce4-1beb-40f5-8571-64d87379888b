const { getWeatherForecast } = require('../../../src/utils/weather.util');

const originalEnv = process.env;

global.fetch = jest.fn();

describe('Weather Utility Tests', () => {
    beforeEach(() => {
        process.env = { ...originalEnv };
        process.env.WEATHER_API_KEY = 'test_api_key';
        process.env.WEATHER_API_BASE_URL = 'https://api.weatherapi.com/v1';
        
        jest.clearAllMocks();
        jest.resetModules();
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    it('should fetch weather forecast successfully', async () => {
        const mockWeatherData = {
            forecast: {
                forecastday: [{
                    day: {
                        maxtemp_c: 25,
                        mintemp_c: 15,
                        condition: { text: 'Sunny' }
                    }
                }]
            }
        };

        global.fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve(mockWeatherData)
        });

        const result = await getWeatherForecast('London');

        expect(result).toEqual(mockWeatherData);
        const fetchCall = fetch.mock.calls[0][0];
        expect(fetchCall).toBeInstanceOf(URL);
        expect(fetchCall.toString()).toContain('https://api.weatherapi.com/v1/forecast.json');
        expect(fetchCall.searchParams.get('key')).toBe('test_api_key');
        expect(fetchCall.searchParams.get('q')).toBe('London');
    });

    it('should handle location with special characters', async () => {
        const mockWeatherData = {
            forecast: {
                forecastday: [{
                    day: {}
                }]
            }
        };

        global.fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve(mockWeatherData)
        });

        await getWeatherForecast('New York, NY');

        const fetchCall = fetch.mock.calls[0][0];
        expect(fetchCall).toBeInstanceOf(URL);
        expect(decodeURIComponent(fetchCall.searchParams.get('q'))).toBe('New York, NY');
    });

    it('should throw error when API key is not configured', async () => {
        process.env.WEATHER_API_KEY = '';

        try {
            await getWeatherForecast('London');
            fail('Should have thrown an error');
        } catch (error) {
            expect(error.message).toBe('Weather API key is not configured');
        }
    });

    it('should throw error when base URL is not configured', async () => {
        process.env.WEATHER_API_BASE_URL = '';

        try {
            await getWeatherForecast('London');
            fail('Should have thrown an error');
        } catch (error) {
            expect(error.message).toBe('Weather API base URL is not configured');
        }
    });

    it('should throw error when location is not provided', async () => {
        try {
            await getWeatherForecast();
            fail('Should have thrown an error');
        } catch (error) {
            expect(error.message).toBe('Location parameter is required');
        }
    });

    it('should handle invalid weather data format', async () => {
        global.fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({ forecast: {} })
        });

        try {
            await getWeatherForecast('London');
            fail('Should have thrown an error');
        } catch (error) {
            expect(error.message).toBe('Invalid weather data format received');
        }
    });

    it('should handle network errors', async () => {
        global.fetch.mockRejectedValueOnce(new Error('Network error'));

        try {
            await getWeatherForecast('London');
            fail('Should have thrown an error');
        } catch (error) {
            expect(error.message).toBe('Failed to fetch weather data');
        }
    });

    it('should accept custom number of days', async () => {
        const mockWeatherData = {
            forecast: {
                forecastday: [{
                    day: {}
                }]
            }
        };

        global.fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve(mockWeatherData)
        });

        await getWeatherForecast('London', 5);

        const fetchCall = fetch.mock.calls[0][0];
        expect(fetchCall).toBeInstanceOf(URL);
        expect(fetchCall.searchParams.get('days')).toBe('5');
    });
}); 
const { GraphQLScalarType, Kind } = require('graphql');

const dateScalar = new GraphQLScalarType({
    name: 'Date',
    description: 'Custom scalar type for date',
    parseValue(value) {
        return new Date(value).toISOString();
    },
    serialize(value) {
        return new Date(value).toISOString();
    },
    parseLiteral(ast) {
        if (ast.kind === Kind.STRING) {
            return new Date(ast.value).toISOString();
        }
        return null;
    },
});

module.exports = {
    Date: dateScalar,
};
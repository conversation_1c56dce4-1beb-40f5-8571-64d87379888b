const { isValidObjectId } = require("../../../utils/validation.util");

const buildMdThemeQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }
    }

    return query;
};

module.exports = buildMdThemeQuery; 
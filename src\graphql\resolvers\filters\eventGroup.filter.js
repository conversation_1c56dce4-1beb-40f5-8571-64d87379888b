const { isValidObjectId } = require('../../../utils/validation.util');
const buildUserQuery = require('./user.filter');
const buildMdInterestQuery = require('./mdInterest.filter');
const buildMdInterestCategoryQuery = require('./mdInterestCategory.filter');
const { User } = require('../../../models/User');
const MdInterest = require('../../../models/MdInterest');
const MdInterestCategory = require('../../../models/MdInterestCategory');
const EventGroup = require('../../../models/EventGroup');

const buildEventGroupFilter = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.type) {
            query.type = filter.type;
        }

        if (filter.interests) {
            const interestQueries = await Promise.all(
                filter.interests.map(interest => buildMdInterestQuery(interest))
            );
            
            if (interestQueries.some(q => Object.keys(q).length > 0)) {
                const matchingInterests = await MdInterest.find({
                    $or: interestQueries.filter(q => Object.keys(q).length > 0)
                }).select('_id');

                if (matchingInterests.length > 0) {
                    query.interests = { $in: matchingInterests.map(interest => interest._id) };
                } else {
                    query.interests = { $in: [] };
                }
            }
        }

        if (filter.interestCategories) {
            const categoryQueries = await Promise.all(
                filter.interestCategories.map(category => buildMdInterestCategoryQuery(category))
            );
            
            if (categoryQueries.some(q => Object.keys(q).length > 0)) {
                const matchingCategories = await MdInterestCategory.find({
                    $or: categoryQueries.filter(q => Object.keys(q).length > 0)
                }).select('_id');

                if (matchingCategories.length > 0) {
                    query.interestCategories = { $in: matchingCategories.map(category => category._id) };
                } else {
                    query.interestCategories = { $in: [] };
                }
            }
        }

        if (filter.members) {
            const memberQueries = await Promise.all(
                filter.members.map(member => buildUserQuery(member))
            );
            
            if (memberQueries.some(q => Object.keys(q).length > 0)) {
                const matchingMembers = await User.find({
                    $or: memberQueries.filter(q => Object.keys(q).length > 0)
                }).select('_id');

                if (matchingMembers.length > 0) {
                    query.members = { $in: matchingMembers.map(member => member._id) };
                } else {
                    query.members = { $in: [] };
                }
            }
        }

        if (filter.organizers) {
            const organizerQueries = await Promise.all(
                filter.organizers.map(organizer => buildUserQuery(organizer))
            );
            
            if (organizerQueries.some(q => Object.keys(q).length > 0)) {
                const matchingOrganizers = await User.find({
                    $or: organizerQueries.filter(q => Object.keys(q).length > 0)
                }).select('_id');

                if (matchingOrganizers.length > 0) {
                    query.organizers = { $in: matchingOrganizers.map(organizer => organizer._id) };
                } else {
                    query.organizers = { $in: [] };
                }
            }
        }

        if (filter.parentGroup) {
            if (typeof filter.parentGroup === 'string') {
                if (isValidObjectId(filter.parentGroup)) {
                    query.parentGroup = filter.parentGroup;
                } else {
                    throw new Error('Invalid parentGroup ID provided');
                }
            } else if (typeof filter.parentGroup === 'object') {
                const parentGroupQuery = await buildEventGroupFilter(filter.parentGroup);
                if (Object.keys(parentGroupQuery).length > 0) {
                    const matchingGroups = await EventGroup.find(parentGroupQuery).select('_id');
                    if (matchingGroups.length > 0) {
                        query.parentGroup = { $in: matchingGroups.map(group => group._id) };
                    } else {
                        query.parentGroup = { $in: [] };
                    }
                }
            }
        }

        if (filter.createdBy) {
            const createdByQuery = await buildUserQuery(filter.createdBy);
            if (Object.keys(createdByQuery).length > 0) {
                const matchingUsers = await User.find(createdByQuery).select('_id');
                if (matchingUsers.length > 0) {
                    query.createdBy = { $in: matchingUsers.map(user => user._id) };
                } else {
                    query.createdBy = { $in: [] };
                }
            }
        }
    }

    return query;
};

module.exports = buildEventGroupFilter; 
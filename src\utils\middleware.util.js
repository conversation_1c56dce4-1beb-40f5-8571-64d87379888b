const { createResponse } = require('./response.util');

function requireRole(role) {
    return (resolver) => {
        return async (parent, args, context, info) => {
            if (!context.roles || !context.roles.includes(role)) {
                return createResponse('UserErrorResponse', 'FAILURE', 'Unauthorized access', {
                    errors: [{ 
                        field: 'authorization', 
                        message: `Only ${role} users can access this endpoint` 
                    }]
                });
            }
            return resolver(parent, args, context, info);
        };
    };
}

module.exports = {
    requireRole
}; 
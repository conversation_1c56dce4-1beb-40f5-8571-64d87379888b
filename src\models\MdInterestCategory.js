const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mdInterestCategorySchema = new Schema({
    title: {type: String, required: true},
    interests: [{type: Schema.Types.ObjectId, ref: 'MdInterest'}],
}, {timestamps: true, collection: 'md_interest_categories'});

const MdInterestCategory = model('MdInterestCategory', mdInterestCategorySchema);

module.exports = MdInterestCategory;

const mongoose = require('mongoose');
const buildMdChecklistQuery = require('../../../../../src/graphql/resolvers/filters/mdChecklist.filter');
const buildMdPartyTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdPartyType.filter');
const MdPartyType = require('../../../../../src/models/MdPartyType');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/mdPartyType.filter');
jest.mock('../../../../../src/models/MdPartyType');

describe('buildMdChecklistQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', async () => {
        const result = await buildMdChecklistQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid ID filter', async () => {
        const validId = 'valid_id';
        isValidObjectId.mockReturnValue(true);

        const result = await buildMdChecklistQuery({ id: validId });

        expect(result).toEqual({ _id: validId });
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });

    it('should throw error for invalid ID', async () => {
        const invalidId = 'invalid_id';
        isValidObjectId.mockReturnValue(false);

        await expect(buildMdChecklistQuery({ id: invalidId }))
            .rejects
            .toThrow('Invalid id provided');
    });

    it('should build query with title filter', async () => {
        const title = 'test title';
        const result = await buildMdChecklistQuery({ title });

        expect(result).toEqual({
            title: { $regex: title, $options: 'i' }
        });
    });

    it('should build query with description filter', async () => {
        const description = 'test description';
        const result = await buildMdChecklistQuery({ description });

        expect(result).toEqual({
            description: { $regex: description, $options: 'i' }
        });
    });

    it('should build query with valid partyType ID string', async () => {
        const validPartyTypeId = 'valid_party_type_id';
        isValidObjectId.mockReturnValue(true);

        const result = await buildMdChecklistQuery({ partyType: validPartyTypeId });

        expect(result).toEqual({ partyType: validPartyTypeId });
        expect(isValidObjectId).toHaveBeenCalledWith(validPartyTypeId);
    });

    it('should throw error for invalid partyType ID string', async () => {
        const invalidPartyTypeId = 'invalid_party_type_id';
        isValidObjectId.mockReturnValue(false);

        await expect(buildMdChecklistQuery({ partyType: invalidPartyTypeId }))
            .rejects
            .toThrow('Invalid partyType ID provided');
    });

    it('should build query with partyType object filter when matching party types exist', async () => {
        const partyTypeFilter = { name: 'test party type' };
        const partyTypeQuery = { name: { $regex: 'test party type', $options: 'i' } };
        const matchingPartyTypes = [
            { _id: 'party_type_1' },
            { _id: 'party_type_2' }
        ];

        buildMdPartyTypeQuery.mockResolvedValue(partyTypeQuery);
        MdPartyType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue(matchingPartyTypes)
        });

        const result = await buildMdChecklistQuery({ partyType: partyTypeFilter });

        expect(result).toEqual({
            partyType: { $in: matchingPartyTypes.map(pt => pt._id) }
        });
        expect(buildMdPartyTypeQuery).toHaveBeenCalledWith(partyTypeFilter);
        expect(MdPartyType.find).toHaveBeenCalledWith(partyTypeQuery);
    });

    it('should build query with empty party types when no matches found', async () => {
        const partyTypeFilter = { name: 'non-existent party type' };
        const partyTypeQuery = { name: { $regex: 'non-existent party type', $options: 'i' } };

        buildMdPartyTypeQuery.mockResolvedValue(partyTypeQuery);
        MdPartyType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([])
        });

        const result = await buildMdChecklistQuery({ partyType: partyTypeFilter });

        expect(result).toEqual({
            partyType: { $in: [] }
        });
    });

    it('should build query with multiple filters', async () => {
        const filter = {
            title: 'test title',
            description: 'test description',
            partyType: 'valid_party_type_id'
        };

        isValidObjectId.mockReturnValue(true);

        const result = await buildMdChecklistQuery(filter);

        expect(result).toEqual({
            title: { $regex: filter.title, $options: 'i' },
            description: { $regex: filter.description, $options: 'i' },
            partyType: filter.partyType
        });
    });
}); 
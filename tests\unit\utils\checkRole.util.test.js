const checkRole = require('../../../src/utils/checkRole.util');

describe('checkRole', () => {
    it('should throw an error if user is not authenticated', () => {
        expect(() => checkRole(null, ['admin'])).toThrow('Not authenticated');
    });

    it('should throw an error if user does not have required role', () => {
        const user = { publicMetadata: { roles: ['user'] } };
        expect(() => checkRole(user, ['admin'])).toThrow('Forbidden');
    });

    it('should not throw an error if user has required role', () => {
        const user = { publicMetadata: { roles: ['admin'] } };
        expect(() => checkRole(user, ['admin'])).not.toThrow();
    });

    it('should throw an error if roles array is empty', () => {
        const user = { publicMetadata: { roles: ['admin'] } };
        expect(() => checkRole(user, [])).toThrow('Forbidden');
    });

    it('should not throw an error if user has one of multiple roles', () => {
        const user = { publicMetadata: { roles: ['admin', 'user'] } };
        expect(() => checkRole(user, ['admin', 'editor'])).not.toThrow();
    });

    it('should throw an error if user has roles that do not match required roles', () => {
        const user = { publicMetadata: { roles: ['user', 'guest'] } };
        expect(() => checkRole(user, ['admin', 'editor'])).toThrow('Forbidden');
    });

    it('should throw an error if user roles are undefined', () => {
        const user = { publicMetadata: { roles: undefined } };
        expect(() => checkRole(user, ['admin'])).toThrow('Forbidden');
    });

    it('should throw an error if user roles are null', () => {
        const user = { publicMetadata: { roles: null } };
        expect(() => checkRole(user, ['admin'])).toThrow('Forbidden');
    });
});
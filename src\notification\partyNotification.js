const notificationProducer = require("../utils/notificationProducer");
const { formatTime, formatDate } = require('../utils/date.util');
const VenueAddress = require('../models/VenueAddress');
const MdServiceLocation = require('../models/MdServiceLocation');
const Host = require('../models/Host');
const Guest = require('../models/Guest');

async function notifyPartyUpdate(oldParty, updatedParty, contextUserId) {
    try {
        if (oldParty.name !== updatedParty.name) {
            await notificationProducer.sendNotification('Party Name Changed', {
                partyId: updatedParty._id.toString(),
                OldEventName: oldParty.name,
                NewEventName: updatedParty.name,
                oldEventName: oldParty.name,
                newEventName: updatedParty.name,
                senderId: contextUserId.toString()
            });
        }

        if (oldParty.time?.toString() !== updatedParty.time?.toString()) {
            await notificationProducer.sendNotification('Party Time Changed', {
                partyId: updatedParty._id.toString(),
                EventName: updatedParty.name,
                NewTime: formatTime(updatedParty.time),
                NewDate: formatDate(updatedParty.time),
                senderId: contextUserId.toString()
            });
        }

        if (oldParty.serviceLocation?.toString() !== updatedParty.serviceLocation?.toString() ||
            oldParty.venueAddress?.toString() !== updatedParty.venueAddress?.toString()) {
            await notificationProducer.sendNotification('Party Location Changed', {
                partyId: updatedParty._id.toString(),
                EventName: updatedParty.name,
                NewLocation: updatedParty.venueAddress ? 
                    (await VenueAddress.findById(updatedParty.venueAddress))?.address || 'TBD' :
                    updatedParty.serviceLocation ?
                        (await MdServiceLocation.findById(updatedParty.serviceLocation))?.name || 'TBD' :
                        'TBD',
                senderId: contextUserId.toString()
            });
        }

    } catch (error) {
        console.error('Error sending party update notifications:', error);
        // Don't throw the error - we don't want to fail the party update if notifications fail
    }
}

async function notifyPartyCancellation(party, contextUserId) {
    try {
        await notificationProducer.sendNotification('Party Cancelled', {
            partyId: party._id.toString(),
            EventName: party.name,
            EventDate: formatDate(party.time),
            senderId: contextUserId.toString()
        });
    } catch (error) {
        console.error('Error sending party cancellation notification:', error);
    }
}

async function notifyRSVPUpdate(guest, party, rsvpStatus, contextUserId) {
    try {
        // Populate the user information to get the guest's name
        const populatedGuest = await Guest.findById(guest._id).populate('user');
        const guestName = populatedGuest.user.firstName + (populatedGuest.user.lastName ? ' ' + populatedGuest.user.lastName : '');

        await notificationProducer.sendNotification('RSVP From Guest', {
            partyId: party._id.toString(),
            EventName: party.name,
            GuestName: guestName,
            RSVPStatus: rsvpStatus,
            senderId: contextUserId.toString()
        });
    } catch (error) {
        console.error('Error sending RSVP update notification:', error);
    }
}

async function notifyCoHostAdded(party, newCoHosts, contextUserId) {
    try {
        const cohosts = await Host.find({ _id: { $in: newCoHosts } }).populate('userId');
        
        for (const cohost of cohosts) {
            if (cohost?.userId) {
                const firstName = cohost.userId.firstName || '';
                const lastName = cohost.userId.lastName || '';
                const fullName = firstName + (lastName ? ' ' + lastName : '');
                
                await notificationProducer.sendNotification('Added as Cohost', {
                    partyId: party._id.toString(),
                    EventName: party.name,
                    UserName: fullName,
                    senderId: contextUserId.toString()
                });
            }
        }
    } catch (error) {
        console.error('Error sending cohost added notification:', error);
    }
}

async function notifyInvitationSent(guest, party, contextUserId) {
    try {
        const location = party.venueAddress ? 
            (await VenueAddress.findById(party.venueAddress))?.address || 'TBD' :
            party.serviceLocation ?
                (await MdServiceLocation.findById(party.serviceLocation))?.name || 'TBD' :
                'TBD';

        const singleRecipient = [{
            userId: guest.user._id,
            email: guest.user.email,
            phone: guest.user.phone,
            role: 'Guest'
        }];

        await notificationProducer.sendNotificationToSpecificRecipients('Invited to Event', {
            partyId: party._id.toString(),
            EventName: party.name,
            EventDate: formatDate(party.time),
            EventLocation: location || 'TBD',
            senderId: contextUserId.toString()
        }, singleRecipient);
    } catch (error) {
        console.error('Error sending invitation notification:', error);
    }
}

async function notifyPhotoAddedToAlbum(mediaFolder, party, contextUserId) {
    try {
        await notificationProducer.sendNotification('Photo Added to Event Album', {
            partyId: party._id.toString(),
            EventName: party.name,
            senderId: contextUserId.toString()
        });
    } catch (error) {
        console.error('Error sending photo added notification:', error);
    }
}

async function notifyPhotoRemovedFromAlbum(mediaFolder, party, contextUserId) {
    try {
        await notificationProducer.sendNotification('Photo Deleted from Media Folder', {
            partyId: party._id.toString(),
            EventName: party.name,
            senderId: contextUserId.toString()
        });
    } catch (error) {
        console.error('Error sending photo removed notification:', error);
        // Don't throw the error - we don't want to fail the photo removal if notification fails
    }
}

module.exports = {
    notifyPartyUpdate,
    notifyPartyCancellation,
    notifyRSVPUpdate,
    notifyCoHostAdded,
    notifyInvitationSent,
    notifyPhotoAddedToAlbum,
    notifyPhotoRemovedFromAlbum
};
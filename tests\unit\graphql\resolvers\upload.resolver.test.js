jest.mock('@azure/storage-blob', () => ({
    BlobServiceClient: {
        fromConnectionString: jest.fn().mockReturnValue({
            getContainerClient: jest.fn().mockReturnValue({})
        })
    }
}));

jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/azureStorage.util');
jest.mock('graphql-upload', () => ({
    GraphQLUpload: jest.fn()
}));

process.env.AZURE_STORAGE_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net';

const uploadResolver = require('../../../../src/graphql/resolvers/upload.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { uploadFilesToBlob, deleteFileFromBlob, getContainerClient } = require('../../../../src/utils/azureStorage.util');
const { GraphQLUpload } = require('graphql-upload');

const mockResolvers = {
    Upload: GraphQLUpload,
    Mutation: {
        uploadFiles: jest.fn().mockImplementation(async (_, args) => {
            return uploadResolver.Mutation.uploadFiles(_, args);
        }),
        deleteFile: jest.fn().mockImplementation(async (_, args) => {
            return uploadResolver.Mutation.deleteFile(_, args);
        })
    }
};

describe('Upload Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should expose GraphQLUpload scalar', () => {
        expect(mockResolvers.Upload).toBe(GraphQLUpload);
    });

    describe('Mutation', () => {
        describe('uploadFiles', () => {
            it('should successfully upload files', async () => {
                const mockFiles = ['file1', 'file2'];
                const containerName = 'testContainer';
                const successResult = {
                    successCount: 2,
                    failureCount: 0,
                    failures: [],
                    successful: [
                        { url: 'url1', key: 'key1' },
                        { url: 'url2', key: 'key2' }
                    ],
                    totalProcessed: 2
                };

                uploadFilesToBlob.mockResolvedValue(successResult);
                createResponse.mockReturnValue('success response');

                const result = await mockResolvers.Mutation.uploadFiles(null, { 
                    files: mockFiles, 
                    containerName 
                });

                expect(uploadFilesToBlob).toHaveBeenCalledWith(mockFiles, containerName);
                expect(createResponse).toHaveBeenCalledWith(
                    'FileUploadResponse',
                    'SUCCESS',
                    'All files uploaded successfully',
                    {
                        result: { bulkResult: successResult },
                        errors: []
                    }
                );
                expect(result).toBe('success response');
            });

            it('should handle partial upload success', async () => {
                const mockFiles = ['file1', 'file2', 'file3'];
                const containerName = 'testContainer';
                const partialResult = {
                    successCount: 2,
                    failureCount: 1,
                    failures: [{
                        filename: 'file3',
                        message: 'Upload failed'
                    }],
                    successful: [
                        { url: 'url1', key: 'key1' },
                        { url: 'url2', key: 'key2' }
                    ],
                    totalProcessed: 3
                };

                uploadFilesToBlob.mockResolvedValue(partialResult);
                createResponse.mockReturnValue('partial success response');

                const result = await mockResolvers.Mutation.uploadFiles(null, { 
                    files: mockFiles, 
                    containerName 
                });

                expect(uploadFilesToBlob).toHaveBeenCalledWith(mockFiles, containerName);
                expect(createResponse).toHaveBeenCalledWith(
                    'FileUploadResponse',
                    'SUCCESS',
                    '2 files uploaded, 1 failed',
                    {
                        result: { bulkResult: partialResult },
                        errors: [{
                            field: 'uploadFiles',
                            message: 'Error uploading file3: Upload failed'
                        }]
                    }
                );
                expect(result).toBe('partial success response');
            });

            it('should handle complete upload failure', async () => {
                const mockFiles = ['file1'];
                const containerName = 'testContainer';
                const error = new Error('Upload failed');

                uploadFilesToBlob.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await mockResolvers.Mutation.uploadFiles(null, { 
                    files: mockFiles, 
                    containerName 
                });

                expect(uploadFilesToBlob).toHaveBeenCalledWith(mockFiles, containerName);
                expect(createResponse).toHaveBeenCalledWith(
                    'FileUploadResponse',
                    'FAILURE',
                    'Error uploading files',
                    {
                        errors: [{ field: 'uploadFiles', message: error.message }]
                    }
                );
                expect(result).toBe('error response');
            });
        });

        describe('deleteFile', () => {
            it('should successfully delete a file', async () => {
                const key = 'testFile.jpg';
                const containerName = 'documents';
                const mockUrl = 'https://storage.com/documents/testFile.jpg';
                
                const mockBlockBlobClient = {
                    url: mockUrl
                };
                
                const mockContainerClient = {
                    getBlockBlobClient: jest.fn().mockReturnValue(mockBlockBlobClient)
                };

                getContainerClient.mockReturnValue(mockContainerClient);
                deleteFileFromBlob.mockResolvedValue(true);
                createResponse.mockReturnValue('success response');

                const result = await mockResolvers.Mutation.deleteFile(null, { 
                    key, 
                    containerName 
                });

                expect(getContainerClient).toHaveBeenCalledWith(containerName);
                expect(mockContainerClient.getBlockBlobClient).toHaveBeenCalledWith(key);
                expect(deleteFileFromBlob).toHaveBeenCalledWith(key, containerName);
                expect(createResponse).toHaveBeenCalledWith(
                    'FileDeleteResponse',
                    'SUCCESS',
                    'File deleted successfully',
                    {
                        result: { file: { key, url: mockUrl } }
                    }
                );
                expect(result).toBe('success response');
            });

            it('should handle file deletion failure', async () => {
                const key = 'testFile.jpg';
                const containerName = 'documents';
                const error = new Error('Delete failed');
                
                const mockBlockBlobClient = {
                    url: 'https://storage.com/documents/testFile.jpg'
                };
                
                const mockContainerClient = {
                    getBlockBlobClient: jest.fn().mockReturnValue(mockBlockBlobClient)
                };

                getContainerClient.mockReturnValue(mockContainerClient);
                deleteFileFromBlob.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await mockResolvers.Mutation.deleteFile(null, { 
                    key, 
                    containerName 
                });

                expect(getContainerClient).toHaveBeenCalledWith(containerName);
                expect(mockContainerClient.getBlockBlobClient).toHaveBeenCalledWith(key);
                expect(deleteFileFromBlob).toHaveBeenCalledWith(key, containerName);
                expect(createResponse).toHaveBeenCalledWith(
                    'FileErrorResponse',
                    'FAILURE',
                    'Error deleting file',
                    {
                        errors: [{ field: 'deleteFile', message: error.message }]
                    }
                );
                expect(result).toBe('error response');
            });

            it('should use default container name if not provided', async () => {
                const key = 'testFile.jpg';
                const mockUrl = 'https://storage.com/documents/testFile.jpg';
                
                const mockBlockBlobClient = {
                    url: mockUrl
                };
                
                const mockContainerClient = {
                    getBlockBlobClient: jest.fn().mockReturnValue(mockBlockBlobClient)
                };

                getContainerClient.mockReturnValue(mockContainerClient);
                deleteFileFromBlob.mockResolvedValue(true);
                createResponse.mockReturnValue('success response');

                const result = await mockResolvers.Mutation.deleteFile(null, { key });

                expect(getContainerClient).toHaveBeenCalledWith('documents');
                expect(mockContainerClient.getBlockBlobClient).toHaveBeenCalledWith(key);
                expect(deleteFileFromBlob).toHaveBeenCalledWith(key, 'documents');
                expect(result).toBe('success response');
            });
        });
    });
}); 
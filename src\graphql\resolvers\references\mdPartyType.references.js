const MdVendorType = require("../../../models/MdVendorType");
const MdEventType = require("../../../models/MdEventType");
const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];

    const mdEventTypeReferences = await MdEventType.find({ partyTypes: id });
    if (hasReferences(mdEventTypeReferences)) {
        references.push('MdEventType');
    }

    return references;
}

module.exports = findReferences;
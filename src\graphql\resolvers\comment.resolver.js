const Comment = require('../../models/Comment');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildCommentQuery = require('./filters/comment.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const Reaction = require('../../models/Reaction');
const { validateReferences } = require('../../utils/validation.util');
const { hasTaskCollaboratorLevelAccess } = require('../../utils/auth/accessLevels.util');
const Task = require('../../models/Task');

const baseCommentIdSchema = {
    mentions: { type: 'array', required: false, model: User },
    reactions: { type: 'array', required: false, model: Reaction }
};

const commentIdSchema = { ...baseCommentIdSchema };

const createCommentIdSchema = {
    ...baseCommentIdSchema,
    mentions: { ...baseCommentIdSchema.mentions },
    reactions: { ...baseCommentIdSchema.reactions }
};

const commentResolvers = {
    Comment: {
        createdBy: async (parent) => {
            try {
                return await User.findById(parent.createdBy);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        },
        mentions: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.mentions } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting mentioned users');
            }
        },
        reactions: async (parent) => {
            try {
                return await Reaction.find({ _id: { $in: parent.reactions } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting reactions');
            }
        }
    },

    Query: {
        getCommentById: async (_, { id }) => {
            try {
                let comment = await getByIdCache(id);
                if (!comment) {
                    comment = await Comment.findById(id)
                        .populate('createdBy')
                        .populate('mentions')
                        .populate('reactions');

                    if (!comment) {
                        return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                            errors: [{ field: 'id', message: 'Comment not found' }]
                        });
                    }
                    await setCache(id, comment);
                }
                return createResponse('CommentResponse', 'SUCCESS', 'Comment retrieved successfully', {
                    result: { comment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error retrieving comment', {
                    errors: [{ field: 'getCommentById', message: error.message }]
                });
            }
        },

        getComments: async (_, { filter, pagination }) => {
            try {
                const query = await buildCommentQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Comment, query, limit, skip);

                const comments = await Comment.find(query)
                    .populate('createdBy')
                    .populate('mentions')
                    .populate('reactions')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (comments.length === 0) {
                    return createResponse('CommentsResponse', 'FAILURE', 'No comments found', {
                        result: { comments },
                        pagination: paginationInfo
                    });
                }

                return createResponse('CommentsResponse', 'SUCCESS', 'Comments retrieved successfully', {
                    result: { comments },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error retrieving comments', {
                    errors: [{ field: 'getComments', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createComment: async (_, { input }, context) => {
            try {
                const validationError = await validateReferences(input, createCommentIdSchema, 'Comment');
                if (validationError) {
                    return createResponse('CommentErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const comment = new Comment({ ...input, createdBy: context.user._id });
                await comment.save();

                const populatedComment = await Comment.findById(comment._id)
                    .populate('createdBy')
                    .populate('mentions')
                    .populate('reactions');

                return createResponse('CommentResponse', 'SUCCESS', 'Comment created successfully', {
                    result: { comment: populatedComment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error creating comment', {
                    errors: [{ field: 'createComment', message: error.message }]
                });
            }
        },

        updateComment: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, commentIdSchema, 'Comment');
                if (validationError) {
                    return createResponse('CommentErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const comment = await Comment.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                )
                .populate('createdBy')
                .populate('mentions')
                .populate('reactions');

                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'id', message: 'Comment not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('CommentResponse', 'SUCCESS', 'Comment updated successfully', {
                    result: { comment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error updating comment', {
                    errors: [{ field: 'updateComment', message: error.message }]
                });
            }
        },

        deleteComment: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Comment');
                if (references.length > 0) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment cannot be deleted', {
                        errors: [{ field: 'id', message: `Comment cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const comment = await Comment.findById(id);
                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'id', message: 'Comment not found' }]
                    });
                }

                const { success, error } = await cascadeDelete('Comment', id);
                if (!success) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Error in cascade deletion', {
                        errors: [{ field: 'deleteComment', message: error.message }]
                    });
                }

                await Comment.findByIdAndDelete(id);
                await clearCacheById(id);

                return createResponse('CommentResponse', 'SUCCESS', 'Comment deleted successfully', {
                    result: { comment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error deleting comment', {
                    errors: [{ field: 'deleteComment', message: error.message }]
                });
            }
        },

        createCommentReaction: async (_, { commentId, reactionId }, context) => {
            try {
                const task = await Task.findOne({ comments: commentId });
                if (!task) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Task not found for comment', {
                        errors: [{ field: 'commentId', message: 'No associated task found for this comment' }]
                    });
                }
                const taskId = task._id;
                if (!await hasTaskCollaboratorLevelAccess(context.user._id, taskId)) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this comment' }]
                    });
                }

                const comment = await Comment.findById(commentId);
                
                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'commentId', message: 'Comment not found' }]
                    });
                }

                const reaction = await Reaction.findById(reactionId);
                if (!reaction) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Reaction not found', {
                        errors: [{ field: 'reactionId', message: 'Reaction not found' }]
                    });
                }

                const existingUserReaction = await Reaction.findOne({
                    _id: { $in: comment.reactions },
                    user: reaction.user
                });

                if (existingUserReaction) {
                    comment.reactions = comment.reactions.filter(id => 
                        id.toString() !== existingUserReaction._id.toString()
                    );
                    await Reaction.findByIdAndDelete(existingUserReaction._id);
                }

                comment.reactions = [...(comment.reactions || []), reactionId];
                await comment.save();

                await clearCacheById(commentId);

                const populatedComment = await Comment.findById(commentId)
                    .populate('createdBy')
                    .populate('mentions')
                    .populate('reactions');

                return createResponse('CommentResponse', 'SUCCESS', 'Reaction updated successfully', {
                    result: { comment: populatedComment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error adding reaction', {
                    errors: [{ field: 'createCommentReaction', message: error.message }]
                });
            }
        },

        deleteCommentReaction: async (_, { commentId, reactionId }) => {
            try {
                const comment = await Comment.findById(commentId);
                
                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'commentId', message: 'Comment not found' }]
                    });
                }

                if (!comment.reactions || !comment.reactions.includes(reactionId)) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Reaction not found', {
                        errors: [{ field: 'reactionId', message: 'Reaction not found on this comment' }]
                    });
                }

                const deletedReaction = await Reaction.findByIdAndDelete(reactionId);
                if (!deletedReaction) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Error deleting reaction', {
                        errors: [{ field: 'reactionId', message: 'Reaction could not be deleted' }]
                    });
                }

                comment.reactions = comment.reactions.filter(id => id.toString() !== reactionId);
                await comment.save();

                await Promise.all([
                    clearCacheById(commentId),
                    clearCacheById(reactionId)
                ]);

                const populatedComment = await Comment.findById(commentId)
                    .populate('createdBy')
                    .populate('mentions')
                    .populate('reactions');

                return createResponse('CommentResponse', 'SUCCESS', 'Reaction removed successfully', {
                    result: { comment: populatedComment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error removing reaction', {
                    errors: [{ field: 'deleteCommentReaction', message: error.message }]
                });
            }
        },

        deleteUserCommentReaction: async (_, { commentId }, context) => {
            try {
                const comment = await Comment.findById(commentId);
                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'commentId', message: 'Comment not found' }]
                    });
                }

                const existingReactions = await Reaction.find({ _id: { $in: comment.reactions } });
                
                const userReaction = existingReactions.find(reaction => 
                    reaction.user.toString() === context.user._id.toString()
                );

                if (!userReaction) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'User reaction not found', {
                        errors: [{ field: 'userId', message: 'No reaction found for this user' }]
                    });
                }

                const deletedReaction = await Reaction.findByIdAndDelete(userReaction._id);
                if (!deletedReaction) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Error deleting reaction', {
                        errors: [{ field: 'userId', message: 'Reaction could not be deleted' }]
                    });
                }

                comment.reactions = comment.reactions.filter(id => 
                    id.toString() !== userReaction._id.toString()
                );
                await comment.save();

                await Promise.all([
                    clearCacheById(commentId),
                    clearCacheById(userReaction._id)
                ]);

                const populatedComment = await Comment.findById(commentId)
                    .populate('createdBy')
                    .populate('mentions')
                    .populate('reactions');

                return createResponse('CommentResponse', 'SUCCESS', 'User reaction removed successfully', {
                    result: { comment: populatedComment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error removing user reaction', {
                    errors: [{ field: 'deleteUserCommentReaction', message: error.message }]
                });
            }
        }
    }
};

module.exports = commentResolvers; 
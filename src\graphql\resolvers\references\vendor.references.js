const PartyService = require("../../../models/PartyService");

const hasReferences = (references) => Array.isArray(references) && references.length > 0;
const findReferences = async (id) => {
    const references = [];

    const partyServiceReferences = await PartyService.find({ vendor: id });
    if (hasReferences(partyServiceReferences)) {
        references.push('PartyService');
    }

    return references;
}

module.exports = findReferences;
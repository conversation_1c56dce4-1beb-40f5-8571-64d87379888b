const { isValidObjectId } = require("../../../utils/validation.util");
const buildPartyQuery = require("./party.filter");
const buildTaskQuery = require("./task.filter");
const Party = require("../../../models/Party");
const Task = require("../../../models/Task");

const buildEventTaskQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.partyId) {
            if (isValidObjectId(filter.partyId)) {
                query.party = filter.partyId;
            } else {
                throw new Error('Invalid party ID provided');
            }
        }

        if (filter.party && !filter.partyId) {
            if (typeof filter.party === 'string') {
                if (isValidObjectId(filter.party)) {
                    query.party = filter.party;
                } else {
                    throw new Error('Invalid party ID provided');
                }
            } else if (typeof filter.party === 'object') {
                const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filter.party);
                if (Object.keys(partyQuery).length > 0) {
                    const matchingParties = await Party.find(partyQuery);
                    const filteredParties = await applyPostQueryFilters(matchingParties);
                    
                    if (filteredParties.length > 0) {
                        query.party = { $in: filteredParties.map(party => party._id) };
                    } else {
                        query.party = { $in: [] };
                    }
                }
            }
        }

        if (filter.task) {
            const taskQuery = await buildTaskQuery(filter.task);
            if (Object.keys(taskQuery).length > 0) {
                const { party, ...taskFilters } = taskQuery;
                Object.assign(query, taskFilters);
            }
        }
    }

    return query;
};

module.exports = buildEventTaskQuery;
const MdNotificationConfig = require('../models/MdNotificationConfig');
const { setCachePermanent } = require('./cache.util');

const ALL_CONFIGS_CACHE_KEY = 'all_notification_configs';

const initializeCache = async () => {
    try {
        console.log('Initializing cache...');
        
        // Initialize notification configs
        const notificationConfigs = await MdNotificationConfig.find();
        if (notificationConfigs && notificationConfigs.length > 0) {
            await setCachePermanent(ALL_CONFIGS_CACHE_KEY, notificationConfigs);
            console.log(`Cached ${notificationConfigs.length} notification configs`);
        } else {
            console.log('No notification configs found to cache');
        }
        
        console.log('Cache initialization completed successfully');
    } catch (error) {
        console.error('Error initializing cache:', error);
        throw error; // Rethrow to handle it in the app startup
    }
};

module.exports = {
    initializeCache
}; 
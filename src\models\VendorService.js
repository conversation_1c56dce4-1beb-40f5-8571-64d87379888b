const { model, Schema } = require('mongoose');

const vendorServiceSchema = new Schema({
    vendorType: { type: Schema.Types.ObjectId, ref: 'MdVendorType', required: true },
    vendor: { type: Schema.Types.ObjectId, ref: 'Vendor', required: true },
    title: { type: String, required: true },
    description: { type: String },
    features: [{ type: Schema.Types.ObjectId, ref: 'MdFeature' }],
    priceRange: { type: String, required: true },
    specialities: { type: String },
    media: [{ type: Schema.Types.ObjectId, ref: 'Media' }]
}, { timestamps: true, collection: 'vendor_services' });

const VendorService = model('VendorService', vendorServiceSchema);

module.exports = VendorService;
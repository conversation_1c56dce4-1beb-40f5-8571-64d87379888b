const { gql } = require('graphql-tag');
const userTypeDef = require('../../../../src/graphql/typedefs/user.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('userTypeDef', () => {
  it('should contain the User type definition', () => {
    const typeDefs = gql`
        ${sharedTypeDef}

        type User {
            id: ID
            roles: [MdRole!]!
            firstName: String!
            lastName: String
            email: String
            phone: String!
            isRegistered: Boolean!
            emailVerified: Boolean!
            externalId: String
            isActive: Boolean!
        }

        input UserFilterInput {
            role: MdRoleFilterInput
            firstName: String
            lastName: String
            email: String
            phone: String
            isRegistered: Boolean
            emailVerified: Boolean
        }

        type UserWrapper {
            user: User!
        }

        type UsersWrapper {
            users: [User]!
        }

        type UsersResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: UsersWrapper!
            pagination: PaginationInfo!
        }

        type UserResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: UserWrapper!
        }

        type UserErrorResponse implements Response {
            status: ResponseStatus!
            message: String!
            errors: [Error!]!
        }

        union UserResult = UserResponse | UserErrorResponse
        union UsersResult = UsersResponse | UserErrorResponse

        type Query {
            getUsers(filters: UserFilterInput, pagination: PaginationInput): UsersResult!
            getUserById(id: ID!): UserResult!
        }

        input RegisterInput {
            roles: [String!]!
            firstName: String!
            lastName: String!
            email: String
            phone: String!
            isRegistered: Boolean!
            externalId: String
        }

        input UserUpdateInput {
            roles: [String!]
            firstName: String
            lastName: String
            email: String
            phone: String
            externalId: String
            isActive: Boolean
        }

        type Mutation {
            register(input: RegisterInput!): UserResult!
            verifyUser(email: String!): UserResult!
            updateUser(id: ID!, input: UserUpdateInput!): UserResult!
        }
    `;

    const normalize = str => str.replace(/\s+/g, '');
    expect(normalize(userTypeDef.loc.source.body)).toEqual(normalize(typeDefs.loc.source.body));
    });
});
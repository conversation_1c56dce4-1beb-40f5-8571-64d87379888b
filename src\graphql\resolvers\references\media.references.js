const VendorService = require('../../../models/VendorService');

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];

    const vendorServiceReferences = await VendorService.find({ 'media': id });
    if (hasReferences(vendorServiceReferences)) {
        references.push('VendorService');
    }

    return references;
}

module.exports = findReferences;
const { model, Schema } = require('mongoose');

const mdPromotionSchema = new Schema({
    partner: { type: Schema.Types.ObjectId, ref: 'MdPartner', required: true },
    name: { type: String, required: true },
    thumbnail: { type: String },
    mediaUrl: { type: String, required: true },
    description: { type: String, required: true },
    ctaLink: { type: String, required: true },
    tags: [{ type: Schema.Types.ObjectId, ref: 'Tag' }],
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    active: { type: Boolean, required: true, default: true }
}, { timestamps: true, collection: 'md_promotions' });

const MdPromotion = model('MdPromotion', mdPromotionSchema);

module.exports = MdPromotion;
const { createResponse } = require('../../utils/response.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const Host = require('../../models/Host');
const { User } = require('../../models/User');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const buildHostQuery = require('./filters/host.filter');

const hostResolvers = {
    Host: {
        userId: async (parent) => {
            try{
                return await User.findById(parent.userId);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        }
    },
    
    Query: {
        getHostById: async (_, { id }) => {
            try {
                const cachedHost = await getByIdCache(id);
                if (cachedHost) {
                return createResponse('HostResponse', 'SUCCESS', 'Host retrieved successfully from cache', { result: { host: cachedHost } });
                }

                const host = await Host.findById(id);
                if (!host) {
                return createResponse('HostErrorResponse', 'FAILURE', 'Host not found', { errors: [{ field: 'id', message: 'Host not found' }] });
                }

                await setCache(id, host);
                return createResponse('HostResponse', 'SUCCESS', 'Host retrieved successfully', { result: { host } });
            } catch (error) {
                console.error(error);
                return createResponse('HostErrorResponse', 'FAILURE', 'Error retrieving host', { errors: [{ field: 'id', message: error.message }] });
            }
        },
        getHosts: async (_, { pagination, filter }) => {
            try {
                const query = await buildHostQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Host, query, limit, skip);

                const hosts = await Host.find(query).limit(limit).skip(skip);
                console.log(hosts);

                if (hosts.length === 0) {
                    return createResponse('HostsResponse', 'FAILURE', 'No hosts found', { result: { hosts }, pagination: paginationInfo });
                }
                return createResponse('HostsResponse', 'SUCCESS', 'Hosts fetched successfully', { result: { hosts }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('HostErrorResponse', 'FAILURE', 'Error getting hosts', { errors: [{ field: 'getHosts', message: error.message }] });
            }
        },
    },
    Mutation: {
        createHost: async (_, { userId }) => {
            try {
                const host = new Host({ userId: userId });
                await host.save();
                await setCache(host.id, host);
                return createResponse('HostResponse', 'SUCCESS', 'Host created successfully', { result: { host } });
            } catch (error) {
                console.error(error);
                return createResponse('HostErrorResponse', 'FAILURE', 'Error creating host', { errors: [{ field: 'userId', message: error.message }] });
            }
        },
        deleteHost: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Host');
                if (references.length > 0) {
                    return createResponse('HostErrorResponse', 'FAILURE', 'Host cannot be deleted', {
                        errors: [{ field: 'id', message: `Host cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }
                const host = await Host.findByIdAndDelete(id);
                if (!host) {
                return createResponse('HostErrorResponse', 'FAILURE', 'Host not found', { errors: [{ field: 'id', message: 'Host not found' }] });
                }

                await clearCacheById(id);
                return createResponse('HostResponse', 'SUCCESS', 'Host deleted successfully', { result: { host } });
            } catch (error) {
                console.error(error);
                return createResponse('HostErrorResponse', 'FAILURE', 'Error deleting host', { errors: [{ field: 'id', message: error.message }] });
            }
        },
    },
};

module.exports = hostResolvers;
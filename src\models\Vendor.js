const { model, Schema } = require('mongoose');

const vendorRatingAggregateSchema = new Schema({
    averageRating: { type: Number, required: true },
    totalRating: { type: Number, required: true },
}, { _id: false });

const vendorSchema = new Schema({
    name: { type: String, required: true },
    primaryContact: { type: Schema.Types.ObjectId, ref: 'VendorUser', required: true },
    contacts: [{ type: Schema.Types.ObjectId, ref: 'VendorUser' }],
    businessAddress: { type: Schema.Types.ObjectId, ref: 'Address', required: true },
    serviceLocations: [{ type: Schema.Types.ObjectId, ref: 'MdServiceLocation' }],
    servicesProvided: [{ type: Schema.Types.ObjectId, ref: 'VendorService' }]
}, { timestamps: true, collection: 'vendors' });

module.exports = model('Vendor', vendorSchema);

// authMiddleware.js
const { sessions, users } = require('@clerk/clerk-sdk-node');

const authMiddleware = async (req, res, next) => {

  if (req.path === '/webhook(.*)') {
    return next();
  }

  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ message: 'Authorization header missing' });
  }

  const token = authHeader.split(' ')[1];

  console.log(token);

  try {
    const session = await sessions.verifySession(token);
    const user = await users.getUser(session.userId);

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};

const roleMiddleware = (requiredRoles) => {
  return (req, res, next) => {
    const userRoles = req.user.publicMetadata.roles || [];

    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    next();
  };
};

module.exports = { authMiddleware, roleMiddleware };
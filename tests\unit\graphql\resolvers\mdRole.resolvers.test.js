const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const MdRole = require('../../../../src/models/MdRole');
const mdRoleResolvers = require('../../../../src/graphql/resolvers/mdRole.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const buildMdRoleQuery = require('../../../../src/graphql/resolvers/filters/mdRole.filter');

jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/models/MdRole');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdRole.filter');
jest.mock('../../../../src/utils/paginationInfo.util');

describe('MdRole Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Query.getMdRoleById', () => {
        it('should return MdRole from cache if available', async () => {
            const id = '1';
            const mdRole = { id, name: 'Admin', description: 'Administrator role' };
            getByIdCache.mockResolvedValue(mdRole);
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdRole fetched successfully', result: { mdRole } });

            const result = await mdRoleResolvers.Query.getMdRoleById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleResponse', 'SUCCESS', 'MdRole fetched successfully', { result: { mdRole } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdRole fetched successfully', result: { mdRole } });
        });

        it('should return an error if MdRole is not found by ID', async () => {
            const id = 'nonexistent-id';
            getByIdCache.mockResolvedValue(null);
            MdRole.findById.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
        
            const result = await mdRoleResolvers.Query.getMdRoleById(null, { id });
        
            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(MdRole.findById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
        });

        it('should return MdRole from database if not in cache and then cache it', async () => {
            const id = '1';
            const mdRole = { id, name: 'Admin', description: 'Administrator role' };
            getByIdCache.mockResolvedValue(null);
            MdRole.findById.mockResolvedValue(mdRole);
            setCache.mockResolvedValue();
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdRole fetched successfully', result: { mdRole } });

            const result = await mdRoleResolvers.Query.getMdRoleById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(MdRole.findById).toHaveBeenCalledWith(id);
            expect(setCache).toHaveBeenCalledWith(id, mdRole);
            expect(createResponse).toHaveBeenCalledWith('MdRoleResponse', 'SUCCESS', 'MdRole fetched successfully', { result: { mdRole } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdRole fetched successfully', result: { mdRole } });
        });

        it('should return an error response if MdRole not found', async () => {
            const id = '1';
            getByIdCache.mockResolvedValue(null);
            MdRole.findById.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });

            const result = await mdRoleResolvers.Query.getMdRoleById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(MdRole.findById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
        });
    });

    describe('Query.getMdRoles', () => {
        it('should fetch MdRoles successfully with pagination', async () => {
            const filter = { name: 'Admin' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Admin', 'i') } };
            const mdRoles = [{ id: '1', name: 'Admin', description: 'Administrator role' }];
            const paginationInfo = { total: 1, limit: 10, skip: 0 };

            buildMdRoleQuery.mockReturnValue(query);
            MdRole.find.mockResolvedValue(mdRoles);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'MdRoles fetched successfully',
                result: { mdRoles },
                pagination: paginationInfo
            });

            const result = await mdRoleResolvers.Query.getMdRoles(null, { filter, pagination });

            expect(buildMdRoleQuery).toHaveBeenCalledWith(filter);
            expect(MdRole.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdRole, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'MdRoles fetched successfully',
                result: { mdRoles },
                pagination: paginationInfo
            });
        });

        it('should return an error if fetching MdRoles fails', async () => {
            const error = new Error('Error fetching mdRoles');
            MdRole.find.mockImplementation(() => { throw error; });
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting mdRoles', errors: [{ field: 'getMdRoles', message: error.message }] });

            const result = await mdRoleResolvers.Query.getMdRoles(null, {});

            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'Error getting mdRoles', { errors: [{ field: 'getMdRoles', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error getting mdRoles', errors: [{ field: 'getMdRoles', message: error.message }] });
        });

        it('should return no MdRoles found when no MdRoles match the query', async () => {
            const filter = { name: 'Admin' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Admin', 'i') } };
            const mdRoles = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };

            buildMdRoleQuery.mockReturnValue(query);
            MdRole.find.mockResolvedValue(mdRoles);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'No MdRoles found',
                result: { mdRoles },
                pagination: paginationInfo
            });

            const result = await mdRoleResolvers.Query.getMdRoles(null, { filter, pagination });

            expect(buildMdRoleQuery).toHaveBeenCalledWith(filter);
            expect(MdRole.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdRole, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'No MdRoles found',
                result: { mdRoles },
                pagination: paginationInfo
            });
        });

        it('should return a failure response when no MdRoles are found', async () => {
            const filter = { name: 'Admin' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Admin', 'i') } };
            const mdRoles = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };

            buildMdRoleQuery.mockReturnValue(query);
            MdRole.find.mockResolvedValue(mdRoles);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'No MdRoles found',
                result: { mdRoles },
                pagination: paginationInfo
            });

            const result = await mdRoleResolvers.Query.getMdRoles(null, { filter, pagination });

            expect(buildMdRoleQuery).toHaveBeenCalledWith(filter);
            expect(MdRole.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdRole, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'No MdRoles found',
                result: { mdRoles },
                pagination: paginationInfo
            });
        });

        it('should return MdRoles with pagination info', async () => {
            const filter = { name: 'Admin' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Admin', 'i') } };
            const mdRoles = [
                { id: '1', name: 'Admin', description: 'Administrator role' },
                { id: '2', name: 'User', description: 'User role' }
            ];
            const paginationInfo = { total: 2, limit: 10, skip: 0 };

            buildMdRoleQuery.mockReturnValue(query);
            MdRole.find.mockResolvedValue(mdRoles);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'MdRoles fetched successfully',
                result: { mdRoles },
                pagination: paginationInfo
            });

            const result = await mdRoleResolvers.Query.getMdRoles(null, { filter, pagination });

            expect(buildMdRoleQuery).toHaveBeenCalledWith(filter);
            expect(MdRole.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdRole, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'MdRoles fetched successfully',
                result: { mdRoles },
                pagination: paginationInfo
            });
        });
    });

    describe('Mutation.createMdRole', () => {
        const name = 'Test Role';
        const description = 'Test Role Description';

        it('should create a new MdRole successfully', async () => {
            const mockMdRole = { name, description, save: jest.fn().mockResolvedValue({}) };
            MdRole.mockImplementation(() => mockMdRole);
            createResponse.mockReturnValue('success response');

            const result = await mdRoleResolvers.Mutation.createMdRole(null, { name, description });

            expect(MdRole).toHaveBeenCalledWith({ name, description });
            expect(mockMdRole.save).toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith('MdRoleResponse', 'SUCCESS', 'MdRole created successfully', { result: { mdRole: mockMdRole } });
            expect(result).toBe('success response');
        });

        it('should handle errors during MdRole creation', async () => {
            const errorMessage = 'Error creating mdRole';
            const mockMdRole = { name, description, save: jest.fn().mockRejectedValue(new Error(errorMessage)) };
            MdRole.mockImplementation(() => mockMdRole);
            createResponse.mockReturnValue('error response');

            const result = await mdRoleResolvers.Mutation.createMdRole(null, { name, description });

            expect(MdRole).toHaveBeenCalledWith({ name, description });
            expect(mockMdRole.save).toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', errorMessage, { errors: [{ field: 'createMdRole', message: errorMessage }] });
            expect(result).toBe('error response');
        });

        it('should handle errors during MdRole creation', async () => {
            const errorMessage = 'Error creating mdRole';
            const mockMdRole = { name, description, save: jest.fn().mockRejectedValue(new Error(errorMessage)) };
            MdRole.mockImplementation(() => mockMdRole);
            createResponse.mockReturnValue('error response');

            const result = await mdRoleResolvers.Mutation.createMdRole(null, { name, description });

            expect(MdRole).toHaveBeenCalledWith({ name, description });
            expect(mockMdRole.save).toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', errorMessage, { errors: [{ field: 'createMdRole', message: errorMessage }] });
            expect(result).toBe('error response');
        });
    });

    describe('Mutation.updateMdRole', () => {
        it('should update MdRole successfully', async () => {
            const id = '1';
            const name = 'Updated Admin';
            const description = 'Updated Administrator role';
            const mdRole = { id, name, description };

            MdRole.findByIdAndUpdate.mockResolvedValue(mdRole);
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdRole updated successfully', result: { mdRole } });

            const result = await mdRoleResolvers.Mutation.updateMdRole(null, { id, name, description });

            expect(MdRole.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, description }, { new: true });
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleResponse', 'SUCCESS', 'MdRole updated successfully', { result: { mdRole } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdRole updated successfully', result: { mdRole } });
        });

        it('should return an error if MdRole is not found', async () => {
            const id = 'nonexistent-id';
            const name = 'Updated Admin';
            const description = 'Updated Administrator role';

            MdRole.findByIdAndUpdate.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'updateMdRole', message: 'MdRole not found' }] });

            const result = await mdRoleResolvers.Mutation.updateMdRole(null, { id, name, description });

            expect(MdRole.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, description }, { new: true });
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'updateMdRole', message: 'MdRole not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'updateMdRole', message: 'MdRole not found' }] });
        });

        it('should return an error if an exception occurs during the update', async () => {
            const id = '1';
            const name = 'Updated Admin';
            const description = 'Updated Administrator role';
            const error = new Error('Error updating mdRole');

            MdRole.findByIdAndUpdate.mockRejectedValue(error);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error updating mdRole', errors: [{ field: 'updateMdRole', message: error.message }] });

            const result = await mdRoleResolvers.Mutation.updateMdRole(null, { id, name, description });

            expect(MdRole.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, description }, { new: true });
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'Error updating mdRole', { errors: [{ field: 'updateMdRole', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error updating mdRole', errors: [{ field: 'updateMdRole', message: error.message }] });
        });
    });
    
    describe('Mutation.deleteMdRole', () => {
        it('should delete an existing MdRole and return success response', async () => {
            const id = '1';
            const mdRole = { id, name: 'Admin', description: 'Administrator role' };
            MdRole.findByIdAndDelete.mockResolvedValue(mdRole);
            clearCacheById.mockResolvedValue();
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdRole deleted successfully', result: { mdRole } });

            const result = await mdRoleResolvers.Mutation.deleteMdRole(null, { id });

            expect(MdRole.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleResponse', 'SUCCESS', 'MdRole deleted successfully', { result: { mdRole } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdRole deleted successfully', result: { mdRole } });
        });

        it('should return an error response if MdRole not found', async () => {
            const id = '1';
            MdRole.findByIdAndDelete.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'deleteMdRole', message: 'MdRole not found' }] });

            const result = await mdRoleResolvers.Mutation.deleteMdRole(null, { id });

            expect(MdRole.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'deleteMdRole', message: 'MdRole not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdRole not found', errors: [{ field: 'deleteMdRole', message: 'MdRole not found' }] });
        });

        it('should return an error response if deletion fails', async () => {
            const id = '1';
            const error = new Error('Error deleting MdRole');
            MdRole.findByIdAndDelete.mockImplementation(() => { throw error; });
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error deleting mdRole', errors: [{ field: 'deleteMdRole', message: error.message }] });

            const result = await mdRoleResolvers.Mutation.deleteMdRole(null, { id });

            expect(MdRole.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdRoleErrorResponse', 'FAILURE', 'Error deleting mdRole', { errors: [{ field: 'deleteMdRole', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error deleting mdRole', errors: [{ field: 'deleteMdRole', message: error.message }] });
        });
    });
});
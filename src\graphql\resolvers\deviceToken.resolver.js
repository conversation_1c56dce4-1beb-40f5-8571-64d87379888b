const DeviceToken = require('../../models/DeviceTokenModel');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');

// TODO: Triggering a build

const deviceTokenResolvers = {
  DeviceToken: {
    user: async (parent) => {
      try {
        return await User.findById(parent.userId);
      } catch (error) {
        console.error('Error in DeviceToken.user resolver:', error);
        throw new Error('Error in DeviceToken.user resolver');
      }
    }
  },

  Mutation: {
    updateDeviceToken: async (_, { input }, context) => {
      try {
        const userId = context.user._id.toString();
        const { token, deviceId, platform } = input;

        if (!token) {
          return createResponse('DeviceTokenError', 'FAILURE', 'Token is required', { errors: [{ field: 'token', message: 'Token is required' }] });
        }

        // Try to find existing device token
        let deviceToken = await DeviceToken.findOne({ userId, deviceId });
        let actionTaken = "updated";

        // Create new device token if it doesn't exist
        if (!deviceToken) {
          deviceToken = new DeviceToken({
            userId,
            deviceId,
            token,
            platform
          });
          actionTaken = "created";
        } else {
          // Update existing device token
          deviceToken.platform = platform || deviceToken.platform;
          deviceToken.token = token;
        }
        
        // Save the device token (works for both create and update)
        await deviceToken.save();

        return createResponse('DeviceTokenResponse', 'SUCCESS', `Device token ${actionTaken} successfully`, { result: { deviceToken } });
      } catch (error) {
        console.error('Error in updateDeviceToken resolver:', error);
        return createResponse('DeviceTokenError', 'FAILURE', 'An error occurred while processing device token', { errors: [{ field: 'updateDeviceToken', message: error.message }] });
      }
    },

    deleteDeviceToken: async (_, { token }, context) => {
      try {
        const userId = context.user._id.toString();

        const deviceToken = await DeviceToken.findOne({ userId, token });

        if (!deviceToken) {
          return createResponse('DeviceTokenErrorResponse', 'FAILURE', 'Device token not found', { errors: [{ field: 'token', message: 'Device token not found' }] });
        }

        await deviceToken.deleteOne();

        return createResponse('DeviceTokenResponse', 'SUCCESS', 'Device token deleted successfully', { result: { deviceToken } });
      } catch (error) {
        console.error('Error in deleteDeviceToken resolver:', error);
        return createResponse('DeviceTokenErrorResponse', 'FAILURE', 'An error occurred while deleting device token', { errors: [{ field: 'deleteDeviceToken', message: error.message }] });
      }
    }
  }
};

module.exports = deviceTokenResolvers;
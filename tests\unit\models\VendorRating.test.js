const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const VendorRating = require('../../../src/models/VendorRating');
const { User } = require('../../../src/models/User');
const Vendor = require('../../../src/models/Vendor');
const VendorUser = require('../../../src/models/VendorUser');
const Address = require('../../../src/models/Address');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('VendorRating Model Test', () => {
    it('should create and save a vendor rating successfully', async () => {
        const user = new User({
            role: ['user'],
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        });
        await user.save();

        const vendorUser = new VendorUser({
            user: user._id
        });
        await vendorUser.save();

        const address = new Address({
            addressType: 'HOME',
            street1: '123 Business St',
            city: 'Test City',
            state: 'TS',
            postalCode: '12345',
            country: 'Test Country'
        });
        await address.save();

        const vendor = new Vendor({
            name: 'Test Vendor',
            primaryContact: vendorUser._id,
            businessAddress: address._id
        });
        await vendor.save();

        const validVendorRating = new VendorRating({
            user: user._id,
            vendor: vendor._id,
            rating: 4,
            comment: 'Great service!'
        });

        const savedVendorRating = await validVendorRating.save();

        expect(savedVendorRating._id).toBeDefined();
        expect(savedVendorRating.user).toEqual(user._id);
        expect(savedVendorRating.vendor).toEqual(vendor._id);
        expect(savedVendorRating.rating).toBe(4);
        expect(savedVendorRating.comment).toBe('Great service!');
        expect(savedVendorRating.createdAt).toBeDefined();
        expect(savedVendorRating.updatedAt).toBeDefined();
    });

    it('should fail to create a vendor rating without required fields', async () => {
        const vendorRating = new VendorRating();

        let err;
        try {
            await vendorRating.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.user).toBeDefined();
        expect(err.errors.vendor).toBeDefined();
        expect(err.errors.rating).toBeDefined();
    });

    it('should fail to create a vendor rating with invalid rating value', async () => {
        const user = new mongoose.Types.ObjectId();
        const vendor = new mongoose.Types.ObjectId();

        const invalidRatingLow = new VendorRating({
            user,
            vendor,
            rating: 0,
            comment: 'Invalid rating'
        });

        const invalidRatingHigh = new VendorRating({
            user,
            vendor,
            rating: 6,
            comment: 'Invalid rating'
        });

        let errLow, errHigh;
        try {
            await invalidRatingLow.validate();
        } catch (error) {
            errLow = error;
        }

        try {
            await invalidRatingHigh.validate();
        } catch (error) {
            errHigh = error;
        }

        expect(errLow).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(errLow.errors.rating).toBeDefined();
        expect(errHigh).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(errHigh.errors.rating).toBeDefined();
    });

    it('should create a vendor rating without optional comment', async () => {
        const user = new mongoose.Types.ObjectId();
        const vendor = new mongoose.Types.ObjectId();

        const vendorRating = new VendorRating({
            user,
            vendor,
            rating: 5
        });

        const savedVendorRating = await vendorRating.save();

        expect(savedVendorRating._id).toBeDefined();
        expect(savedVendorRating.user).toEqual(user);
        expect(savedVendorRating.vendor).toEqual(vendor);
        expect(savedVendorRating.rating).toBe(5);
        expect(savedVendorRating.comment).toBeUndefined();
    });
});

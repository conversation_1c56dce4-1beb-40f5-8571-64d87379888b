const mongoose = require('mongoose');
const PartyService = require('../../../../src/models/PartyService');
const MdVendorType = require('../../../../src/models/MdVendorType');
const Vendor = require('../../../../src/models/Vendor');
const Task = require('../../../../src/models/Task');
const Transaction = require('../../../../src/models/Transaction');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildPartyServiceQuery = require('../../../../src/graphql/resolvers/filters/partyService.filter');
const { validateIds, isValidObjectId } = require('../../../../src/utils/validation.util');
const partyServiceResolvers = require('../../../../src/graphql/resolvers/partyService.resolver');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/models/PartyService');
jest.mock('../../../../src/models/MdVendorType');
jest.mock('../../../../src/models/Vendor');
jest.mock('../../../../src/models/Task');
jest.mock('../../../../src/models/Transaction');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/partyService.filter');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util', () => ({
    validateIds: jest.fn(),
    isValidObjectId: jest.fn()
}));
jest.mock('../../../../src/graphql/resolvers/partyService.resolver', () => {
    const actual = jest.requireActual('../../../../src/graphql/resolvers/partyService.resolver');
    return {
        ...actual,
        partyServiceIdSchema: {
            vendorType: 'objectId',
            vendor: 'objectId',
            tasks: ['objectId'],
            transactions: ['objectId']
        },
        Mutation: {
            ...actual.Mutation
        }
    };
});

jest.mock('mongoose', () => ({
    ...jest.requireActual('mongoose'),
    isValidObjectId: jest.fn()
}));

describe('partyServiceResolvers', () => {
    beforeAll(() => {
        jest.mock('mongoose', () => ({
            ...jest.requireActual('mongoose'),
            isValidObjectId: jest.fn()
        }));
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('PartyService field resolvers', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            console.error = jest.fn();
        });
    
        describe('vendorType', () => {
            it('should handle error in vendorType field resolver', async () => {
                const parent = { vendorType: 'vendorTypeId' };
                const error = new Error('Database error');
                
                MdVendorType.findById.mockRejectedValue(error);
    
                await expect(partyServiceResolvers.PartyService.vendorType(parent))
                    .rejects.toThrow('Error getting vendor type');
    
                expect(console.error).toHaveBeenCalledWith(error);
                expect(MdVendorType.findById).toHaveBeenCalledWith(parent.vendorType);
            });

            it('should resolve vendorType', async () => {
                const mockVendorType = { _id: 'vendorTypeId', name: 'Test Vendor Type' };
                const parent = { vendorType: 'vendorTypeId' };
                
                MdVendorType.findById.mockResolvedValue(mockVendorType);
                
                const result = await partyServiceResolvers.PartyService.vendorType(parent);
                
                expect(MdVendorType.findById).toHaveBeenCalledWith(parent.vendorType);
                expect(result).toEqual(mockVendorType);
            });
        });

        describe('vendor', () => {
            it('should handle error in vendor field resolver', async () => {
                const parent = { vendor: 'vendorId' };
                const error = new Error('Database error');
                
                Vendor.findById.mockRejectedValue(error);
    
                await expect(partyServiceResolvers.PartyService.vendor(parent))
                    .rejects.toThrow('Error getting vendor');
    
                expect(console.error).toHaveBeenCalledWith(error);
                expect(Vendor.findById).toHaveBeenCalledWith(parent.vendor);
            });

            it('should resolve vendor', async () => {
                const mockVendor = { _id: 'vendorId', name: 'Test Vendor' };
                const parent = { vendor: 'vendorId' };
                
                Vendor.findById.mockResolvedValue(mockVendor);
                
                const result = await partyServiceResolvers.PartyService.vendor(parent);
                
                expect(Vendor.findById).toHaveBeenCalledWith(parent.vendor);
                expect(result).toEqual(mockVendor);
            });
        });        

        describe('tasks', () => {
            it('should handle error in tasks field resolver', async () => {
                const parent = { tasks: ['taskId1', 'taskId2'] };
                const error = new Error('Database error');
                
                Task.find.mockRejectedValue(error);
    
                await expect(partyServiceResolvers.PartyService.tasks(parent))
                    .rejects.toThrow('Error getting tasks');
    
                expect(console.error).toHaveBeenCalledWith(error);
                expect(Task.find).toHaveBeenCalledWith({ _id: { $in: parent.tasks } });
            });

            it('should resolve tasks', async () => {
                const mockTasks = [{ _id: 'taskId1' }, { _id: 'taskId2' }];
                const parent = { tasks: ['taskId1', 'taskId2'] };
                
                Task.find.mockResolvedValue(mockTasks);
                
                const result = await partyServiceResolvers.PartyService.tasks(parent);
                
                expect(Task.find).toHaveBeenCalledWith({ _id: { $in: parent.tasks } });
                expect(result).toEqual(mockTasks);
            });
        });

        describe('transactions', () => {
            it('should handle error in transactions field resolver', async () => {
                const parent = { transactions: ['transactionId1', 'transactionId2'] };
                const error = new Error('Database error');
                
                Transaction.find.mockRejectedValue(error);
    
                await expect(partyServiceResolvers.PartyService.transactions(parent))
                    .rejects.toThrow('Error getting transactions');
    
                expect(console.error).toHaveBeenCalledWith(error);
                expect(Transaction.find).toHaveBeenCalledWith({ _id: { $in: parent.transactions } });
            });

            it('should resolve transactions', async () => {
                const mockTransactions = [{ _id: 'transactionId1' }, { _id: 'transactionId2' }];
                const parent = { transactions: ['transactionId1', 'transactionId2'] };
                
                Transaction.find.mockResolvedValue(mockTransactions);
                
                const result = await partyServiceResolvers.PartyService.transactions(parent);
                
                expect(Transaction.find).toHaveBeenCalledWith({ _id: { $in: parent.transactions } });
                expect(result).toEqual(mockTransactions);
            });
        });
    });

    describe('Query', () => {
        describe('getPartyServiceById', () => {
            it('should return cached party service if available', async () => {
                const mockPartyService = { _id: 'serviceId' };
                getByIdCache.mockResolvedValue(mockPartyService);
                createResponse.mockReturnValue('successResponse');

                const result = await partyServiceResolvers.Query.getPartyServiceById(null, { id: 'serviceId' });

                expect(getByIdCache).toHaveBeenCalledWith('serviceId');
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceResponse',
                    'SUCCESS',
                    'Party service retrieved successfully',
                    { result: { partyService: mockPartyService } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache party service if not in cache', async () => {
                const mockPartyService = { _id: 'serviceId' };
                getByIdCache.mockResolvedValue(null);
                
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate.mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPartyService);
                
                PartyService.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('successResponse');

                const result = await partyServiceResolvers.Query.getPartyServiceById(null, { id: 'serviceId' });

                expect(PartyService.findById).toHaveBeenCalledWith('serviceId');
                expect(mockPopulateChain.populate).toHaveBeenCalledTimes(4);
                expect(setCache).toHaveBeenCalledWith('serviceId', mockPartyService);
                expect(result).toBe('successResponse');
            });

            it('should return error response when party service is not found', async () => {
                const id = 'nonexistentId';
                getByIdCache.mockResolvedValue(null);
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate.mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(null);
                
                PartyService.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Query.getPartyServiceById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(PartyService.findById).toHaveBeenCalledWith(id);
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('vendorType');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('vendor');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('tasks');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('transactions');
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Party service not found',
                    {
                        errors: [{ field: 'id', message: 'Party service not found' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if an error occurs during retrieval', async () => {
                const id = 'serviceId';
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Query.getPartyServiceById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Error retrieving party service',
                    { errors: [{ field: 'getPartyServiceById', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getPartyServices', () => {
            it('should return party services with pagination', async () => {
                const mockFilter = { status: 'ACTIVE' };
                const mockPagination = { limit: 10, skip: 0 };
                const mockQuery = { status: 'ACTIVE' };
                const mockPaginationInfo = { totalPages: 1, totalItems: 1 };
                const mockPartyServices = [{ _id: 'serviceId' }];

                buildPartyServiceQuery.mockReturnValue(mockQuery);
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);

                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockPartyServices),
                };
                PartyService.find.mockReturnValue(mockPopulateChain);

                createResponse.mockReturnValue('successResponse');

                const result = await partyServiceResolvers.Query.getPartyServices(null, {
                    filter: mockFilter,
                    pagination: mockPagination
                });

                expect(buildPartyServiceQuery).toHaveBeenCalledWith(mockFilter);
                expect(getPaginationInfo).toHaveBeenCalledWith(PartyService, mockQuery, 10, 0);
                expect(PartyService.find).toHaveBeenCalledWith(mockQuery);
                expect(result).toBe('successResponse');
            });

            it('should return error response when an error occurs', async () => {
                const error = new Error('Database error');
                const filter = { someFilter: 'value' };
                const pagination = { limit: 10, skip: 0 };
                
                buildPartyServiceQuery.mockReturnValue({});
                
                PartyService.find.mockImplementation(() => {
                    throw error;
                });

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving party services',
                    errors: [{ field: 'getPartyServices', message: error.message }]
                });

                const result = await partyServiceResolvers.Query.getPartyServices(
                    null,
                    { filter, pagination }
                );

                expect(buildPartyServiceQuery).toHaveBeenCalledWith(filter);
                expect(PartyService.find).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Error retrieving party services',
                    {
                        errors: [{ field: 'getPartyServices', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving party services',
                    errors: [{ field: 'getPartyServices', message: error.message }]
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createPartyService', () => {
            it('should create party service successfully', async () => {
                const input = {
                    vendorType: 'vendorTypeId',
                    vendor: 'vendorId',
                    tasks: ['taskId'],
                    transactions: ['transactionId']
                };

                validateIds.mockReturnValue([]);
                
                const mockPartyService = {
                    _id: 'newId',
                    ...input,
                    save: jest.fn().mockResolvedValue({ _id: 'newId', ...input })
                };
                
                PartyService.mockImplementation(() => mockPartyService);

                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate.mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce({ _id: 'newId', ...input });

                PartyService.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('successResponse');

                const result = await partyServiceResolvers.Mutation.createPartyService(null, { input });

                expect(validateIds).toHaveBeenCalledWith(input, expect.any(Object));
                expect(mockPartyService.save).toHaveBeenCalled();
                expect(PartyService.findById).toHaveBeenCalledWith('newId');
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceResponse',
                    'SUCCESS',
                    'Party service created successfully',
                    { result: { partyService: { _id: 'newId', ...input } } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if validation fails', async () => {
                const input = { vendorType: 'invalid' };
                const validationErrors = [{ field: 'vendor', message: 'Required field missing' }];

                validateIds.mockReturnValue(validationErrors);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Mutation.createPartyService(null, { input });

                expect(validateIds).toHaveBeenCalledWith(input, expect.any(Object));
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Invalid input',
                    { errors: validationErrors }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deletePartyService', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should check for references before deletion', async () => {
                const id = 'partyServiceId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Mutation.deletePartyService(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'PartyService');
                expect(PartyService.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Party service cannot be deleted',
                    {
                        errors: [{ 
                            field: 'id', 
                            message: 'Party service cannot be deleted as it is being used in: Collection1, Collection2' 
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should delete party service when no references exist', async () => {
                const id = 'partyServiceId';
                const partyService = { _id: id, name: 'Test Party Service' };
                
                findReferences.mockResolvedValue([]);
                PartyService.findByIdAndDelete.mockResolvedValue(partyService);
                createResponse.mockReturnValue('successResponse');

                const result = await partyServiceResolvers.Mutation.deletePartyService(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'PartyService');
                expect(PartyService.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceResponse',
                    'SUCCESS',
                    'Party service deleted successfully',
                    { result: { partyService } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if party service not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                PartyService.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Mutation.deletePartyService(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'PartyService');
                expect(PartyService.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Party service not found',
                    { errors: [{ field: 'id', message: 'Party service not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors during deletion', async () => {
                const id = 'partyServiceId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                PartyService.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyServiceResolvers.Mutation.deletePartyService(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'PartyService');
                expect(PartyService.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyServiceErrorResponse',
                    'FAILURE',
                    'Error deleting party service',
                    { errors: [{ field: 'deletePartyService', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
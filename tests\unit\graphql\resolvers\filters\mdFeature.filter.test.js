const buildMdFeatureQuery = require('../../../../../src/graphql/resolvers/filters/mdFeature.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdFeatureQuery', () => {
    it('should return an empty query object when no filter is provided', () => {
        const result = buildMdFeatureQuery();
        expect(result).toEqual({});
    });

    it('should build a query with a valid id', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const result = buildMdFeatureQuery({ id: validId });
        expect(result).toEqual({ _id: validId });
    });

    it('should throw an error for an invalid id', () => {
        const invalidId = 'invalid_id';
        isValidObjectId.mockReturnValue(false);
        expect(() => buildMdFeatureQuery({ id: invalidId })).toThrow('Invalid id provided');
    });

    it('should build a query with a name filter', () => {
        const nameFilter = 'testName';
        const result = buildMdFeatureQuery({ name: nameFilter });
        expect(result).toEqual({ name: { $regex: nameFilter, $options: 'i' } });
    });

    it('should build a query with both id and name filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        const nameFilter = 'testName';
        isValidObjectId.mockReturnValue(true);
        const result = buildMdFeatureQuery({ id: validId, name: nameFilter });
        expect(result).toEqual({
            _id: validId,
            name: { $regex: nameFilter, $options: 'i' }
        });
    });
});
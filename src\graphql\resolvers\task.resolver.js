const Task = require('../../models/Task');
const Document = require('../../models/Document');
const Comment = require('../../models/Comment');
const Party = require('../../models/Party');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildTaskQuery = require('./filters/task.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');
const TaskCollaborator = require('../../models/TaskCollaborator');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');
const { deleteFileFromBlob } = require('../../utils/azureStorage.util');
const { hasTaskCollaboratorLevelAccess } = require('../../utils/auth/accessLevels.util');
const { hasEventLevelAccess } = require('../../utils/auth/accessLevels.util');
const { notifyTaskUpdated, notifyTaskCancelled, notifyTaskAttachmentAdded } = require('../../notification/taskNotification');

const baseTaskIdSchema = {
    attachments: { type: 'array', required: false, model: Document },
    comments: { type: 'array', required: false, model: Comment },
    party: { type: 'single', model: Party },
    assignedTo: { type: 'array', required: false, model: User }
};

const taskIdSchema = { ...baseTaskIdSchema };

const createTaskIdSchema = {
    ...baseTaskIdSchema,
    party: { ...baseTaskIdSchema.party, required: true }
};

const createTaskCollaborators = async (taskId, userIds) => {
    const collaborators = [];
    for (const userId of userIds) {
        // Check if collaborator already exists
        let taskCollaborator = await TaskCollaborator.findOne({
            task: taskId,
            user: userId
        });

        // If not exists, create new one
        if (!taskCollaborator) {
            taskCollaborator = new TaskCollaborator({
                task: taskId,
                user: userId,
                assignedOn: new Date()
            });
            await taskCollaborator.save();
        }
        collaborators.push(taskCollaborator._id);
    }
    return collaborators;
};

const taskResolvers = {
    Task: {
        attachments: async (parent) => {
            try {
                return await Document.find({ _id: { $in: parent.attachments } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting attachments');
            }
        },
        comments: async (parent) => {
            try {
                return await Comment.find({ _id: { $in: parent.comments } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting comments');
            }
        },
        party: async (parent) => {
            try {
                return await Party.findById(parent.party);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party');
            }
        },
        createdBy: async (parent) => {
            try {
                return await User.findById(parent.createdBy);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting creator');
            }
        },
        assignedTo: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.assignedTo } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting assigned users');
            }
        },
        collaborators: async (parent) => {
            try {
                return await TaskCollaborator.find({ task: parent._id })
                    .populate('user');
            } catch (error) {
                console.error(error);
                throw new Error('Error getting task collaborators');
            }
        }
    },

    Query: {
        getTaskById: async (_, { id }, context) => {
            try {
                if (!await hasTaskCollaboratorLevelAccess(context.user._id, id)) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this task' }]
                    });
                }

                const task = await Task.findById(id)
                    .populate('attachments')
                    .populate('comments')
                    .populate('party')
                    .populate('createdBy')
                    .populate('assignedTo');

                if (!task) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'id', message: 'Task not found' }]
                    });
                }
                return createResponse('TaskResponse', 'SUCCESS', 'Task fetched successfully', {
                    result: { task }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskErrorResponse', 'FAILURE', 'Error getting task', {
                    errors: [{ field: 'getTaskById', message: error.message }]
                });
            }
        },

        getTasks: async (_, { filter, pagination }) => {
            try {
                const query = await buildTaskQuery(filter) || {};

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Task, query, limit, skip);

                const tasks = await Task.find(query)
                    .populate('attachments')
                    .populate('comments')
                    .populate('party')
                    .populate('createdBy')
                    .populate('assignedTo')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (tasks.length === 0) {
                    return createResponse('TasksResponse', 'FAILURE', 'No tasks found', {
                        result: { tasks },
                        pagination: paginationInfo
                    });
                }

                return createResponse('TasksResponse', 'SUCCESS', 'Tasks fetched successfully', {
                    result: { tasks },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskErrorResponse', 'FAILURE', 'Error getting tasks', {
                    errors: [{ field: 'getTasks', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createTask: async (_, { input }, context) => {
            try {
                const validationError = await validateReferences(input, createTaskIdSchema, 'Task');
                if (validationError) {
                    return createResponse('TaskErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const party = await Party.findById(input.party);

                const hasAccess = await hasEventLevelAccess(context.user._id, party.eventId);
                if (!hasAccess) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const task = new Task({
                    ...input,
                    createdBy: context.user._id
                });
                await task.save();

                // Create task collaborators for assigned users
                if (input.assignedTo && input.assignedTo.length > 0) {
                    const collaboratorIds = await createTaskCollaborators(task._id, input.assignedTo);
                    task.collaborators = collaboratorIds;
                    await task.save();
                }

                const populatedTask = await Task.findById(task._id)
                    .populate('attachments')
                    .populate('comments')
                    .populate('party')
                    .populate('createdBy')
                    .populate('assignedTo')
                    .populate('collaborators');

                await notifyTaskUpdated(populatedTask, party, context.user._id);

                await clearCacheById(task._id);
                return createResponse('TaskResponse', 'SUCCESS', 'Task created successfully', {
                    result: { task: populatedTask }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskErrorResponse', 'FAILURE', 'Error creating task', {
                    errors: [{ field: 'createTask', message: error.message }]
                });
            }
        },

        updateTask: async (_, { id, input }, context) => {
            try {
                const validationError = await validateReferences(input, taskIdSchema, 'Task');
                if (validationError) {
                    return createResponse('TaskErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const existingTask = await Task.findById(id);

                const partyId = input.party || existingTask.party;
                const party = await Party.findById(partyId);
                const hasAccess = await hasEventLevelAccess(context.user._id, party.eventId);
                if (!hasAccess) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                // If assignedTo is being updated, update collaborators
                if (input.assignedTo) {
                    const collaboratorIds = await createTaskCollaborators(id, input.assignedTo);
                    input.collaborators = {
                        $addToSet: { $each: collaboratorIds }
                    };
                }

                const updatedTask = await Task.findByIdAndUpdate(
                    id,
                    { 
                        ...input,
                        ...(input.collaborators && {
                            collaborators: input.collaborators
                        })
                    },
                    { new: true }
                )
                .populate('attachments')
                .populate('comments')
                .populate('party')
                .populate('createdBy')
                .populate('assignedTo')
                .populate({
                    path: 'collaborators',
                    populate: { path: 'user' }
                });

                if (!updatedTask) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'updateTask', message: 'Task not found' }]
                    });
                }

                await notifyTaskUpdated(updatedTask, party, context.user._id);

                await clearCacheById(id);
                return createResponse('TaskResponse', 'SUCCESS', 'Task updated successfully', {
                    result: { task: updatedTask }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskErrorResponse', 'FAILURE', 'Error updating task', {
                    errors: [{ field: 'updateTask', message: error.message }]
                });
            }
        },

        deleteTask: async (_, { id }, context) => {
            try {
                const existingTask = await Task.findById(id)
                    .populate('createdBy');
                const partyId = existingTask.party;
                const party = await Party.findById(partyId);
                const hasAccess = await hasEventLevelAccess(context.user._id, party.eventId);
                if (!hasAccess) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const references = await findReferences(id, 'Task');
                if (references.length > 0) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Task cannot be deleted', {
                        errors: [{ field: 'id', message: `Task cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                await notifyTaskCancelled(existingTask, party, context.user._id);

                const { success, error } = await cascadeDelete('Task', id);
                if (!success) {
                    return createResponse('TaskErrorResponse', 'FAILURE', 'Error in cascade deletion', {
                        errors: [{ field: 'deleteTask', message: error.message }]
                    });
                }

                await Task.findByIdAndDelete(id);
                await clearCacheById(id);

                return createResponse('TaskResponse', 'SUCCESS', 'Task deleted successfully', {
                    result: { task: existingTask }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskErrorResponse', 'FAILURE', 'Error deleting task', {
                    errors: [{ field: 'deleteTask', message: error.message }]
                });
            }
        },

        createTaskComment: async (_, { taskId, input }, context) => {
            try {
                if (!await hasTaskCollaboratorLevelAccess(context.user._id, taskId)) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this task' }]
                    });
                }

                const task = await Task.findById(taskId);
                if (!task) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'taskId', message: 'Task not found' }]
                    });
                }

                const comment = new Comment({
                    ...input,
                    task: taskId,
                    createdBy: context.user._id
                });
                await comment.save();

                await Task.findByIdAndUpdate(taskId, {
                    $push: { comments: comment._id }
                });

                return createResponse('CommentResponse', 'SUCCESS', 'Comment created successfully', {
                    result: { comment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error creating comment', {
                    errors: [{ field: 'createTaskComment', message: error.message }]
                });
            }
        },

        updateTaskComment: async (_, { taskId, commentId, input }) => {
            try {
                const task = await Task.findById(taskId);
                if (!task) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'taskId', message: 'Task not found' }]
                    });
                }

                if (!task.comments.includes(commentId)) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found in this task', {
                        errors: [{ field: 'commentId', message: 'Comment not found in this task' }]
                    });
                }

                const updatedComment = await Comment.findByIdAndUpdate(
                    commentId,
                    { ...input },
                    { new: true }
                );

                if (!updatedComment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'commentId', message: 'Comment not found' }]
                    });
                }

                await clearCacheById(taskId);
                await clearCacheById(commentId);

                return createResponse('CommentResponse', 'SUCCESS', 'Comment updated successfully', {
                    result: { comment: updatedComment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error updating comment', {
                    errors: [{ field: 'updateTaskComment', message: error.message }]
                });
            }
        },

        deleteTaskComment: async (_, { id }) => {
            try {
                const comment = await Comment.findById(id);
                if (!comment) {
                    return createResponse('CommentErrorResponse', 'FAILURE', 'Comment not found', {
                        errors: [{ field: 'id', message: 'Comment not found' }]
                    });
                }

                await Task.findByIdAndUpdate(comment.task, {
                    $pull: { comments: id }
                });

                await Comment.findByIdAndDelete(id);
                await clearCacheById(id);

                return createResponse('CommentResponse', 'SUCCESS', 'Comment deleted successfully', {
                    result: { comment }
                });
            } catch (error) {
                console.error(error);
                return createResponse('CommentErrorResponse', 'FAILURE', 'Error deleting comment', {
                    errors: [{ field: 'deleteTaskComment', message: error.message }]
                });
            }
        },

        createTaskAttachment: async (_, { taskId, input }, context) => {
            try {
                if (!await hasTaskCollaboratorLevelAccess(context.user._id, taskId)) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this task' }]
                    });
                }

                const task = await Task.findById(taskId);
                if (!task) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'taskId', message: 'Task not found' }]
                    });
                }

                const document = new Document(input);
                await document.save();

                await Task.findByIdAndUpdate(
                    taskId,
                    { $push: { attachments: document._id } },
                    { new: true }
                );

                // Get party information for notification
                const party = await Party.findById(task.party);

                await notifyTaskAttachmentAdded(task, party, context.user._id);

                return createResponse('DocumentResponse', 'SUCCESS', 'Task attachment created successfully', {
                    result: { document }
                });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error creating task attachment', {
                    errors: [{ field: 'createTaskAttachment', message: error.message }]
                });
            }
        },

        deleteTaskAttachment: async (_, { id }, context) => {
            try {
                const task = await Task.findOne({ attachments: id });
                if (!task) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Task not found for this attachment', {
                        errors: [{ field: 'id', message: 'Task not found for this attachment' }]
                    });
                }

                if (!await hasTaskCollaboratorLevelAccess(context.user._id, task._id)) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this task' }]
                    });
                }

                const document = await Document.findById(id);
                if (!document) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Attachment not found', {
                        errors: [{ field: 'id', message: 'Attachment not found' }]
                    });
                }

                await Task.updateMany(
                    { attachments: id },
                    { $pull: { attachments: id } }
                );

                if (document.documentUrl) {
                    try {
                        const blobUrl = new URL(document.documentUrl);
                        const pathParts = blobUrl.pathname.split('/');
                        const container = pathParts[1];
                        const blobKey = pathParts[2];

                        await deleteFileFromBlob(blobKey, container);
                    } catch (error) {
                        console.error(`Error deleting file from blob storage: ${error.message}`);
                        
                        await Task.updateMany(
                            { _id: document.task },
                            { $addToSet: { attachments: id } }
                        );

                        return createResponse('DocumentErrorResponse', 'FAILURE', 'Error deleting file from storage', {
                            errors: [{ field: 'deleteTaskAttachment', message: error.message }]
                        });
                    }
                }

                await Document.findByIdAndDelete(id);

                return createResponse('DocumentResponse', 'SUCCESS', 'Task attachment deleted successfully', {
                    result: { document }
                });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error deleting task attachment', {
                    errors: [{ field: 'deleteTaskAttachment', message: error.message }]
                });
            }
        }
    }
};

module.exports = taskResolvers;

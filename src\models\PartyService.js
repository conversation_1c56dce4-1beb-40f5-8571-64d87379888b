const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const partyServiceSchema = new Schema({
    vendorType: { type: Schema.Types.ObjectId, ref: 'MdVendorType', required: true },
    vendor: { type: Schema.Types.ObjectId, ref: 'Vendor', required: true },
    time: { type: Date, required: true },
    city: { type: String, required: true },
    address: { type: String, required: true },
    budget: { type: Number, required: true, default: 0.0 },
    expenditure: { type: Number, required: true, default: 0.0 },
    transactions: [{ type: Schema.Types.ObjectId, ref: 'Transaction' }]
}, { timestamps: true, collection: 'party_services' });

const PartyService = model('PartyService', partyServiceSchema);

module.exports = PartyService;
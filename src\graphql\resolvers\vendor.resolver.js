const Vendor = require('../../models/Vendor');
const VendorUser = require('../../models/VendorUser');
const Address = require('../../models/Address');
const MdServiceLocation = require('../../models/MdServiceLocation');
const VendorService = require('../../models/VendorService');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildVendorQuery = require('./filters/vendor.filter');
const VendorRating = require('../../models/VendorRating');
const findReferences = require('./references/vendor.references');

const vendorResolvers = {
    Vendor: {
        primaryContact: async (vendor) => {
            try {
                return await VendorUser.findById(vendor.primaryContact);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting primary contact');
            }
        },
        contacts: async (vendor) => {
            try {
                return await VendorUser.find({ _id: { $in: vendor.contacts } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting contacts');
            }
        },
        businessAddress: async (vendor) => {
            try {
                return await Address.findById(vendor.businessAddress);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting business address');
            }
        },
        serviceLocations: async (vendor) => {
            try {
                return await MdServiceLocation.find({ _id: { $in: vendor.serviceLocations } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting service locations');
            }
        },
        servicesProvided: async (vendor) => {
            try {
                return await VendorService.find({ _id: { $in: vendor.servicesProvided } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting services provided');
            }
        },
        ratingAggregates: async (parent) => {
            try {
                const aggregateResult = await VendorRating.aggregate([
                    { $match: { vendor: parent._id } },
                    {
                        $group: {
                            _id: null,
                            averageRating: { $avg: '$rating' },
                            totalRating: { $sum: 1 }
                        }
                    }
                ]);

                if (!aggregateResult.length) {
                    return {
                        averageRating: 0,
                        totalRating: 0
                    };
                }

                return {
                    averageRating: Number(aggregateResult[0].averageRating.toFixed(1)),
                    totalRating: aggregateResult[0].totalRating
                };
            } catch (error) {
                console.error('Error calculating rating aggregates:', error);
                throw new Error('Error calculating rating aggregates');
            }
        },
        reviews: async (parent) => {
            try {
                return await VendorRating.find({ vendor: parent.id });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting reviews');
            }
        }
    },

    Query: {
        getVendorById: async (_, { id }) => {
            try {
                let vendor = await getByIdCache(id);
                if (!vendor) {
                    vendor = await Vendor.findById(id);
                    if (!vendor) {
                        return createResponse('VendorErrorResponse', 'FAILURE', 'Vendor not found', {
                            errors: [{ field: 'id', message: 'Vendor not found' }]
                        });
                    }
                    await setCache(id, vendor);
                }
                return createResponse('VendorResponse', 'SUCCESS', 'Vendor retrieved successfully', {
                    result: { vendor }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorErrorResponse', 'FAILURE', 'Error retrieving vendor', {
                    errors: [{ field: 'getVendorById', message: error.message }]
                });
            }
        },

        getVendors: async (_, { filter, pagination }) => {
            try {
                const pipeline = await buildVendorQuery(filter);
                
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const countPipeline = [...pipeline, { $count: 'total' }];
                const countResult = await Vendor.aggregate(countPipeline);
                const totalCount = countResult[0]?.total || 0;

                pipeline.push({ $skip: skip });
                pipeline.push({ $limit: limit });
                pipeline.push({ $sort: { createdAt: -1 } });

                const vendors = await Vendor.aggregate(pipeline);

                const paginationInfo = {
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    currentPage: Math.floor(skip / limit) + 1,
                    pageSize: limit,
                    hasNextPage: skip + limit < totalCount,
                    hasPreviousPage: skip > 0
                };

                if (vendors.length === 0) {
                    return createResponse('VendorsResponse', 'FAILURE', 'No vendors found', {
                        result: { vendors },
                        pagination: paginationInfo
                    });
                }

                const mappedVendors = vendors.map(vendor => ({
                    ...vendor,
                    id: vendor._id.toString()
                }));

                return createResponse('VendorsResponse', 'SUCCESS', 'Vendors fetched successfully', {
                    result: { vendors: mappedVendors },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorErrorResponse', 'FAILURE', 'Error retrieving vendors', {
                    errors: [{ field: 'getVendors', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createVendor: async (_, { input }) => {
            try {
                const vendor = new Vendor(input);
                await vendor.save();
                
                return createResponse('VendorResponse', 'SUCCESS', 'Vendor created successfully', {
                    result: { vendor }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorErrorResponse', 'FAILURE', 'Error creating vendor', {
                    errors: [{ field: 'createVendor', message: error.message }]
                });
            }
        },

        updateVendor: async (_, { id, input }) => {
            try {
                const vendor = await Vendor.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!vendor) {
                    return createResponse('VendorErrorResponse', 'FAILURE', 'Vendor not found', {
                        errors: [{ field: 'id', message: 'Vendor not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('VendorResponse', 'SUCCESS', 'Vendor updated successfully', {
                    result: { vendor }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorErrorResponse', 'FAILURE', 'Error updating vendor', {
                    errors: [{ field: 'updateVendor', message: error.message }]
                });
            }
        },

        deleteVendor: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('VendorErrorResponse', 'FAILURE', 'Vendor cannot be deleted', {
                        errors: [{ field: 'deleteVendor', message: `Vendor cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }

                const vendor = await Vendor.findByIdAndDelete(id);
                
                if (!vendor) {
                    return createResponse('VendorErrorResponse', 'FAILURE', 'Vendor not found', {
                        errors: [{ field: 'id', message: 'Vendor not found' }]
                    });
                }

                await clearCacheById(id);

                return createResponse('VendorResponse', 'SUCCESS', 'Vendor deleted successfully', {
                    result: { vendor }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorErrorResponse', 'FAILURE', 'Error deleting vendor', {
                    errors: [{ field: 'deleteVendor', message: error.message }]
                });
            }
        }
    }
};

module.exports = vendorResolvers;

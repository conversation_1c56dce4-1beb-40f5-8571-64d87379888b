const MdIcon = require('../../models/MdIcon');
const MdInterest = require('../../models/MdInterest');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const buildMdInterestQuery = require('./filters/mdInterest.filter');
const { findReferences } = require('../../utils/referenceCheck.util');

const mdInterestResolvers = {
    MdInterest: {
        icon: async (parent) => {
            try{
                const icon = await MdIcon.findById(parent.icon);
                return icon;
            } catch (error) {
                console.error(error);
                throw new Error(error);
            }
        }
    },

    Query: {
        getMdInterestById: async (_, { id }, context) => {
            try{
                let mdInterest = await getByIdCache(id);
                if (!mdInterest) {
                    mdInterest = await MdInterest.findById(id);
                    if (!mdInterest) {
                        return createResponse('MdInterestErrorResponse', 'FAILURE', 'MdInterest not found', { errors: [{ field: 'id', message: 'MdInterest not found' }] });
                    }
                    await setCache(id, mdInterest);
                }
                return createResponse('MdInterestResponse', 'SUCCESS', 'MdInterest fetched successfully', { result: { mdInterest } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestErrorResponse', 'FAILURE', 'Error fetching MdInterest', { errors: [{ field: 'getMdInterestById', message: error.message }] });
            }
        },

        getMdInterests: async (_, { filter, pagination }, context) => {
            try{
                const query = await buildMdInterestQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
                const mdInterests = await MdInterest.find(query).skip(skip).limit(limit).sort({ createdAt: -1 });
                const paginationInfo = await getPaginationInfo(MdInterest, query, limit, skip);
                if (mdInterests.length === 0) {
                    return createResponse('MdInterestsResponse', 'FAILURE', 'No MdInterests found', { result: { mdInterests }, pagination: paginationInfo });
                }
                return createResponse('MdInterestsResponse', 'SUCCESS', 'MdInterests fetched successfully', { result: { mdInterests }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestErrorResponse', 'FAILURE', 'Error fetching MdInterests', { errors: [{ field: 'getMdInterests', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdInterest: async (_, { input }, context) => {
            try{
                const mdInterest = new MdInterest(input);
                await mdInterest.save();
                await setCache(mdInterest._id, mdInterest);
                return createResponse('MdInterestResponse', 'SUCCESS', 'MdInterest created successfully', { result: { mdInterest } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestErrorResponse', 'FAILURE', 'Error creating MdInterest', { errors: [{ field: 'createMdInterest', message: error.message }] });
            }
        },

        updateMdInterest: async (_, { id, input }, context) => {
            try{
                const mdInterest = await MdInterest.findByIdAndUpdate(id, input, { new: true });
                if (!mdInterest) {
                    return createResponse('MdInterestErrorResponse', 'FAILURE', 'MdInterest not found', { errors: [{ field: 'id', message: 'MdInterest not found' }] });
                }
                await setCache(id, mdInterest);
                return createResponse('MdInterestResponse', 'SUCCESS', 'MdInterest updated successfully', { result: { mdInterest } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestErrorResponse', 'FAILURE', 'Error updating MdInterest', { errors: [{ field: 'updateMdInterest', message: error.message }] });
            }
        },

        deleteMdInterest: async (_, { id }, context) => {
            try{
                const mdInterest = await MdInterest.findById(id);
                if (!mdInterest) {
                    return createResponse('MdInterestErrorResponse', 'FAILURE', 'MdInterest not found', { errors: [{ field: 'id', message: 'MdInterest not found' }] });
                }
                const references = await findReferences(id, 'MdInterest');
                if (references.length > 0) {
                    return createResponse('MdInterestErrorResponse', 'FAILURE', 'MdInterest cannot be deleted', { errors: [{ field: 'id', message: `MdInterest cannot be deleted as it is being used in: ${references.join(', ')}` }] });
                }
                await MdInterest.findByIdAndDelete(id);
                await clearCacheById(id);
                return createResponse('MdInterestResponse', 'SUCCESS', 'MdInterest deleted successfully', { result: { mdInterest } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestErrorResponse', 'FAILURE', 'Error deleting MdInterest', { errors: [{ field: 'deleteMdInterest', message: error.message }] });
            }
        }
    }
}

module.exports = mdInterestResolvers;

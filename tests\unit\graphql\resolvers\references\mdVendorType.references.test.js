const PartyService = require('../../../../../src/models/PartyService');
const VendorService = require('../../../../../src/models/VendorService');
const findReferences = require('../../../../../src/graphql/resolvers/references/mdVendorType.references');

jest.mock('../../../../../src/models/PartyService');
jest.mock('../../../../../src/models/VendorService');

describe('mdVendorType References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        VendorService.find.mockResolvedValue([]);
        PartyService.find.mockResolvedValue([]);

        const result = await findReferences('vendorTypeId');

        expect(result).toEqual([]);
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
        expect(PartyService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });

    it('should return VendorService when only vendor service references exist', async () => {
        VendorService.find.mockResolvedValue([{ id: 'service1' }]);
        PartyService.find.mockResolvedValue([]);

        const result = await findReferences('vendorTypeId');

        expect(result).toEqual(['VendorService']);
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
        expect(PartyService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });

    it('should return PartyService when only party service references exist', async () => {
        VendorService.find.mockResolvedValue([]);
        PartyService.find.mockResolvedValue([{ id: 'party1' }]);

        const result = await findReferences('vendorTypeId');

        expect(result).toEqual(['PartyService']);
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
        expect(PartyService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });

    it('should return both services when both references exist', async () => {
        VendorService.find.mockResolvedValue([{ id: 'service1' }]);
        PartyService.find.mockResolvedValue([{ id: 'party1' }]);

        const result = await findReferences('vendorTypeId');

        expect(result).toEqual(['VendorService', 'PartyService']);
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
        expect(PartyService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });

    it('should handle errors from VendorService.find', async () => {
        const error = new Error('Database error');
        VendorService.find.mockRejectedValue(error);
        PartyService.find.mockResolvedValue([]);

        await expect(findReferences('vendorTypeId')).rejects.toThrow('Database error');
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });

    it('should handle errors from PartyService.find', async () => {
        const error = new Error('Database error');
        VendorService.find.mockResolvedValue([]);
        PartyService.find.mockRejectedValue(error);

        await expect(findReferences('vendorTypeId')).rejects.toThrow('Database error');
        expect(VendorService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
        expect(PartyService.find).toHaveBeenCalledWith({ vendorType: 'vendorTypeId' });
    });
});
const buildArticleFilter = (filter) => {
  if (!filter) return () => true;

  return (article) => {
    if (filter.id && article._id !== filter.id && article.id !== filter.id) {
      return false;
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      const matchesSearch = 
        (article.headline?.toLowerCase().includes(searchLower)) ||
        (article.description?.toLowerCase().includes(searchLower)) ||
        (article.source?.link?.toLowerCase().includes(searchLower)) ||
        (article.source?.mainUrl?.toLowerCase().includes(searchLower));
      
      if (!matchesSearch) {
        return false;
      }
    } else {
      if (filter.headline && !article.headline?.toLowerCase().includes(filter.headline.toLowerCase())) {
        return false;
      }

      if (filter.description && !article.description?.toLowerCase().includes(filter.description.toLowerCase())) {
        return false;
      }

      if (filter.source) {
        if (filter.source.link && !article.source?.link?.toLowerCase().includes(filter.source.link.toLowerCase())) {
          return false;
        }
        if (filter.source.mainUrl && !article.source?.mainUrl?.toLowerCase().includes(filter.source.mainUrl.toLowerCase())) {
          return false;
        }
      }
    }

    // Check date
    if (filter.date && article.date !== filter.date) {
      return false;
    }

    // Check date range
    if (filter.dateRange) {
      const articleDate = new Date(article.date);
      if (filter.dateRange.start && articleDate < new Date(filter.dateRange.start)) {
        return false;
      }
      if (filter.dateRange.end && articleDate > new Date(filter.dateRange.end)) {
        return false;
      }
    }

    return true;
  };
};

module.exports = buildArticleFilter; 
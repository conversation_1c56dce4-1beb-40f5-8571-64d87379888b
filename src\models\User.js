const { model, Schema } = require('mongoose');
const MdRole = require('./MdRole');
const { updateUserRole } = require('../utils/clerk.util');

const userSchema = new Schema({
  role: { type: [String], required: [true, 'Role is required'], default: ['user'] },
  firstName: { type: String, required: true },
  lastName: { type: String },
  email: { type: String, unique: true, sparse: true,
    set: v => v === '' || v === null ? undefined : v
  },
  phone: { type: String, required: true, unique: true },
  profilePicture: { type: String }, 
  isRegistered: { type: Boolean, default: false },
  emailVerified: { type: Boolean, default: false },
  phoneVerified: { type: Boolean, default: false },
  externalId: { type: String },
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

userSchema.index({ email: 1 }, { unique: true, sparse: true });

const User = model('User', userSchema);

async function createUserFromJson(jsonData) {
  try {
      const data = jsonData.data;
      const defaultRole = await MdRole.findOne({ name: 'user' });
      if (!defaultRole) {
          throw new Error('Default user role not found');
      }

      const user = new User({
          role: ["user"],
          firstName: data.first_name && data.first_name.length > 0 ? data.first_name : "unknown",
          lastName: data.last_name && data.last_name.length > 0 ? data.last_name : "unknown",
          email: data.email_addresses[0].email_address,
          phone: data.phone_numbers.length > 0 ? data.phone_numbers[0].phone_number : "************",
          profilePicture: data.profile_image_url || data.picture || data.avatar_url || undefined,
          isRegistered: true,
          emailVerified: true,
          phoneVerified: true,
          externalId: data.id,
          isActive: true,
      });
      await user.save();

      if (user.externalId) {
          try {
              await updateUserRole(user.externalId, user.role);
              console.log('Clerk role updated successfully for user:', user._id);
          } catch (clerkError) {
              console.error('Error updating Clerk role during user creation:', clerkError);
              // We don't throw here to avoid failing the user creation
              // The role can be updated later if needed
          }
      }

      console.log('User saved successfully:', user);
      return user;
  } catch (error) {
      console.error('Error saving user:', error);
      throw error;
  }
}

module.exports = {User, createUserFromJson };
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdFunFact = require('../../../src/models/MdFunFact');
const MdEventType = require('../../../src/models/MdEventType');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdFunFact Model Test', () => {
    it('should create and save a MdFunFact successfully', async () => {
        const mockEventType = new MdEventType({
            name: 'Birthday Party',
            description: 'A birthday celebration',
            duration: 'ONE_DAY'
        });
        await mockEventType.save();

        const validMdFunFact = new MdFunFact({
            funFact: 'Did you know? The tradition of birthday cakes began in Ancient Greece!',
            eventTypes: [mockEventType._id]
        });

        const savedMdFunFact = await validMdFunFact.save();

        expect(savedMdFunFact._id).toBeDefined();
        expect(savedMdFunFact.funFact).toBe('Did you know? The tradition of birthday cakes began in Ancient Greece!');
        expect(savedMdFunFact.eventTypes[0]).toEqual(mockEventType._id);
    });

    it('should create and save a MdFunFact with multiple eventTypes', async () => {
        const mockEventType1 = new MdEventType({
            name: 'Birthday Party',
            description: 'A birthday celebration',
            duration: 'ONE_DAY'
        });
        await mockEventType1.save();

        const mockEventType2 = new MdEventType({
            name: 'Wedding',
            description: 'A wedding celebration',
            duration: 'ONE_DAY'
        });
        await mockEventType2.save();

        const validMdFunFact = new MdFunFact({
            funFact: 'This is a fun fact for multiple event types!',
            eventTypes: [mockEventType1._id, mockEventType2._id]
        });

        const savedMdFunFact = await validMdFunFact.save();

        expect(savedMdFunFact._id).toBeDefined();
        expect(savedMdFunFact.funFact).toBe('This is a fun fact for multiple event types!');
        expect(savedMdFunFact.eventTypes).toHaveLength(2);
        expect(savedMdFunFact.eventTypes).toContainEqual(mockEventType1._id);
        expect(savedMdFunFact.eventTypes).toContainEqual(mockEventType2._id);
    });

    it('should fail to create a MdFunFact with invalid eventTypes reference', async () => {
        const mdFunFact = new MdFunFact({
            funFact: 'Test fun fact',
            eventTypes: ['invalid_id']
        });

        let err;
        try {
            await mdFunFact.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors['eventTypes.0']).toBeDefined();
    });
});
const notificationProducer = require("../utils/notificationProducer");
const { User } = require('../models/User');


async function notifyMessageAdded(eventName, userName, messageContent, partyId, senderId) {
    try {
        await notificationProducer.sendNotification('User Added Message', {
            partyId: partyId,
            EventName: eventName,
            UserName: userName,
            MessageSnippet: messageContent.substring(0, 100) + (messageContent.length > 100 ? '...' : ''), // truncate long messages
            senderId: senderId
        });
    } catch (error) {
        console.error('Failed to send message added notification:', error);
    }
}

async function notifyPhotoAddedInMessage(eventName, userName, partyId, senderId) {
    try {
        await notificationProducer.sendNotification('User Added Photo', {
            partyId: partyId,
            EventName: eventName,
            UserName: userName,
            senderId: senderId
        });
    } catch (error) {
        console.error('Failed to send photo added notification:', error);
    }
}

async function notifyLocationTrackingRequest(eventName, userName, partyId, guestId, senderId) {
    try {
        await notificationProducer.sendNotificationToSpecificRecipients('Request for Location Tracking', {
            partyId: partyId,
            EventName: eventName,
            UserName: userName,
            senderId: senderId
        }, [{
            userId: guestId,
            role: 'Guest'
        }]);
    } catch (error) {
        console.error('Failed to send location tracking request notification:', error);
    }
}

module.exports = {
    notifyMessageAdded,
    notifyPhotoAddedInMessage,
    notifyLocationTrackingRequest
};


const notificationProducer = require("../utils/notificationProducer");
const EventGroup = require("../models/EventGroup");
const { User } = require("../models/User");

const notifyInviteToCircle = async (eventGroup, members = [], contextUserId) => {
    try{
        if (members.length > 0) {
            const users = await User.find({ _id: { $in: members } });
            const recipients = users.map(user => ({
                userId: user._id,
                email: user.email,
                phone: user.phone,
                role: 'Member'
            }));
            await notificationProducer.sendNotificationToSpecificRecipients('Invited To Circle', {
                CircleName: eventGroup.name,
                eventGroupId: eventGroup._id,
                senderId: contextUserId
            }, recipients);
        }
        else
            await notificationProducer.sendCircleNotification('Invited To Circle', {
                CircleName: eventGroup.name,
                eventGroupId: eventGroup._id,
                senderId: contextUserId
            });
    }
    catch (error) {
        console.error('Error sending circle invite notifications:', error);
    }
}

const notifyCirclePhotoUpdated = async (eventGroup, contextUserId) => {
    try{
        await notificationProducer.sendCircleNotification('Circle Photo Updated', {
            CircleName: eventGroup.name,
            eventGroupId: eventGroup._id,
            senderId: contextUserId
        });
    }
    catch (error) {
        console.error('Error sending circle photo updated notifications:', error);
    }
}

const notifyCircleInterestsChanged = async (eventGroup, contextUserId) => {
    try{
        await notificationProducer.sendCircleNotification('Circle Interests Changed', {
            CircleName: eventGroup.name,
            eventGroupId: eventGroup._id,
            senderId: contextUserId
        });
    }
    catch (error) {
        console.error('Error sending circle interests changed notifications:', error);
    }
}

const notifyCircleTitleChanged = async (oldEventGroup, updatedEventGroup, contextUserId) => {
    try {
        await notificationProducer.sendCircleNotification('Circle Title Changed', {
            OldCircleTitle: oldEventGroup.name,
            NewCircleTitle: updatedEventGroup.name,
            eventGroupId: updatedEventGroup._id,
            senderId: contextUserId
        });
    }
    catch (error) {
        console.error('Error sending circle title changed notifications:', error);
    }
}

const notifyCircleDescriptionChanged = async (eventGroup, contextUserId) => {
    try {
        await notificationProducer.sendCircleNotification('Circle Description Changed', {
            CircleName: eventGroup.name,
            eventGroupId: eventGroup._id,
            senderId: contextUserId
        });
    }
    catch (error) {
        console.error('Error sending circle description changed notifications:', error);
    }
}

const notifyNewEventAdded = async (eventGroupId, event, contextUserId) => {
    try {
        const eventGroup = await EventGroup.findById(eventGroupId);
        if (!eventGroup) 
            throw new Error('Event group not found');
        await notificationProducer.sendCircleNotification('New Event Added', {
            CircleName: eventGroup.name,
            EventName: event.name,
            eventGroupId: eventGroupId,
            senderId: contextUserId
        });
    }
    catch (error) {
        console.error('Error sending new event added notifications:', error);
    }
}

module.exports = {
    notifyInviteToCircle,
    notifyCirclePhotoUpdated,
    notifyCircleInterestsChanged,
    notifyCircleTitleChanged,
    notifyCircleDescriptionChanged,
    notifyNewEventAdded
}

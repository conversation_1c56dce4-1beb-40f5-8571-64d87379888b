const { User } = require('../../../../src/models/User');
const MdRole = require('../../../../src/models/MdRole');
const userResolvers = require('../../../../src/graphql/resolvers/user.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildUserQuery = require('../../../../src/graphql/resolvers/filters/user.filter');
const { validateReferences } = require('../../../../src/utils/validation.util');

const userIdSchema = {
    roles: { type: 'array', required: true, model: MdRole }
};

jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/MdRole');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/user.filter');
jest.mock('../../../../src/utils/validation.util');

console.error = jest.fn();

describe('User Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('User Field Resolvers', () => {
        describe('roles', () => {
            it('should return user roles successfully', async () => {
                const mockRoles = [
                    { _id: 'role1', name: 'admin' },
                    { _id: 'role2', name: 'user' }
                ];
                const parent = { roles: ['role1', 'role2'] };

                MdRole.find.mockResolvedValue(mockRoles);

                const result = await userResolvers.User.roles(parent);

                expect(MdRole.find).toHaveBeenCalledWith({ _id: { $in: parent.roles } });
                expect(result).toEqual(mockRoles);
            });

            it('should throw error when getting roles fails', async () => {
                const parent = { roles: ['role1'] };
                const error = new Error('Database error');

                MdRole.find.mockRejectedValue(error);

                await expect(userResolvers.User.roles(parent))
                    .rejects
                    .toThrow('Error getting user roles');
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getUserById', () => {
            it('should return cached user if available', async () => {
                const mockUser = { _id: 'userId', name: 'Test User' };
                getByIdCache.mockResolvedValue(mockUser);
                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Query.getUserById(null, { id: 'userId' });

                expect(getByIdCache).toHaveBeenCalledWith('userId');
                expect(User.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'UserResponse',
                    'SUCCESS',
                    'User retrieved successfully',
                    { result: { user: mockUser } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache user if not in cache', async () => {
                const mockUser = { _id: 'userId', name: 'Test User' };
                getByIdCache.mockResolvedValue(null);
                User.findById.mockResolvedValue(mockUser);
                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Query.getUserById(null, { id: 'userId' });

                expect(getByIdCache).toHaveBeenCalledWith('userId');
                expect(User.findById).toHaveBeenCalledWith('userId');
                expect(setCache).toHaveBeenCalledWith('userId', mockUser);
                expect(createResponse).toHaveBeenCalledWith(
                    'UserResponse',
                    'SUCCESS',
                    'User retrieved successfully',
                    { result: { user: mockUser } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when user not found', async () => {
                getByIdCache.mockResolvedValue(null);
                User.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Query.getUserById(null, { id: 'userId' });

                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'User not found',
                    { errors: [{ field: 'id', message: 'User not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when an error occurs', async () => {
                const id = 'testUserId';
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Query.getUserById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'Error retrieving user',
                    {
                        errors: [{ field: 'getUserById', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getUsers', () => {
            it('should return users with pagination successfully', async () => {
                const mockUsers = [{ _id: 'user1' }, { _id: 'user2' }];
                const mockPaginationInfo = { total: 2, hasMore: false };
                const mockQuery = { status: 'active' };
                const mockFilters = { status: 'active' };
                const mockPagination = { limit: 10, skip: 0 };

                buildUserQuery.mockReturnValue(mockQuery);
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                User.find.mockReturnValue({
                    skip: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockResolvedValue(mockUsers)
                });
                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Query.getUsers(null, {
                    filters: mockFilters,
                    pagination: mockPagination
                });

                expect(buildUserQuery).toHaveBeenCalledWith(mockFilters);
                expect(getPaginationInfo).toHaveBeenCalledWith(User, mockQuery, 10, 0);
                expect(createResponse).toHaveBeenCalledWith(
                    'UsersResponse',
                    'SUCCESS',
                    'Users fetched successfully',
                    {
                        result: { users: mockUsers },
                        pagination: mockPaginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should return appropriate response when no users found', async () => {
                const mockQuery = {};
                const mockPaginationInfo = { total: 0, hasMore: false };

                buildUserQuery.mockReturnValue(mockQuery);
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                User.find.mockReturnValue({
                    skip: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockResolvedValue([])
                });
                createResponse.mockReturnValue('emptyResponse');

                const result = await userResolvers.Query.getUsers(null, {});

                expect(createResponse).toHaveBeenCalledWith(
                    'UsersResponse',
                    'FAILURE',
                    'No users found',
                    {
                        result: { users: [] },
                        pagination: mockPaginationInfo
                    }
                );
                expect(result).toBe('emptyResponse');
            });

            it('should return error response when an error occurs', async () => {
                const filters = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
                
                buildUserQuery.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error getting users',
                    errors: [{ field: 'getUsers', message: error.message }]
                });

                const result = await userResolvers.Query.getUsers(null, { filters, pagination });

                expect(buildUserQuery).toHaveBeenCalledWith(filters);
                expect(console.error).toHaveBeenCalledWith(error);
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'Error getting users',
                    {
                        errors: [{ field: 'getUsers', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error getting users',
                    errors: [{ field: 'getUsers', message: error.message }]
                });
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('register', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should update existing unregistered user successfully', async () => {
                const input = {
                    phone: '1234567890',
                    roles: ['roleId1', 'roleId2']
                };

                validateReferences.mockResolvedValue(null);
                
                const existingUser = {
                    _id: 'userId',
                    phone: input.phone,
                    isRegistered: false
                };
                User.findOne.mockResolvedValue(existingUser);
                
                const updatedUser = { ...existingUser, ...input, isRegistered: true };
                User.findByIdAndUpdate.mockResolvedValue(updatedUser);

                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'User');
                expect(User.findOne).toHaveBeenCalledWith({ phone: input.phone });
                expect(User.findByIdAndUpdate).toHaveBeenCalledWith(
                    existingUser._id,
                    { ...input, isRegistered: true },
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'UserResponse',
                    'SUCCESS',
                    'User registered successfully',
                    { result: { user: updatedUser } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fail if user already exists and is registered', async () => {
                const input = {
                    phone: '1234567890',
                    roles: ['roleId1', 'roleId2']
                };

                validateReferences.mockResolvedValue(null);
                
                const existingUser = {
                    _id: 'userId',
                    phone: input.phone,
                    isRegistered: true
                };
                User.findOne.mockResolvedValue(existingUser);

                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'User');
                expect(User.findOne).toHaveBeenCalledWith({ phone: input.phone });
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'User already exists',
                    { errors: [{ field: 'phone', message: 'A user with this phone number already exists' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should fail if validation fails', async () => {
                const input = {
                    phone: '1234567890',
                    roles: ['invalidRoleId']
                };

                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'roles', message: 'Invalid role reference' }]
                };
                validateReferences.mockResolvedValue(validationError);

                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'User');
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors', async () => {
                const input = {
                    phone: '1234567890',
                    roles: ['roleId1']
                };

                const error = new Error('Database error');
                validateReferences.mockResolvedValue(null);
                User.findOne.mockRejectedValue(error);

                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'User');
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'Error registering user',
                    { errors: [{ field: 'register', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should create a new user when no existing user is found', async () => {
                const input = {
                    phone: '+1234567890',
                    roles: ['roleId1', 'roleId2'],
                    name: 'Test User'
                };

                validateReferences.mockResolvedValue(null);

                User.findOne.mockResolvedValue(null);

                const savedUser = {
                    _id: 'newUserId',
                    ...input,
                    isRegistered: true,
                    save: jest.fn().mockResolvedValue(true)
                };

                User.mockImplementation(() => savedUser);

                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, userIdSchema, 'User');
                expect(User.findOne).toHaveBeenCalledWith({ phone: input.phone });
                expect(User).toHaveBeenCalledWith(input);
                expect(savedUser.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'UserResponse',
                    'SUCCESS',
                    'User registered successfully',
                    { result: { user: savedUser } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when validation fails', async () => {
                const input = {
                    phone: '1234567890',
                    roles: ['invalidRoleId']
                };
                const validationError = {
                    message: 'Invalid reference',
                    errors: [
                        { field: 'roles', message: 'Invalid role reference' }
                    ]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.register(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, userIdSchema, 'User');
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
                expect(User.prototype.save).not.toHaveBeenCalled();
            });
        });

        describe('verifyUser', () => {
            it('should verify user successfully', async () => {
                const mockUser = {
                    id: 'userId',
                    email: '<EMAIL>',
                    save: jest.fn().mockResolvedValue(true)
                };

                User.findOne.mockResolvedValue(mockUser);
                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Mutation.verifyUser(null, {
                    email: '<EMAIL>'
                });

                expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
                expect(mockUser.emailVerified).toBe(true);
                expect(mockUser.save).toHaveBeenCalled();
                expect(clearCacheById).toHaveBeenCalledWith(mockUser.id);
                expect(result).toBe('successResponse');
            });

            it('should return error when user not found', async () => {
                User.findOne.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.verifyUser(null, {
                    email: '<EMAIL>'
                });

                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'User not found',
                    { errors: [{ field: 'email', message: 'User not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when an error occurs during verification', async () => {
                const email = '<EMAIL>';
                const error = new Error('Database error');
                
                User.findOne.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.verifyUser(null, { email });

                expect(User.findOne).toHaveBeenCalledWith({ email });
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'Error verifying user',
                    {
                        errors: [{ field: 'verifyUser', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
                expect(clearCacheById).not.toHaveBeenCalled();
            });
        });

        describe('updateUser', () => {
            it('should update user successfully', async () => {
                const id = 'userId';
                const input = { name: 'Updated Name', roles: ['role1'] };
                const mockUser = { _id: id, ...input };

                validateReferences.mockResolvedValue(null);
                User.findByIdAndUpdate.mockResolvedValue(mockUser);
                createResponse.mockReturnValue('successResponse');

                const result = await userResolvers.Mutation.updateUser(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'User');
                expect(User.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'UserResponse',
                    'SUCCESS',
                    'User updated successfully',
                    { result: { user: mockUser } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error when user not found', async () => {
                const id = 'nonexistentId';
                const input = { name: 'Updated Name' };

                validateReferences.mockResolvedValue(null);
                User.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await userResolvers.Mutation.updateUser(null, { id, input });

                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'User not found',
                    { errors: [{ field: 'id', message: 'User not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when update fails', async () => {
                const id = 'userId';
                const input = { 
                    name: 'John Doe',
                    roles: ['roleId1', 'roleId2']
                };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                User.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error updating user',
                    errors: [{ field: 'updateUser', message: error.message }]
                });

                const result = await userResolvers.Mutation.updateUser(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, userIdSchema, 'User');
                expect(User.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'UserErrorResponse',
                    'FAILURE',
                    'Error updating user',
                    { errors: [{ field: 'updateUser', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error updating user',
                    errors: [{ field: 'updateUser', message: error.message }]
                });
            });
        });
    });
});
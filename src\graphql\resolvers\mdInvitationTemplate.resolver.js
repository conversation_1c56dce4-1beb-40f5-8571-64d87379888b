const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { validateReferences } = require('../../utils/validation.util');
const buildMdInvitationTemplateQuery = require('./filters/mdInvitationTemplate.filter');
const findReferences = require('./references/tag.references');
const MdInvitationTemplate = require('../../models/MdInvitaionTemplate');
const Tag = require('../../models/Tag');

const tagIdSchema = {
    tags: {
        type: 'array',
        model: Tag,
        required: true
    }
};

const mdInvitationTemplateResolvers = {
    MdInvitationTemplate: {
        tags: async (parent) => {
            return await Tag.find({ _id: { $in: parent.tags } });
        }
    },

    Query: {
        getMdInvitationTemplateById: async (_, { id }) => {
            try {
                const cachedTemplate = await getByIdCache(id);
                if (cachedTemplate) return createResponse('MdInvitationTemplateResponse', 'SUCCESS', 'Invitation template found', { result: { mdInvitationTemplate: cachedTemplate } });

                const mdInvitationTemplate = await MdInvitationTemplate.findById(id).populate('tags');
                if (!mdInvitationTemplate) {
                    return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invitation template not found', { errors: [{ field: 'id', message: 'Invitation template not found' }] });
                }

                await setCache(id, mdInvitationTemplate);
                return createResponse('MdInvitationTemplateResponse', 'SUCCESS', 'Invitation template found', { result: { mdInvitationTemplate } });
            } catch (error) {
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error fetching invitation template', { errors: [{ field: 'getMdInvitationTemplateById', message: error.message }] });
            }
        },

        getMdInvitationTemplates: async (_, { filter = {}, pagination = {} }) => {
            try {
                const query = await buildMdInvitationTemplateQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = getPaginationInfo(MdInvitationTemplate, query, limit, skip);

                const mdInvitationTemplates = await MdInvitationTemplate.find(query)
                    .populate('tags')
                    .skip(skip)
                    .limit(limit)
                    .sort({ createdAt: -1 });

                if (mdInvitationTemplates.length === 0) {
                    return createResponse('MdInvitationTemplatesResponse', 'FAILURE', 'No invitation templates found', { result: { mdInvitationTemplates }, pagination: paginationInfo });
                }

                return createResponse('MdInvitationTemplatesResponse', 'SUCCESS', 'Invitation templates found', { result: { mdInvitationTemplates }, pagination: paginationInfo });
            } catch (error) {
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error fetching invitation templates', { errors: [{ field: 'getMdInvitationTemplates', message: error.message }] });
            }
        },

        getAllMdInvitationTemplateTags: async () => {
            try {
                const uniqueTagIds = await MdInvitationTemplate.distinct('tags');
                
                // Fetch the actual tag documents
                const tags = await Tag.find({ _id: { $in: uniqueTagIds } });

                console.log(tags);

                if (tags.length === 0) {
                    return createResponse('MdInvitationTemplateTagsResponse', 'SUCCESS', 'No tags found', {
                        result: { tags: [] }
                    });
                }

                return createResponse('MdInvitationTemplateTagsResponse', 'SUCCESS', 'Tags fetched successfully', {
                    result: { tags }
                });

            } catch (error) {
                console.error(error);
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error getting tags', {
                    errors: [{ field: 'getAllMdInvitationTemplateTags', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdInvitationTemplate: async (_, { input }) => {
            try {
                // Validate tag references if they exist
                if (input.tags?.length) {
                    const validationErrors = await validateReferences(input, tagIdSchema, 'MdInvitationTemplate');
                    if (validationErrors) {
                        return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invalid tags', {
                            errors: validationErrors.errors
                        });
                    }
                }

                const mdInvitationTemplate = new MdInvitationTemplate(input);
                await mdInvitationTemplate.save();
                await mdInvitationTemplate.populate('tags');

                return createResponse('MdInvitationTemplateResponse', 'SUCCESS', 'Invitation template created successfully', {
                    result: { mdInvitationTemplate }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error creating invitation template', {
                    errors: [{ field: 'createMdInvitationTemplate', message: error.message }]
                });
            }
        },

        updateMdInvitationTemplate: async (_, { id, input }) => {
            try {
                // Validate tag references if they exist
                if (input.tags?.length) {
                    const validationErrors = await validateReferences(input, tagIdSchema, 'MdInvitationTemplate');
                    if (validationErrors) {
                        return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invalid tags', {
                            errors: validationErrors.errors
                        });
                    }
                }

                const mdInvitationTemplate = await MdInvitationTemplate.findByIdAndUpdate(
                    id,
                    { $set: input },
                    { new: true }
                ).populate('tags');

                if (!mdInvitationTemplate) {
                    return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invitation template not found', { errors: [{ field: 'updateMdInvitationTemplate', message: 'Invitation template not found' }] });
                }

                await clearCacheById('mdInvitationTemplate', id);
                return createResponse('MdInvitationTemplateResponse', 'SUCCESS', 'Invitation template updated successfully', { result: { mdInvitationTemplate } });
            } catch (error) {
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error updating invitation template', { errors: [{ field: 'updateMdInvitationTemplate', message: error.message }] });
            }
        },

        deleteMdInvitationTemplate: async (_, { id }) => {
            try {
                // Check for references before deletion
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invitation template is referenced by other entities', { errors: [{ field: 'deleteMdInvitationTemplate', message: 'Cannot delete invitation template with existing references' }] });
                }

                const mdInvitationTemplate = await MdInvitationTemplate.findByIdAndDelete(id);
                if (!mdInvitationTemplate) {
                    return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Invitation template not found', { errors: [{ field: 'deleteMdInvitationTemplate', message: 'Invitation template not found' }] });
                }

                await clearCacheById('mdInvitationTemplate', id);
                return createResponse('MdInvitationTemplateResponse', 'SUCCESS', 'Invitation template deleted successfully', { result: { mdInvitationTemplate } });
            } catch (error) {
                return createResponse('MdInvitationTemplateErrorResponse', 'FAILURE', 'Error deleting invitation template', { errors: [{ field: 'deleteMdInvitationTemplate', message: error.message }] });
            }
        }
    }
};

module.exports = mdInvitationTemplateResolvers;

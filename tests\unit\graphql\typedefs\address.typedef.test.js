const { gql } = require('graphql-tag');
const addressTypeDef = require('../../../../src/graphql/typedefs/address.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('addressTypeDef', () => {
    it('should contain the Address type definitions', () => {
        const expectedTypeDefs = gql`
        ${sharedTypeDef}

        enum AddressType {
            HOME
            WORK
            OTHER
        }

        type Address {
            id: ID!
            addressType: AddressType!
            street1: String!
            street2: String
            city: String!
            state: String!
            postalCode: String!
            country: String!
            isPrimary: Boolean!
        }

        input AddressFilterInput {
            addressType: AddressType
            city: String
            state: String
            country: String
            isPrimary: Boolean
        }

        type AddressWrapper {
            address: Address!
        }

        type AddressesWrapper {
            addresses: [Address]!
        }

        type AddressResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: AddressWrapper!
        }

        type AddressesResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: AddressesWrapper!
            pagination: PaginationInfo!
        }

        type AddressErrorResponse implements Response {
            status: ResponseStatus!
            message: String!
            errors: [Error!]!
        }

        union AddressResult = AddressResponse | AddressErrorResponse
        union AddressesResult = AddressesResponse | AddressErrorResponse

        type Query {
            getAddressById(id: ID!): AddressResult!
            getAddresses(filter: AddressFilterInput, pagination: PaginationInput): AddressesResult!
        }

        input AddressInput {
            addressType: AddressType!
            street1: String!
            street2: String
            city: String!
            state: String!
            postalCode: String!
            country: String!
            isPrimary: Boolean!
        }

        input AddressUpdateInput {
            addressType: AddressType
            street1: String
            street2: String
            city: String
            state: String
            postalCode: String
            country: String
            isPrimary: Boolean
        }

        type Mutation {
            createAddress(input: AddressInput!): AddressResult!
            updateAddress(id: ID!, input: AddressUpdateInput!): AddressResult!
            deleteAddress(id: ID!): AddressResult!
        }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(addressTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});
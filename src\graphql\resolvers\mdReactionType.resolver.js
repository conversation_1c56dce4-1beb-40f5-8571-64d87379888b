const MdReactionType = require('../../models/MdReactionType');
const MdIcon = require('../../models/MdIcon');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMdReactionTypeQuery = require('./filters/mdReactionType.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');

const baseMdReactionTypeIdSchema = {
    icon: { type: 'single', model: MdIcon }
};

const mdReactionTypeIdSchema = {
    ...baseMdReactionTypeIdSchema
};

const createMdReactionTypeIdSchema = {
    ...baseMdReactionTypeIdSchema,
    icon: { ...baseMdReactionTypeIdSchema.icon, required: true }
};

const mdReactionTypeResolvers = {
    MdReactionType: {
        icon: async (parent) => {
            try {
                return await MdIcon.findById(parent.icon);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting icon');
            }
        }
    },

    Query: {
        getMdReactionTypeById: async (_, { id }) => {
            try {
                let mdReactionType = await getByIdCache(id);
                if (!mdReactionType) {
                    mdReactionType = await MdReactionType.findById(id).populate('icon');
                    if (!mdReactionType) {
                        return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'MdReactionType not found', {
                            errors: [{ field: 'id', message: 'MdReactionType not found' }]
                        });
                    }
                    await setCache(id, mdReactionType);
                }
                return createResponse('MdReactionTypeResponse', 'SUCCESS', 'MdReactionType fetched successfully', {
                    result: { mdReactionType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'Error getting MdReactionType', {
                    errors: [{ field: 'getMdReactionTypeById', message: error.message }]
                });
            }
        },

        getMdReactionTypes: async (_, { filter, pagination }) => {
            try {
                const query = await buildMdReactionTypeQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdReactionType, query, limit, skip);

                const mdReactionTypes = await MdReactionType.find(query)
                    .populate('icon')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (mdReactionTypes.length === 0) {
                    return createResponse('MdReactionTypesResponse', 'FAILURE', 'No ReactionTypes found', {
                        result: { mdReactionTypes },
                        pagination: paginationInfo
                    });
                }

                return createResponse('MdReactionTypesResponse', 'SUCCESS', 'ReactionTypes fetched successfully', {
                    result: { mdReactionTypes },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'Error getting ReactionTypes', {
                    errors: [{ field: 'getMdReactionTypes', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdReactionType: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createMdReactionTypeIdSchema, 'MdReactionType');
                if (validationError) {
                    return createResponse('MdReactionTypeErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdReactionType = new MdReactionType(input);
                await mdReactionType.save();

                const populatedReactionType = await MdReactionType.findById(mdReactionType._id).populate('icon');

                return createResponse('MdReactionTypeResponse', 'SUCCESS', 'ReactionType created successfully', {
                    result: { mdReactionType: populatedReactionType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'Error creating ReactionType', {
                    errors: [{ field: 'createMdReactionType', message: error.message }]
                });
            }
        },

        updateMdReactionType: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdReactionTypeIdSchema, 'MdReactionType');
                if (validationError) {
                    return createResponse('MdReactionTypeErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdReactionType = await MdReactionType.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                ).populate('icon');

                if (!mdReactionType) {
                    return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'ReactionType not found', {
                        errors: [{ field: 'id', message: 'ReactionType not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdReactionTypeResponse', 'SUCCESS', 'ReactionType updated successfully', {
                    result: { mdReactionType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'Error updating ReactionType', {
                    errors: [{ field: 'updateMdReactionType', message: error.message }]
                });
            }
        },

        deleteMdReactionType: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdReactionType');
                if (references.length > 0) {
                    return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'ReactionType cannot be deleted', {
                        errors: [{ field: 'id', message: `ReactionType cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const mdReactionType = await MdReactionType.findByIdAndDelete(id);
                
                if (!mdReactionType) {
                    return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'ReactionType not found', {
                        errors: [{ field: 'id', message: 'ReactionType not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdReactionTypeResponse', 'SUCCESS', 'ReactionType deleted successfully', {
                    result: { mdReactionType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdReactionTypeErrorResponse', 'FAILURE', 'Error deleting ReactionType', {
                    errors: [{ field: 'deleteMdReactionType', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdReactionTypeResolvers; 
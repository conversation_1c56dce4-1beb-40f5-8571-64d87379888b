const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { User } = require('../../../src/models/User');
const Host = require('../../../src/models/Host');
const { validateCoHosts, processCoHost, processCoHosts } = require('../../../src/utils/coHost.util');

jest.mock('../../../src/models/User');
jest.mock('../../../src/models/Host');

describe('CoHost Utility Functions', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('validateCoHosts', () => {
        it('should return no errors for valid coHosts', () => {
            const validCoHosts = [
                { name: '<PERSON>', phone: '1234567890', email: '<EMAIL>' },
                { name: '<PERSON>', phone: '0987654321', email: '<EMAIL>' }
            ];

            const errors = validateCoHosts(validCoHosts);
            expect(errors).toHaveLength(0);
        });

        it('should return errors for missing required fields', () => {
            const invalidCoHosts = [
                { email: '<EMAIL>' },
                { name: '', phone: '' }
            ];

            const errors = validateCoHosts(invalidCoHosts);
            expect(errors).toHaveLength(4);
            expect(errors).toContainEqual({ field: 'name', message: 'Name is required for co-host' });
            expect(errors).toContainEqual({ field: 'phone', message: 'Phone number is required for co-host' });
        });
    });

    describe('processCoHost', () => {
        const mockCoHost = {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        };

        it('should create new user and host if they do not exist', async () => {
            const mockUser = { _id: 'userId123' };
            const mockHost = { _id: 'hostId123' };

            User.findOne.mockResolvedValue(null);

            const mockUserInstance = {
                _id: mockUser._id,
                save: jest.fn().mockResolvedValue(mockUser)
            };
            User.mockImplementation(() => mockUserInstance);

            Host.findOne.mockResolvedValue(null);

            const mockHostInstance = {
                _id: mockHost._id,
                save: jest.fn().mockResolvedValue(mockHost)
            };
            Host.mockImplementation(() => mockHostInstance);

            const result = await processCoHost(mockCoHost);

            expect(User.findOne).toHaveBeenCalledWith({ phone: mockCoHost.phone });
            expect(mockUserInstance.save).toHaveBeenCalled();
            expect(Host.findOne).toHaveBeenCalledWith({ userId: mockUserInstance._id });
            expect(mockHostInstance.save).toHaveBeenCalled();
            expect(result).toBe(mockHost._id);
        });

        it('should use existing user and create new host if user exists', async () => {
            const mockUser = { _id: 'userId123' };
            const mockHost = { _id: 'hostId123' };

            User.findOne.mockResolvedValue(mockUser);

            Host.findOne.mockResolvedValue(null);

            const mockHostInstance = {
                _id: mockHost._id,
                save: jest.fn().mockResolvedValue(mockHost)
            };
            Host.mockImplementation(() => mockHostInstance);

            const result = await processCoHost(mockCoHost);

            expect(User.findOne).toHaveBeenCalledWith({ phone: mockCoHost.phone });
            expect(User.prototype.save).not.toHaveBeenCalled();
            expect(Host.findOne).toHaveBeenCalledWith({ userId: mockUser._id });
            expect(mockHostInstance.save).toHaveBeenCalled();
            expect(result).toBe(mockHost._id);
        });

        it('should use existing user and host if both exist', async () => {
            const mockUser = { _id: 'userId123' };
            const mockHost = { _id: 'hostId123' };

            User.findOne.mockResolvedValue(mockUser);
            Host.findOne.mockResolvedValue(mockHost);

            const result = await processCoHost(mockCoHost);

            expect(User.findOne).toHaveBeenCalledWith({ phone: mockCoHost.phone });
            expect(User.prototype.save).not.toHaveBeenCalled();
            expect(Host.findOne).toHaveBeenCalledWith({ userId: mockUser._id });
            expect(Host.prototype.save).not.toHaveBeenCalled();
            expect(result).toBe(mockHost._id);
        });
    });

    describe('processCoHosts', () => {
        it('should return empty array for no coHosts', async () => {
            const result = await processCoHosts([]);
            expect(result).toEqual([]);
        });

        it('should return errors if validation fails', async () => {
            const invalidCoHosts = [
                { email: '<EMAIL>' },
                { name: '', phone: '' }
            ];

            const result = await processCoHosts(invalidCoHosts);
            expect(result.errors).toBeDefined();
            expect(result.errors).toHaveLength(4);
        });

        it('should process multiple coHosts successfully', async () => {
            const mockCoHosts = [
                { name: 'John Doe', email: '<EMAIL>', phone: '1234567890' },
                { name: 'Jane Doe', email: '<EMAIL>', phone: '0987654321' }
            ];

            const mockHostIds = ['hostId1', 'hostId2'];
            jest.spyOn(Promise, 'all').mockResolvedValue(mockHostIds);

            const result = await processCoHosts(mockCoHosts);
            expect(result).toEqual({ coHostIds: mockHostIds });
        });
    });
}); 
const buildVendorUserQuery = (filters) => {
    const pipeline = [];

    if (filters && (filters.vendorFirstName || filters.vendorLastName || filters.vendorEmail)) {
        pipeline.push({
            $lookup: {
                from: 'users',
                localField: 'user',
                foreignField: '_id',
                as: 'userDetails'
            }
        });

        pipeline.push({ $unwind: '$userDetails' });

        const matchConditions = {};
        if (filters.vendorFirstName) {
            matchConditions['userDetails.firstName'] = { $regex: new RegExp(filters.vendorFirstName, 'i') };
        }
        if (filters.vendorLastName) {
            matchConditions['userDetails.lastName'] = { $regex: new RegExp(filters.vendorLastName, 'i') };
        }
        if (filters.vendorEmail) {
            matchConditions['userDetails.email'] = { $regex: new RegExp(filters.vendorEmail, 'i') };
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }
    }

    pipeline.push({
        $project: {
            _id: 1,
            user: 1,
            userDetails: 1,
            ...Object.fromEntries(Object.keys(require('../../../models/VendorUser').schema.paths).map(field => [field, 1]))
        }
    });

    return pipeline;
};

module.exports = buildVendorUserQuery;
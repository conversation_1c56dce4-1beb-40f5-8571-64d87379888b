const MdPartner = require('../../../../src/models/MdPartner');
const MdIcon = require('../../../../src/models/MdIcon');
const MdPartnerCategory = require('../../../../src/models/MdPartnerCategory');
const { clearCacheById, setCache, getByIdCache } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const mdPartnerResolvers = require('../../../../src/graphql/resolvers/mdPartner.resolver');
const buildMdPartnerQuery = require('../../../../src/graphql/resolvers/filters/mdPartner.filter');
const mongoose = require('mongoose');
const Tag = require('../../../../src/models/Tag');
const validateReferences = require('../../../../src/utils/validation.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

const basePartnerIdSchema = {
    icon: { type: 'single', model: MdIcon },
    categories: { type: 'array', model: Tag }
};

const mdPartnerIdSchema = {
    ...basePartnerIdSchema
};

const createMdPartnerIdSchema = {
    ...basePartnerIdSchema,
    icon: { ...basePartnerIdSchema.icon, required: true },
    categories: { ...basePartnerIdSchema.categories, required: true }
};

jest.mock('../../../../src/models/MdPartner');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/models/MdPartnerCategory');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdPartner.filter');
jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/utils/referenceCheck.util', () => ({
    findReferences: jest.fn()
}));
jest.spyOn(console, 'error').mockImplementation(() => {});

beforeEach(() => {
    jest.clearAllMocks();
    MdPartner.findByIdAndUpdate = jest.fn();
    MdPartner.findByIdAndDelete = jest.fn();
});

describe('mdPartnerResolvers', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('MdPartner', () => {
        describe('icon', () => {
            it('should return the icon if found', async () => {
                const parent = { icon: 'iconId' };
                const icon = { id: 'iconId', name: 'Icon Name' };
                MdIcon.findById.mockResolvedValue(icon);

                const result = await mdPartnerResolvers.MdPartner.icon(parent);

                expect(MdIcon.findById).toHaveBeenCalledWith(parent.icon);
                expect(result).toEqual(icon);
            });

            it('should throw an error if icon not found', async () => {
                const parent = { icon: 'iconId' };
                MdIcon.findById.mockResolvedValue(null);

                await expect(mdPartnerResolvers.MdPartner.icon(parent)).rejects.toThrow('Error getting icon');
            });

            it('should throw an error on exception', async () => {
                const parent = { icon: 'iconId' };
                const error = new Error('Database error');
                MdIcon.findById.mockRejectedValue(error);

                await expect(mdPartnerResolvers.MdPartner.icon(parent)).rejects.toThrow('Error getting icon');
            });
        });

        describe('categories', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should successfully retrieve categories', async () => {
                const mockCategories = [
                    { _id: 'cat1', name: 'Category 1' },
                    { _id: 'cat2', name: 'Category 2' }
                ];
                
                const parent = {
                    categories: ['cat1', 'cat2']
                };

                Tag.find.mockResolvedValue(mockCategories);

                const result = await mdPartnerResolvers.MdPartner.categories(parent);

                expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: parent.categories } });
                expect(result).toEqual(mockCategories);
            });

            it('should throw error when retrieving categories fails', async () => {
                const parent = {
                    categories: ['cat1', 'cat2']
                };

                Tag.find.mockRejectedValue(new Error('Database error'));

                await expect(mdPartnerResolvers.MdPartner.categories(parent))
                    .rejects
                    .toThrow('Error getting categories');

                expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: parent.categories } });
            });
        });
    });

    describe('Query', () => {
        describe('getMdPartners', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should fetch partners successfully with pagination', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 5, skip: 0 };
                const query = { name: { $regex: 'Test', $options: 'i' } };
                const paginationInfo = {
                    totalCount: 10,
                    hasNextPage: true,
                    hasPreviousPage: false
                };

                const mockPartners = [
                    { _id: '1', name: 'Partner 1' },
                    { _id: '2', name: 'Partner 2' }
                ];

                buildMdPartnerQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockPartners)
                };

                MdPartner.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('successResponse');

                const result = await mdPartnerResolvers.Query.getMdPartners(null, { filter, pagination });

                expect(buildMdPartnerQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdPartner, query, 5, 0);
                expect(MdPartner.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith('categories');
                expect(mockFind.populate).toHaveBeenCalledWith('icon');
                expect(mockFind.limit).toHaveBeenCalledWith(5);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnersResponse',
                    'SUCCESS',
                    'MdPartners fetched successfully',
                    {
                        result: { mdPartners: mockPartners },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle empty results', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const query = {};
                const paginationInfo = {
                    totalCount: 0,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                buildMdPartnerQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([])
                };

                MdPartner.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('failureResponse');

                const result = await mdPartnerResolvers.Query.getMdPartners(null, { filter, pagination });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnersResponse',
                    'FAILURE',
                    'No mdPartners found',
                    {
                        result: { mdPartners: [] },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('failureResponse');
            });

            it('should handle errors', async () => {
                const error = new Error('Database error');
                buildMdPartnerQuery.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Query.getMdPartners(null, {});

                expect(console.error).toHaveBeenCalledWith(error);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'Error retrieving MdPartners',
                    {
                        errors: [{ field: 'getMdPartners', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should use default pagination values when not provided', async () => {
                const filter = {};
                const query = {};
                const paginationInfo = {
                    totalCount: 5,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                buildMdPartnerQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([{ _id: '1', name: 'Partner 1' }])
                };

                MdPartner.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('successResponse');

                await mdPartnerResolvers.Query.getMdPartners(null, { filter });

                expect(getPaginationInfo).toHaveBeenCalledWith(MdPartner, query, 10, 0);
                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
            });
        });

        describe('getMdPartnerById', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return partner from cache if available', async () => {
                const cachedPartner = {
                    _id: 'partnerId',
                    name: 'Cached Partner'
                };

                getByIdCache.mockResolvedValue(cachedPartner);
                createResponse.mockReturnValue('successResponse');

                const result = await mdPartnerResolvers.Query.getMdPartnerById(null, { id: 'partnerId' });

                expect(getByIdCache).toHaveBeenCalledWith('partnerId');
                expect(MdPartner.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerResponse',
                    'SUCCESS',
                    'MdPartner fetched successfully',
                    { result: { mdPartner: cachedPartner } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache partner if not in cache', async () => {
                const partner = {
                    _id: 'partnerId',
                    name: 'Test Partner',
                    populate: jest.fn().mockReturnThis()
                };

                getByIdCache.mockResolvedValue(null);
                MdPartner.findById.mockResolvedValue(partner);
                createResponse.mockReturnValue('successResponse');

                const result = await mdPartnerResolvers.Query.getMdPartnerById(null, { id: 'partnerId' });

                expect(getByIdCache).toHaveBeenCalledWith('partnerId');
                expect(MdPartner.findById).toHaveBeenCalledWith('partnerId');
                expect(partner.populate).toHaveBeenCalledWith('categories');
                expect(partner.populate).toHaveBeenCalledWith('icon');
                expect(setCache).toHaveBeenCalledWith('partnerId', partner);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerResponse',
                    'SUCCESS',
                    'MdPartner fetched successfully',
                    { result: { mdPartner: partner } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if partner not found', async () => {
                getByIdCache.mockResolvedValue(null);
                MdPartner.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Query.getMdPartnerById(null, { id: 'nonexistentId' });

                expect(getByIdCache).toHaveBeenCalledWith('nonexistentId');
                expect(MdPartner.findById).toHaveBeenCalledWith('nonexistentId');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'MdPartner not found',
                    { errors: [{ field: 'getMdPartnerById', message: 'MdPartner not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors and return error response', async () => {
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Query.getMdPartnerById(null, { id: 'partnerId' });

                expect(getByIdCache).toHaveBeenCalledWith('partnerId');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'Error retrieving MdPartner',
                    { errors: [{ field: 'getMdPartnerById', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdPartner', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should create and return the MdPartner', async () => {
                const input = { name: 'Partner Name', category: 'categoryId', icon: 'iconId', description: 'Partner Description' };
                const mdPartner = { _id: 'partnerId', ...input };

                validateReferences.validateReferences.mockResolvedValue(null);
                MdPartner.mockImplementation((data) => ({
                    ...data,
                    save: jest.fn().mockResolvedValue({ ...data, _id: 'partnerId' })
                }));
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdPartner created successfully', result: { mdPartner } });

                const result = await mdPartnerResolvers.Mutation.createMdPartner(null, { input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(input, createMdPartnerIdSchema, 'MdPartner');
                expect(MdPartner).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdPartner created successfully', result: { mdPartner } });
            });

            it('should return an error response if creation fails', async () => {
                const input = { name: 'Partner Name', category: 'categoryId', icon: 'iconId', description: 'Partner Description' };
                const error = new Error('Error creating mdPartner');

                MdPartner.mockImplementation((data) => ({
                    ...data,
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdPartner', errors: [{ field: 'createMdPartner', message: error.message }] });

                const result = await mdPartnerResolvers.Mutation.createMdPartner(null, { input });

                expect(MdPartner).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdPartner', errors: [{ field: 'createMdPartner', message: error.message }] });
            });

            it('should return error response if an unexpected error occurs', async () => {
                const input = { name: 'Partner Name', category: 'categoryId', icon: 'iconId', description: 'Partner Description' };
                const error = new Error('Unexpected error');

                MdPartner.mockImplementation((data) => ({
                    ...data,
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdPartner', errors: [{ field: 'createMdPartner', message: error.message }] });

                const result = await mdPartnerResolvers.Mutation.createMdPartner(null, { input });

                expect(MdPartner).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdPartner', errors: [{ field: 'createMdPartner', message: error.message }] });
            });

            it('should return validation error response when validation fails', async () => {
                const input = {
                    name: 'Test Partner',
                    icon: 'invalidIconId',
                    categories: ['invalidCategoryId']
                };

                const validationError = {
                    message: 'Validation failed',
                    errors: [
                        { field: 'icon', message: 'Invalid icon reference' },
                        { field: 'categories', message: 'Invalid categories reference' }
                    ]
                };

                validateReferences.validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Mutation.createMdPartner(null, { input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(
                    input, 
                    createMdPartnerIdSchema, 
                    'MdPartner'
                );
                expect(MdPartner).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('updateMdPartner', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should update MdPartner successfully', async () => {
                const id = 'partnerId';
                const input = { name: 'Updated Partner' };
                const mdPartner = { _id: id, name: 'Updated Partner', icon: 'iconId' };
                const mockUpdateResponse = {
                    populate: jest.fn().mockResolvedValue(mdPartner)
                };

                validateReferences.validateReferences.mockResolvedValue(null);
                MdPartner.findByIdAndUpdate.mockReturnValue(mockUpdateResponse);
                createResponse.mockReturnValue('successResponse');
                clearCacheById.mockResolvedValue();

                const result = await mdPartnerResolvers.Mutation.updateMdPartner(null, { id, input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(input, mdPartnerIdSchema, 'MdPartner');
                expect(MdPartner.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(mockUpdateResponse.populate).toHaveBeenCalledWith('icon');
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toBe('successResponse');
            });

            it('should handle validation errors', async () => {
                const id = 'partnerId';
                const input = { 
                    name: 'Updated Partner',
                    categories: ['invalidCategoryId']
                };
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'categories', message: 'Invalid category reference' }]
                };

                validateReferences.validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Mutation.updateMdPartner(null, { id, input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(input, mdPartnerIdSchema, 'MdPartner');
                expect(MdPartner.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(result).toBe('errorResponse');
            });

            it('should handle database errors during update', async () => {
                const id = 'partnerId';
                const input = {
                    name: 'Updated Partner',
                    description: 'Updated description'
                };
                const error = new Error('Database error');

                validateReferences.validateReferences.mockResolvedValue(null);
                
                const mockPopulate = jest.fn().mockRejectedValue(error);
                MdPartner.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Mutation.updateMdPartner(null, { id, input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdPartner');
                expect(MdPartner.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(mockPopulate).toHaveBeenCalledWith('icon');
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'Error updating MdPartner',
                    { errors: [{ field: 'updateMdPartner', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when partner not found during update', async () => {
                const id = 'nonexistentId';
                const input = { 
                    name: 'Updated Partner',
                    icon: 'iconId',
                    categories: ['categoryId']
                };

                validateReferences.validateReferences.mockResolvedValue(null);
                
                const mockPopulate = jest.fn().mockResolvedValue(null);
                MdPartner.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Mutation.updateMdPartner(null, { id, input });

                expect(validateReferences.validateReferences).toHaveBeenCalledWith(input, mdPartnerIdSchema, 'MdPartner');
                expect(MdPartner.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(mockPopulate).toHaveBeenCalledWith('icon');
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'MdPartner not found',
                    { errors: [{ field: 'updateMdPartner', message: 'MdPartner not found' }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdPartner', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should delete MdPartner successfully', async () => {
                const id = 'partnerId';
                const mdPartner = { _id: id, name: 'Partner Name', icon: 'iconId' };

                findReferences.mockResolvedValue([]);
                MdPartner.findByIdAndDelete.mockResolvedValue(mdPartner);
                createResponse.mockReturnValue('successResponse');
                clearCacheById.mockResolvedValue();

                const result = await mdPartnerResolvers.Mutation.deleteMdPartner(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartner');
                expect(MdPartner.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerResponse',
                    'SUCCESS',
                    'MdPartner deleted successfully',
                    { result: { mdPartner } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors during deletion', async () => {
                const id = 'partnerId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdPartner.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerResolvers.Mutation.deleteMdPartner(null, { id });
                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartner');
                expect(MdPartner.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerErrorResponse',
                    'FAILURE',
                    'Error deleting MdPartner',
                    { errors: [{ field: 'deleteMdPartner', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    });
});
{"name": "graphql-api", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "test": "jest", "start": "nodemon index.js", "test:coverage": "npx jest --coverage --forceExit"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@azure/storage-blob": "^12.26.0", "@clerk/clerk-sdk-node": "^5.1.1", "@clerk/express": "^1.3.31", "@googlemaps/google-maps-services-js": "^3.4.0", "@graphql-tools/load-files": "^7.0.0", "amqplib": "^0.10.5", "apollo-server": "^3.13.0", "apollo-server-express": "^3.13.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "emoji-regex": "^10.4.0", "expo-server-sdk": "^3.14.0", "express": "^4.21.0", "graphql": "^16.9.0", "graphql-tools": "^9.0.1", "graphql-upload": "^13.0.0", "install": "^0.13.0", "ioredis": "^5.4.1", "jose": "^5.9.6", "mongodb-memory-server": "^10.0.1", "mongoose": "^8.6.2", "nodemon": "^3.1.4", "npm": "^10.8.3", "redis": "^4.7.0", "socket.io": "^4.8.1", "svix": "^1.35.0", "uuid": "^11.0.3"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^7.0.0"}}
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Tag = require('../../../src/models/Tag');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { 
        useNewUrlParser: true, 
        useUnifiedTopology: true
    });
    await Tag.createIndexes();
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Tag Model Test', () => {
    beforeEach(async () => {
        await Tag.deleteMany({}); // Clear all tags before each test
    });

    it('should create and save a Tag successfully', async () => {
        const mockIcon = new MdIcon({
            name: 'Test Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const validTag = new Tag({
            name: 'Test Tag',
            icon: mockIcon._id,
            slug: 'test-tag'
        });

        const savedTag = await validTag.save();

        expect(savedTag._id).toBeDefined();
        expect(savedTag.name).toBe('Test Tag');
        expect(savedTag.icon).toEqual(mockIcon._id);
        expect(savedTag.slug).toBe('test-tag');
        expect(savedTag.createdAt).toBeDefined();
        expect(savedTag.updatedAt).toBeDefined();
    });

    it('should fail to create a Tag without required fields', async () => {
        const tag = new Tag();

        let err;
        try {
            await tag.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.slug).toBeDefined();
    });

    it('should fail to create a Tag with invalid icon reference', async () => {
        const invalidTag = new Tag({
            name: 'Invalid Tag',
            icon: 'invalid_id',
            slug: 'invalid-tag'
        });

        let err;
        try {
            await invalidTag.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.icon).toBeDefined();
    });

    it('should fail to create a Tag with duplicate name', async () => {
        const firstTag = new Tag({
            name: 'Duplicate Tag',
            slug: 'duplicate-tag-1'
        });
        await firstTag.save();

        const duplicateTag = new Tag({
            name: 'Duplicate Tag',
            slug: 'duplicate-tag-2'
        });

        let err;
        try {
            await duplicateTag.save();
        } catch (error) {
            err = error;
        }

        expect(err).toBeDefined();
        expect(err.code).toBe(11000); // MongoDB duplicate key error code
    });

    it('should fail to create a Tag with duplicate slug', async () => {
        const firstTag = new Tag({
            name: 'First Tag',
            slug: 'duplicate-slug'
        });
        await firstTag.save();

        const duplicateTag = new Tag({
            name: 'Second Tag',
            slug: 'duplicate-slug'
        });

        let err;
        try {
            await duplicateTag.save();
        } catch (error) {
            err = error;
        }

        expect(err).toBeDefined();
        expect(err.code).toBe(11000);
    });

    it('should fail to create a Tag with name exceeding maxlength', async () => {
        const longTag = new Tag({
            name: 'a'.repeat(51), // 51 characters, max is 50
            slug: 'long-tag'
        });

        let err;
        try {
            await longTag.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
    });

    it('should fail to create a Tag with slug exceeding maxlength', async () => {
        const longTag = new Tag({
            name: 'Long Tag',
            slug: 'a'.repeat(51) // 51 characters, max is 50
        });

        let err;
        try {
            await longTag.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.slug).toBeDefined();
    });
});

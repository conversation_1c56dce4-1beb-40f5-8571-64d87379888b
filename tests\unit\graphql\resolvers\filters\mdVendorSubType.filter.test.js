const { isValidObjectId } = require('../../../../../src/utils/validation.util');
const buildMdVendorSubTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdVendorSubType.filter');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdVendorSubTypeQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', () => {
        const result = buildMdVendorSubTypeQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid id filter', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);

        const result = buildMdVendorSubTypeQuery({ id: validId });

        expect(result).toEqual({ _id: validId });
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });

    it('should throw error with invalid id filter', () => {
        const invalidId = 'invalid-id';
        isValidObjectId.mockReturnValue(false);

        expect(() => {
            buildMdVendorSubTypeQuery({ id: invalidId });
        }).toThrow('Invalid id provided');

        expect(isValidObjectId).toHaveBeenCalledWith(invalidId);
    });

    it('should build query with name filter', () => {
        const name = 'Test Name';
        const result = buildMdVendorSubTypeQuery({ name });

        expect(result).toEqual({
            name: { $regex: name, $options: 'i' }
        });
    });

    it('should build query with description filter', () => {
        const description = 'Test Description';
        const result = buildMdVendorSubTypeQuery({ description });

        expect(result).toEqual({
            description: { $regex: description, $options: 'i' }
        });
    });

    it('should build query with multiple filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        const name = 'Test Name';
        const description = 'Test Description';
        
        isValidObjectId.mockReturnValue(true);

        const result = buildMdVendorSubTypeQuery({
            id: validId,
            name,
            description
        });

        expect(result).toEqual({
            _id: validId,
            name: { $regex: name, $options: 'i' },
            description: { $regex: description, $options: 'i' }
        });
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });
}); 
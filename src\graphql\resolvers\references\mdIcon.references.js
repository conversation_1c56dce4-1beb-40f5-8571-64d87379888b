const MdAppSettings = require("../../../models/MdAppSettings");
const MdFeature = require("../../../models/MdFeature");
const MdHostLayout = require("../../../models/MdHostLayout");
const MdPartner = require("../../../models/MdPartner");
const MdPartnerCategory = require("../../../models/MdPartnerCategory");
const MdVendorType = require("../../../models/MdVendorType");
const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];
    
    const mdFeatureReferences = await MdFeature.find({ icon: id });
    if (hasReferences(mdFeatureReferences)) {
        references.push('MdFeature');
    }

    const mdPartnerReferences = await MdPartner.find({ icon: id });
    if (hasReferences(mdPartnerReferences)) {
        references.push('MdPartner');
    }

    const mdPartnerCategoryReferences = await MdPartnerCategory.find({ icon: id });
    if (hasReferences(mdPartnerCategoryReferences)) {
        references.push('MdPartnerCategory');
    }

    const mdHostLayoutNavBarReferences = await MdHostLayout.find({
        $or: [
            { 'navBar.home': id },
            { 'navBar.planning': id },
            { 'navBar.guests': id },
            { 'navBar.apps': id },
            { 'navBar.messages': id }
        ]
    });
    if (hasReferences(mdHostLayoutNavBarReferences)) {
        references.push('MdHostLayout (navBar fields)');
    }

    const mdVendorTypeReferences = await MdVendorType.find({ icon: id });
    if (hasReferences(mdVendorTypeReferences)) {
        references.push('MdVendorType');
    }

    const mdAppSettingsReferences = await MdAppSettings.find({ 
        $or: [
            { 'navBars.userDashboard.tabs.home': id },
            { 'navBars.userDashboard.tabs.myEvents': id },
            { 'navBars.userDashboard.tabs.publicEvents': id },
            { 'navBars.hostEventDashboard.tabs.eventsDb': id },
            { 'navBars.hostEventDashboard.tabs.tasks': id },
            { 'navBars.hostEventDashboard.tabs.guests': id },
            { 'navBars.hostEventDashboard.tabs.apps': id },
            { 'navBars.hostEventDashboard.tabs.messages': id }
        ]
    });
    if (hasReferences(mdAppSettingsReferences)) {
        references.push('MdAppSettings');
    }

    return references;
}

module.exports = findReferences;
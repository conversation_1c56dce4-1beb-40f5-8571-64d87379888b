const HostLayoutResolvers = require('../../../../src/graphql/resolvers/hostLayout.resolver');
const MdHostLayout = require('../../../../src/models/MdHostLayout');
const { createResponse } = require('../../../../src/utils/response.util');
const mongoose = require('mongoose');

jest.mock('../../../../src/models/MdHostLayout');
jest.mock('../../../../src/utils/response.util', () => ({
    createResponse: jest.fn()
}));

describe('HostLayout Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Query', () => {
        describe('getHostLayout', () => {
            it('should return host layout successfully', async () => {
                const mockHostLayout = {
                    _id: new mongoose.Types.ObjectId(),
                    navBar: {
                        home: new mongoose.Types.ObjectId(),
                        planning: new mongoose.Types.ObjectId(),
                        guests: new mongoose.Types.ObjectId(),
                        apps: new mongoose.Types.ObjectId(),
                        messages: new mongoose.Types.ObjectId(),
                    },
                };

                MdHostLayout.findOne.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(mockHostLayout),
                });

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdHostLayout fetched successfully',
                    result: { hostLayout: mockHostLayout }
                });

                const result = await HostLayoutResolvers.Query.getHostLayout();

                expect(result.status).toBe('SUCCESS');
                expect(result.message).toBe('MdHostLayout fetched successfully');
                expect(result.result.hostLayout).toEqual(mockHostLayout);

                expect(MdHostLayout.findOne).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutResponse',
                    'SUCCESS',
                    'MdHostLayout fetched successfully',
                    { result: { hostLayout: mockHostLayout } }
                );
            });

            it('should handle errors during getHostLayout', async () => {
                const error = new Error('Fetch error');
                MdHostLayout.findOne.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    errors: [{ message: 'Fetch error' }]
                });

                const result = await HostLayoutResolvers.Query.getHostLayout();

                expect(result).toEqual({
                    status: 'FAILURE',
                    errors: [{ message: 'Fetch error' }]
                });
                expect(MdHostLayout.findOne).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutErrorResponse',
                    'FAILURE',
                    'Error getting MdHostLayout',
                    { errors: [{ field: 'getHostLayout', message: 'Fetch error' }] }
                );
            });


        });
    });

    describe('Mutation', () => {
        describe('createHostLayout', () => {
            it('should create a new host layout successfully', async () => {
                const mockInput = {
                    navBar: {
                        home: 'mockHomeIconId',
                        planning: 'mockPlanningIconId',
                        guests: 'mockGuestsIconId',
                        apps: 'mockAppsIconId',
                        messages: 'mockMessagesIconId'
                    }
                };

                const mockCreatedHostLayout = {
                    _id: 'mockHostLayoutId',
                    navBar: mockInput.navBar
                };

                const mockPopulatedHostLayout = {
                    ...mockCreatedHostLayout,
                    navBar: {
                        home: { _id: 'mockHomeIconId', name: 'Home Icon' },
                        planning: { _id: 'mockPlanningIconId', name: 'Planning Icon' },
                        guests: { _id: 'mockGuestsIconId', name: 'Guests Icon' },
                        apps: { _id: 'mockAppsIconId', name: 'Apps Icon' },
                        messages: { _id: 'mockMessagesIconId', name: 'Messages Icon' }
                    }
                };

                MdHostLayout.prototype.save = jest.fn().mockResolvedValue(mockCreatedHostLayout);
                MdHostLayout.findById = jest.fn().mockReturnValue({
                    populate: jest.fn().mockResolvedValue(mockPopulatedHostLayout)
                });

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdHostLayout created successfully',
                    result: { hostLayout: mockPopulatedHostLayout }
                });

                const result = await HostLayoutResolvers.Mutation.createHostLayout(null, { input: mockInput });

                expect(result.status).toBe('SUCCESS');
                expect(result.message).toBe('MdHostLayout created successfully');
                expect(result.result.hostLayout).toEqual(mockPopulatedHostLayout);

                expect(MdHostLayout.prototype.save).toHaveBeenCalled();
                //expect(MdHostLayout.findById).toHaveBeenCalledWith(mockCreatedHostLayout._id);
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutResponse',
                    'SUCCESS',
                    'MdHostLayout created successfully',
                    { result: { hostLayout: mockPopulatedHostLayout } }
                );
            });

            it('should handle errors during createHostLayout', async () => {
                const mockInput = {
                    navBar: {
                        home: 'mockHomeIconId',
                        planning: 'mockPlanningIconId',
                        guests: 'mockGuestsIconId',
                        apps: 'mockAppsIconId',
                        messages: 'mockMessagesIconId'
                    }
                };

                const error = new Error('Creation error');
                MdHostLayout.prototype.save = jest.fn().mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    errors: [{ message: 'Creation error' }]
                });

                const result = await HostLayoutResolvers.Mutation.createHostLayout(null, { input: mockInput });

                expect(result).toEqual({
                    status: 'FAILURE',
                    errors: [{ message: 'Creation error' }]
                });
                expect(MdHostLayout.prototype.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutErrorResponse',
                    'FAILURE',
                    'Error creating MdHostLayout',
                    { errors: [{ field: 'createHostLayout', message: 'Creation error' }] }
                );
            });
        });

        describe('updateHostLayout', () => {
            it('should update and return updated host layout', async () => {
                const id = '1';
                const input = { id: id, navBar: { home: 'newHomeId' } };

                const mockExistingHostLayout = {
                    id: id,
                    navBar: {
                        home: 'oldHomeId',
                        planning: 'oldPlanningId',
                        guests: 'oldGuestsId',
                        apps: 'oldAppsId',
                        messages: 'oldMessagesId'
                    }
                };

                const mockUpdatedHostLayout = {
                    ...mockExistingHostLayout,
                    navBar: {
                        ...mockExistingHostLayout.navBar,
                        home: 'newHomeId'
                    }
                };

                const mockSave = jest.fn().mockResolvedValue(mockUpdatedHostLayout);

                const mockMongooseDocument = {
                    ...mockUpdatedHostLayout,
                    save: mockSave
                };

                const mockPopulate = jest.fn().mockResolvedValue(mockUpdatedHostLayout);
                const mockFindById = jest.fn().mockReturnValue({
                    populate: mockPopulate,
                    ...mockMongooseDocument
                });

                MdHostLayout.findById = mockFindById;

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdHostLayout updated successfully',
                    result: { hostLayout: mockUpdatedHostLayout }
                });

                const result = await HostLayoutResolvers.Mutation.updateHostLayout(null, { id, input });

                expect(result.status).toBe('SUCCESS');
                expect(result.message).toBe('MdHostLayout updated successfully');
                expect(result.result.hostLayout).toEqual(mockUpdatedHostLayout);

                expect(MdHostLayout.findById).toHaveBeenCalledWith(id);
                expect(mockSave).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutResponse',
                    'SUCCESS',
                    'MdHostLayout updated successfully',
                    { result: { hostLayout: mockUpdatedHostLayout } }
                );
            });

            it('should return an error response when MdHostLayout is not found', async () => {
                const id = 'nonexistentId';
                const input = {
                    id: id,
                    navBar: {
                        home: 'newHomeId'
                    }
                };

                MdHostLayout.findById = jest.fn().mockResolvedValue(null);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'MdHostLayout not found',
                    errors: [{ field: 'updateHostLayout', message: 'MdHostLayout not found' }]
                });

                const result = await HostLayoutResolvers.Mutation.updateHostLayout(null, { id, input });

                expect(result.status).toBe('FAILURE');
                expect(result.message).toBe('MdHostLayout not found');
                expect(result.errors).toEqual([{ field: 'updateHostLayout', message: 'MdHostLayout not found' }]);

                expect(MdHostLayout.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutErrorResponse',
                    'FAILURE',
                    'MdHostLayout not found',
                    { errors: [{ field: 'updateHostLayout', message: 'MdHostLayout not found' }] }
                );
            });

            it('should return an error response when an error occurs during update', async () => {
                const id = 'validId';
                const input = {
                    id: id,
                    navBar: {
                        home: 'newHomeId'
                    }
                };

                const mockError = new Error('Database error');

                MdHostLayout.findById = jest.fn().mockRejectedValue(mockError);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error updating MdHostLayout',
                    errors: [{ field: 'updateHostLayout', message: 'Database error' }]
                });

                const result = await HostLayoutResolvers.Mutation.updateHostLayout(null, { id, input });

                expect(result.status).toBe('FAILURE');
                expect(result.message).toBe('Error updating MdHostLayout');
                expect(result.errors).toEqual([{ field: 'updateHostLayout', message: 'Database error' }]);

                expect(MdHostLayout.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutErrorResponse',
                    'FAILURE',
                    'Error updating MdHostLayout',
                    { errors: [{ field: 'updateHostLayout', message: 'Database error' }] }
                );
            });
        });

        describe('deleteHostLayout', () => {
            it('should delete a host layout successfully', async () => {
                const id = 'validId';
                const mockHostLayout = {
                    _id: id,
                    navBar: {
                        home: 'homeIconId',
                        planning: 'planningIconId',
                        guests: 'guestsIconId',
                        apps: 'appsIconId',
                        messages: 'messagesIconId'
                    }
                };

                MdHostLayout.findById = jest.fn().mockResolvedValue(mockHostLayout);
                MdHostLayout.findByIdAndDelete = jest.fn().mockResolvedValue(mockHostLayout);

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdHostLayout deleted successfully',
                    result: { hostLayout: mockHostLayout }
                });

                const result = await HostLayoutResolvers.Mutation.deleteHostLayout(null, { id });

                expect(result.status).toBe('SUCCESS');
                expect(result.message).toBe('MdHostLayout deleted successfully');
                expect(result.result.hostLayout).toEqual(mockHostLayout);

                expect(MdHostLayout.findById).toHaveBeenCalledWith(id);
                expect(MdHostLayout.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutResponse',
                    'SUCCESS',
                    'MdHostLayout deleted successfully',
                    { result: { hostLayout: mockHostLayout } }
                );
            });

            it('should return an error response when host layout is not found', async () => {
                const id = 'invalidId';

                MdHostLayout.findById = jest.fn().mockResolvedValue(null);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'MdHostLayout not found',
                    errors: [{ field: 'deleteHostLayout', message: 'MdHostLayout not found' }]
                });

                const result = await HostLayoutResolvers.Mutation.deleteHostLayout(null, { id });

                expect(result.status).toBe('FAILURE');
                expect(result.message).toBe('MdHostLayout not found');
                expect(result.errors).toEqual([{ field: 'deleteHostLayout', message: 'MdHostLayout not found' }]);

                expect(MdHostLayout.findById).toHaveBeenCalledWith(id);
                expect(MdHostLayout.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'HostLayoutErrorResponse',
                    'FAILURE',
                    'MdHostLayout not found',
                    { errors: [{ field: 'deleteHostLayout', message: 'MdHostLayout not found' }] }
                );
            });
        });
    });
});
const { gql } = require('graphql-tag');
const partyTypeDef = require('../../../../src/graphql/typedefs/party.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('partyTypeDef', () => {
  it('should contain the Party type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

    type WeatherCondition {
        text: String
        icon: String
    }

    type Weather {
        avgTempC: Float
        avgTempF: Float
        condition: WeatherCondition
    }

    type Party {
        id: ID!
        name: String!
        partyType: MdPartyType!
        time: DateTime!
        serviceLocation: MdServiceLocation
        expectedGuestCount: Int!
        actualGuestCount: Int!
        coHosts: [Host!]
        services: [PartyService!]
        vendorTypes: [MdVendorType!]
        totalBudget: Float!
        totalExpenditure: Float!
        guests: [Guest!]
        event: Event!
        weather: Weather
    }

    input PartyFilterInput {
        name: String
        timeRange: DateTimeRangeInput
        minExpectedGuests: Int
        maxExpectedGuests: Int
        minBudget: Float
        maxBudget: Float
        minExpenditure: Float
        maxExpenditure: Float
        serviceLocation: MdServiceLocationFilterInput
        vendorTypes: MdVendorTypeFilterInput
        eventId: ID
    }

    type PartyWrapper {
        party: Party!
    }

    type PartiesWrapper {
        parties: [Party]!
    }

    type PartyResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartyWrapper!
    }

    type PartiesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartiesWrapper!
        pagination: PaginationInfo!
    }

    type PartyErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union PartyResult = PartyResponse | PartyErrorResponse
    union PartiesResult = PartiesResponse | PartyErrorResponse

    type Query {
        getPartyById(id: ID!): PartyResult!
        getParties(filter: PartyFilterInput, pagination: PaginationInput): PartiesResult!
    }

    input CoHostInput {
        name: String!
        phone: String!
        email: String
    }

    input PartyInput {
        name: String!
        partyType: ID!
        time: DateTime!
        services: [ID!]
        totalBudget: Float!
        expectedGuestCount: Int!
        serviceLocation: ID
        vendorTypes: [ID!]
        eventId: ID!
        coHosts: [CoHostInput!]
    }

    input PartyUpdateInput {
        name: String
        partyType: ID
        time: DateTime
        coHosts: [CoHostInput!]
        services: [ID!]
        vendorTypes: [ID!]
        totalBudget: Float
        expectedGuestCount: Int
        serviceLocation: ID
        eventId: ID
    }

    type Mutation {
        createParty(input: PartyInput!): PartyResult!
        updateParty(id: ID!, input: PartyUpdateInput!): PartyResult!
        deleteParty(id: ID!): PartyResult!
    }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(partyTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});

const { Expo } = require('expo-server-sdk');

let expo = new Expo();

const testPushNotification = async (userId, expoToken) => {
  try {
    console.log(`Attempting to send push notification to user ${userId} with token: ${expoToken}`);
    
    if (!expoToken) {
      console.error('No Expo token provided');
      return { success: false, message: 'No Expo token provided' };
    }

    // Check that all your push tokens appear to be valid Expo push tokens
    if (!Expo.isExpoPushToken(expoToken)) {
      console.error(`Push token ${expoToken} is not a valid Expo push token`);
      return { success: false, message: 'Invalid Expo push token' };
    }

    // Create the notification payload
    const notificationContent = {
      to: expoToken,
      sound: 'default',
      title: '🔔 Server Test Notification',
      body: `This is a test notification for user ${userId} - tap to open`,
      priority: 'high',
      data: {
        userId,
        type: 'TEST_NOTIFICATION',
        timestamp: new Date().toISOString(),
        screen: 'Notifications'
      }
    };
    
    console.log('Sending notification with payload:', JSON.stringify(notificationContent));

    // Send the notification using Expo Server SDK
    const ticketChunk = await expo.sendPushNotificationsAsync([notificationContent]);
    const ticket = ticketChunk[0];
    
    console.log('Expo API response:', JSON.stringify(ticket));
    
    if (ticket.status === 'ok') {
      console.log(`Successfully sent notification to ${expoToken}, ID: ${ticket.id}`);
      return { 
        success: true, 
        message: 'Test notification sent successfully',
        data: {
          id: ticket.id,
          userId
        }
      };
    } else {
      console.error('Error sending test notification:', ticket);
      return {
        success: false,
        message: 'Failed to send test notification',
        error: ticket
      };
    }
  } catch (error) {
    console.error('Error in testPushNotification:', error);
    return { 
      success: false, 
      message: error.message || 'An unknown error occurred'
    };
  }
};

const sendPushNotifications = async (messages) => {
  try {
    // Create a filtered array of valid messages
    const validMessages = [];
    const invalidTokens = [];

    // Validate all tokens before sending
    for (let message of messages) {
      if (!message.to) {
        console.warn('Message missing destination token', message);
        continue;
      }

      // Check if the token is valid
      if (!Expo.isExpoPushToken(message.to)) {
        console.error(`Push token ${message.to} is not a valid Expo push token`);
        invalidTokens.push({
          token: message.to,
          error: 'Invalid Expo push token format'
        });
        continue;
      }

      // Add default properties if not specified
      const validMessage = {
        ...message,
        sound: message.sound || 'default',
        priority: message.priority || 'high'
      };
      
      validMessages.push(validMessage);
    }

    // If no valid messages, return early
    if (validMessages.length === 0) {
      return {
        success: false,
        message: 'No valid push tokens to send to',
        invalidTokens
      };
    }

    // Chunk the notifications to avoid rate limiting
    const chunks = expo.chunkPushNotifications(validMessages);
    const tickets = [];

    // Send each chunk of notifications
    for (let chunk of chunks) {
      try {
        const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        console.log('Sent push notifications chunk:', ticketChunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error('Error sending notification chunk:', error);
      }
    }

    // Process all tickets and handle errors
    const receiptIds = [];
    const errors = [];

    tickets.forEach((ticket, index) => {
      if (ticket.status === 'error') {
        errors.push({
          token: validMessages[index].to,
          error: ticket.details?.error || 'Unknown error',
          details: ticket.details
        });

        // Check if token is no longer valid and should be removed
        if (ticket.details?.error === 'DeviceNotRegistered') {
          invalidTokens.push({
            token: validMessages[index].to,
            error: 'DeviceNotRegistered'
          });
        }
      } else if (ticket.status === 'ok') {
        receiptIds.push({
          receiptId: ticket.id,
          token: validMessages[index].to
        });
      }
    });

    return {
      success: true,
      tickets,
      receiptIds,
      errors,
      invalidTokens
    };
  } catch (error) {
    console.error('Error in sendPushNotifications:', error);
    return {
      success: false,
      message: error.message || 'An unknown error occurred'
    };
  }
};

const checkPushNotificationReceipts = async (receiptIds) => {
  try {
    if (!receiptIds || receiptIds.length === 0) {
      return {
        success: false,
        message: 'No receipt IDs provided'
      };
    }

    // Chunk the receipt IDs to avoid rate limiting
    const receiptIdChunks = expo.chunkPushNotificationReceiptIds(receiptIds);
    const receipts = {};
    const invalidTokens = [];

    // Check each chunk of receipts
    for (let chunk of receiptIdChunks) {
      try {
        const receiptChunk = await expo.getPushNotificationReceiptsAsync(chunk);
        console.log('Push notification receipts:', receiptChunk);
        
        // Process receipts and collect tokens that should be removed
        Object.entries(receiptChunk).forEach(([receiptId, receipt]) => {
          receipts[receiptId] = receipt;
          
          if (receipt.status === 'error') {
            console.error(`Error in receipt for ID ${receiptId}:`, receipt);
            
            // If the device is no longer registered, mark the token for removal
            if (receipt.details?.error === 'DeviceNotRegistered') {
              invalidTokens.push({
                receiptId,
                error: 'DeviceNotRegistered'
              });
            }
          }
        });
      } catch (error) {
        console.error('Error checking receipts chunk:', error);
      }
    }

    return {
      success: true,
      receipts,
      invalidTokens
    };
  } catch (error) {
    console.error('Error in checkPushNotificationReceipts:', error);
    return {
      success: false,
      message: error.message || 'An unknown error occurred'
    };
  }
};

const sendUserNotification = async (userId, expoToken, content) => {
  try {
    if (!expoToken) {
      console.error('No Expo token provided');
      return { success: false, message: 'No Expo token provided' };
    }

    if (!Expo.isExpoPushToken(expoToken)) {
      console.error(`Push token ${expoToken} is not a valid Expo push token`);
      return { success: false, message: 'Invalid Expo push token' };
    }

    // Ensure required content fields
    if (!content.title || !content.body) {
      return {
        success: false,
        message: 'Notification requires at least title and body'
      };
    }

    // Create the notification payload with defaults and user data
    const notificationContent = {
      to: expoToken,
      sound: content.sound || 'default',
      title: content.title,
      body: content.body,
      priority: content.priority || 'high',
      data: {
        userId,
        ...(content.data || {}),
        timestamp: new Date().toISOString()
      }
    };

    // Add optional fields if present
    if (content.badge) notificationContent.badge = content.badge;
    if (content.channelId) notificationContent.channelId = content.channelId;

    console.log('Sending notification with payload:', JSON.stringify(notificationContent));

    // Send the notification using Expo Server SDK
    const ticketChunk = await expo.sendPushNotificationsAsync([notificationContent]);
    const ticket = ticketChunk[0];
    
    console.log('Expo API response:', JSON.stringify(ticket));
    
    if (ticket.status === 'ok') {
      console.log(`Successfully sent notification to ${expoToken}, ID: ${ticket.id}`);
      return { 
        success: true, 
        message: 'Notification sent successfully',
        data: {
          id: ticket.id,
          userId
        }
      };
    } else {
      console.error('Error sending notification:', ticket);
      return {
        success: false,
        message: 'Failed to send notification',
        error: ticket
      };
    }
  } catch (error) {
    console.error('Error in sendUserNotification:', error);
    return { 
      success: false, 
      message: error.message || 'An unknown error occurred'
    };
  }
};

const sendMultiUserNotification = async (userTokens, content) => {
  try {
    if (!userTokens || !Array.isArray(userTokens) || userTokens.length === 0) {
      return {
        success: false,
        message: 'No user tokens provided'
      };
    }

    // Create messages for each user token
    const messages = userTokens.map(userToken => ({
      to: userToken.token,
      sound: content.sound || 'default',
      title: content.title,
      body: content.body,
      priority: content.priority || 'high',
      data: {
        userId: userToken.userId,
        ...(content.data || {}),
        timestamp: new Date().toISOString()
      },
      ...(content.badge ? { badge: content.badge } : {}),
      ...(content.channelId ? { channelId: content.channelId } : {})
    }));

    // Use the bulk send function
    return await sendPushNotifications(messages);
  } catch (error) {
    console.error('Error in sendMultiUserNotification:', error);
    return {
      success: false,
      message: error.message || 'An unknown error occurred'
    };
  }
};

const directTokenTest = async (token) => {
  try {
    console.log(`Testing direct token: ${token}`);
    
    if (!token) {
      return { success: false, message: 'No token provided' };
    }

    // Check that the token appears to be a valid Expo push token
    if (!Expo.isExpoPushToken(token)) {
      console.error(`Push token ${token} is not a valid Expo push token`);
      return { 
        success: false, 
        message: 'Invalid Expo push token format',
        details: 'Token should start with ExponentPushToken[ and end with ]' 
      };
    }

    // Create a test notification
    const message = {
      to: token,
      sound: 'default',
      title: '🧪 Direct Token Test',
      body: 'This is a direct test of your Expo push token',
      priority: 'high',
      data: {
        type: 'TOKEN_TEST',
        timestamp: new Date().toISOString()
      }
    };

    // Send the notification
    const ticketChunk = await expo.sendPushNotificationsAsync([message]);
    const ticket = ticketChunk[0];
    
    console.log('Direct token test response:', JSON.stringify(ticket));
    
    if (ticket.status === 'ok') {
      return { 
        success: true, 
        message: 'Token test successful! Check your device for the notification',
        receipt: ticket.id
      };
    } else {
      return {
        success: false,
        message: 'Token test failed',
        error: ticket.details?.error || 'Unknown error',
        details: ticket
      };
    }
  } catch (error) {
    console.error('Error in directTokenTest:', error);
    return { 
      success: false, 
      message: error.message || 'An unknown error occurred'
    };
  }
};

module.exports = {
  sendPushNotifications,
  sendMultiUserNotification,
  testPushNotification,
  directTokenTest,
  checkPushNotificationReceipts,
  sendUserNotification
}; 
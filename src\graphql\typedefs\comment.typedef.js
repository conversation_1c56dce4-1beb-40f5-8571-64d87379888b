const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Comment {
        id: ID!
        content: String!
        createdBy: User!
        mentions: [User!]
        reactions: [Reaction!]
        createdAt: DateTime
        updatedAt: DateTime
    }

    input CommentFilterInput {
        content: String
        createdBy: UserFilterInput
        createdAt: DateTimeRangeInput
        updatedAt: DateTimeRangeInput
        mentions: [UserFilterInput!]
    }

    type CommentWrapper {
        comment: Comment!
    }

    type CommentsWrapper {
        comments: [Comment]!
    }

    type CommentResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: CommentWrapper!
    }

    type CommentsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: CommentsWrapper!
        pagination: PaginationInfo!
    }

    type CommentErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union CommentResult = CommentResponse | CommentErrorResponse
    union CommentsResult = CommentsResponse | CommentErrorResponse

    type Query {
        getCommentById(id: ID!): CommentResult!
        getComments(filter: CommentFilterInput, pagination: PaginationInput): CommentsResult!
    }

    input CommentInput {
        content: String!
        mentions: [ID!]
        reactions: [ID!]
    }

    input CommentUpdateInput {
        content: String
        mentions: [ID!]
        reactions: [ID!]
    }

    type Mutation {
        createComment(input: CommentInput!): CommentResult!
        updateComment(id: ID!, input: CommentUpdateInput!): CommentResult!
        deleteComment(id: ID!): CommentResult!

        createCommentReaction(commentId: ID!, reactionId: ID!): CommentResult!
        deleteCommentReaction(commentId: ID!, reactionId: ID!): CommentResult!
        deleteUserCommentReaction(commentId: ID!): CommentResult!
    }
`;
const mongoose = require('mongoose');
const vendorResolvers = require('../../../../src/graphql/resolvers/vendor.resolver');
const VendorUser = require('../../../../src/models/VendorUser');
const Address = require('../../../../src/models/Address');
const MdServiceLocation = require('../../../../src/models/MdServiceLocation');
const VendorService = require('../../../../src/models/VendorService');
const VendorRating = require('../../../../src/models/VendorRating');
const { createResponse } = require('../../../../src/utils/response.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildVendorQuery = require('../../../../src/graphql/resolvers/filters/vendor.filter');
const Vendor = require('../../../../src/models/Vendor');
const findReferences = require('../../../../src/graphql/resolvers/references/vendor.references');

jest.mock('../../../../src/models/Vendor');
jest.mock('../../../../src/models/VendorUser');
jest.mock('../../../../src/models/Address');
jest.mock('../../../../src/models/MdServiceLocation');
jest.mock('../../../../src/models/VendorService');
jest.mock('../../../../src/models/VendorRating');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/vendor.filter');
jest.mock('../../../../src/graphql/resolvers/references/vendor.references');

describe('vendorResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Vendor Field Resolvers', () => {
        describe('primaryContact', () => {
            it('should return primary contact for a vendor', async () => {
                const vendor = {
                    primaryContact: 'contactId'
                };
                const mockContact = { _id: 'contactId', name: 'John Doe' };

                VendorUser.findById.mockResolvedValue(mockContact);

                const result = await vendorResolvers.Vendor.primaryContact(vendor);

                expect(VendorUser.findById).toHaveBeenCalledWith(vendor.primaryContact);
                expect(result).toEqual(mockContact);
            });

            it('should throw error when fetching primary contact fails', async () => {
                const vendor = {
                    primaryContact: 'contactId'
                };
                const error = new Error('Database error');

                VendorUser.findById.mockRejectedValue(error);
                console.error = jest.fn();

                await expect(vendorResolvers.Vendor.primaryContact(vendor))
                    .rejects
                    .toThrow('Error getting primary contact');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });

        describe('contacts', () => {
            it('should return contacts for a vendor', async () => {
                const parent = {
                    contacts: ['contact1', 'contact2']
                };
                const mockContacts = [
                    { _id: 'contact1', name: 'Contact 1' },
                    { _id: 'contact2', name: 'Contact 2' }
                ];
    
                VendorUser.find.mockResolvedValue(mockContacts);
    
                const result = await vendorResolvers.Vendor.contacts(parent);
    
                expect(VendorUser.find).toHaveBeenCalledWith({ _id: { $in: parent.contacts } });
                expect(result).toEqual(mockContacts);
            });
    
            it('should throw error when fetching contacts fails', async () => {
                const parent = {
                    contacts: ['contact1']
                };
                const error = new Error('Database error');
    
                VendorUser.find.mockRejectedValue(error);
                console.error = jest.fn();
    
                await expect(vendorResolvers.Vendor.contacts(parent))
                    .rejects
                    .toThrow('Error getting contacts');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    
        describe('businessAddress', () => {
            it('should return business address for a vendor', async () => {
                const parent = {
                    businessAddress: 'address1'
                };
                const mockAddress = { _id: 'address1', street: '123 Main St' };
    
                Address.findById.mockResolvedValue(mockAddress);
    
                const result = await vendorResolvers.Vendor.businessAddress(parent);
    
                expect(Address.findById).toHaveBeenCalledWith(parent.businessAddress);
                expect(result).toEqual(mockAddress);
            });
    
            it('should throw error when fetching business address fails', async () => {
                const parent = {
                    businessAddress: 'address1'
                };
                const error = new Error('Database error');
    
                Address.findById.mockRejectedValue(error);
                console.error = jest.fn();
    
                await expect(vendorResolvers.Vendor.businessAddress(parent))
                    .rejects
                    .toThrow('Error getting business address');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    
        describe('serviceLocations', () => {
            it('should return service locations for a vendor', async () => {
                const parent = {
                    serviceLocations: ['location1', 'location2']
                };
                const mockLocations = [
                    { _id: 'location1', name: 'Location 1' },
                    { _id: 'location2', name: 'Location 2' }
                ];
    
                MdServiceLocation.find.mockResolvedValue(mockLocations);
    
                const result = await vendorResolvers.Vendor.serviceLocations(parent);
    
                expect(MdServiceLocation.find).toHaveBeenCalledWith({ _id: { $in: parent.serviceLocations } });
                expect(result).toEqual(mockLocations);
            });
    
            it('should throw error when fetching service locations fails', async () => {
                const parent = {
                    serviceLocations: ['location1']
                };
                const error = new Error('Database error');
    
                MdServiceLocation.find.mockRejectedValue(error);
                console.error = jest.fn();
    
                await expect(vendorResolvers.Vendor.serviceLocations(parent))
                    .rejects
                    .toThrow('Error getting service locations');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    
        describe('servicesProvided', () => {
            it('should return services provided for a vendor', async () => {
                const parent = {
                    servicesProvided: ['service1', 'service2']
                };
                const mockServices = [
                    { _id: 'service1', name: 'Service 1' },
                    { _id: 'service2', name: 'Service 2' }
                ];
    
                VendorService.find.mockResolvedValue(mockServices);
    
                const result = await vendorResolvers.Vendor.servicesProvided(parent);
    
                expect(VendorService.find).toHaveBeenCalledWith({ _id: { $in: parent.servicesProvided } });
                expect(result).toEqual(mockServices);
            });
    
            it('should throw error when fetching services provided fails', async () => {
                const parent = {
                    servicesProvided: ['service1']
                };
                const error = new Error('Database error');
    
                VendorService.find.mockRejectedValue(error);
                console.error = jest.fn();
    
                await expect(vendorResolvers.Vendor.servicesProvided(parent))
                    .rejects
                    .toThrow('Error getting services provided');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });

        describe('ratingAggregates', () => {
            it('should return rating aggregates for a vendor', async () => {
                const parent = { _id: 'vendorId' };
                const mockAggregateResult = [{
                    averageRating: 4.5,
                    totalRating: 10
                }];

                VendorRating.aggregate.mockResolvedValue(mockAggregateResult);

                const result = await vendorResolvers.Vendor.ratingAggregates(parent);

                expect(VendorRating.aggregate).toHaveBeenCalledWith([
                    { $match: { vendor: parent._id } },
                    {
                        $group: {
                            _id: null,
                            averageRating: { $avg: '$rating' },
                            totalRating: { $sum: 1 }
                        }
                    }
                ]);
                expect(result).toEqual({
                    averageRating: 4.5,
                    totalRating: 10
                });
            });

            it('should return zero ratings when no ratings exist', async () => {
                const parent = { _id: 'vendorId' };
                VendorRating.aggregate.mockResolvedValue([]);

                const result = await vendorResolvers.Vendor.ratingAggregates(parent);

                expect(result).toEqual({
                    averageRating: 0,
                    totalRating: 0
                });
            });

            it('should throw error when aggregation fails', async () => {
                const parent = { _id: 'vendorId' };
                const error = new Error('Aggregation error');
                
                VendorRating.aggregate.mockRejectedValue(error);

                await expect(vendorResolvers.Vendor.ratingAggregates(parent))
                  .rejects
                  .toThrow('Error calculating rating aggregates');

                expect(VendorRating.aggregate).toHaveBeenCalledWith([
                  { $match: { vendor: parent._id } },
                  {
                    $group: {
                      _id: null,
                      averageRating: { $avg: '$rating' },
                      totalRating: { $sum: 1 }
                    }
                  }
                ]);

                expect(console.error).toHaveBeenCalledWith('Error calculating rating aggregates:', error);
            });
        });

        describe('reviews', () => {
            it('should return reviews for a vendor', async () => {
                const parent = {
                    id: 'vendorId123'
                };
                const mockReviews = [
                    { _id: 'review1', rating: 5 },
                    { _id: 'review2', rating: 4 }
                ];

                VendorRating.find.mockResolvedValue(mockReviews);

                const result = await vendorResolvers.Vendor.reviews(parent);

                expect(VendorRating.find).toHaveBeenCalledWith({ vendor: parent.id });
                expect(result).toEqual(mockReviews);
            });

            it('should throw error when fetching reviews fails', async () => {
                const parent = {
                    id: 'vendorId123'
                };
                const error = new Error('Database error');

                VendorRating.find.mockRejectedValue(error);

                await expect(vendorResolvers.Vendor.reviews(parent))
                    .rejects
                    .toThrow('Error getting reviews');

                expect(VendorRating.find).toHaveBeenCalledWith({ vendor: parent.id });
            });
        });
    });

    describe('Query', () => {
        describe('getVendorById', () => {
            it('should return vendor from cache if available', async () => {
                const id = 'vendorId';
                const cachedVendor = { _id: id, name: 'Cached Vendor' };
                
                getByIdCache.mockResolvedValue(cachedVendor);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Vendor retrieved successfully',
                    result: { vendor: cachedVendor }
                });

                const result = await vendorResolvers.Query.getVendorById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Vendor.findById).not.toHaveBeenCalled();
                expect(setCache).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorResponse',
                    'SUCCESS',
                    'Vendor retrieved successfully',
                    { result: { vendor: cachedVendor } }
                );
            });

            it('should return error response if vendor not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                Vendor.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Vendor not found',
                    errors: [{ field: 'id', message: 'Vendor not found' }]
                });

                const result = await vendorResolvers.Query.getVendorById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Vendor.findById).toHaveBeenCalledWith(id);
                expect(setCache).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Vendor not found',
                    { errors: [{ field: 'id', message: 'Vendor not found' }] }
                );
            });

            it('should handle errors during vendor retrieval', async () => {
                const id = 'vendorId';
                const error = new Error('Database error');
                
                getByIdCache.mockResolvedValue(null);
                Vendor.findById.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving vendor',
                    errors: [{ field: 'getVendorById', message: error.message }]
                });

                const result = await vendorResolvers.Query.getVendorById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Vendor.findById).toHaveBeenCalledWith(id);
                expect(setCache).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Error retrieving vendor',
                    { errors: [{ field: 'getVendorById', message: error.message }] }
                );
            });
        });

        describe('getVendors', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return vendors with pagination when found', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const pipeline = [{ $match: { name: { $regex: 'test', $options: 'i' } } }];
                const vendors = [
                    { _id: '1', name: 'Vendor 1' },
                    { _id: '2', name: 'Vendor 2' }
                ];

                buildVendorQuery.mockResolvedValue(pipeline);

                Vendor.aggregate.mockImplementation((pipe) => {
                    if (pipe[pipe.length - 1].$count) {
                        return Promise.resolve([{ total: 2 }]);
                    }
                    return Promise.resolve(vendors);
                });

                const paginationInfo = {
                    totalItems: 2,
                    totalPages: 1,
                    currentPage: 1,
                    pageSize: 10,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                const mappedVendors = vendors.map(vendor => ({
                    ...vendor,
                    id: vendor._id.toString()
                }));

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Vendors fetched successfully',
                    result: { vendors: mappedVendors },
                    pagination: paginationInfo
                });

                const result = await vendorResolvers.Query.getVendors(null, { filter, pagination });

                expect(buildVendorQuery).toHaveBeenCalledWith(filter);
                expect(Vendor.aggregate).toHaveBeenCalledTimes(2);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorsResponse',
                    'SUCCESS',
                    'Vendors fetched successfully',
                    {
                        result: { vendors: mappedVendors },
                        pagination: paginationInfo
                    }
                );
                expect(result.status).toBe('SUCCESS');
            });

            it('should return failure response when no vendors found', async () => {
                const filter = { name: 'nonexistent' };
                const pagination = { limit: 10, skip: 0 };
                const pipeline = [{ $match: { name: { $regex: 'nonexistent', $options: 'i' } } }];
                const vendors = [];

                buildVendorQuery.mockResolvedValue(pipeline);

                Vendor.aggregate.mockImplementation((pipe) => {
                    if (pipe[pipe.length - 1].$count) {
                        return Promise.resolve([{ total: 0 }]);
                    }
                    return Promise.resolve(vendors);
                });

                const paginationInfo = {
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                    pageSize: 10,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'No vendors found',
                    result: { vendors },
                    pagination: paginationInfo
                });

                const result = await vendorResolvers.Query.getVendors(null, { filter, pagination });

                expect(buildVendorQuery).toHaveBeenCalledWith(filter);
                expect(Vendor.aggregate).toHaveBeenCalledTimes(2);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorsResponse',
                    'FAILURE',
                    'No vendors found',
                    {
                        result: { vendors },
                        pagination: paginationInfo
                    }
                );
                expect(result.status).toBe('FAILURE');
            });

            it('should handle errors and return error response', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                buildVendorQuery.mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving vendors',
                    errors: [{ field: 'getVendors', message: error.message }]
                });

                const result = await vendorResolvers.Query.getVendors(null, { filter, pagination });

                expect(buildVendorQuery).toHaveBeenCalledWith(filter);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Error retrieving vendors',
                    {
                        errors: [{ field: 'getVendors', message: error.message }]
                    }
                );
                expect(result.status).toBe('FAILURE');
            });
        });
    });

    describe('Mutation', () => {
        describe('createVendor', () => {
            it('should create and return the vendor', async () => {
                const input = { name: 'New Vendor', description: 'Test Description' };
                const vendorMock = {
                    _id: 'vendorId',
                    ...input,
                    save: jest.fn().mockResolvedValue(true)
                };
                
                Vendor.mockImplementation(() => vendorMock);
                createResponse.mockReturnValue('successResponse');

                const result = await vendorResolvers.Mutation.createVendor(null, { input });

                expect(Vendor).toHaveBeenCalledWith(input);
                expect(vendorMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorResponse',
                    'SUCCESS',
                    'Vendor created successfully',
                    { result: { vendor: vendorMock } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if creation fails', async () => {
                const input = { name: 'New Vendor' };
                const error = new Error('Database error');

                Vendor.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue('errorResponse');

                const result = await vendorResolvers.Mutation.createVendor(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Error creating vendor',
                    { errors: [{ field: 'createVendor', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteVendor', () => {
            it('should check for references before deletion', async () => {
                const id = 'vendorId';
                const references = ['PartyService'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Vendor cannot be deleted',
                    errors: [{
                        field: 'deleteVendor',
                        message: 'Vendor cannot be deleted as it has references in the following collections: PartyService'
                    }]
                });

                const result = await vendorResolvers.Mutation.deleteVendor(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Vendor.findByIdAndDelete).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Vendor cannot be deleted',
                    errors: [{
                        field: 'deleteVendor',
                        message: 'Vendor cannot be deleted as it has references in the following collections: PartyService'
                    }]
                });
            });

            it('should delete vendor when no references exist', async () => {
                const id = 'vendorId';
                const mockVendor = { _id: id, name: 'Test Vendor' };
                
                findReferences.mockResolvedValue([]);
                Vendor.findByIdAndDelete.mockResolvedValue(mockVendor);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Vendor deleted successfully',
                    result: { vendor: mockVendor }
                });

                const result = await vendorResolvers.Mutation.deleteVendor(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Vendor.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Vendor deleted successfully',
                    result: { vendor: mockVendor }
                });
            });

            it('should return error response if vendor not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                Vendor.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Vendor not found',
                    errors: [{ field: 'id', message: 'Vendor not found' }]
                });

                const result = await vendorResolvers.Mutation.deleteVendor(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Vendor.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Vendor not found',
                    errors: [{ field: 'id', message: 'Vendor not found' }]
                });
            });

            it('should handle errors during deletion', async () => {
                const id = 'vendorId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Vendor.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting vendor',
                    errors: [{ field: 'deleteVendor', message: 'Database error' }]
                });

                const result = await vendorResolvers.Mutation.deleteVendor(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Vendor.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting vendor',
                    errors: [{ field: 'deleteVendor', message: 'Database error' }]
                });
            });
        });

        describe('updateVendor', () => {
            it('should update and return vendor successfully', async () => {
                const id = 'vendorId';
                const input = { 
                    name: 'Updated Vendor Name',
                    description: 'Updated description'
                };
                const updatedVendor = { 
                    _id: id,
                    ...input
                };

                Vendor.findByIdAndUpdate.mockResolvedValue(updatedVendor);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await vendorResolvers.Mutation.updateVendor(null, { id, input });

                expect(Vendor.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorResponse',
                    'SUCCESS',
                    'Vendor updated successfully',
                    { result: { vendor: updatedVendor } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if vendor not found', async () => {
                const id = 'nonexistentId';
                const input = { name: 'Updated Name' };

                Vendor.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await vendorResolvers.Mutation.updateVendor(null, { id, input });

                expect(Vendor.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Vendor not found',
                    { errors: [{ field: 'id', message: 'Vendor not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if update fails', async () => {
                const id = 'vendorId';
                const input = { name: 'Updated Name' };
                const error = new Error('Database error');

                Vendor.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await vendorResolvers.Mutation.updateVendor(null, { id, input });

                expect(Vendor.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'VendorErrorResponse',
                    'FAILURE',
                    'Error updating vendor',
                    { errors: [{ field: 'updateVendor', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});

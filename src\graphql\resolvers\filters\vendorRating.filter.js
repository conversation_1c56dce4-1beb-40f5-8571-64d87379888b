const mongoose = require('mongoose');
const { isValidObjectId } = require('../../../utils/validation.util');

const buildVendorRatingQuery = (filters) => {
    const pipeline = [];

    if (filters) {
        const matchConditions = {};

        if (filters.userFirstName || filters.userLastName) {
            pipeline.push({
                $lookup: {
                    from: 'users',
                    localField: 'user',
                    foreignField: '_id',
                    as: 'userDetails'
                }
            });

            pipeline.push({ $unwind: '$userDetails' });

            if (filters.userFirstName) {
                matchConditions['userDetails.firstName'] = { $regex: new RegExp(filters.userFirstName, 'i') };
            }
            if (filters.userLastName) {
                matchConditions['userDetails.lastName'] = { $regex: new RegExp(filters.userLastName, 'i') };
            }
        }

        if (filters.vendorName) {
            pipeline.push({
                $lookup: {
                    from: 'vendors',
                    localField: 'vendor',
                    foreignField: '_id',
                    as: 'vendorDetails'
                }
            });

            pipeline.push({ $unwind: '$vendorDetails' });
            matchConditions['vendorDetails.name'] = { $regex: new RegExp(filters.vendorName, 'i') };
        }

        if (filters.vendorId) {
            if (isValidObjectId(filters.vendorId)) {
                matchConditions.vendor = filters.vendorId;
            } else {
                throw new Error('Invalid vendor ID provided');
            }
        }

        if (filters.minRating !== undefined || filters.maxRating !== undefined) {
            matchConditions.rating = {};
            if (filters.minRating !== undefined) {
                matchConditions.rating.$gte = filters.minRating;
            }
            if (filters.maxRating !== undefined) {
                matchConditions.rating.$lte = filters.maxRating;
            }
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }
    }

    pipeline.push({
        $project: {
            _id: 1,
            user: 1,
            vendor: 1,
            rating: 1,
            comment: 1,
            userDetails: 1,
            vendorDetails: 1
        }
    });

    return pipeline;
};

module.exports = buildVendorRatingQuery;

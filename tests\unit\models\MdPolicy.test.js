const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdPolicy = require('../../../src/models/MdPolicy');
const Tag = require('../../../src/models/Tag');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdPolicy Model Test', () => {
    it('should create and save a MdPolicy successfully', async () => {
        const mockIcon = new MdIcon({
            name: 'Test Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const mockTag = new Tag({
            name: 'Test Tag',
            icon: mockIcon._id,
            slug: 'test-tag'
        });
        await mockTag.save();

        const validMdPolicy = new MdPolicy({
            name: 'Test Policy',
            description: 'This is a test policy',
            tags: [mockTag._id]
        });

        const savedMdPolicy = await validMdPolicy.save();

        expect(savedMdPolicy._id).toBeDefined();
        expect(savedMdPolicy.name).toBe('Test Policy');
        expect(savedMdPolicy.description).toBe('This is a test policy');
        expect(savedMdPolicy.tags).toHaveLength(1);
        expect(savedMdPolicy.tags[0]).toEqual(mockTag._id);
        expect(savedMdPolicy.createdAt).toBeDefined();
        expect(savedMdPolicy.updatedAt).toBeDefined();
    });

    it('should fail to create a MdPolicy without required fields', async () => {
        const mdPolicy = new MdPolicy();

        let err;
        try {
            await mdPolicy.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });

    it('should create a MdPolicy without tags', async () => {
        const validMdPolicy = new MdPolicy({
            name: 'Test Policy Without Tags',
            description: 'This is a test policy without tags'
        });

        const savedMdPolicy = await validMdPolicy.save();

        expect(savedMdPolicy._id).toBeDefined();
        expect(savedMdPolicy.name).toBe('Test Policy Without Tags');
        expect(savedMdPolicy.description).toBe('This is a test policy without tags');
        expect(savedMdPolicy.tags).toHaveLength(0);
    });

    it('should fail to create a MdPolicy with invalid tag reference', async () => {
        const invalidMdPolicy = new MdPolicy({
            name: 'Invalid Policy',
            description: 'This policy has an invalid tag reference',
            tags: ['invalid_id']
        });

        let err;
        try {
            await invalidMdPolicy.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors['tags.0']).toBeDefined();
    });
}); 
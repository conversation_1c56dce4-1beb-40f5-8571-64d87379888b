const MdVendorType = require('../../models/MdVendorType');
const MdFeature = require('../../models/MdFeature');
const MdIcon = require('../../models/MdIcon');
const MdPartner = require('../../models/MdPartner');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { createResponse } = require('../../utils/response.util');
const buildMdVendorTypeQuery = require('./filters/mdVendorType.filter');
const findReferences = require('./references/mdVendorType.references');

const mdVendorTypeResolvers = {
    MdVendorType: {
        primaryFeature: async (parent) => {
            return parent.primaryFeature ? await MdFeature.findById(parent.primaryFeature) : null;
        },
        features: async (parent) => {
            return await MdFeature.find({ _id: { $in: parent.features } });
        },
        mainFeatures: async (parent) => {
            return await MdFeature.find({ _id: { $in: parent.mainFeatures } });
        },
        icon: async (parent) => {
            return parent.icon ? await MdIcon.findById(parent.icon) : null;
        },
        partners: async (parent) => {
            return await MdPartner.find({ _id: { $in: parent.partners } });
        },
    },

    Query: {
        getMdVendorTypeById: async (_, { id }) => {
            try {
                let mdVendorType = await getByIdCache(id) || await MdVendorType.findById(id);
                if (mdVendorType) {
                    await setCache(id, mdVendorType);
                } else {
                    return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType not found', { errors: [{ field: 'id', message: 'MdVendorType not found' }] });
                }
                return createResponse('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType fetched successfully', { result: { mdVendorType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'Error getting MdVendorType', { errors: [{ field: 'getMdVendorTypeById', message: error.message }] });
            }
        },

        getMdVendorTypes: async (_, { filter, pagination }) => {
            try {
                const query = buildMdVendorTypeQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdVendorType, query, limit, skip);

                const mdVendorTypes = await MdVendorType.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdVendorTypes.length === 0) {
                    return createResponse('MdVendorTypesResponse', 'FAILURE', 'No MdVendorTypes found', { result: { mdVendorTypes }, pagination: paginationInfo });
                }
                return createResponse('MdVendorTypesResponse', 'SUCCESS', 'MdVendorTypes fetched successfully', { result: { mdVendorTypes }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'Error getting MdVendorTypes', { errors: [{ field: 'getMdVendorTypes', message: error.message }] });
            }
        },
    },

    Mutation: {
        createMdVendorType: async (_, { input }) => {
            try {
                const mdVendorType = new MdVendorType(input);
                await mdVendorType.save();
                return createResponse('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType created successfully', { result: { mdVendorType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'Error creating MdVendorType', { errors: [{ field: 'createMdVendorType', message: error.message }] });
            }
        },

        updateMdVendorType: async (_, { id, input }) => {
            try {
                const mdVendorType = await MdVendorType.findByIdAndUpdate(id, input, { new: true });
                if (!mdVendorType) {
                    return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType not found', { errors: [{ field: 'updateMdVendorType', message: 'MdVendorType not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType updated successfully', { result: { mdVendorType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'Error updating MdVendorType', { errors: [{ field: 'updateMdVendorType', message: error.message }] });
            }
        },

        deleteMdVendorType: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType cannot be deleted', {
                        errors: [{ field: 'deleteMdVendorType', message: `MdVendorType cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }

                const mdVendorType = await MdVendorType.findByIdAndDelete(id);
                if (!mdVendorType) {
                    return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType not found', { errors: [{ field: 'deleteMdVendorType', message: 'MdVendorType not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType deleted successfully', { result: { mdVendorType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorTypeErrorResponse', 'FAILURE', 'Error deleting MdVendorType', { errors: [{ field: 'deleteMdVendorType', message: error.message }] });
            }
        },
    },
};

module.exports = mdVendorTypeResolvers;
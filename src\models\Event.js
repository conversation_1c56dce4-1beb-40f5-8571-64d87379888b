const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const eventSchema = new Schema({
  name: { type: String, required: true },
  description: { type: String, required: true },
  eventType: { type: Schema.Types.ObjectId, ref: 'MdEventType', required: false },
  mainHost: { type: Schema.Types.ObjectId, ref: 'Host', required: true },
  coHosts: [{ type: Schema.Types.ObjectId, ref: 'Host' }],
  guests: [{ type: Schema.Types.ObjectId, ref: 'Guest' }],
  location: { type: Schema.Types.ObjectId, ref: 'MdServiceLocation', required: false },
  startDate: { type: Date },
  endDate: { type: Date },
  eventGroupId: { type: Schema.Types.ObjectId, ref: 'EventGroup' },
  eventStatus: { type: String, enum: ['PLANNED', 'CONFIRMED'], default: 'CONFIRMED' },
}, { timestamps: true });

const Event = model('Event', eventSchema);

module.exports = Event;
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdPromotion = require('../../../src/models/MdPromotion');
const MdPartner = require('../../../src/models/MdPartner');
const Tag = require('../../../src/models/Tag');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdPromotion Model Test', () => {
    it('should create and save a MdPromotion successfully', async () => {
        const mockPartner = new MdPartner({
            name: 'Test Partner',
            category: new mongoose.Types.ObjectId(),
            description: 'Test Description',
            icon: new mongoose.Types.ObjectId()
        });
        await mockPartner.save();

        const mockTag = new Tag({
            name: 'Test Tag',
            slug: 'test-tag'
        });
        await mockTag.save();

        const validMdPromotion = new MdPromotion({
            partner: mockPartner._id,
            name: 'Test Promotion',
            thumbnail: 'https://example.com/thumbnail.jpg',
            mediaUrl: 'https://example.com/media.mp4',
            description: 'Test promotion description',
            ctaLink: 'https://example.com/promotion',
            tags: [mockTag._id],
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-12-31'),
            active: true
        });

        const savedMdPromotion = await validMdPromotion.save();

        expect(savedMdPromotion._id).toBeDefined();
        expect(savedMdPromotion.partner).toEqual(mockPartner._id);
        expect(savedMdPromotion.name).toBe('Test Promotion');
        expect(savedMdPromotion.thumbnail).toBe('https://example.com/thumbnail.jpg');
        expect(savedMdPromotion.mediaUrl).toBe('https://example.com/media.mp4');
        expect(savedMdPromotion.description).toBe('Test promotion description');
        expect(savedMdPromotion.ctaLink).toBe('https://example.com/promotion');
        expect(savedMdPromotion.tags).toContainEqual(mockTag._id);
        expect(savedMdPromotion.startDate).toEqual(new Date('2024-01-01'));
        expect(savedMdPromotion.endDate).toEqual(new Date('2024-12-31'));
        expect(savedMdPromotion.active).toBe(true);
        expect(savedMdPromotion.createdAt).toBeDefined();
        expect(savedMdPromotion.updatedAt).toBeDefined();
    });

    it('should fail to create a MdPromotion without required fields', async () => {
        const mdPromotion = new MdPromotion();

        let err;
        try {
            await mdPromotion.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.partner).toBeDefined();
        expect(err.errors.name).toBeDefined();
        expect(err.errors.thumbnail).toBeDefined();
        expect(err.errors.mediaUrl).toBeDefined();
        expect(err.errors.description).toBeDefined();
        expect(err.errors.ctaLink).toBeDefined();
        expect(err.errors.startDate).toBeDefined();
        expect(err.errors.endDate).toBeDefined();
    });

    it('should create a MdPromotion with default active value', async () => {
        const mockPartner = new MdPartner({
            name: 'Test Partner',
            category: new mongoose.Types.ObjectId(),
            description: 'Test Description',
            icon: new mongoose.Types.ObjectId()
        });
        await mockPartner.save();

        const minimalMdPromotion = new MdPromotion({
            partner: mockPartner._id,
            name: 'Minimal Promotion',
            thumbnail: 'https://example.com/thumbnail.jpg',
            mediaUrl: 'https://example.com/media.mp4',
            description: 'Minimal promotion description',
            ctaLink: 'https://example.com/promotion',
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-12-31')
        });

        const savedMdPromotion = await minimalMdPromotion.save();

        expect(savedMdPromotion._id).toBeDefined();
        expect(savedMdPromotion.active).toBe(true);
        expect(savedMdPromotion.tags).toEqual([]);
    });

    it('should fail to create a MdPromotion with invalid reference IDs', async () => {
        const invalidMdPromotion = new MdPromotion({
            partner: 'invalid_id',
            name: 'Invalid Promotion',
            thumbnail: 'https://example.com/thumbnail.jpg',
            mediaUrl: 'https://example.com/media.mp4',
            description: 'Invalid promotion description',
            ctaLink: 'https://example.com/promotion',
            tags: ['invalid_id'],
            startDate: new Date('2024-01-01'),
            endDate: new Date('2024-12-31')
        });

        let err;
        try {
            await invalidMdPromotion.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.partner).toBeDefined();
        expect(err.errors['tags.0']).toBeDefined();
    });
}); 
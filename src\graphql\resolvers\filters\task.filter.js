const { isValidObjectId } = require("../../../utils/validation.util");
const buildDocumentQuery = require("./document.filter");
const buildPartyQuery = require("./party.filter");
const buildUserQuery = require("./user.filter");
const Document = require("../../../models/Document");
const Party = require("../../../models/Party");
const { User } = require("../../../models/User");

const buildTaskQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.title) {
            query.title = { $regex: filter.title, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.status) {
            if (Array.isArray(filter.status)) {
                query.status = { $in: filter.status };
            } else {
                query.status = filter.status;
            }
        }

        if (filter.attachments) {
            const documentPipeline = buildDocumentQuery(filter.attachments);
            if (documentPipeline.length > 0) {
                const matchingDocuments = await Document.aggregate(documentPipeline);
                if (matchingDocuments.length > 0) {
                    query.attachments = { $in: matchingDocuments.map(doc => doc._id) };
                } else {
                    query.attachments = { $in: [] };
                }
            }
        }

        if (filter.party) {
            if (typeof filter.party === 'string') {
                if (isValidObjectId(filter.party)) {
                    query.party = filter.party;
                } else {
                    throw new Error('Invalid party ID provided');
                }
            } else if (typeof filter.party === 'object') {
                const { query: partyQuery } = await buildPartyQuery(filter.party);
                if (Object.keys(partyQuery).length > 0) {
                    const matchingParties = await Party.find(partyQuery).select('_id');
                    if (matchingParties.length > 0) {
                        query.party = { $in: matchingParties.map(party => party._id) };
                    } else {
                        query.party = { $in: [] };
                    }
                }
            }
        }

        if (filter.createdBy) {
            if (typeof filter.createdBy === 'string') {
                if (isValidObjectId(filter.createdBy)) {
                    query.createdBy = filter.createdBy;
                } else {
                    throw new Error('Invalid createdBy ID provided');
                }
            } else if (typeof filter.createdBy === 'object') {
                const userQuery = await buildUserQuery(filter.createdBy);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.createdBy = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.createdBy = { $in: [] };
                    }
                }
            }
        }

        if (filter.assignedTo && Array.isArray(filter.assignedTo)) {
            const assignedToQueries = await Promise.all(
                filter.assignedTo.map(async (assignedTo) => {
                    if (assignedTo.id) {
                        if (isValidObjectId(assignedTo.id)) {
                            return assignedTo.id;
                        } else {
                            throw new Error('Invalid assignedTo user ID provided');
                        }
                    } else if (assignedTo.filter) {
                        const userQuery = await buildUserQuery(assignedTo.filter);
                        if (Object.keys(userQuery).length > 0) {
                            const matchingUsers = await User.find(userQuery).select('_id');
                            return matchingUsers.map(user => user._id);
                        }
                    }
                    return [];
                })
            );

            const flattenedUsers = assignedToQueries.flat().filter(Boolean);
            if (flattenedUsers.length > 0) {
                query.assignedTo = { $in: flattenedUsers };
            } else {
                query.assignedTo = { $in: [] };
            }
        }

        if (filter.dueDate) {
            query.dueDate = {};
            if (filter.dueDate.start) {
                query.dueDate.$gte = filter.dueDate.start;
            }
            if (filter.dueDate.end) {
                query.dueDate.$lte = filter.dueDate.end;
            }
        }
    }

    return query;
};

module.exports = buildTaskQuery;
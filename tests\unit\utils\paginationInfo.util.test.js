const { Cursor } = require('mongoose');
const { getPaginationInfo } = require('../../../src/utils/paginationInfo.util');

describe('getPaginationInfo', () => {
    it('should return pagination info', async () => {
        const mockModel = {
            countDocuments: jest.fn().mockResolvedValue(100),
        };

        const query = {};
        const limit = 10;
        const skip = 0;

        const result = await getPaginationInfo(mockModel, query, limit, skip);

        expect(result).toEqual({
            totalItems: 100,
            totalPages: 10,
            pageSize: limit,
            currentPage: 1,
            skip
        });
        expect(mockModel.countDocuments).toHaveBeenCalledWith(query);
    });
    it('should return pagination info for page 2', async () => {
        const mockModel = {
            countDocuments: jest.fn().mockResolvedValue(100),
        };

        const query = {};
        const limit = 10;
        const skip = 10;

        const result = await getPaginationInfo(mockModel, query, limit, skip);

        expect(result).toEqual({
            totalItems: 100,
            totalPages: 10,
            pageSize: limit,
            currentPage: 2,
            skip
        });
        expect(mockModel.countDocuments).toHaveBeenCalledWith(query);
    });
});
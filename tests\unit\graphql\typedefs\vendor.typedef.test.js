const { gql } = require('graphql-tag');
const vendorTypeDef = require('../../../../src/graphql/typedefs/vendor.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('vendorTypeDef', () => {
  it('should contain the Vendor type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type VendorRatingAggregate {
        averageRating: Float!
        totalRating: Int!
      }

      type Vendor {
        id: ID!
        name: String!
        primaryContact: VendorUser!
        contacts: [VendorUser!]
        businessAddress: Address!
        serviceLocations: [MdServiceLocation!]
        servicesProvided: [VendorService!]
        ratingAggregates: VendorRatingAggregate!
        reviews: [VendorRating!]
      }

      input VendorFilterInput {
        name: String
        minRating: Float
        minTotalReviews: Int
      }

      type VendorWrapper {
        vendor: Vendor!
      }

      type VendorsWrapper {
        vendors: [Vendor]!
      }

      type VendorResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorWrapper!
      }

      type VendorsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorsWrapper!
        pagination: PaginationInfo!
      }

      type VendorErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union VendorResult = VendorResponse | VendorErrorResponse
      union VendorsResult = VendorsResponse | VendorErrorResponse

      type Query {
        getVendorById(id: ID!): VendorResult!
        getVendors(filter: VendorFilterInput, pagination: PaginationInput): VendorsResult!
      }

      input VendorInput {
        name: String!
        primaryContact: ID!
        contacts: [ID!]
        businessAddress: ID!
        serviceLocations: [ID!]
        servicesProvided: [ID!]
      }

      input VendorUpdateInput {
        name: String
        primaryContact: ID
        contacts: [ID!]
        businessAddress: ID
        serviceLocations: [ID!]
        servicesProvided: [ID!]
      }

      type Mutation {
        createVendor(input: VendorInput!): VendorResult!
        updateVendor(id: ID!, input: VendorUpdateInput!): VendorResult!
        deleteVendor(id: ID!): VendorResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(vendorTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});


const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdPartyType {
        id: ID!
        name: String!
        description: String!
        vendorTypes: [MdVendorType!]
        landscapeImage: String
        portraitImage: String
        createdAt: Date!
        updatedAt: Date!
    }

    input MdPartyTypeFilterInput {
        id: String
        name: String
    }

    type MdPartyTypeWrapper {
        mdPartyType: MdPartyType!
    }

    type MdPartyTypesWrapper {
        mdPartyTypes: [MdPartyType]!
    }

    type MdPartyTypeResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartyTypeWrapper!
    }

    type MdPartyTypesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartyTypesWrapper!
        pagination: PaginationInfo!
    }

    type MdPartyTypeErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdPartyTypeResult = MdPartyTypeResponse | MdPartyTypeErrorResponse
    union MdPartyTypesResult = MdPartyTypesResponse | MdPartyTypeErrorResponse

    type Query {
        getMdPartyTypeById(id: ID!): MdPartyTypeResult!
        getMdPartyTypes(filter: MdPartyTypeFilterInput, pagination: PaginationInput): MdPartyTypesResult!
    }

    input MdPartyTypeInput {
        name: String!
        description: String!
        vendorTypes: [String!]
        landscapeImage: String
        portraitImage: String
    }

    input MdPartyTypeUpdateInput {
        name: String
        description: String
        vendorTypes: [String]
        landscapeImage: String
        portraitImage: String
    }

    type Mutation {
        createMdPartyType(input: MdPartyTypeInput!): MdPartyTypeResult!
        updateMdPartyType(id: ID!, input: MdPartyTypeUpdateInput!): MdPartyTypeResult!
        deleteMdPartyType(id: ID!): MdPartyTypeResult!
    }
`;
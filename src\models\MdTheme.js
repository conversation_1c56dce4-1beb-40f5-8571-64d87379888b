const { model, Schema } = require('mongoose');

const mdThemeSchema = new Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    primaryColor: { type: String, required: true },
    secondaryColor: { type: String, required: true },
    backgroundColor: { type: String, required: true },
    textColor: { type: String, required: true }
}, { timestamps: true, collection: 'md_themes' });

const MdTheme = model('MdTheme', mdThemeSchema);

module.exports = MdTheme; 
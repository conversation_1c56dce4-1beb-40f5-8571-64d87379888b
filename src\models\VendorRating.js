const { model, Schema } = require('mongoose');

const vendorRatingSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    vendor: { type: Schema.Types.ObjectId, ref: 'Vendor', required: true },
    rating: { 
        type: Number, 
        required: true,
        min: 1,
        max: 5
    },
    comment: { type: String }
}, { timestamps: true, collection: 'vendor_ratings' });

const VendorRating = model('VendorRating', vendorRatingSchema);

module.exports = VendorRating;
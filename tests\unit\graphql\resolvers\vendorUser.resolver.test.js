const VendorUser = require('../../../../src/models/VendorUser');
const { User } = require('../../../../src/models/User');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildVendorUserQuery = require('../../../../src/graphql/resolvers/filters/vendorUser.filter');
const vendorUserResolvers = require('../../../../src/graphql/resolvers/vendorUser.resolver');
const findReferences = require('../../../../src/graphql/resolvers/references/vendorUser.references');

jest.mock('../../../../src/models/VendorUser');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/vendorUser.filter');
jest.mock('../../../../src/graphql/resolvers/references/vendorUser.references');

describe('vendorUserResolvers', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('VendorUser: user', () => {
        it('should return the user when found', async () => {
          const mockUser = { _id: 'user123', name: 'John Doe' };
          const parent = { user: 'user123' };
    
          User.findById = jest.fn().mockResolvedValue(mockUser);
    
          const result = await vendorUserResolvers.VendorUser.user(parent);
    
          expect(User.findById).toHaveBeenCalledWith('user123');
          expect(result).toEqual(mockUser);
        });
    
        it('should throw an error when user is not found', async () => {
          const parent = { user: 'nonexistentuser' };
    
          User.findById = jest.fn().mockRejectedValue(new Error('User not found'));
    
          await expect(vendorUserResolvers.VendorUser.user(parent)).rejects.toThrow('Error getting user');
          expect(User.findById).toHaveBeenCalledWith('nonexistentuser');
        });
    });
  
    describe('Query: getVendorUserById', () => {
        it('should return vendor user if found in cache', async () => {
            const mockVendorUser = { id: '1', user: '1' };
            getByIdCache.mockResolvedValue(mockVendorUser);
            createResponse.mockReturnValue('success response');
        
            const result = await vendorUserResolvers.Query.getVendorUserById(null, { id: '1' });
        
            expect(getByIdCache).toHaveBeenCalledWith('1');
            expect(setCache).toHaveBeenCalledWith('1', mockVendorUser);
            expect(createResponse).toHaveBeenCalledWith('VendorUserResponse', 'SUCCESS', 'VendorUser fetched successfully', { result: { vendorUser: mockVendorUser } });
            expect(result).toBe('success response');
        });
  
        it('should fetch and cache vendor user if not in cache', async () => {
            const mockVendorUser = { id: '1', user: '1' };
            getByIdCache.mockResolvedValueOnce(null);
            VendorUser.findById = jest.fn().mockReturnValue({
                populate: jest.fn().mockResolvedValue(mockVendorUser)
            });
            createResponse.mockReturnValue('success response');
        
            const result = await vendorUserResolvers.Query.getVendorUserById({}, { id: '1' });
        
            expect(getByIdCache).toHaveBeenCalledWith('1');
            expect(VendorUser.findById).toHaveBeenCalledWith('1');
            expect(setCache).toHaveBeenCalledWith('1', mockVendorUser);
            expect(createResponse).toHaveBeenCalledWith('VendorUserResponse', 'SUCCESS', 'VendorUser fetched successfully', { result: { vendorUser: mockVendorUser } });
            expect(result).toBe('success response');
        });
  
        it('should return error response if vendor user not found', async () => {
            getByIdCache.mockResolvedValue(null);
            VendorUser.findById = jest.fn().mockReturnValue({
                populate: jest.fn().mockResolvedValue(null)
            });
            createResponse.mockReturnValue('error response');

            const result = await vendorUserResolvers.Query.getVendorUserById({}, { id: '1' });

            expect(createResponse).toHaveBeenCalledWith('VendorUserErrorResponse', 'FAILURE', 'VendorUser not found', { errors: [{ field: 'id', message: 'VendorUser not found' }] });
            expect(result).toBe('error response');
        });
  
        it('should handle error and return error response', async () => {
            getByIdCache.mockRejectedValue(new Error('Error fetching VendorUser'));
            createResponse.mockReturnValue('error response');
    
            const result = await vendorUserResolvers.Query.getVendorUserById(null, { id: '1' });
    
            expect(createResponse).toHaveBeenCalledWith('VendorUserErrorResponse', 'FAILURE', 'Error getting VendorUser', { errors: [{ field: 'getVendorUserById', message: 'Error fetching VendorUser' }] });
            expect(result).toBe('error response');
        });
    });

    describe('Query: getVendorUsers', () => {
        afterEach(() => {
          jest.clearAllMocks();
        });
    
        it('should return VendorUsers successfully', async () => {
            const filter = { vendorFirstName: 'John' };
            const pagination = { limit: 10, skip: 0 };
            const mockPipeline = [{ $match: { 'userDetails.firstName': /John/i } }];
            const mockVendorUsers = [
                { _id: '1', userDetails: { _id: 'user1', firstName: 'John' } },
                { _id: '2', userDetails: { _id: 'user2', firstName: 'John' } },
            ];
        
            buildVendorUserQuery.mockReturnValue(mockPipeline);
            VendorUser.aggregate.mockImplementation((pipeline) => {
                if (pipeline[pipeline.length - 1].$count) {
                return Promise.resolve([{ total: 2 }]);
                }
                return Promise.resolve(mockVendorUsers);
            });
        
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'VendorUsers fetched successfully',
                result: {
                vendorUsers: mockVendorUsers.map(user => ({
                    ...user,
                    id: user._id.toString(),
                    userDetails: {
                    ...user.userDetails,
                    id: user.userDetails._id.toString(),
                    },
                })),
                },
                pagination: {
                totalItems: 2,
                totalPages: 1,
                currentPage: 1,
                pageSize: 10,
                hasNextPage: false,
                hasPreviousPage: false,
                },
            });
        
            const result = await vendorUserResolvers.Query.getVendorUsers(null, { filter, pagination });
        
            expect(buildVendorUserQuery).toHaveBeenCalledWith(filter);
            expect(VendorUser.aggregate).toHaveBeenCalledTimes(2);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUsersResponse',
                'SUCCESS',
                'VendorUsers fetched successfully',
                expect.objectContaining({
                result: expect.any(Object),
                pagination: expect.any(Object),
                })
            );
            expect(result).toEqual(expect.objectContaining({
                status: 'SUCCESS',
                message: 'VendorUsers fetched successfully',
            }));
        });
        
        it('should return failure response when no VendorUsers are found', async () => {
            const filter = { vendorFirstName: 'NonExistent' };
            const pagination = { limit: 10, skip: 0 };
            const mockPipeline = [{ $match: { 'userDetails.firstName': /NonExistent/i } }];
        
            buildVendorUserQuery.mockReturnValue(mockPipeline);
            VendorUser.aggregate.mockResolvedValue([]);
        
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'No VendorUsers found',
                result: { vendorUsers: [] },
                pagination: expect.any(Object),
            });
        
            const result = await vendorUserResolvers.Query.getVendorUsers(null, { filter, pagination });
        
            expect(buildVendorUserQuery).toHaveBeenCalledWith(filter);
            expect(VendorUser.aggregate).toHaveBeenCalledTimes(2);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUsersResponse',
                'FAILURE',
                'No VendorUsers found',
                expect.objectContaining({
                result: { vendorUsers: [] },
                pagination: expect.any(Object),
                })
            );
            expect(result).toEqual(expect.objectContaining({
                status: 'FAILURE',
                message: 'No VendorUsers found',
            }));
        });
        
        it('should handle errors and return an error response', async () => {
            const filter = { vendorFirstName: 'John' };
            const pagination = { limit: 10, skip: 0 };
            const mockError = new Error('Database error');
        
            buildVendorUserQuery.mockReturnValue([]);
            VendorUser.aggregate.mockRejectedValue(mockError);
        
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'Error getting VendorUsers',
                errors: [{ field: 'getVendorUsers', message: mockError.message }],
            });
        
            const result = await vendorUserResolvers.Query.getVendorUsers(null, { filter, pagination });
        
            expect(buildVendorUserQuery).toHaveBeenCalledWith(filter);
            expect(VendorUser.aggregate).toHaveBeenCalledTimes(1);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUserErrorResponse',
                'FAILURE',
                'Error getting VendorUsers',
                { errors: [{ field: 'getVendorUsers', message: mockError.message }] }
            );
            expect(result).toEqual(expect.objectContaining({
                status: 'FAILURE',
                message: 'Error getting VendorUsers',
            }));
        });
    });
  
    describe('Mutation: createVendorUser', () => {
        it('should create a new vendor user successfully', async () => {
            const mockInput = { user: '1' };
            const vendorUserMock = {
              _id: '123',
              ...mockInput,
              save: jest.fn().mockResolvedValue(true)
            };
            VendorUser.mockImplementation(() => vendorUserMock);
            const createResponseMock = {
              status: 'SUCCESS',
              message: 'VendorUser created successfully',
              result: { vendorUser: vendorUserMock }
            };
            createResponse.mockReturnValue(createResponseMock);
          
            const result = await vendorUserResolvers.Mutation.createVendorUser(null, { userId: '1' });
          
            expect(VendorUser).toHaveBeenCalledWith({ user: '1' });
            expect(vendorUserMock.save).toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith('VendorUserResponse', 'SUCCESS', 'VendorUser created successfully', { result: { vendorUser: vendorUserMock } });
            expect(result).toBe(createResponseMock);
        });
  
        it('should handle error during vendor user creation and return error response', async () => {
            VendorUser.mockImplementation(() => ({
            save: jest.fn().mockRejectedValue(new Error('Error creating VendorUser'))
            }));
            createResponse.mockReturnValue('error response');
    
            const result = await vendorUserResolvers.Mutation.createVendorUser(null, { userId: '1' });
    
            expect(createResponse).toHaveBeenCalledWith('VendorUserErrorResponse', 'FAILURE', 'Error creating VendorUser', { errors: [{ field: 'createVendorUser', message: 'Error creating VendorUser' }] });
            expect(result).toBe('error response');
        });
    });
  
    describe('Mutation: deleteVendorUser', () => {
        it('should return an error if the vendorUser has references', async () => {
            const id = 'vendorUserId';
            const references = ['Collection1', 'Collection2'];
            
            findReferences.mockResolvedValue(references);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'VendorUser cannot be deleted',
                errors: [{ 
                    field: 'id', 
                    message: `VendorUser cannot be deleted as it has references in the following collections: ${references.join(', ')}` 
                }]
            });
    
            const result = await vendorUserResolvers.Mutation.deleteVendorUser(null, { id });
    
            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorUser.findByIdAndDelete).not.toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUserErrorResponse',
                'FAILURE',
                'VendorUser cannot be deleted',
                { 
                    errors: [{ 
                        field: 'id', 
                        message: `VendorUser cannot be deleted as it has references in the following collections: ${references.join(', ')}` 
                    }] 
                }
            );
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'VendorUser cannot be deleted',
                errors: [{ 
                    field: 'id', 
                    message: `VendorUser cannot be deleted as it has references in the following collections: ${references.join(', ')}` 
                }]
            });
        });
    
        it('should delete vendorUser successfully when no references exist', async () => {
            const id = 'vendorUserId';
            const vendorUser = { _id: id, name: 'Test VendorUser' };
    
            findReferences.mockResolvedValue([]);
            VendorUser.findByIdAndDelete.mockResolvedValue(vendorUser);
            clearCacheById.mockResolvedValue();
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'VendorUser deleted successfully',
                result: { vendorUser }
            });
    
            const result = await vendorUserResolvers.Mutation.deleteVendorUser(null, { id });
    
            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorUser.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUserResponse',
                'SUCCESS',
                'VendorUser deleted successfully',
                { result: { vendorUser } }
            );
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'VendorUser deleted successfully',
                result: { vendorUser }
            });
        });
    
        it('should return error if vendorUser not found', async () => {
            const id = 'nonexistentId';
    
            findReferences.mockResolvedValue([]);
            VendorUser.findByIdAndDelete.mockResolvedValue(null);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'VendorUser not found',
                errors: [{ field: 'id', message: 'VendorUser not found' }]
            });
    
            const result = await vendorUserResolvers.Mutation.deleteVendorUser(null, { id });
    
            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorUser.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorUserErrorResponse',
                'FAILURE',
                'VendorUser not found',
                { errors: [{ field: 'id', message: 'VendorUser not found' }] }
            );
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'VendorUser not found',
                errors: [{ field: 'id', message: 'VendorUser not found' }]
            });
        });
    });
});
const { isValidObjectId } = require("../../../utils/validation.util");
const buildTagQuery = require("./tag.filter");
const Tag = require("../../../models/Tag");

const buildMdPolicyQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.tag) {
            if (typeof filter.tag === 'string') {
                if (isValidObjectId(filter.tag)) {
                    query.tags = filter.tag;
                } else {
                    throw new Error('Invalid tag ID provided');
                }
            } else if (typeof filter.tag === 'object') {
                const tagQuery = await buildTagQuery(filter.tag);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.tags = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.tags = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdPolicyQuery; 
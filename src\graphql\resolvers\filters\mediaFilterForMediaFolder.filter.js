const { isValidObjectId } = require("../../../utils/validation.util");
const buildUserQuery = require("./user.filter");
const { User } = require("../../../models/User");
const VendorUser = require("../../../models/VendorUser");
const MediaFolder = require("../../../models/MediaFolder");

const buildMediaFilterForMediaFolderQuery = async (filter, mediaFolderId) => {
    const query = {};

    if (filter) {
        if (filter.title) {
            query.title = { $regex: filter.title, $options: 'i' };
        }

        if (filter.uploadedAt) {
            query.uploadedAt = {};
            if (filter.uploadedAt.start) {
                query.uploadedAt.$gte = filter.uploadedAt.start;
            }
            if (filter.uploadedAt.end) {
                query.uploadedAt.$lte = filter.uploadedAt.end;
            }
        }

        if (filter.ownerId) {
            if (isValidObjectId(filter.ownerId)) {
                query.owner = filter.ownerId;
            } else {
                throw new Error('Invalid owner ID provided');
            }
        }

        if (filter.owner && !filter.ownerId) {
            if (typeof filter.owner === 'string') {
                if (isValidObjectId(filter.owner)) {
                    query.owner = filter.owner;
                } else {
                    throw new Error('Invalid owner ID provided');
                }
            } else if (typeof filter.owner === 'object') {
                const userQuery = await buildUserQuery(filter.owner);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.owner = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.owner = { $in: [] };
                    }
                }
            }
        }

        if (filter.tags && Array.isArray(filter.tags)) {
            query.tags = { $all: filter.tags };
        }

        if (filter.userType) {
            switch (filter.userType) {
                case 'HOST':
                    query.owner = { $in: await MediaFolder.distinct('owner', { _id: mediaFolderId }) };
                    break;
                case 'GUEST':
                    const guestFolder = await MediaFolder.findOne({
                        _id: mediaFolderId,
                        $or: [
                            { contributors: { $exists: true, $ne: [] } },
                            { sharedWith: { $exists: true, $ne: [] } }
                        ]
                    });
                    const guestUserIds = guestFolder ? [
                        ...new Set([
                            ...(guestFolder.contributors || []),
                            ...(guestFolder.sharedWith || [])
                        ])
                    ] : [];
                    query.owner = { $in: guestUserIds };
                    break;
                case 'VENDOR':
                    const vendorUserIds = await getVendorUserIds();
                    query.owner = { $in: vendorUserIds };
                    break;
                default:
                    throw new Error('Invalid userType provided');
            }
        }
    }

    return query;
};

const getVendorUserIds = async () => {
    const vendorUsers = await VendorUser.find({}).select('_id');
    return vendorUsers.map(vu => vu._id);
};

module.exports = buildMediaFilterForMediaFolderQuery; 
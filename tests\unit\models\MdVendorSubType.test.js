const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdVendorSubType = require('../../../src/models/MdVendorSubType');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdVendorSubType Model Test', () => {
    it('should create and save a MdVendorSubType successfully', async () => {
        const mockIcon = new MdIcon({
            name: 'Test Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const validMdVendorSubType = new MdVendorSubType({
            name: 'Test Vendor SubType',
            description: 'Test Description',
            icon: mockIcon._id
        });

        const savedMdVendorSubType = await validMdVendorSubType.save();

        expect(savedMdVendorSubType._id).toBeDefined();
        expect(savedMdVendorSubType.name).toBe('Test Vendor SubType');
        expect(savedMdVendorSubType.description).toBe('Test Description');
        expect(savedMdVendorSubType.icon).toEqual(mockIcon._id);
        expect(savedMdVendorSubType.createdAt).toBeDefined();
        expect(savedMdVendorSubType.updatedAt).toBeDefined();
    });

    it('should fail to create a MdVendorSubType without required fields', async () => {
        const mdVendorSubType = new MdVendorSubType();

        let err;
        try {
            await mdVendorSubType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });

    it('should create a MdVendorSubType without optional icon field', async () => {
        const minimalMdVendorSubType = new MdVendorSubType({
            name: 'Minimal SubType',
            description: 'Minimal Description'
        });

        const savedMinimalMdVendorSubType = await minimalMdVendorSubType.save();

        expect(savedMinimalMdVendorSubType._id).toBeDefined();
        expect(savedMinimalMdVendorSubType.name).toBe('Minimal SubType');
        expect(savedMinimalMdVendorSubType.description).toBe('Minimal Description');
        expect(savedMinimalMdVendorSubType.icon).toBeUndefined();
    });

    it('should fail to create a MdVendorSubType with invalid icon reference', async () => {
        const invalidMdVendorSubType = new MdVendorSubType({
            name: 'Invalid SubType',
            description: 'Invalid Description',
            icon: 'invalid_id'
        });

        let err;
        try {
            await invalidMdVendorSubType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.icon).toBeDefined();
    });
}); 
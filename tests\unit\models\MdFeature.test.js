const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdFeature = require('../../../src/models/MdFeature');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdFeature Model Test', () => {
    it('should create and save a MdFeature successfully', async () => {
        const validMdFeature = new MdFeature({
            name: 'Feature Name',
            icon: new mongoose.Types.ObjectId(),
        });

        const savedMdFeature = await validMdFeature.save();

        expect(savedMdFeature._id).toBeDefined();
        expect(savedMdFeature.name).toBe('Feature Name');
        expect(savedMdFeature.icon).toBeInstanceOf(mongoose.Types.ObjectId);
    });

    it('should create and save a MdFeature without icon', async () => {
        const validMdFeature = new MdFeature({
            name: 'Feature Name',
        });

        const savedMdFeature = await validMdFeature.save();

        expect(savedMdFeature._id).toBeDefined();
        expect(savedMdFeature.name).toBe('Feature Name');
        expect(savedMdFeature.icon).toBeUndefined();
    });

    it('should fail to create a MdFeature without required fields', async () => {
        const mdFeature = new MdFeature();

        let err;
        try {
            await mdFeature.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.icon).toBeUndefined();
    });
});
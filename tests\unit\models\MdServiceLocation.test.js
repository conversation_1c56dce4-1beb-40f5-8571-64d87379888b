const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdServiceLocation = require('../../../src/models/MdServiceLocation');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdServiceLocation Model Test', () => {
    it('should create and save a MdServiceLocation successfully', async () => {
        const validMdServiceLocation = new MdServiceLocation({
            name: 'Downtown Area',
            city: 'New York',
            state: 'NY',
            areas: ['Manhattan', 'Brooklyn']
        });

        const savedMdServiceLocation = await validMdServiceLocation.save();

        expect(savedMdServiceLocation._id).toBeDefined();
        expect(savedMdServiceLocation.name).toBe('Downtown Area');
        expect(savedMdServiceLocation.city).toBe('New York');
        expect(savedMdServiceLocation.state).toBe('NY');
        expect(savedMdServiceLocation.areas).toHaveLength(2);
        expect(savedMdServiceLocation.areas).toContain('Manhattan');
        expect(savedMdServiceLocation.areas).toContain('Brooklyn');
        expect(savedMdServiceLocation.createdAt).toBeDefined();
        expect(savedMdServiceLocation.updatedAt).toBeDefined();
    });

    it('should fail to create a MdServiceLocation without required fields', async () => {
        const mdServiceLocation = new MdServiceLocation();

        let err;
        try {
            await mdServiceLocation.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.city).toBeDefined();
        expect(err.errors.state).toBeDefined();
    });

    it('should create a MdServiceLocation with empty areas array', async () => {
        const minimalMdServiceLocation = new MdServiceLocation({
            name: 'Test Location',
            city: 'Test City',
            state: 'TS'
        });

        const savedMdServiceLocation = await minimalMdServiceLocation.save();

        expect(savedMdServiceLocation._id).toBeDefined();
        expect(savedMdServiceLocation.name).toBe('Test Location');
        expect(savedMdServiceLocation.city).toBe('Test City');
        expect(savedMdServiceLocation.state).toBe('TS');
        expect(savedMdServiceLocation.areas).toEqual([]);
    });

    it('should update a MdServiceLocation successfully', async () => {
        const mdServiceLocation = new MdServiceLocation({
            name: 'Initial Location',
            city: 'Initial City',
            state: 'IC',
            areas: ['Area1']
        });

        const savedMdServiceLocation = await mdServiceLocation.save();

        savedMdServiceLocation.name = 'Updated Location';
        savedMdServiceLocation.areas.push('Area2');
        const updatedMdServiceLocation = await savedMdServiceLocation.save();

        expect(updatedMdServiceLocation.name).toBe('Updated Location');
        expect(updatedMdServiceLocation.areas).toHaveLength(2);
        expect(updatedMdServiceLocation.areas).toContain('Area2');
    });
});

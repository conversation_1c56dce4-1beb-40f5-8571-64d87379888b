const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdRole {
        id: ID
        name: String!
        description: String!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdRoleFilterInput {
        id: String!
        name: String
    }

    type MdRoleWrapper {
        mdRole: MdRole!
    }

    type MdRolesWrapper {
        mdRoles: [MdRole]!
    }

    type MdRolesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdRolesWrapper!
        pagination: PaginationInfo!
    }

    type MdRoleResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdRoleWrapper!
    }

    type MdRoleErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdRoleResult = MdRoleResponse | MdRoleErrorResponse
    union MdRolesResult = MdRolesResponse | MdRoleErrorResponse

    type Query {
        getMdRoleById(id: ID!): MdRoleResult!
        getMdRoles(filter: MdRoleFilterInput, pagination: PaginationInput): MdRolesResult!
    }

    type Mutation {
        createMdRole(name: String!, description: String): MdRoleResult!
        updateMdRole(id: ID!, name: String, description: String): MdRoleResult!
        deleteMdRole(id: ID!): MdRoleResult!
    }
`;
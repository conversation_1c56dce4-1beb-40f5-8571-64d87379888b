const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdTheme {
        id: ID!
        name: String!
        description: String!
        primaryColor: String!
        secondaryColor: String!
        backgroundColor: String!
        textColor: String!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdThemeFilterInput {
        id: String
        name: String
        description: String
    }

    type MdThemeWrapper {
        mdTheme: MdTheme!
    }

    type MdThemesWrapper {
        mdThemes: [MdTheme]!
    }

    type MdThemesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdThemesWrapper!
        pagination: PaginationInfo!
    }

    type MdThemeResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdThemeWrapper!
    }

    type MdThemeErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdThemeResult = MdThemeResponse | MdThemeErrorResponse
    union MdThemesResult = MdThemesResponse | MdThemeErrorResponse

    type Query {
        getMdThemeById(id: ID!): MdThemeResult!
        getMdThemes(filter: MdThemeFilterInput, pagination: PaginationInput): MdThemesResult!
    }

    input MdThemeInput {
        name: String!
        description: String!
        primaryColor: String!
        secondaryColor: String!
        backgroundColor: String!
        textColor: String!
    }

    input MdThemeUpdateInput {
        name: String
        description: String
        primaryColor: String
        secondaryColor: String
        backgroundColor: String
        textColor: String
    }

    type Mutation {
        createMdTheme(input: MdThemeInput!): MdThemeResult!
        updateMdTheme(id: ID!, input: MdThemeUpdateInput!): MdThemeResult!
        deleteMdTheme(id: ID!): MdThemeResult!
    }
`; 
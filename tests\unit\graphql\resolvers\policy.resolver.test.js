const mongoose = require('mongoose');
const Policy = require('../../../../src/models/Policy');
const Vendor = require('../../../../src/models/Vendor');
const Tag = require('../../../../src/models/Tag');
const policyResolvers = require('../../../../src/graphql/resolvers/policy.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const buildPolicyQuery = require('../../../../src/graphql/resolvers/filters/policy.filter');

const basePolicyIdSchema = {
    vendor: { type: 'single', model: Vendor },
    tags: { type: 'array', required: false, model: Tag }
};

const policyIdSchema = { ...basePolicyIdSchema };

const createPolicyIdSchema = {
    ...basePolicyIdSchema,
    vendor: { ...basePolicyIdSchema.vendor, required: true }
};

jest.mock('../../../../src/models/Policy');
jest.mock('../../../../src/models/Vendor');
jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/graphql/resolvers/filters/policy.filter');

describe('Policy Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Policy Field Resolvers', () => {
        const mockParent = {
            vendor: 'vendorId',
            tags: ['tagId1', 'tagId2']
        };

        describe('vendor', () => {
            it('should return vendor successfully', async () => {
                const mockVendor = { _id: 'vendorId', name: 'Test Vendor' };
                Vendor.findById.mockResolvedValue(mockVendor);

                const result = await policyResolvers.Policy.vendor(mockParent);
                expect(result).toEqual(mockVendor);
                expect(Vendor.findById).toHaveBeenCalledWith('vendorId');
            });

            it('should handle errors', async () => {
                Vendor.findById.mockRejectedValue(new Error('Database error'));
                await expect(policyResolvers.Policy.vendor(mockParent))
                    .rejects
                    .toThrow('Error getting vendor');
            });
        });

        describe('tags', () => {
            it('should return tags successfully', async () => {
                const mockTags = [
                    { _id: 'tagId1', name: 'Tag 1' },
                    { _id: 'tagId2', name: 'Tag 2' }
                ];
                Tag.find.mockResolvedValue(mockTags);

                const result = await policyResolvers.Policy.tags(mockParent);
                expect(result).toEqual(mockTags);
                expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: ['tagId1', 'tagId2'] } });
            });

            it('should handle errors', async () => {
                Tag.find.mockRejectedValue(new Error('Database error'));
                await expect(policyResolvers.Policy.tags(mockParent))
                    .rejects
                    .toThrow('Error getting tags');
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getPolicyById', () => {
            it('should return cached policy if exists', async () => {
                const mockPolicy = { _id: 'policyId', name: 'Test Policy' };
                getByIdCache.mockResolvedValue(mockPolicy);
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Query.getPolicyById(null, { id: 'policyId' });

                expect(getByIdCache).toHaveBeenCalledWith('policyId');
                expect(Policy.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyResponse',
                    'SUCCESS',
                    'Policy retrieved successfully',
                    { result: { policy: mockPolicy } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache policy if not in cache', async () => {
                const mockPolicy = { _id: 'policyId', name: 'Test Policy' };
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPolicy);

                getByIdCache.mockResolvedValue(null);
                Policy.findById.mockReturnValue(mockPopulateChain);
                setCache.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Query.getPolicyById(null, { id: 'policyId' });

                expect(getByIdCache).toHaveBeenCalledWith('policyId');
                expect(Policy.findById).toHaveBeenCalledWith('policyId');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('vendor');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('tags');
                expect(setCache).toHaveBeenCalledWith('policyId', mockPolicy);
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyResponse',
                    'SUCCESS',
                    'Policy retrieved successfully',
                    { result: { policy: mockPolicy } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle policy not found', async () => {
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(null);

                getByIdCache.mockResolvedValue(null);
                Policy.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Query.getPolicyById(null, { id: 'policyId' });

                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Policy not found',
                    { errors: [{ field: 'id', message: 'Policy not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors when retrieving policy fails', async () => {
                const id = 'testPolicyId';
                const error = new Error('Database error');
                
                getByIdCache.mockRejectedValue(error);
                Policy.findById.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    populate: jest.fn().mockRejectedValue(error)
                });
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving policy',
                    errors: [{ field: 'getPolicyById', message: error.message }]
                });

                const result = await policyResolvers.Query.getPolicyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Error retrieving policy',
                    {
                        errors: [{ field: 'getPolicyById', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving policy',
                    errors: [{ field: 'getPolicyById', message: error.message }]
                });
            });
        });

        describe('getPolicies', () => {
            it('should return policies successfully', async () => {
                const mockPolicies = [{ _id: 'policyId1' }, { _id: 'policyId2' }];
                const mockPaginationInfo = { total: 2, hasMore: false };
                
                buildPolicyQuery.mockResolvedValue({});
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                Policy.find.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockPolicies)
                });
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Query.getPolicies(null, { 
                    filter: {}, 
                    pagination: { limit: 10, skip: 0 } 
                });

                expect(buildPolicyQuery).toHaveBeenCalledWith({});
                expect(getPaginationInfo).toHaveBeenCalled();
                expect(Policy.find).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PoliciesResponse',
                    'SUCCESS',
                    'Policies fetched successfully',
                    {
                        result: { policies: mockPolicies },
                        pagination: mockPaginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle no policies found', async () => {
                buildPolicyQuery.mockResolvedValue({});
                Policy.find.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([])
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Query.getPolicies(null, { 
                    filter: {}, 
                    pagination: { limit: 10, skip: 0 } 
                });

                expect(createResponse).toHaveBeenCalledWith(
                    'PoliciesResponse',
                    'FAILURE',
                    'No policies found',
                    expect.any(Object)
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors when retrieving policies', async () => {
                const filter = { name: 'Test Policy' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
                
                buildPolicyQuery.mockRejectedValue(error);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving policies',
                    errors: [{ field: 'getPolicies', message: error.message }]
                });

                const result = await policyResolvers.Query.getPolicies(null, { filter, pagination });

                expect(buildPolicyQuery).toHaveBeenCalledWith(filter);
                expect(Policy.find).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Error retrieving policies',
                    {
                        errors: [{ field: 'getPolicies', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving policies',
                    errors: [{ field: 'getPolicies', message: error.message }]
                });
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createPolicy', () => {
            it('should create policy successfully', async () => {
                const input = { 
                    name: 'New Policy',
                    vendor: 'vendorId',
                    tags: ['tagId']
                };
                const savedPolicy = { _id: 'newId', ...input };
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(savedPolicy);
                
                validateReferences.mockResolvedValue(null);
                const mockSave = jest.fn().mockResolvedValue(savedPolicy);
                Policy.mockImplementation(() => ({
                    save: mockSave,
                    _id: 'newId'
                }));
                Policy.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Mutation.createPolicy(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'Policy');
                expect(Policy).toHaveBeenCalledWith(input);
                expect(mockSave).toHaveBeenCalled();
                expect(Policy.findById).toHaveBeenCalledWith('newId');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('vendor');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('tags');
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyResponse',
                    'SUCCESS',
                    'Policy created successfully',
                    { result: { policy: savedPolicy } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle validation errors', async () => {
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'vendor', message: 'Invalid vendor reference' }]
                };
                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Mutation.createPolicy(null, { 
                    input: { name: 'Test Policy' } 
                });

                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deletePolicy', () => {
            it('should delete policy successfully', async () => {
                const mockPolicy = { _id: 'policyId' };
                findReferences.mockResolvedValue([]);
                Policy.findByIdAndDelete.mockResolvedValue(mockPolicy);
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Mutation.deletePolicy(null, { id: 'policyId' });

                expect(findReferences).toHaveBeenCalledWith('policyId', 'Policy');
                expect(Policy.findByIdAndDelete).toHaveBeenCalledWith('policyId');
                expect(clearCacheById).toHaveBeenCalledWith('policyId');
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyResponse',
                    'SUCCESS',
                    'Policy deleted successfully',
                    { result: { policy: mockPolicy } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle policy with references', async () => {
                findReferences.mockResolvedValue(['Reference1', 'Reference2']);
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Mutation.deletePolicy(null, { id: 'policyId' });

                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Policy cannot be deleted',
                    { errors: [{ field: 'id', message: expect.any(String) }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle and return error response when deletion fails', async () => {
                const error = new Error('Database connection failed');
                Policy.findByIdAndDelete.mockRejectedValue(error);
                
                findReferences.mockResolvedValue([]);

                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await policyResolvers.Mutation.deletePolicy(null, { id: 'test-id' });

                expect(result).toEqual({
                    type: 'PolicyErrorResponse',
                    status: 'FAILURE',
                    message: 'Error deleting policy',
                    errors: [{ field: 'deletePolicy', message: error.message }]
                });

                expect(findReferences).toHaveBeenCalledWith('test-id', 'Policy');
                
                expect(Policy.findByIdAndDelete).toHaveBeenCalledWith('test-id');
                
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Error deleting policy',
                    {
                        errors: [{ field: 'deletePolicy', message: error.message }]
                    }
                );
            });
        });

        describe('updatePolicy', () => {
            const mockId = 'policyId';
            const mockInput = {
                name: 'Updated Policy',
                description: 'Updated description',
                vendor: 'vendorId',
                tags: ['tagId1', 'tagId2']
            };

            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should update policy successfully', async () => {
                const mockUpdatedPolicy = { _id: mockId, ...mockInput };
                const mockPopulatedPolicy = {
                    ...mockUpdatedPolicy,
                    vendor: { _id: 'vendorId', name: 'Vendor Name' },
                    tags: [
                        { _id: 'tagId1', name: 'Tag 1' },
                        { _id: 'tagId2', name: 'Tag 2' }
                    ]
                };

                validateReferences.mockResolvedValue(null);
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(mockPopulatedPolicy);
                
                Policy.findByIdAndUpdate.mockReturnValue(mockPopulateChain);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await policyResolvers.Mutation.updatePolicy(null, { id: mockId, input: mockInput });

                expect(validateReferences).toHaveBeenCalledWith(mockInput, policyIdSchema, 'Policy');
                expect(Policy.findByIdAndUpdate).toHaveBeenCalledWith(
                    mockId,
                    mockInput,
                    { new: true }
                );
                expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(1, 'vendor');
                expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(2, 'tags');
                expect(clearCacheById).toHaveBeenCalledWith(mockId);
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyResponse',
                    'SUCCESS',
                    'Policy updated successfully',
                    { result: { policy: mockPopulatedPolicy } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle policy not found', async () => {
                validateReferences.mockResolvedValue(null);
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate
                    .mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(null);
                
                Policy.findByIdAndUpdate.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Mutation.updatePolicy(null, { id: mockId, input: mockInput });

                expect(Policy.findByIdAndUpdate).toHaveBeenCalledWith(
                    mockId,
                    mockInput,
                    { new: true }
                );
                expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(1, 'vendor');
                expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(2, 'tags');
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    'Policy not found',
                    { errors: [{ field: 'id', message: 'Policy not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle validation errors', async () => {
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'vendor', message: 'Invalid vendor reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await policyResolvers.Mutation.updatePolicy(null, { id: mockId, input: mockInput });

                expect(validateReferences).toHaveBeenCalledWith(mockInput, policyIdSchema, 'Policy');
                expect(Policy.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PolicyErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
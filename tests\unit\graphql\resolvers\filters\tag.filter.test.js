const mongoose = require('mongoose');
const buildTagQuery = require('../../../../../src/graphql/resolvers/filters/tag.filter');
const buildMdIconQuery = require('../../../../../src/graphql/resolvers/filters/mdIcon.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/mdIcon.filter');

describe('buildTagQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', async () => {
        const result = await buildTagQuery();
        expect(result).toEqual({});
    });

    it('should build name filter correctly', async () => {
        const filter = { name: 'test' };
        const result = await buildTagQuery(filter);
        expect(result).toEqual({
            name: { $regex: 'test', $options: 'i' }
        });
    });

    it('should build single id filter correctly', async () => {
        const filter = { id: 'testId' };
        const result = await buildTagQuery(filter);
        expect(result).toEqual({
            _id: 'testId'
        });
    });

    it('should build multiple id filter correctly', async () => {
        const filter = { id: ['id1', 'id2'] };
        const result = await buildTagQuery(filter);
        expect(result).toEqual({
            _id: { $in: ['id1', 'id2'] }
        });
    });

    it('should build slug filter correctly', async () => {
        const filter = { slug: 'test-slug' };
        const result = await buildTagQuery(filter);
        expect(result).toEqual({
            slug: { $regex: 'test-slug', $options: 'i' }
        });
    });

    describe('icon filter', () => {
        it('should handle string icon ID correctly when valid', async () => {
            const filter = { icon: 'validIconId' };
            isValidObjectId.mockReturnValue(true);

            const result = await buildTagQuery(filter);
            
            expect(result).toEqual({
                icon: 'validIconId'
            });
            expect(isValidObjectId).toHaveBeenCalledWith('validIconId');
        });

        it('should throw error for invalid icon ID string', async () => {
            const filter = { icon: 'invalidIconId' };
            isValidObjectId.mockReturnValue(false);

            await expect(buildTagQuery(filter))
                .rejects
                .toThrow('Invalid icon ID provided');
            
            expect(isValidObjectId).toHaveBeenCalledWith('invalidIconId');
        });

        it('should handle icon object filter correctly', async () => {
            const filter = { icon: { name: 'testIcon' } };
            const mockIconQuery = { _id: 'iconId' };
            buildMdIconQuery.mockResolvedValue(mockIconQuery);

            const result = await buildTagQuery(filter);

            expect(result).toEqual({
                icon: 'iconId'
            });
            expect(buildMdIconQuery).toHaveBeenCalledWith({ name: 'testIcon' });
        });

        it('should not include icon in query if icon object query returns empty', async () => {
            const filter = { icon: { name: 'testIcon' } };
            buildMdIconQuery.mockResolvedValue({});

            const result = await buildTagQuery(filter);

            expect(result).toEqual({});
            expect(buildMdIconQuery).toHaveBeenCalledWith({ name: 'testIcon' });
        });
    });

    it('should combine multiple filters correctly', async () => {
        const filter = {
            name: 'test',
            slug: 'test-slug',
            id: 'testId'
        };
        
        const result = await buildTagQuery(filter);
        
        expect(result).toEqual({
            name: { $regex: 'test', $options: 'i' },
            slug: { $regex: 'test-slug', $options: 'i' },
            _id: 'testId'
        });
    });
});
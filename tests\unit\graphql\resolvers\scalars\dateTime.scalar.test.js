const { DateTime } = require('../../../../../src/graphql/resolvers/scalars/dateTime.scalar');

describe('DateTime Scalar', () => {
    const validDateString = '2023-05-17T12:34:56.789Z';
    const validDate = new Date(validDateString);

    describe('parseValue', () => {
        it('should parse a valid date string', () => {
        expect(DateTime.parseValue(validDateString)).toBe(validDateString);
        });

        it('should parse a valid Date object', () => {
        expect(DateTime.parseValue(validDate)).toBe(validDateString);
        });

        it('should throw an error for invalid date', () => {
        expect(() => DateTime.parseValue('invalid date')).toThrow();
        });
    });

    describe('serialize', () => {
        it('should serialize a valid date string', () => {
        expect(DateTime.serialize(validDateString)).toBe(validDateString);
        });

        it('should serialize a valid Date object', () => {
        expect(DateTime.serialize(validDate)).toBe(validDateString);
        });

        it('should throw an error for invalid date', () => {
        expect(() => DateTime.serialize('invalid date')).toThrow();
        });
    });

    describe('parseLiteral', () => {
        it('should parse a valid STRING literal', () => {
        const ast = { kind: 'StringValue', value: validDateString };
        expect(DateTime.parseLiteral(ast)).toBe(validDateString);
        });

        it('should return null for non-STRING literals', () => {
        const ast = { kind: 'IntValue', value: '1234' };
        expect(DateTime.parseLiteral(ast)).toBeNull();
        });

        it('should throw an error for invalid date string', () => {
        const ast = { kind: 'StringValue', value: 'invalid date' };
        expect(() => DateTime.parseLiteral(ast)).toThrow();
        });
    });
});
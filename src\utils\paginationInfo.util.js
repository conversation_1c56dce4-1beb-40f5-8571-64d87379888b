const getPaginationInfo = async (model, query, limit, skip, modifier = null) => {
    const totalItems = modifier 
        ? await modifier(await model.countDocuments(query), query)
        : await model.countDocuments(query);

    return {
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: Math.floor(skip / limit) + 1,
        pageSize: limit,
        skip
    };
};

module.exports = { getPaginationInfo };
const mongoose = require('mongoose');
const PartyService = require('../../../../../src/models/PartyService');
const findReferences = require('../../../../../src/graphql/resolvers/references/vendor.references');

jest.mock('../../../../../src/models/PartyService');

describe('Vendor References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        PartyService.find.mockResolvedValue([]);

        const vendorId = new mongoose.Types.ObjectId();
        const result = await findReferences(vendorId);

        expect(PartyService.find).toHaveBeenCalledWith({ vendor: vendorId });
        expect(result).toEqual([]);
    });

    it('should return PartyService when references exist', async () => {
        const mockPartyServices = [
            { _id: new mongoose.Types.ObjectId() },
            { _id: new mongoose.Types.ObjectId() }
        ];
        PartyService.find.mockResolvedValue(mockPartyServices);

        const vendorId = new mongoose.Types.ObjectId();
        const result = await findReferences(vendorId);

        expect(PartyService.find).toHaveBeenCalledWith({ vendor: vendorId });
        expect(result).toEqual(['PartyService']);
    });

    it('should handle invalid input gracefully', async () => {
        PartyService.find.mockResolvedValue(null);

        const result = await findReferences('invalid_id');

        expect(PartyService.find).toHaveBeenCalledWith({ vendor: 'invalid_id' });
        expect(result).toEqual([]);
    });

    it('should handle database errors properly', async () => {
        const error = new Error('Database error');
        PartyService.find.mockRejectedValue(error);

        await expect(findReferences('some_id')).rejects.toThrow('Database error');
        expect(PartyService.find).toHaveBeenCalledWith({ vendor: 'some_id' });
    });
}); 
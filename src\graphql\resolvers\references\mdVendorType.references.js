const PartyService = require("../../../models/PartyService");
const VendorService = require("../../../models/VendorService");

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];

    const vendorServiceReferences = await VendorService.find({ vendorType: id });
    if (hasReferences(vendorServiceReferences)) {
        references.push('VendorService');
    }

    const partyServiceReferences = await PartyService.find({ vendorType: id });
    if (hasReferences(partyServiceReferences)) {
        references.push('PartyService');
    }

    return references;
}

module.exports = findReferences;

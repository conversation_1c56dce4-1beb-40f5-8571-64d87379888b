const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdFunFact {
        id: ID!
        funFact: String!
        eventTypes: [MdEventType!]!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdFunFactFilterInput {
        id: String
        funFact: String
        eventTypes: MdEventTypeFilterInput
    }

    type MdFunFactWrapper {
        mdFunFact: MdFunFact!
    }

    type MdFunFactsWrapper {
        mdFunFacts: [MdFunFact]!
    }

    type MdFunFactsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdFunFactsWrapper!
        pagination: PaginationInfo!
    }

    type MdFunFactResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdFunFactWrapper!
    }

    type MdFunFactErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdFunFactResult = MdFunFactResponse | MdFunFactErrorResponse
    union MdFunFactsResult = MdFunFactsResponse | MdFunFactErrorResponse

    type Query {
        getMdFunFactById(id: ID!): MdFunFactResult!
        getMdFunFacts(filter: MdFunFactFilterInput, pagination: PaginationInput): MdFunFactsResult!
    }

    input MdFunFactInput {
        funFact: String!
        eventTypes: [ID!]!
    }

    input MdFunFactUpdateInput {
        id: ID!
        funFact: String
        eventTypes: [ID!]
    }

    type Mutation {
        createMdFunFact(input: MdFunFactInput!): MdFunFactResult!
        updateMdFunFact(input: MdFunFactUpdateInput!): MdFunFactResult!
        deleteMdFunFact(id: ID!): MdFunFactResult!
    }
`;

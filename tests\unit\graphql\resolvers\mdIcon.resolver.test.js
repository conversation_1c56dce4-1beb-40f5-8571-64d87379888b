const MdIcon = require('../../../../src/models/MdIcon');
const mdIconResolvers = require('../../../../src/graphql/resolvers/mdIcon.resolver');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const buildMdIconQuery = require('../../../../src/graphql/resolvers/filters/mdIcon.filter');
const resolvers = require('../../../../src/graphql/resolvers/mdIcon.resolver');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdIcon.filter');
const mockFindReferences = jest.fn();
jest.mock('../../../../src/utils/referenceCheck.util');

describe('MdIconResolver', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        findReferences.mockResolvedValue([]);
    });

    describe('Query.getMdIconById', () => {
        it('should return a MdIcon if found', async () => {
            const id = '1';
            const mdIcon = { id, name: 'Icon Name', iconSrc: '<iconSrc></iconSrc>' };
            getByIdCache.mockResolvedValue(null);
            MdIcon.findById.mockResolvedValue(mdIcon);
            setCache.mockResolvedValue(true);
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdIcon fetched successfully', result: { mdIcon } });

            const result = await mdIconResolvers.Query.getMdIconById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(MdIcon.findById).toHaveBeenCalledWith(id);
            expect(setCache).toHaveBeenCalledWith(id, mdIcon);
            expect(createResponse).toHaveBeenCalledWith('MdIconResponse', 'SUCCESS', 'MdIcon fetched successfully', { result: { mdIcon } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdIcon fetched successfully', result: { mdIcon } });
        });

        it('should return an error response if MdIcon is not found', async () => {
            const id = '1';
            getByIdCache.mockResolvedValue(null);
            MdIcon.findById.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdIcon not found', errors: [{ field: 'getMdIconById', message: 'MdIcon not found' }] });

            const result = await mdIconResolvers.Query.getMdIconById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(MdIcon.findById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'MdIcon not found', { errors: [{ field: 'getMdIconById', message: 'MdIcon not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdIcon not found', errors: [{ field: 'getMdIconById', message: 'MdIcon not found' }] });
        });

        it('should return an error response if an error occurs', async () => {
            const id = '1';
            const error = new Error('Error getting mdIcon');
            getByIdCache.mockRejectedValue(error);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting mdIcon', errors: [{ field: 'getMdIconById', message: error.message }] });

            const result = await mdIconResolvers.Query.getMdIconById(null, { id });

            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'Error getting mdIcon', { errors: [{ field: 'getMdIconById', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error getting mdIcon', errors: [{ field: 'getMdIconById', message: error.message }] });
        });
    });

    describe('Query.getMdIcons', () => {
        it('should fetch MdIcons successfully with pagination', async () => {
            const filter = { name: 'Icon' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Icon', 'i') } };
            const mdIcons = [{ id: '1', name: 'Icon Name', iconSrc: '<iconSrc></iconSrc>' }];
            const paginationInfo = { total: 1, limit: 10, skip: 0 };
    
            buildMdIconQuery.mockReturnValue(query);
            MdIcon.find.mockResolvedValue(mdIcons);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'MdIcons fetched successfully',
                result: { mdIcons },
                pagination: paginationInfo
            });
    
            const result = await mdIconResolvers.Query.getMdIcons(null, { filter, pagination });
    
            expect(buildMdIconQuery).toHaveBeenCalledWith(filter);
            expect(MdIcon.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdIcon, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'MdIcons fetched successfully',
                result: { mdIcons },
                pagination: paginationInfo
            });
        });
    
        it('should return an error if fetching MdIcons fails', async () => {
            const error = new Error('Error fetching mdIcons');
            MdIcon.find.mockImplementation(() => { throw error; });
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting mdIcons', errors: [{ field: 'getMdIcons', message: error.message }] });
    
            const result = await mdIconResolvers.Query.getMdIcons(null, {});
    
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'Error getting mdIcons', { errors: [{ field: 'getMdIcons', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error getting mdIcons', errors: [{ field: 'getMdIcons', message: error.message }] });
        });
    
        it('should return no MdIcons found when no MdIcons match the query', async () => {
            const filter = { name: 'Icon' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Icon', 'i') } };
            const mdIcons = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };
    
            buildMdIconQuery.mockReturnValue(query);
            MdIcon.find.mockResolvedValue(mdIcons);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'No MdIcons found',
                result: { mdIcons },
                pagination: paginationInfo
            });
    
            const result = await mdIconResolvers.Query.getMdIcons(null, { filter, pagination });
    
            expect(buildMdIconQuery).toHaveBeenCalledWith(filter);
            expect(MdIcon.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdIcon, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'No MdIcons found',
                result: { mdIcons },
                pagination: paginationInfo
            });
        });
    
        it('should return a failure response when no MdIcons are found', async () => {
            const filter = { name: 'Icon' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Icon', 'i') } };
            const mdIcons = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };
    
            buildMdIconQuery.mockReturnValue(query);
            MdIcon.find.mockResolvedValue(mdIcons);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'No MdIcons found',
                result: { mdIcons },
                pagination: paginationInfo
            });
    
            const result = await mdIconResolvers.Query.getMdIcons(null, { filter, pagination });
    
            expect(buildMdIconQuery).toHaveBeenCalledWith(filter);
            expect(MdIcon.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdIcon, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'No MdIcons found',
                result: { mdIcons },
                pagination: paginationInfo
            });
        });
    
        it('should return MdIcons with pagination info', async () => {
            const filter = { name: 'Icon' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: new RegExp('Icon', 'i') } };
            const mdIcons = [
                { id: '1', name: 'Icon Name', iconSrc: '<iconSrc></iconSrc>' },
                { id: '2', name: 'Another Icon', iconSrc: '<iconSrc></iconSrc>' }
            ];
            const paginationInfo = { total: 2, limit: 10, skip: 0 };
    
            buildMdIconQuery.mockReturnValue(query);
            MdIcon.find.mockResolvedValue(mdIcons);
            getPaginationInfo.mockResolvedValue(paginationInfo);
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'MdIcons fetched successfully',
                result: { mdIcons },
                pagination: paginationInfo
            });
    
            const result = await mdIconResolvers.Query.getMdIcons(null, { filter, pagination });
    
            expect(buildMdIconQuery).toHaveBeenCalledWith(filter);
            expect(MdIcon.find).toHaveBeenCalledWith(query);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdIcon, query, pagination.limit, pagination.skip);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'MdIcons fetched successfully',
                result: { mdIcons },
                pagination: paginationInfo
            });
        });
    });

    describe('Mutation.createMdIcon', () => {
        it('should create and return a MdIcon', async () => {
            const name = 'Icon Name';
            const iconSrc = '<iconSrc></iconSrc>';
            const mdIcon = { id: '1', name, iconSrc, save: jest.fn().mockResolvedValue({ id: '1', name, iconSrc }) };
            MdIcon.mockImplementation(() => mdIcon);
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdIcon created successfully', result: { mdIcon } });
    
            const result = await mdIconResolvers.Mutation.createMdIcon(null, { name, iconSrc });
    
            expect(MdIcon).toHaveBeenCalledWith({ name, iconSrc });
            expect(mdIcon.save).toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith('MdIconResponse', 'SUCCESS', 'MdIcon created successfully', {result: { mdIcon } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdIcon created successfully', result: { mdIcon } });
        });

        it('should return an error response if an error occurs', async () => {
            const name = 'Icon Name';
            const iconSrc = '<iconSrc></iconSrc>';
            const error = new Error('Error creating mdIcon');
            MdIcon.mockImplementation(() => ({
                save: jest.fn().mockRejectedValue(error),
            }));
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdIcon', errors: [{ field: 'createMdIcon', message: error.message }] });

            const result = await mdIconResolvers.Mutation.createMdIcon(null, { name, iconSrc });

            expect(MdIcon).toHaveBeenCalledWith({ name, iconSrc });
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'Error creating mdIcon', { errors: [{ field: 'createMdIcon', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdIcon', errors: [{ field: 'createMdIcon', message: error.message }] });
        });
    });

    describe('Mutation.updateMdIcon', () => {
        it('should update and return a MdIcon', async () => {
            const id = '1';
            const name = 'Updated Icon Name';
            const iconSrc = '<iconSrc></iconSrc>';
            const mdIcon = { id, name, iconSrc };
            MdIcon.findByIdAndUpdate.mockResolvedValue(mdIcon);
            createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdIcon updated successfully', result: { mdIcon } });

            const result = await mdIconResolvers.Mutation.updateMdIcon(null, { id, name, iconSrc });

            expect(MdIcon.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, iconSrc }, { new: true });
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith('MdIconResponse', 'SUCCESS', 'MdIcon updated successfully', { result: { mdIcon } });
            expect(result).toEqual({ status: 'SUCCESS', message: 'MdIcon updated successfully', result: { mdIcon } });
        });

        it('should return an error response if MdIcon is not found', async () => {
            const id = '1';
            const name = 'Updated Icon Name';
            const iconSrc = '<iconSrc></iconSrc>';
            MdIcon.findByIdAndUpdate.mockResolvedValue(null);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdIcon not found', errors: [{ field: 'updateMdIcon', message: 'MdIcon not found' }] });

            const result = await mdIconResolvers.Mutation.updateMdIcon(null, { id, name, iconSrc });

            expect(MdIcon.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, iconSrc }, { new: true });
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'MdIcon not found', { errors: [{ field: 'updateMdIcon', message: 'MdIcon not found' }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'MdIcon not found', errors: [{ field: 'updateMdIcon', message: 'MdIcon not found' }] });
        });

        it('should return an error response if an error occurs', async () => {
            const id = '1';
            const name = 'Updated Icon Name';
            const iconSrc = '<iconSrc></iconSrc>';
            const error = new Error('Error updating mdIcon');
            MdIcon.findByIdAndUpdate.mockRejectedValue(error);
            createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error updating mdIcon', errors: [{ field: 'updateMdIcon', message: error.message }] });

            const result = await mdIconResolvers.Mutation.updateMdIcon(null, { id, name, iconSrc });

            expect(MdIcon.findByIdAndUpdate).toHaveBeenCalledWith(id, { name, iconSrc }, { new: true });
            expect(createResponse).toHaveBeenCalledWith('MdIconErrorResponse', 'FAILURE', 'Error updating mdIcon', { errors: [{ field: 'updateMdIcon', message: error.message }] });
            expect(result).toEqual({ status: 'FAILURE', message: 'Error updating mdIcon', errors: [{ field: 'updateMdIcon', message: error.message }] });
        });
    });

    describe('Mutation.deleteMdIcon', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should check for references before deletion', async () => {
            const id = 'iconId';
            const references = ['MdFeature', 'MdHostLayout'];
            
            findReferences.mockResolvedValue(references);
            createResponse.mockReturnValue('errorResponse');

            const result = await mdIconResolvers.Mutation.deleteMdIcon(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id, 'MdIcon');
            expect(MdIcon.findByIdAndDelete).not.toHaveBeenCalled();
            expect(createResponse).toHaveBeenCalledWith(
                'MdIconErrorResponse',
                'FAILURE',
                'MdIcon cannot be deleted',
                {
                    errors: [{ 
                        field: 'deleteMdIcon', 
                        message: `MdIcon cannot be deleted as it has references in the following collections: ${references.join(', ')}` 
                    }]
                }
            );
            expect(result).toBe('errorResponse');
        });

        it('should delete mdIcon when no references exist', async () => {
            const id = 'iconId';
            const mockMdIcon = { _id: id, name: 'Test Icon' };
            
            findReferences.mockResolvedValue([]);
            MdIcon.findByIdAndDelete.mockResolvedValue(mockMdIcon);
            clearCacheById.mockResolvedValue();
            createResponse.mockReturnValue('successResponse');

            const result = await mdIconResolvers.Mutation.deleteMdIcon(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id, 'MdIcon');
            expect(MdIcon.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'MdIconResponse',
                'SUCCESS',
                'MdIcon deleted successfully',
                { result: { mdIcon: mockMdIcon } }
            );
            expect(result).toBe('successResponse');
        });

        it('should return error response if mdIcon not found', async () => {
            const id = 'nonexistentId';
            
            findReferences.mockResolvedValue([]);
            MdIcon.findByIdAndDelete.mockResolvedValue(null);
            createResponse.mockReturnValue('errorResponse');

            const result = await mdIconResolvers.Mutation.deleteMdIcon(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id, 'MdIcon');
            expect(MdIcon.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'MdIconErrorResponse',
                'FAILURE',
                'MdIcon not found',
                { errors: [{ field: 'deleteMdIcon', message: 'MdIcon not found' }] }
            );
            expect(result).toBe('errorResponse');
        });

        it('should handle errors during deletion', async () => {
            const id = 'iconId';
            const error = new Error('Database error');
            
            findReferences.mockResolvedValue([]);
            MdIcon.findByIdAndDelete.mockRejectedValue(error);
            createResponse.mockReturnValue('errorResponse');

            const result = await mdIconResolvers.Mutation.deleteMdIcon(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id, 'MdIcon');
            expect(MdIcon.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'MdIconErrorResponse',
                'FAILURE',
                'Error deleting mdIcon',
                { errors: [{ field: 'deleteMdIcon', message: error.message }] }
            );
            expect(result).toBe('errorResponse');
        });
    });
});
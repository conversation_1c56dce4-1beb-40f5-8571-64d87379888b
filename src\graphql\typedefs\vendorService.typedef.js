const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type VendorService {
        id: ID!
        vendorType: MdVendorType!
        vendor: Vendor!
        title: String!
        description: String
        features: [MdFeature!]
        priceRange: String!
        specialities: String
        media: [Media!]
    }

    input VendorServiceFilterInput {
        id: String
        vendorType: MdVendorTypeFilterInput
        vendor: VendorFilterInput
        title: String
        description: String
        features: [MdFeatureFilterInput!]
        priceRange: String
        specialities: String
        media: [MediaFilterInput!]
    }

    type VendorServiceWrapper {
        vendorService: VendorService!
    }

    type VendorServicesWrapper {
        vendorServices: [VendorService]!
    }

    type VendorServiceResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorServiceWrapper!
    }

    type VendorServicesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorServicesWrapper!
        pagination: PaginationInfo!
    }

    type VendorServiceErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union VendorServiceResult = VendorServiceResponse | VendorServiceErrorResponse
    union VendorServicesResult = VendorServicesResponse | VendorServiceErrorResponse

    type Query {
        getVendorServiceById(id: ID!): VendorServiceResult!
        getVendorServices(filter: VendorServiceFilterInput, pagination: PaginationInput): VendorServicesResult!
    }

    input VendorServiceInput {
        vendorType: ID!
        vendor: ID!
        title: String!
        description: String
        features: [ID!]
        priceRange: String!
        specialities: String
        media: [ID!]
    }

    input VendorServiceUpdateInput {
        vendorType: ID
        vendor: ID
        title: String
        description: String
        features: [ID!]
        priceRange: String
        specialities: String
        media: [ID!]
    }

    type Mutation {
        createVendorService(input: VendorServiceInput!): VendorServiceResult!
        updateVendorService(id: ID!, input: VendorServiceUpdateInput!): VendorServiceResult!
        deleteVendorService(id: ID!): VendorServiceResult!
    }
`;
const mdAppSettingsResolvers = require('../../../../src/graphql/resolvers/mdAppSettings.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const MdAppSettings = require('../../../../src/models/MdAppSettings');
const { getByIdCache, setCache, getBySubstring, clearCacheBySubstring } = require('../../../../src/utils/cache.util');
const mongoose = require('mongoose');
const { transformIcon } = require('../../../../src/utils/transformation.util');
const { validateReferences } = require('../../../../src/utils/validation.util');

jest.mock('../../../../src/models/MdAppSettings');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/transformation.util');
jest.mock('../../../../src/utils/validation.util');

let saveMock;

beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
    console.error.mockRestore();
});

beforeEach(() => {
    jest.clearAllMocks();
    
    saveMock = jest.fn().mockResolvedValue({ _id: 'testId' });
    
    MdAppSettings.mockReset();
    MdAppSettings.mockImplementation((data) => {
        return {
            ...data,
            _id: 'testId',
            save: saveMock
        };
    });

    MdAppSettings.findOne = jest.fn().mockResolvedValue(null);
});

describe('mdAppSettingsResolvers', () => {
    describe('Query', () => {
        describe('getMdAppSettingsByVersion', () => {
            it('should return cached mdAppSettings if available', async () => {
                const version = '1.0.0';
                const cachedSettings = {
                    version: '1.0.0',
                    navBars: {
                        userDashboard: { tabs: {} },
                        hostEventDashboard: { tabs: {} }
                    }
                };

                getByIdCache.mockResolvedValue(cachedSettings);
                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettingsByVersion(null, { version });

                expect(getByIdCache).toHaveBeenCalledWith('mdAppSettings1.0.0');
                expect(setCache).toHaveBeenCalledWith('mdAppSettings1.0.0', cachedSettings);
                expect(MdAppSettings.findOne).not.toHaveBeenCalled();
                expect(result).toBe('successResponse');
            });

            it('should fetch and transform mdAppSettings from database if not cached', async () => {
                const version = '1.0.0';
                const mockIcon = { _id: 'icon1', name: 'icon1' };
                const transformedIcon = { id: 'icon1', name: 'transformed' };
                const dbSettings = {
                    version: '1.0.0',
                    navBars: {
                        userDashboard: {
                            tabs: {
                                home: mockIcon,
                                myEvents: mockIcon
                            }
                        },
                        hostEventDashboard: {
                            tabs: {
                                eventsDb: mockIcon,
                                tasks: mockIcon
                            }
                        }
                    }
                };

                getByIdCache.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(dbSettings);
                MdAppSettings.findOne.mockReturnValue({ populate: () => ({ populate: mockPopulate }) });
                transformIcon.mockReturnValue(transformedIcon);
                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettingsByVersion(null, { version });

                expect(getByIdCache).toHaveBeenCalledWith('mdAppSettings1.0.0');
                expect(MdAppSettings.findOne).toHaveBeenCalledWith({ version });
                expect(transformIcon).toHaveBeenCalledTimes(4);
                expect(setCache).toHaveBeenCalled();
                expect(result).toBe('successResponse');
            });

            it('should return error response when mdAppSettings not found', async () => {
                const version = '1.0.0';
                
                getByIdCache.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(null);
                MdAppSettings.findOne.mockReturnValue({ populate: () => ({ populate: mockPopulate }) });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettingsByVersion(null, { version });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    'MdAppSettings not found',
                    { errors: [{ field: 'getMdAppSettingsByVersion', message: 'MdAppSettings not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors and return error response', async () => {
                const version = '1.0.0';
                const error = new Error('Database error');

                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettingsByVersion(null, { version });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    'Error getting MdAppSettings',
                    { errors: [{ field: 'getMdAppSettingsByVersion', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getMdAppSettings', () => {
            it('should return cached settings if available', async () => {
                const cachedSettings = {
                    version: '1.0',
                    navBars: {
                        userDashboard: { tabs: {} },
                        hostEventDashboard: { tabs: {} }
                    }
                };

                getBySubstring.mockResolvedValue(cachedSettings);
                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettings();

                expect(getBySubstring).toHaveBeenCalledWith('mdAppSettings');
                expect(setCache).toHaveBeenCalledWith('mdAppSettings1.0', cachedSettings);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsResponse',
                    'SUCCESS',
                    'MdAppSettings fetched successfully',
                    { result: { mdAppSettings: cachedSettings } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and transform settings from database when cache miss', async () => {
                const mockIcon = {
                    _id: 'icon123',
                    name: 'testIcon',
                    iconSrc: 'test.svg',
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                const dbSettings = {
                    version: '1.0',
                    navBars: {
                        userDashboard: { tabs: {} },
                        hostEventDashboard: { tabs: {} }
                    }
                };

                const mockPopulate = jest.fn().mockResolvedValue(dbSettings);
                const mockSort = jest.fn().mockReturnValue({ populate: () => ({ populate: mockPopulate }) });
                MdAppSettings.findOne.mockReturnValue({ sort: mockSort });
                getBySubstring.mockResolvedValue(null);
                setCache.mockImplementation(() => Promise.resolve());
                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Query.getMdAppSettings();

                expect(getBySubstring).toHaveBeenCalledWith('mdAppSettings');
                expect(MdAppSettings.findOne).toHaveBeenCalledWith({});
                expect(setCache).toHaveBeenCalledWith('mdAppSettings1.0', expect.any(Object));
                expect(result).toBe('successResponse');
            });

            it('should handle case when no settings are found', async () => {
                getBySubstring.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(null);
                const mockSort = jest.fn().mockReturnValue({ populate: () => ({ populate: mockPopulate }) });
                MdAppSettings.findOne.mockReturnValue({ sort: mockSort });
                createResponse.mockReturnValue('errorResponse');
            
                const result = await mdAppSettingsResolvers.Query.getMdAppSettings();
            
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    'MdAppSettings not found',
                    { errors: [{ field: 'getMdAppSettings', message: 'MdAppSettings not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors during execution', async () => {
                const error = new Error('Database error');
                getBySubstring.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');
            
                const result = await mdAppSettingsResolvers.Query.getMdAppSettings();
            
                expect(console.error).toHaveBeenCalledWith(error);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    'Error getting MdAppSettings',
                    { errors: [{ field: 'getMdAppSettings', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle icon transformation errors gracefully', async () => {
                const invalidIcon = {
                    _id: null,
                    name: 'testIcon'
                };
            
                const dbSettings = {
                    version: '1.0',
                    navBars: {
                        userDashboard: {
                            tabs: {
                                home: invalidIcon
                            }
                        }
                    }
                };
            
                getBySubstring.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(dbSettings);
                const mockSort = jest.fn().mockReturnValue({ populate: () => ({ populate: mockPopulate }) });
                MdAppSettings.findOne.mockReturnValue({ sort: mockSort });
                createResponse.mockReturnValue('successResponse');
            
                const result = await mdAppSettingsResolvers.Query.getMdAppSettings();
            
                expect(result).toBe('successResponse');
            
                expect(result).toBe('successResponse');
            });
        });

        describe('getMdAppSettingsVersions', () => {

            it('should return unique MdAppSettings versions successfully', async () => {
                const mockVersions = [
                    { version: '1.0.0', createdAt: new Date() },
                    { version: '1.1.0', createdAt: new Date() },
                    { version: '1.0.0', createdAt: new Date() },
                ];

                MdAppSettings.find.mockResolvedValue(mockVersions);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdAppSettings versions fetched successfully',
                    result: ['1.0.0', '1.1.0'],
                });

                const result = await mdAppSettingsResolvers.Query.getMdAppSettingsVersions();

                expect(MdAppSettings.find).toHaveBeenCalledWith({}, 'version');
                expect(MdAppSettings.find).toHaveBeenCalledWith({}, 'version');
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'MdAppSettings versions fetched successfully',
                    result: ['1.0.0', '1.1.0'],
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdAppSettings', () => {
            const mockInput = {
                version: '1.0',
                navBars: {
                    userDashboard: {
                        tabs: {
                            home: 'homeIconId',
                            myEvents: 'myEventsIconId',
                            publicEvents: 'publicEventsIconId'
                        }
                    },
                    hostEventDashboard: {
                        tabs: {
                            eventsDb: 'eventsDbIconId',
                            tasks: 'tasksIconId',
                            guests: 'guestsIconId',
                            apps: 'appsIconId',
                            messages: 'messagesIconId'
                        }
                    }
                }
            };

            const mockPopulatedSettings = {
                _id: 'settingsId',
                ...mockInput,
                navBars: {
                    userDashboard: {
                        tabs: {
                            home: { _id: 'homeIconId', name: 'Home Icon' },
                            myEvents: { _id: 'myEventsIconId', name: 'My Events Icon' },
                            publicEvents: { _id: 'publicEventsIconId', name: 'Public Events Icon' }
                        }
                    },
                    hostEventDashboard: {
                        tabs: {
                            eventsDb: { _id: 'eventsDbIconId', name: 'Events DB Icon' },
                            tasks: { _id: 'tasksIconId', name: 'Tasks Icon' },
                            guests: { _id: 'guestsIconId', name: 'Guests Icon' },
                            apps: { _id: 'appsIconId', name: 'Apps Icon' },
                            messages: { _id: 'messagesIconId', name: 'Messages Icon' }
                        }
                    }
                }
            };

            it('should create new MdAppSettings when version does not exist', async () => {
                validateReferences.mockResolvedValue(null);
                MdAppSettings.findOne.mockResolvedValue(null);
                
                const mockSave = jest.fn().mockResolvedValue({ _id: 'newId', ...mockInput });
                MdAppSettings.mockImplementation(() => ({
                    save: mockSave
                }));

                const mockPopulate = jest.fn().mockReturnThis();
                MdAppSettings.findById.mockReturnValue({
                    populate: mockPopulate
                });
                mockPopulate.mockReturnValueOnce({
                    populate: jest.fn().mockResolvedValue(mockPopulatedSettings)
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Mutation.createMdAppSettings(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(MdAppSettings.findOne).toHaveBeenCalledWith({ version: mockInput.version });
                expect(mockSave).toHaveBeenCalled();
                expect(clearCacheBySubstring).toHaveBeenCalledWith("mdAppSettings");
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsResponse',
                    'SUCCESS',
                    'MdAppSettings created successfully',
                    { result: { mdAppSettings: mockPopulatedSettings } }
                );
                expect(result).toBe('successResponse');
            });

            it('should update existing MdAppSettings when version exists', async () => {
                validateReferences.mockResolvedValue(null);
                const existingSettings = { _id: 'existingId', version: '1.0' };
                MdAppSettings.findOne.mockResolvedValue(existingSettings);

                const mockPopulate = jest.fn().mockReturnThis();
                MdAppSettings.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });
                mockPopulate.mockReturnValueOnce({
                    populate: jest.fn().mockResolvedValue(mockPopulatedSettings)
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdAppSettingsResolvers.Mutation.createMdAppSettings(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(MdAppSettings.findOne).toHaveBeenCalledWith({ version: mockInput.version });
                expect(MdAppSettings.findByIdAndUpdate).toHaveBeenCalledWith(
                    existingSettings._id,
                    mockInput,
                    { new: true }
                );
                expect(clearCacheBySubstring).toHaveBeenCalledWith("mdAppSettings");
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsResponse',
                    'SUCCESS',
                    'MdAppSettings updated successfully',
                    { result: { mdAppSettings: mockPopulatedSettings } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when validation fails', async () => {
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'test', message: 'Invalid field' }]
                };
                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdAppSettingsResolvers.Mutation.createMdAppSettings(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors', async () => {
                const error = new Error('Unexpected error');
                validateReferences.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdAppSettingsResolvers.Mutation.createMdAppSettings(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdAppSettingsErrorResponse',
                    'FAILURE',
                    'Error creating MdAppSettings',
                    { errors: [{ field: 'createMdAppSettings', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });

    describe('Icon Transformation Tests', () => {
        it('should handle null or undefined icon values', async () => {
            const mdAppSettings = {
                version: '1.0',
                navBars: {
                    userDashboard: {
                        tabs: {
                            home: null,
                            myEvents: undefined,
                            publicEvents: {}
                        }
                    },
                    hostEventDashboard: {
                        tabs: {
                            eventsDb: null,
                            tasks: undefined,
                            guests: {},
                            apps: null,
                            messages: undefined
                        }
                    }
                }
            };

            getByIdCache.mockResolvedValue(null);
            MdAppSettings.findOne.mockReturnValue({
                populate: jest.fn().mockReturnThis(),
                exec: jest.fn().mockResolvedValue(mdAppSettings)
            });

            createResponse.mockImplementation((type, status, message, data) => ({
                result: {
                    mdAppSettings: mdAppSettings
                }
            }));

            const result = await mdAppSettingsResolvers.Query.getMdAppSettingsByVersion(null, { version: '1.0' });

            expect(transformIcon).not.toHaveBeenCalled();

            expect(result.result.mdAppSettings.navBars.userDashboard.tabs).toEqual({
                home: null,
                myEvents: undefined,
                publicEvents: {}
            });
            expect(result.result.mdAppSettings.navBars.hostEventDashboard.tabs).toEqual({
                eventsDb: null,
                tasks: undefined,
                guests: {},
                apps: null,
                messages: undefined
            });
        });
    });
});
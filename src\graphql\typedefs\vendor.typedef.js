const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type VendorRatingAggregate {
        averageRating: Float!
        totalRating: Int!
    }

    type Vendor {
        id: ID!
        name: String!
        primaryContact: VendorUser!
        contacts: [VendorUser!]
        businessAddress: Address!
        serviceLocations: [MdServiceLocation!]
        servicesProvided: [VendorService!]
        ratingAggregates: VendorRatingAggregate!
        reviews: [VendorRating!]
    }

    input VendorFilterInput {
        name: String
        primaryContact: VendorUserFilterInput
        contacts: [VendorUserFilterInput!]
        businessAddress: AddressFilterInput
        serviceLocations: [MdServiceLocationFilterInput!]
        servicesProvided: [VendorServiceFilterInput!]
        minRating: Float
        minTotalReviews: Int
    }

    type VendorWrapper {
        vendor: Vendor!
    }

    type VendorsWrapper {
        vendors: [Vendor]!
    }

    type VendorResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorWrapper!
    }

    type VendorsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorsWrapper!
        pagination: PaginationInfo!
    }

    type VendorErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union VendorResult = VendorResponse | VendorErrorResponse
    union VendorsResult = VendorsResponse | VendorErrorResponse

    type Query {
        getVendorById(id: ID!): VendorResult!
        getVendors(filter: VendorFilterInput, pagination: PaginationInput): VendorsResult!
    }

    input VendorInput {
        name: String!
        primaryContact: ID!
        contacts: [ID!]
        businessAddress: ID!
        serviceLocations: [ID!]
    }

    input VendorUpdateInput {
        name: String
        primaryContact: ID
        contacts: [ID!]
        businessAddress: ID
        serviceLocations: [ID!]
    }

    type Mutation {
        createVendor(input: VendorInput!): VendorResult!
        updateVendor(id: ID!, input: VendorUpdateInput!): VendorResult!
        deleteVendor(id: ID!): VendorResult!
    }
`;

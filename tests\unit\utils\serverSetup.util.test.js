const express = require("express");
const request = require("supertest");
const mongoose = require("mongoose");
const { ApolloServer } = require("apollo-server-express");
const {
  configureMiddleware,
  createServer,
  createContext,
  getTokenFromHeader,
  connectToDbAndRunApp,
} = require("../../../src/utils/serverSetup.util");
const { GraphQLUpload } = require('graphql-upload');

jest.mock("mongoose");

const mockConnection = {
  on: jest.fn(),
  once: jest.fn(),
  readyState: 1, 
};

mongoose.connection = mockConnection;

jest.mock("@clerk/clerk-sdk-node", () => ({
  sessions: {
    verifySession: jest.fn(),
  },
  users: {
    getUser: jest.fn(),
  },
}));

describe("Server Tests", () => {
  let app;
  let server;

  beforeAll(() => {
    app = express();
    configureMiddleware(app);
  });

  afterAll(async () => {
    await mongoose.disconnect();
  });

  beforeEach(() => {
    console.error = jest.fn();
    jest.clearAllMocks(); 
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("Middleware modifies response.json to handle errors", async () => {
    app.get("/test", (req, res) => {
      res.json({ errors: [{ extensions: { http: { status: 400 } } }] });
    });

    const response = await request(app).get("/test");
    expect(response.status).toBe(400);
  });

  test("Create Apollo server", async () => {
    const typeDefs = `
      scalar Upload
      type Query {
        hello: String
      }
    `;
    const resolvers = {
      Upload: GraphQLUpload,
      Query: {
        hello: () => "Hello World",
      },
    };

    server = await createServer(app, typeDefs, resolvers);
    const response = await request(app).post("/graphql").send({
      query: "{ hello }",
    });
    expect(response.body.data.hello).toBe("Hello World");
  });

  test("Extract token from header", () => {
    const req = {
      headers: {
        authorization: "Bearer testtoken",
      },
    };
    const token = getTokenFromHeader(req);
    expect(token).toBe("testtoken");
  });

  test("Create context with valid token", async () => {
    const { sessions, users } = require("@clerk/clerk-sdk-node");
    const req = {
      headers: {
        authorization: "Bearer testtoken",
      },
    };

    sessions.verifySession.mockResolvedValue({ userId: "testuser" });
    users.getUser.mockResolvedValue({ id: "testuser" });

    const context = await createContext({ req });
    expect(context.user).toEqual({ id: "testuser" });
  });

  test("Create context with invalid token should return empty object", async () => {
    const { sessions, users } = require("@clerk/clerk-sdk-node");
    const req = {
      headers: {
        authorization: "Bearer invalidtoken",
      },
    };

    sessions.verifySession.mockRejectedValue(new Error("Invalid token"));

    const context = await createContext({ req });
    
    expect(context).toEqual({});
    
    expect(console.error).toHaveBeenCalledWith(
      "Invalid token",
      expect.any(Error)
    );
  });

  test("Connect to MongoDB and start app", async () => {
    const MONGODB = "mongodb://localhost/test";
    process.env.MONGODB = MONGODB;
    const PORT = 5001;

    mongoose.connect.mockResolvedValueOnce();

    mockConnection.on.mockImplementation((event, callback) => {
      if (event === 'connected') {
        callback(); 
      }
    });

    const mockServer = {
      graphqlPath: '/graphql'
    };

    await connectToDbAndRunApp(app, mockServer);
    expect(mongoose.connect).toHaveBeenCalledWith(MONGODB, {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
    });
    expect(mockConnection.on).toHaveBeenCalledWith('disconnected', expect.any(Function));
    expect(mockConnection.on).toHaveBeenCalledWith('connected', expect.any(Function));
  }, 10000);

  test("Should handle initial MongoDB connection failure", async () => {
    const MONGODB = "mongodb://localhost/test";
    process.env.MONGODB = MONGODB;
    
    mongoose.connect.mockRejectedValueOnce(new Error("Connection failed"));
    
    let isConnectedBefore = false;
    mockConnection.on.mockImplementation((event, callback) => {
      if (event === 'disconnected') {
        callback();
      }
    });

    const mockServer = {
      graphqlPath: '/graphql'
    };

    await expect(connectToDbAndRunApp(app, mockServer)).rejects.toThrow("Connection failed");
    
    expect(mongoose.connect).toHaveBeenCalledWith(MONGODB, {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
    });
    
    expect(console.error).toHaveBeenCalledWith(
      "Initial MongoDB connection failed:",
      expect.any(Error)
    );
    expect(console.error).toHaveBeenCalledWith(
      "Initial MongoDB connection failed."
    );
  });
});

describe('serverSetup.util', () => {
  let originalEnv;

  beforeEach(() => {
    originalEnv = process.env;
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('connectToDbAndRunApp', () => {
    it('should reject with error when MONGODB environment variable is not set', async () => {
      delete process.env.MONGODB;

      const mockApp = {};
      const mockServer = {};

      await expect(connectToDbAndRunApp(mockApp, mockServer))
        .rejects
        .toThrow('MONGODB environment variable is not set.');

      expect(console.error)
        .toHaveBeenCalledWith('MONGODB environment variable is not set.');
    });
  });
});

describe('serverSetup.util.js', () => {
  let consoleErrorSpy;
  const mockApp = {
    listen: jest.fn()
  };
  const mockServer = {
    graphqlPath: '/graphql'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.MONGODB = 'mongodb://localhost:27017/test';
    process.env.PORT = '5010';
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
    delete process.env.MONGODB;
    delete process.env.PORT;
  });

  describe('MongoDB Connection Error Handling', () => {
    it('should log appropriate messages when initial connection fails', async () => {
      const connectionError = new Error('Connection failed');
      let errorCallback;

      mongoose.connection.on.mockImplementation((event, callback) => {
        if (event === 'error') {
          errorCallback = callback;
        }
      });

      mongoose.connect.mockRejectedValue(connectionError);

      const connectPromise = connectToDbAndRunApp(mockApp, mockServer);
      
      errorCallback(connectionError);

      await expect(connectPromise).rejects.toThrow(connectionError);

      expect(consoleErrorSpy).toHaveBeenCalledWith('MongoDB connection error:', connectionError);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Unable to connect to MongoDB.');
    });

    it('should handle connection errors differently after initial connection', async () => {
      let isConnectedBefore = false;
      let errorCallback;

      mongoose.connection.on.mockImplementation((event, callback) => {
        if (event === 'error') {
          errorCallback = callback;
        }
        if (event === 'connected') {
          callback();
          isConnectedBefore = true;
        }
      });

      mongoose.connect.mockResolvedValue();
      mockApp.listen.mockImplementation((port, cb) => cb());

      const connectPromise = connectToDbAndRunApp(mockApp, mockServer);

      const subsequentError = new Error('Subsequent error');
      errorCallback(subsequentError);

      await connectPromise;

      expect(consoleErrorSpy).toHaveBeenCalledWith('MongoDB connection error:', subsequentError);
      expect(consoleErrorSpy).not.toHaveBeenCalledWith('Unable to connect to MongoDB.');
    });

    it('should attempt reconnection after disconnection if previously connected', async () => {
      let disconnectCallback;
      let connectedCallback;

      mongoose.connection.on.mockImplementation((event, callback) => {
        if (event === 'disconnected') {
          disconnectCallback = callback;
        }
        if (event === 'connected') {
          connectedCallback = callback;
        }
      });

      mongoose.connect.mockResolvedValue();
      mockApp.listen.mockImplementation((port, cb) => cb());

      const connectPromise = connectToDbAndRunApp(mockApp, mockServer);
      
      connectedCallback();
      
      disconnectCallback();

      await connectPromise;

      expect(consoleErrorSpy).toHaveBeenCalledWith('MongoDB disconnected!');
      expect(consoleErrorSpy).toHaveBeenCalledWith('MongoDB connection lost. Attempting to reconnect...');
    });
  });
});

describe('serverSetup.util', () => {
  let mockApp;
  let mockServer;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.MONGODB = 'mongodb://localhost:27017/test';
    
    mockApp = {
      listen: jest.fn()
    };
    
    mockServer = {
      graphqlPath: '/graphql'
    };
  });

  afterEach(() => {
    delete process.env.MONGODB;
  });

  describe('connectToDbAndRunApp', () => {
    it('should reject with error when server fails to start', async () => {
      const serverError = new Error('Server start error');
      mockApp.listen = jest.fn((port, callback) => {
        callback(serverError);
      });

      await expect(connectToDbAndRunApp(mockApp, mockServer))
        .rejects
        .toThrow('Server start error');

      expect(mockApp.listen).toHaveBeenCalled();
    });

    it('should handle server start error with proper error message', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error');
      const serverError = new Error('Port already in use');
      
      mockApp.listen = jest.fn((port, callback) => {
        callback(serverError);
      });

      try {
        await connectToDbAndRunApp(mockApp, mockServer);
      } catch (error) {
        expect(error.message).toBe('Server start error');
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error starting server:', serverError);
      }

      consoleErrorSpy.mockRestore();
    });
  });
});

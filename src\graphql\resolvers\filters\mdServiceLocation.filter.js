const mongoose = require('mongoose');
const { isValidObjectId } = require('../../../utils/validation.util');

const buildMdServiceLocationQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }
        if (filter.name) {
            query.name = { $regex: new RegExp(filter.name, 'i') };
        }
        if (filter.city) {
            query.city = { $regex: new RegExp(filter.city, 'i') };
        }
        if (filter.state) {
            query.state = { $regex: new RegExp(filter.state, 'i') };
        }
        if (filter.areas && filter.areas.length > 0) {
            query.areas = { $in: filter.areas.map(area => new RegExp(area, 'i')) };
        }
    }

    return query;
};

module.exports = buildMdServiceLocationQuery;
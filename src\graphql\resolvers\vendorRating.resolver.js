const VendorRating = require('../../models/VendorRating');
const { User } = require('../../models/User');
const Vendor = require('../../models/Vendor');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildVendorRatingQuery = require('./filters/vendorRating.filter');

const vendorRatingResolvers = {
    VendorRating: {
        user: async (parent) => {
            try {
                return await User.findById(parent.user);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        },
        vendor: async (parent) => {
            try {
                return await Vendor.findById(parent.vendor);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor');
            }
        }
    },

    Query: {
        getVendorRatingById: async (_, { id }) => {
            try {
                let vendorRating = await getByIdCache(id);
                if (!vendorRating) {
                    vendorRating = await VendorRating.findById(id);
                    if (!vendorRating) {
                        return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Vendor rating not found', {
                            errors: [{ field: 'id', message: 'Vendor rating not found' }]
                        });
                    }
                    await setCache(id, vendorRating);
                }
                return createResponse('VendorRatingResponse', 'SUCCESS', 'Vendor rating retrieved successfully', {
                    result: { vendorRating }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Error retrieving vendor rating', {
                    errors: [{ field: 'getVendorRatingById', message: error.message }]
                });
            }
        },

        getVendorRatings: async (_, { filter, pagination }) => {
            try {
                const pipeline = buildVendorRatingQuery(filter);
                
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const countPipeline = [...pipeline, { $count: 'total' }];
                const countResult = await VendorRating.aggregate(countPipeline);
                const totalCount = countResult[0]?.total || 0;

                pipeline.push({ $skip: skip });
                pipeline.push({ $limit: limit });
                pipeline.push({ $sort: { createdAt: -1 } });

                const vendorRatings = await VendorRating.aggregate(pipeline);

                const paginationInfo = {
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    currentPage: Math.floor(skip / limit) + 1,
                    pageSize: limit,
                    hasNextPage: skip + limit < totalCount,
                    hasPreviousPage: skip > 0
                };

                if (vendorRatings.length === 0) {
                    return createResponse('VendorRatingsResponse', 'FAILURE', 'No vendor ratings found', {
                        result: { vendorRatings },
                        pagination: paginationInfo
                    });
                }

                const mappedVendorRatings = vendorRatings.map(rating => ({
                    ...rating,
                    id: rating._id.toString(),
                    userDetails: rating.userDetails ? {
                        ...rating.userDetails,
                        id: rating.userDetails._id.toString()
                    } : null,
                    vendorDetails: rating.vendorDetails ? {
                        ...rating.vendorDetails,
                        id: rating.vendorDetails._id.toString()
                    } : null
                }));

                return createResponse('VendorRatingsResponse', 'SUCCESS', 'Vendor ratings fetched successfully', {
                    result: { vendorRatings: mappedVendorRatings },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Error retrieving vendor ratings', {
                    errors: [{ field: 'getVendorRatings', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createVendorRating: async (_, { input }) => {
            try {
                const vendorRating = new VendorRating(input);
                await vendorRating.save();
                
                return createResponse('VendorRatingResponse', 'SUCCESS', 'Vendor rating created successfully', {
                    result: { vendorRating }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Error creating vendor rating', {
                    errors: [{ field: 'createVendorRating', message: error.message }]
                });
            }
        },

        updateVendorRating: async (_, { id, input }) => {
            try {
                const vendorRating = await VendorRating.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                ).populate('user vendor');

                if (!vendorRating) {
                    return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Vendor rating not found', {
                        errors: [{ field: 'id', message: 'Vendor rating not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('VendorRatingResponse', 'SUCCESS', 'Vendor rating updated successfully', {
                    result: { vendorRating }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Error updating vendor rating', {
                    errors: [{ field: 'updateVendorRating', message: error.message }]
                });
            }
        },

        deleteVendorRating: async (_, { id }) => {
            try {
                const vendorRating = await VendorRating.findByIdAndDelete(id);
                
                if (!vendorRating) {
                    return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Vendor rating not found', {
                        errors: [{ field: 'id', message: 'Vendor rating not found' }]
                    });
                }

                await clearCacheById(id);

                return createResponse('VendorRatingResponse', 'SUCCESS', 'Vendor rating deleted successfully', {
                    result: { vendorRating }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorRatingErrorResponse', 'FAILURE', 'Error deleting vendor rating', {
                    errors: [{ field: 'deleteVendorRating', message: error.message }]
                });
            }
        }
    }
};

module.exports = vendorRatingResolvers;

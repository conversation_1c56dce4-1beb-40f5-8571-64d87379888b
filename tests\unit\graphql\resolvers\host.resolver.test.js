const { createResponse } = require('../../../../src/utils/response.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const Host = require('../../../../src/models/Host');
const { User } = require('../../../../src/models/User');
const hostResolvers = require('../../../../src/graphql/resolvers/host.resolver');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/models/Host');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/referenceCheck.util');

describe('hostResolvers', () => {
    describe('Host', () => {
        it('should return user by userId', async () => {
            const userId = '123';
            const user = { id: userId, name: 'John Doe' };
            User.findById.mockResolvedValue(user);

            const result = await hostResolvers.Host.userId({ userId });

            expect(User.findById).toHaveBeenCalledWith(userId);
            expect(result).toEqual(user);
        });

        it('should return null if user not found', async () => {
            const userId = '123';
            User.findById.mockResolvedValue(null);
        
            const result = await hostResolvers.Host.userId({ userId });
        
            expect(User.findById).toHaveBeenCalledWith(userId);
            expect(result).toBeNull();
        });

        it('should throw an error if user retrieval fails', async () => {
            const userId = '123';
            const error = new Error('Error getting user');
            User.findById.mockRejectedValue(error);

            await expect(hostResolvers.Host.userId({ userId })).rejects.toThrow('Error getting user');
        });
    });

    describe('Query', () => {
        describe('getHostById', () => {
            it('should return host from cache if available', async () => {
                const id = '1';
                const cachedHost = { id, userId: '123' };
                getByIdCache.mockResolvedValue(cachedHost);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Host retrieved successfully from cache',
                    result: { host: cachedHost },
                });

                const result = await hostResolvers.Query.getHostById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Host retrieved successfully from cache',
                    result: { host: cachedHost },
                });
            });

            it('should return error if cache retrieval fails', async () => {
                const id = '1';
                const error = new Error('Cache error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving host',
                    errors: [{ field: 'id', message: error.message }],
                });
            
                const result = await hostResolvers.Query.getHostById(null, { id });
            
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving host',
                    errors: [{ field: 'id', message: error.message }],
                });
            });

            it('should return host from database if not in cache', async () => {
                const id = '1';
                const host = { id, userId: '123' };
                getByIdCache.mockResolvedValue(null);
                Host.findById.mockResolvedValue(host);
                setCache.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Host retrieved successfully',
                    result: { host },
                });

                const result = await hostResolvers.Query.getHostById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Host.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, host);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Host retrieved successfully',
                    result: { host },
                });
            });

            it('should return error if host not found', async () => {
                const id = '1';
                getByIdCache.mockResolvedValue(null);
                Host.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }],
                });

                const result = await hostResolvers.Query.getHostById(null, { id });

                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }],
                });
            });

            it('should return error on exception', async () => {
                const id = '1';
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving host',
                    errors: [{ field: 'id', message: error.message }],
                });

                const result = await hostResolvers.Query.getHostById(null, { id });

                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving host',
                    errors: [{ field: 'id', message: error.message }],
                });
            });

            it('should return error if host is not found in both cache and database', async () => {
                const id = '1';
                getByIdCache.mockResolvedValue(null);
                Host.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }],
                });
            
                const result = await hostResolvers.Query.getHostById(null, { id });
            
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Host.findById).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }],
                });
            });
        });

        describe('getHosts', () => {
            it('should return hosts with pagination', async () => {
                const pagination = { limit: 10, skip: 0 };
                const hosts = [{ id: '1', userId: '123' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                Host.find.mockResolvedValue(hosts);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Hosts fetched successfully',
                    result: { hosts },
                    pagination: paginationInfo,
                });

                const result = await hostResolvers.Query.getHosts(null, { pagination });

                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Hosts fetched successfully',
                    result: { hosts },
                    pagination: paginationInfo,
                });
            });

            it('should use default pagination if none is provided', async () => {
                const hosts = [{ id: '1', userId: '123' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                Host.find.mockResolvedValue(hosts);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Hosts fetched successfully',
                    result: { hosts },
                    pagination: paginationInfo,
                });
            
                const result = await hostResolvers.Query.getHosts(null, { pagination: null });
            
                expect(getPaginationInfo).toHaveBeenCalledWith(Host, {}, 10, 0);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Hosts fetched successfully',
                    result: { hosts },
                    pagination: paginationInfo,
                });
            });

            it('should return error if no hosts found', async () => {
                const pagination = { limit: 10, skip: 0 };
                const hosts = [];
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
                Host.find.mockResolvedValue(hosts);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'No hosts found',
                    result: { hosts },
                    pagination: paginationInfo,
                });

                const result = await hostResolvers.Query.getHosts(null, { pagination });

                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'No hosts found',
                    result: { hosts },
                    pagination: paginationInfo,
                });
            });

            it('should return error on exception', async () => {
                const pagination = { limit: 10, page: 1 };
                const error = new Error('Database error');
            
                Host.find.mockImplementation(() => ({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    exec: jest.fn().mockRejectedValue(error),
                }));
            
                getPaginationInfo.mockResolvedValue({ limit: 10, skip: 0, total: 0 });
            
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error getting hosts',
                    errors: [{ field: 'getHosts', message: error.message }],
                });
            
                const result = await hostResolvers.Query.getHosts(null, { pagination });
            
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error getting hosts',
                    errors: [{ field: 'getHosts', message: error.message }],
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createHost', () => {
            it('should create and return host', async () => {
                const userId = '123';
                const host = { id: '1', userId };
                Host.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(host),
                }));
                setCache.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Host created successfully',
                    result: { host },
                });

                const result = await hostResolvers.Mutation.createHost(null, { userId });

                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Host created successfully',
                    result: { host },
                });
            });

            it('should create host but return error if caching fails', async () => {
                const userId = '123';
                const host = { id: '1', userId };
                Host.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(host),
                }));
                const error = new Error('Cache error');
                setCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Host created successfully, but cache failed',
                    result: { host },
                    errors: [{ field: 'cache', message: error.message }],
                });
            
                const result = await hostResolvers.Mutation.createHost(null, { userId });
            
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Host created successfully, but cache failed',
                    result: { host },
                    errors: [{ field: 'cache', message: error.message }],
                });
            });

            it('should return error on exception', async () => {
                const userId = '123';
                const error = new Error('Database error');
                Host.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating host',
                    errors: [{ field: 'userId', message: error.message }],
                });

                const result = await hostResolvers.Mutation.createHost(null, { userId });

                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating host',
                    errors: [{ field: 'userId', message: error.message }],
                });
            });
        });

        describe('deleteHost', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should check for references before deletion', async () => {
                const id = 'hostId';
                const references = ['Party', 'Event'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Host cannot be deleted',
                    errors: [{
                        field: 'id',
                        message: 'Host cannot be deleted as it is being used in: Party, Event'
                    }]
                });

                const result = await hostResolvers.Mutation.deleteHost(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Host');
                expect(Host.findByIdAndDelete).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Host cannot be deleted',
                    errors: [{
                        field: 'id',
                        message: 'Host cannot be deleted as it is being used in: Party, Event'
                    }]
                });
            });

            it('should delete host when no references exist', async () => {
                const id = 'hostId';
                const mockHost = { _id: id, userId: 'userId' };
                
                findReferences.mockResolvedValue([]);
                Host.findByIdAndDelete.mockResolvedValue(mockHost);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Host deleted successfully',
                    result: { host: mockHost }
                });

                const result = await hostResolvers.Mutation.deleteHost(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Host');
                expect(Host.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Host deleted successfully',
                    result: { host: mockHost }
                });
            });

            it('should return error when host not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                Host.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }]
                });

                const result = await hostResolvers.Mutation.deleteHost(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Host');
                expect(Host.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Host not found',
                    errors: [{ field: 'id', message: 'Host not found' }]
                });
            });

            it('should handle errors during deletion', async () => {
                const id = 'hostId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Host.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting host',
                    errors: [{ field: 'id', message: error.message }]
                });

                const result = await hostResolvers.Mutation.deleteHost(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Host');
                expect(Host.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting host',
                    errors: [{ field: 'id', message: error.message }]
                });
            });
        });
    });
});
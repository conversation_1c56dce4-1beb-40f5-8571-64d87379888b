const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Host {
        id: ID
        userId: User!
    }

    input HostFilterInput {
        eventId: ID
        userId: UserFilterInput
    }

    type HostWrapper {
        host: Host!
    }

    type HostsWrapper {
        hosts: [Host]!
    }

    type HostResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostWrapper!
    }

    type HostsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostsWrapper!
        pagination: PaginationInfo!
    }

    type HostErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union HostResult = HostResponse | HostErrorResponse
    union HostsResult = HostsResponse | HostErrorResponse

    type Query {
        getHostById(id: ID!): HostResult!
        getHosts(pagination: PaginationInput, filter: HostFilterInput): HostsResult!
    }

    type Mutation {
        createHost(userId: ID!): HostResult!
        deleteHost(id: ID!): HostResult!
    }
`;
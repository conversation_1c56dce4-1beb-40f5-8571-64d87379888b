const { model, Schema } = require('mongoose');

const invitationSchema = new Schema({
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: true },
    message: { type: String, required: false },
    media: [{ type: Schema.Types.ObjectId, ref: 'Media', required: false }],
    savedGuests: [{ type: Schema.Types.ObjectId, ref: 'Guest', required: false }],
    sentToGuests: [{ type: Schema.Types.ObjectId, ref: 'Guest', required: false }]
}, { timestamps: true, collection: 'invitations' });

const Invitation = model('Invitation', invitationSchema);

module.exports = Invitation;

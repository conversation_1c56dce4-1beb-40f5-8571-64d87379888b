const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdVendorType = require('../../../src/models/MdVendorType');
const MdFeature = require('../../../src/models/MdFeature');
const MdIcon = require('../../../src/models/MdIcon');
const MdPartner = require('../../../src/models/MdPartner');
const MdPartnerCategory = require('../../../src/models/MdPartnerCategory');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdVendorType Model Test', () => {
    it('should create and save a MdVendorType successfully', async () => {
        const mockFeature = new MdFeature({ name: 'Test Feature', icon: new mongoose.Types.ObjectId() });
        await mockFeature.save();
    
        const mockIcon = new MdIcon({ name: 'Test Icon', iconSrc: '<iconSrc></iconSrc>' });
        await mockIcon.save();
    
        const mockCategory = new MdPartnerCategory({ name: 'Test Category', icon: mockIcon._id, description: 'Test Description' });
        await mockCategory.save();
    
        const mockPartner = new MdPartner({
            name: 'Test Partner',
            description: 'Test Description',
            category: mockCategory._id,
            icon: mockIcon._id
        });
        await mockPartner.save();
    
        const validMdVendorType = new MdVendorType({
            name: 'Test Vendor Type',
            description: 'Test Description',
            primaryFeature: mockFeature._id,
            features: [mockFeature._id],
            mainFeatures: [mockFeature._id],
            bannerImage: 'http://example.com/banner.jpg',
            speciality: 'Test Speciality',
            icon: mockIcon._id,
            partners: [mockPartner._id]
        });
    
        const savedMdVendorType = await validMdVendorType.save();
    
        expect(savedMdVendorType._id).toBeDefined();
        expect(savedMdVendorType.name).toBe('Test Vendor Type');
        expect(savedMdVendorType.description).toBe('Test Description');
        expect(savedMdVendorType.primaryFeature).toEqual(mockFeature._id);
        expect(savedMdVendorType.features).toContainEqual(mockFeature._id);
        expect(savedMdVendorType.mainFeatures).toContainEqual(mockFeature._id);
        expect(savedMdVendorType.bannerImage).toBe('http://example.com/banner.jpg');
        expect(savedMdVendorType.speciality).toBe('Test Speciality');
        expect(savedMdVendorType.icon).toEqual(mockIcon._id);
        expect(savedMdVendorType.partners).toContainEqual(mockPartner._id);
    });

    it('should fail to create a MdVendorType without required fields', async () => {
        const mdVendorType = new MdVendorType();

        let err;
        try {
            await mdVendorType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });

    it('should create a MdVendorType with minimal required fields', async () => {
        const minimalMdVendorType = new MdVendorType({
            name: 'Minimal Vendor Type',
            description: 'Minimal Description'
        });

        const savedMinimalMdVendorType = await minimalMdVendorType.save();

        expect(savedMinimalMdVendorType._id).toBeDefined();
        expect(savedMinimalMdVendorType.name).toBe('Minimal Vendor Type');
        expect(savedMinimalMdVendorType.description).toBe('Minimal Description');
    });

    it('should fail to create a MdVendorType with invalid reference IDs', async () => {
        const invalidMdVendorType = new MdVendorType({
            name: 'Invalid Vendor Type',
            description: 'Invalid Description',
            primaryFeature: 'invalid_id',
            features: ['invalid_id'],
            mainFeatures: ['invalid_id'],
            icon: 'invalid_id',
            partners: ['invalid_id']
        });

        let err;
        try {
            await invalidMdVendorType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.primaryFeature).toBeDefined();
        expect(err.errors['features.0']).toBeDefined();
        expect(err.errors['mainFeatures.0']).toBeDefined();
        expect(err.errors.icon).toBeDefined();
        expect(err.errors['partners.0']).toBeDefined();
    });
});
const VenueAddress = require('../../models/VenueAddress');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildVenueAddressQuery = require('./filters/venueAddress.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { Client } = require('@googlemaps/google-maps-services-js');

const googleMapsClient = new Client({});

const venueAddressResolvers = {
    Query: {
        getVenueAddressById: async (_, { id }) => {
            try {
                let venueAddress = await getByIdCache(id);
                if (!venueAddress) {
                    venueAddress = await VenueAddress.findById(id);
                    if (!venueAddress) {
                        return createResponse('VenueAddressErrorResponse', 'FAILURE', 'VenueAddress not found', {
                            errors: [{ field: 'id', message: 'VenueAddress not found' }]
                        });
                    }
                    await setCache(id, venueAddress);
                }
                return createResponse('VenueAddressResponse', 'SUCCESS', 'VenueAddress retrieved successfully', {
                    result: { venueAddress }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Error retrieving venue address', {
                    errors: [{ field: 'getVenueAddressById', message: error.message }]
                });
            }
        },

        getVenueAddresses: async (_, { filter, pagination }) => {
            try {
                const query = await buildVenueAddressQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(VenueAddress, query, limit, skip);

                const venueAddresses = await VenueAddress.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (venueAddresses.length === 0) {
                    return createResponse('VenueAddressesResponse', 'FAILURE', 'No venue addresses found', {
                        result: { venueAddresses },
                        pagination: paginationInfo
                    });
                }

                return createResponse('VenueAddressesResponse', 'SUCCESS', 'Venue addresses fetched successfully', {
                    result: { venueAddresses },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Error retrieving venue addresses', {
                    errors: [{ field: 'getVenueAddresses', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createVenueAddress: async (_, { input }) => {
            try {
                const addressDetails = await VenueAddress.extractAddressDetails(input.placeId);
                const venueAddress = new VenueAddress({
                    ...input,
                    ...addressDetails
                });

                await venueAddress.updateCoordinatesFromPlaceId();
                await venueAddress.save();
                return createResponse('VenueAddressResponse', 'SUCCESS', 'Venue address created successfully', {
                    result: { venueAddress }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Error creating venue address', {
                    errors: [{ field: 'createVenueAddress', message: error.message }]
                });
            }
        },

        updateVenueAddress: async (_, { id, input }) => {
            try {
                const existingVenueAddress = await VenueAddress.findById(id);
                if (!existingVenueAddress) {
                    return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Venue address not found', {
                        errors: [{ field: 'id', message: 'Venue address not found' }]
                    });
                }

                if (input.placeId) {
                    const addressDetails = await VenueAddress.extractAddressDetails(input.placeId);
                    input = { ...input, ...addressDetails };
                }

                const venueAddress = await VenueAddress.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (venueAddress.placeId) await venueAddress.updateCoordinatesFromPlaceId();

                await clearCacheById(id);
                return createResponse('VenueAddressResponse', 'SUCCESS', 'Venue address updated successfully', {
                    result: { venueAddress }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Error updating venue address', {
                    errors: [{ field: 'updateVenueAddress', message: error.message }]
                });
            }
        },

        deleteVenueAddress: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'VenueAddress');
                if (references.length > 0) {
                    return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Venue address cannot be deleted', {
                        errors: [{ field: 'id', message: `Venue address cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const venueAddress = await VenueAddress.findByIdAndDelete(id);

                if (!venueAddress) {
                    return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Venue address not found', {
                        errors: [{ field: 'id', message: 'Venue address not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('VenueAddressResponse', 'SUCCESS', 'Venue address deleted successfully', {
                    result: { venueAddress }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VenueAddressErrorResponse', 'FAILURE', 'Error deleting venue address', {
                    errors: [{ field: 'deleteVenueAddress', message: error.message }]
                });
            }
        }
    }
};

module.exports = venueAddressResolvers; 
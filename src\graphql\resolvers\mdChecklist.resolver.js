const MdChecklist = require('../../models/MdChecklist');
const MdPartyType = require('../../models/MdPartyType');
const { getByIdCache, clearCacheById, setCache } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { createResponse } = require('../../utils/response.util');
const { validateReferences } = require('../../utils/validation.util');
const buildMdChecklistQuery = require('./filters/mdChecklist.filter');

const baseMdChecklistIdSchema = {
    partyType: { type: 'single', model: MdPartyType }
};

const mdChecklistIdSchema = { ...baseMdChecklistIdSchema };

const createMdChecklistIdSchema = { ...baseMdChecklistIdSchema,
    partyType: { ...baseMdChecklistIdSchema.partyType, required: true }
};

const mdChecklistResolvers = {
    MdChecklist: {
        partyType: async (parent) => {
            try {
                return await MdPartyType.findById(parent.partyType);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party type');
            }
        }
    },

    Query: {
        getMdChecklistById: async (_, { id }) => {
            try {
                let mdChecklist = await getByIdCache(id) || await MdChecklist.findById(id);
                if (mdChecklist) {
                    await setCache(id, mdChecklist);
                } else {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'MdChecklist not found', { 
                        errors: [{ field: 'getMdChecklistById', message: 'MdChecklist not found' }] 
                    });
                }
                return createResponse('MdChecklistResponse', 'SUCCESS', 'MdChecklist fetched successfully', { 
                    result: { mdChecklist } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Error getting MdChecklist', { 
                    errors: [{ field: 'getMdChecklistById', message: error.message }] 
                });
            }
        },

        getMdChecklists: async (_, { filter, pagination }) => {
            try {
                const query = await buildMdChecklistQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdChecklist, query, limit, skip);

                const mdChecklists = await MdChecklist.find(query)
                    .populate('partyType')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (mdChecklists.length === 0) {
                    return createResponse('MdChecklistsResponse', 'FAILURE', 'No MdChecklists found', { 
                        result: { mdChecklists }, 
                        pagination: paginationInfo 
                    });
                }
                return createResponse('MdChecklistsResponse', 'SUCCESS', 'MdChecklists fetched successfully', { 
                    result: { mdChecklists }, 
                    pagination: paginationInfo 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Error getting MdChecklists', { 
                    errors: [{ field: 'getMdChecklists', message: error.message }] 
                });
            }
        }
    },

    Mutation: {
        createMdChecklist: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createMdChecklistIdSchema, 'MdChecklist');
                if (validationError) {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors: validationError.errors
                    });
                }
                const mdChecklist = new MdChecklist(input);
                await mdChecklist.save();
                return createResponse('MdChecklistResponse', 'SUCCESS', 'MdChecklist created successfully', { 
                    result: { mdChecklist } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Error creating MdChecklist', { 
                    errors: [{ field: 'createMdChecklist', message: error.message }] 
                });
            }
        },

        updateMdChecklist: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdChecklistIdSchema, 'MdChecklist');
                if (validationError) {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors: validationError.errors
                    });
                }
                const mdChecklist = await MdChecklist.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!mdChecklist) {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'MdChecklist not found', { 
                        errors: [{ field: 'updateMdChecklist', message: 'MdChecklist not found' }] 
                    });
                }

                await clearCacheById(id);
                return createResponse('MdChecklistResponse', 'SUCCESS', 'MdChecklist updated successfully', { 
                    result: { mdChecklist } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Error updating MdChecklist', { 
                    errors: [{ field: 'updateMdChecklist', message: error.message }] 
                });
            }
        },

        deleteMdChecklist: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdChecklist');
                if (references.length > 0) {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'MdChecklist cannot be deleted', { 
                        errors: [{ field: 'id', message: `MdChecklist cannot be deleted as it is being used in: ${references.join(', ')}` }] 
                    });
                }

                const mdChecklist = await MdChecklist.findByIdAndDelete(id);
                
                if (!mdChecklist) {
                    return createResponse('MdChecklistErrorResponse', 'FAILURE', 'MdChecklist not found', { 
                        errors: [{ field: 'deleteMdChecklist', message: 'MdChecklist not found' }] 
                    });
                }

                await clearCacheById(id);
                return createResponse('MdChecklistResponse', 'SUCCESS', 'MdChecklist deleted successfully', { 
                    result: { mdChecklist } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdChecklistErrorResponse', 'FAILURE', 'Error deleting MdChecklist', { 
                    errors: [{ field: 'deleteMdChecklist', message: error.message }] 
                });
            }
        }
    }
};

module.exports = mdChecklistResolvers; 
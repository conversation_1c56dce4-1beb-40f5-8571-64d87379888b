const Comment = require('../models/Comment');
const Reaction = require('../models/Reaction');
const Task = require('../models/Task');
const Document = require('../models/Document');
const Media = require('../models/Media');
const Message = require('../models/Message');
const { clearCacheById } = require('./cache.util');
const { deleteFileFromBlob } = require('./azureStorage.util');
const MediaFolder = require('../models/MediaFolder');


const cascadeDelete = async (modelName, id, options = {}) => {
    try {
        switch (modelName) {
            case 'Document':
                const document = await Document.findById(id);
                if (!document) return { success: false, error: { message: 'Document not found' } };

                if (document.documentUrl) {
                    try {
                        const blobUrl = new URL(document.documentUrl);
                        const pathParts = blobUrl.pathname.split('/');
                        const container = pathParts[1];
                        const blobKey = pathParts[2];

                        await deleteFileFromBlob(blobKey, container);
                    } catch (error) {
                        console.error(`Error deleting file from blob storage: ${error.message}`);
                        return { success: false, error };
                    }
                }

                await Document.findByIdAndDelete(id);
                return { success: true };

            case 'Task':
                const task = await Task.findById(id);
                if (!task) return { success: false, error: { message: 'Task not found' } };

                if (task.attachments && task.attachments.length > 0) {
                    const documents = await Document.find({ _id: { $in: task.attachments } });
                    
                    await Promise.all(documents.map(async (doc) => {
                        if (doc.documentUrl) {
                            try {
                                const blobUrl = new URL(doc.documentUrl);
                                const pathParts = blobUrl.pathname.split('/');
                                const container = pathParts[1];
                                const blobKey = pathParts[2];

                                await deleteFileFromBlob(blobKey, container);
                            } catch (error) {
                                console.error(`Error deleting file from blob storage: ${error.message}`);
                            }
                        }
                    }));

                    await Document.deleteMany({ _id: { $in: task.attachments } });
                    await Promise.all(task.attachments.map(aid => clearCacheById(aid)));
                }

                const comments = await Comment.find({ _id: { $in: task.comments } });
                const reactionIds = comments.reduce((acc, comment) => {
                    return acc.concat(comment.reactions || []);
                }, []);

                await Reaction.deleteMany({ _id: { $in: reactionIds } });
                await Comment.deleteMany({ _id: { $in: task.comments } });
                
                await Promise.all([
                    ...reactionIds.map(rid => clearCacheById(rid)),
                    ...task.comments.map(cid => clearCacheById(cid))
                ]);

                await Task.findByIdAndDelete(id);
                return { success: true };

            case 'Comment':
                const comment = await Comment.findById(id);
                if (!comment) return { success: false, error: { message: 'Comment not found' } };

                if (comment.reactions && comment.reactions.length > 0) {
                    await Reaction.deleteMany({ _id: { $in: comment.reactions } });
                    await Promise.all(comment.reactions.map(rid => clearCacheById(rid)));
                }

                if (comment.task) {
                    await Task.findByIdAndUpdate(comment.task, {
                        $pull: { comments: id }
                    });
                }

                return { success: true };

            case 'Message':
                const message = await Message.findById(id);
                if (!message) return { success: false, error: { message: 'Message not found' } };

                // Recursive function to delete message tree
                const deleteMessageTree = async (messageId) => {
                    const currentMessage = await Message.findById(messageId);
                    
                    // 1. Delete all media
                    if (currentMessage.media && currentMessage.media.length > 0) {
                        // 1.1 Delete from blob storage
                        const mediaItems = await Media.find({ _id: { $in: currentMessage.media } });
                        await Promise.all(mediaItems.map(async (media) => {
                            if (media.url) {
                                try {
                                    const blobUrl = new URL(media.url);
                                    const pathParts = blobUrl.pathname.split('/');
                                    const container = pathParts[1];
                                    const blobKey = pathParts[2];
                                    await deleteFileFromBlob(blobKey, container);

                                } catch (error) {
                                    console.error(`Error deleting media from blob storage: ${error.message}`);
                                }
                            }
                        }));
                        
                        const mediaFolder = await MediaFolder.findOne({ event: currentMessage.eventId });
                        if (mediaFolder) {
                            mediaFolder.media = mediaFolder.media.filter(mid => !currentMessage.media.includes(mid));
                            await mediaFolder.save();
                        }
                        // Delete media documents
                        await Media.deleteMany({ _id: { $in: currentMessage.media } });
                        await Promise.all(currentMessage.media.map(mid => clearCacheById(mid)));
                    }

                    // 2. Delete reactions
                    if (currentMessage.reactions && currentMessage.reactions.length > 0) {
                        await Reaction.deleteMany({ _id: { $in: currentMessage.reactions } });
                        await Promise.all(currentMessage.reactions.map(rid => clearCacheById(rid)));
                    }

                    // 3. Delete all child messages
                    const childMessages = await Message.find({ parentMessageId: messageId });
                    await Promise.all(childMessages.map(child => deleteMessageTree(child._id)));

                    // Finally delete this message
                    await Message.findByIdAndDelete(messageId);
                };

                await deleteMessageTree(id);
                return { success: true };

            default:
                return { success: false, error: { message: 'Unsupported model type' } };
        }
    } catch (error) {
        console.error('Cascade delete error:', error);
        return { success: false, error };
    }
};

module.exports = { cascadeDelete }; 
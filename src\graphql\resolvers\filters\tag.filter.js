const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdIconQuery = require("./mdIcon.filter");

const buildTagQuery = async (filter) => {
  const query = {};

  if (filter) {
    if (filter?.name) {
      query.name = { $regex: filter.name, $options: "i" };
    }

    if (filter.id) {
      if (Array.isArray(filter.id)) {
        query._id = { $in: filter.id };
      } else {
        query._id = filter.id;
      }
    }

    if (filter.slug) {
      query.slug = { $regex: filter.slug, $options: "i" };
    }

    if (filter.icon) {
      if (typeof filter.icon === 'string') {
        if (isValidObjectId(filter.icon)) {
          query.icon = filter.icon;
        } else {
          throw new Error('Invalid icon ID provided');
        }
      } else if (typeof filter.icon === 'object') {
        const iconQuery = await buildMdIconQuery(filter.icon);
        if (Object.keys(iconQuery).length > 0) {
          query.icon = iconQuery._id;
        }
      }
    }
  }

  return query;
};

module.exports = buildTagQuery;

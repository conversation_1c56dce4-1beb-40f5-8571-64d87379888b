const mongoose = require('mongoose');
const buildUserQuery = require('../../../../../src/graphql/resolvers/filters/user.filter');
const MdRole = require('../../../../../src/models/MdRole');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');
const { buildRoleQuery } = require('../../../../../src/graphql/resolvers/filters/mdRole.filter');

jest.mock('../../../../../src/models/MdRole');
jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/mdRole.filter', () => ({
    buildRoleQuery: jest.fn()
}));

describe('buildUserQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filters provided', async () => {
        const result = await buildUserQuery(null);
        expect(result).toEqual({});
    });

    it('should build query with string filters using regex', async () => {
        const filters = {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        };

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            firstName: { $regex: 'John', $options: 'i' },
            lastName: { $regex: 'Doe', $options: 'i' },
            email: { $regex: '<EMAIL>', $options: 'i' },
            phone: { $regex: new RegExp('1234567890'), $options: 'i' }
        });
    });

    it('should build query with boolean filters', async () => {
        const filters = {
            isRegistered: true,
            emailVerified: false
        };

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            isRegistered: true,
            emailVerified: false
        });
    });

    it('should handle role filter with valid role ID string', async () => {
        const roleId = new mongoose.Types.ObjectId().toString();
        const filters = {
            role: roleId
        };

        isValidObjectId.mockReturnValue(true);

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            role: roleId
        });
        expect(isValidObjectId).toHaveBeenCalledWith(roleId);
    });

    it('should throw error for invalid role ID string', async () => {
        const filters = {
            role: 'invalid-id'
        };

        isValidObjectId.mockReturnValue(false);

        await expect(buildUserQuery(filters)).rejects.toThrow('Invalid role ID provided');
    });

    it('should handle role filter with role object query', async () => {
        const roleFilters = { name: 'admin' };
        const roleId = new mongoose.Types.ObjectId();
        const filters = {
            role: roleFilters
        };

        const mockRoleQuery = { name: { $regex: 'admin', $options: 'i' } };
        buildRoleQuery.mockResolvedValue(mockRoleQuery);
        
        MdRole.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([{ _id: roleId }])
        });

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            role: { $in: [roleId] }
        });
    });

    it('should handle role filter with no matching roles', async () => {
        const roleFilters = { name: 'nonexistent' };
        const filters = {
            role: roleFilters
        };

        const mockRoleQuery = { name: { $regex: 'nonexistent', $options: 'i' } };
        buildRoleQuery.mockResolvedValue(mockRoleQuery);
        
        MdRole.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([])
        });

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            role: { $in: [] }
        });
    });

    it('should handle empty role query', async () => {
        const filters = {
            role: {}
        };

        buildRoleQuery.mockResolvedValue({});

        const result = await buildUserQuery(filters);

        expect(result).toEqual({});
    });

    it('should combine multiple filter types', async () => {
        const roleId = new mongoose.Types.ObjectId().toString();
        const filters = {
            firstName: 'John',
            isRegistered: true,
            role: roleId
        };

        isValidObjectId.mockReturnValue(true);

        const result = await buildUserQuery(filters);

        expect(result).toEqual({
            firstName: { $regex: 'John', $options: 'i' },
            isRegistered: true,
            role: roleId
        });
    });

    describe('phone filter', () => {
        it('should escape special characters in phone number', async () => {
            const filters = {
                phone: '+****************'
            };

            const result = await buildUserQuery(filters);

            expect(result.phone.$regex.toString()).toBe('/\\+1\\ \\(555\\)\\ 123\\-4567/');
            expect(result.phone.$options).toBe('i');
        });

        it('should handle phone numbers with various special characters', async () => {
            const testCases = [
                {
                    input: '(*************',
                    expected: '/\\(123\\)\\ 456\\-7890/'
                },
                {
                    input: '******.567.8900',
                    expected: '/\\+1\\.234\\.567\\.8900/'
                },
                {
                    input: '123#456*789',
                    expected: '/123\\#456\\*789/'
                }
            ];

            for (const testCase of testCases) {
                const result = await buildUserQuery({ phone: testCase.input });
                expect(result.phone.$regex.toString()).toBe(testCase.expected);
                expect(result.phone.$options).toBe('i');
            }
        });
    });
}); 
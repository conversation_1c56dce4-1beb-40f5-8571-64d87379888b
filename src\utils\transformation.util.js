const transformIcon = (icon) => {
    if (!icon || !icon._id) return icon;
    try {
        return {
            _id: icon._id,
            id: icon._id.toString(),
            name: icon.name || '',
            iconSrc: icon.iconSrc || '',
            createdAt: icon.createdAt,
            updatedAt: icon.updatedAt
        };
    } catch (error) {
        console.error('Error transforming icon:', error);
        return icon;
    }
};

module.exports = {
    transformIcon
};
const { createResponse } = require('../../utils/response.util');
const UserArticleActivity = require('../../models/UserArticleActivity');
const Article = require('../../models/Article');
const { isValidObjectId } = require('../../utils/validation.util');
const buildUserArticleActivityFilter = require('./filters/userArticleActivity.filter');

const userArticleActivityResolvers = {
  UserArticleActivity: {
    articleId: async (parent) => {
      try {
        const article = await Article.findById(parent.articleId);
        if (article) {
          return {
            id: article._id,
            headline: article.headline,
            description: article.text,
            image: article.image,
            source: article.source,
            date: article.date,
            createdAt: article.createdAt,
            updatedAt: article.updatedAt
          };
        }
        return null;
      } catch (error) {
        console.error('Error fetching article:', error);
        return null;
      }
    }
  },
  Query: {
    getUserArticleActivity: async (_, { articleId }, context) => {
      if (!context.user) {
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Unauthorized', {
          errors: [{ field: 'auth', message: 'User not authenticated' }]
        });
      }

      try {
        const activity = await UserArticleActivity.findOne({
          userId: context.user._id,
          articleId
        });

        return createResponse('UserArticleActivityResponse', 'SUCCESS', 'Activity fetched successfully', {
          result: {
            activity
          }
        });
      } catch (error) {
        console.error('Error fetching user article activity:', error);
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Error fetching activity', {
          errors: [{ field: 'getUserArticleActivity', message: error.message }]
        });
      }
    },

    getUserArticleActivities: async (_, { filter, pagination }, context) => {
      if (!context.user) {
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Unauthorized', {
          errors: [{ field: 'auth', message: 'User not authenticated' }]
        });
      }

      try {
        const query = { userId: context.user._id };
        if (filter) {
          Object.assign(query, await buildUserArticleActivityFilter(filter, context.user._id));
        }
        
        const limit = pagination?.limit || 10;
        const skip = pagination?.skip || 0;

        const [activities, totalCount] = await Promise.all([
          UserArticleActivity.find(query)
            .sort({ createdAt: -1 })
            .limit(limit)
            .skip(skip),
          UserArticleActivity.countDocuments(query)
        ]);

        return createResponse('UserArticleActivitiesResponse', 'SUCCESS', 'Activities fetched successfully', {
          result: {
            activities,
            totalCount
          }
        });
      } catch (error) {
        console.error('Error fetching user article activities:', error);
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Error fetching activities', {
          errors: [{ field: 'getUserArticleActivities', message: error.message }]
        });
      }
    }
  },

  Mutation: {
    updateUserArticleActivity: async (_, { input }, context) => {
      if (!context.user) {
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Unauthorized', {
          errors: [{ field: 'auth', message: 'User not authenticated' }]
        });
      }

      try {
        const { articleId, ...activityData } = input;

        const activity = await UserArticleActivity.findOneAndUpdate(
          { userId: context.user._id, articleId },
          { 
            $set: { 
              ...activityData,
              userId: context.user._id 
            }
          },
          { 
            new: true,
            upsert: true
          }
        );

        return createResponse('UserArticleActivityResponse', 'SUCCESS', 'Activity updated successfully', {
          result: {
            activity
          }
        });
      } catch (error) {
        console.error('Error updating user article activity:', error);
        return createResponse('UserArticleActivityErrorResponse', 'FAILURE', 'Error updating activity', {
          errors: [{ field: 'updateUserArticleActivity', message: error.message }]
        });
      }
    }
  }
};

module.exports = userArticleActivityResolvers; 
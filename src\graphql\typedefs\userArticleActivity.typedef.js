const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const userArticleActivityTypeDefs = gql`
  ${sharedTypeDef}

  type UserArticleActivity {
    id: ID
    userId: ID
    articleId: Article
    liked: Boolean
    saved: Boolean
    shared: Boolean
    createdAt: DateTime
    updatedAt: DateTime
  }

  type UserArticleActivityResult {
    activity: UserArticleActivity
  }

  type UserArticleActivitiesResult {
    activities: [UserArticleActivity]
    totalCount: Int
  }

  type UserArticleActivityResponse {
    status: String
    message: String
    result: UserArticleActivityResult
  }

  type UserArticleActivitiesResponse {
    status: String
    message: String
    result: UserArticleActivitiesResult
  }

  type UserArticleActivityError {
    field: String
    message: String
  }

  type UserArticleActivityErrorResponse {
    status: String
    message: String
    errors: [UserArticleActivityError]
  }

  input UserArticleActivityInput {
    articleId: ID!
    liked: Boolean
    saved: Boolean
    shared: Boolean
  }

  input UserArticleActivityFilter {
    article: ArticleFilter
    liked: Boolean
    saved: Boolean
    shared: Boolean
    createdAt: DateRange
    updatedAt: DateRange
  }

  input DateRange {
    from: String
    to: String
  }

  extend type Query {
    getUserArticleActivity(articleId: ID!): UserArticleActivityResponse
    getUserArticleActivities(
      filter: UserArticleActivityFilter
      pagination: PaginationInput
    ): UserArticleActivitiesResponse
  }

  extend type Mutation {
    updateUserArticleActivity(input: UserArticleActivityInput!): UserArticleActivityResponse
  }
`;

module.exports = userArticleActivityTypeDefs; 
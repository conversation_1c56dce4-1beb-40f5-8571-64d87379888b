const MdFunFact = require('../../../../src/models/MdFunFact');
const MdEventType = require('../../../../src/models/MdEventType');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildMdFunFactQuery = require('../../../../src/graphql/resolvers/filters/mdFunFact.filter');
const mdFunFactResolvers = require('../../../../src/graphql/resolvers/mdFunFact.resolver');
const { validateReferences } = require('../../../../src/utils/validation.util');

const mdFunFactIdSchema = {
    eventTypes: { type: 'array', required: false, model: MdEventType }
};

jest.mock('../../../../src/models/MdFunFact');
jest.mock('../../../../src/models/MdEventType');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdFunFact.filter');
jest.mock('../../../../src/utils/validation.util');

describe('mdFunFactResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdFunFact Field Resolvers', () => {
        describe('eventTypes', () => {
            it('should return event types for a fun fact', async () => {
                const parent = {
                    eventTypes: ['eventType1', 'eventType2']
                };
                const mockEventTypes = [
                    { _id: 'eventType1', name: 'Event 1' },
                    { _id: 'eventType2', name: 'Event 2' }
                ];

                MdEventType.find.mockResolvedValue(mockEventTypes);

                const result = await mdFunFactResolvers.MdFunFact.eventTypes(parent);

                expect(MdEventType.find).toHaveBeenCalledWith({ _id: { $in: parent.eventTypes } });
                expect(result).toEqual(mockEventTypes);
            });

            it('should throw error when fetching event types fails', async () => {
                const parent = {
                    eventTypes: ['eventType1']
                };
                const error = new Error('Database error');

                MdEventType.find.mockRejectedValue(error);

                await expect(mdFunFactResolvers.MdFunFact.eventTypes(parent))
                    .rejects
                    .toThrow('Error getting event types');
            });
        });
    });

    describe('Query', () => {
        describe('getMdFunFactById', () => {
            it('should return fun fact from cache if available', async () => {
                const id = 'funFactId';
                const cachedFunFact = { _id: id, funFact: 'Cached fun fact' };

                getByIdCache.mockResolvedValue(cachedFunFact);
                createResponse.mockReturnValue('cachedResponse');

                const result = await mdFunFactResolvers.Query.getMdFunFactById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactResponse',
                    'SUCCESS',
                    'Fun fact retrieved successfully from cache',
                    { result: { mdFunFact: cachedFunFact } }
                );
                expect(result).toBe('cachedResponse');
            });

            it('should return fun fact from database if not in cache', async () => {
                const id = 'funFactId';
                const mdFunFact = { _id: id, funFact: 'Database fun fact' };
                const mockPopulate = jest.fn().mockResolvedValue(mdFunFact);

                getByIdCache.mockResolvedValue(null);
                MdFunFact.findById.mockReturnValue({ populate: mockPopulate });
                createResponse.mockReturnValue('databaseResponse');

                const result = await mdFunFactResolvers.Query.getMdFunFactById(null, { id });

                expect(MdFunFact.findById).toHaveBeenCalledWith(id);
                expect(mockPopulate).toHaveBeenCalledWith('eventTypes');
                expect(setCache).toHaveBeenCalledWith(id, mdFunFact);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactResponse',
                    'SUCCESS',
                    'Fun fact retrieved successfully',
                    { result: { mdFunFact } }
                );
                expect(result).toBe('databaseResponse');
            });

            it('should return error response if fun fact not found', async () => {
                const id = 'nonexistentId';
                const mockPopulate = jest.fn().mockResolvedValue(null);

                getByIdCache.mockResolvedValue(null);
                MdFunFact.findById.mockReturnValue({ populate: mockPopulate });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Query.getMdFunFactById(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Fun fact not found',
                    { errors: [{ field: 'id', message: 'Fun fact not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if an error occurs', async () => {
                const id = 'funFactId';
                const error = new Error('Database error');

                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Query.getMdFunFactById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Error retrieving fun fact',
                    { errors: [{ field: 'getMdFunFactById', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getMdFunFacts', () => {
            it('should return fun facts successfully', async () => {
                const filter = { funFact: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { funFact: { $regex: 'test', $options: 'i' } };
                const mdFunFacts = [
                    { _id: '1', funFact: 'Test fact 1' },
                    { _id: '2', funFact: 'Test fact 2' }
                ];
                const paginationInfo = {
                    total: 2,
                    limit: 10,
                    skip: 0
                };

                buildMdFunFactQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockPopulate = jest.fn().mockReturnThis();
                const mockSort = jest.fn().mockReturnThis();
                const mockSkip = jest.fn().mockReturnThis();
                const mockLimit = jest.fn().mockResolvedValue(mdFunFacts);

                MdFunFact.find.mockReturnValue({
                    populate: mockPopulate,
                    sort: mockSort,
                    skip: mockSkip,
                    limit: mockLimit
                });

                const result = await mdFunFactResolvers.Query.getMdFunFacts(null, { filter, pagination });

                expect(MdFunFact.find).toHaveBeenCalledWith(query);
                expect(mockPopulate).toHaveBeenCalledWith('eventTypes');
                expect(mockSort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(mockSkip).toHaveBeenCalledWith(0);
                expect(mockLimit).toHaveBeenCalledWith(10);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactsResponse',
                    'SUCCESS',
                    'Fun facts fetched successfully',
                    {
                        result: { mdFunFacts },
                        pagination: paginationInfo
                    }
                );
            });

            it('should handle empty results', async () => {
                const query = {};
                const paginationInfo = {
                    total: 0,
                    limit: 10,
                    skip: 0
                };

                buildMdFunFactQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdFunFact.find.mockResolvedValue([]);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Fun facts fetched successfully',
                    result: { mdFunFacts: [] },
                    pagination: paginationInfo
                });

                const result = await mdFunFactResolvers.Query.getMdFunFacts(null, {
                    pagination: { limit: 10, skip: 0 }
                });

                expect(result.status).toBe('SUCCESS');
                expect(result.result.mdFunFacts).toEqual([]);
                expect(result.pagination).toEqual(paginationInfo);
            });

            it('should handle errors during fetch', async () => {
                const error = new Error('Database error');
                
                MdFunFact.find.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockRejectedValue(error)
                });

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving fun facts',
                    errors: [{ field: 'getMdFunFacts', message: error.message }]
                });

                const result = await mdFunFactResolvers.Query.getMdFunFacts(null, {
                    pagination: { limit: 10, skip: 0 }
                });

                expect(result.status).toBe('FAILURE');
                expect(result.errors).toBeDefined();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Error retrieving fun facts',
                    { errors: [{ field: 'getMdFunFacts', message: error.message }] }
                );
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdFunFact', () => {
            it('should create and return the fun fact', async () => {
                const input = { funFact: 'New fun fact', eventTypes: ['type1'] };
                const savedFunFact = { _id: 'newId', ...input };
                const populatedFunFact = { ...savedFunFact, eventTypes: [{ _id: 'type1', name: 'Type 1' }] };

                MdFunFact.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(savedFunFact)
                }));

                const mockFindById = jest.fn().mockReturnValue({
                    populate: jest.fn().mockResolvedValue(populatedFunFact)
                });
                MdFunFact.findById = mockFindById;

                createResponse.mockReturnValue('successResponse');

                const result = await mdFunFactResolvers.Mutation.createMdFunFact(null, { input });

                expect(MdFunFact).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactResponse',
                    'SUCCESS',
                    'Fun fact created successfully',
                    { result: { mdFunFact: populatedFunFact } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if creation fails', async () => {
                const input = { funFact: 'Test Fun Fact', eventTypes: ['eventType1'] };
                const error = new Error('Database error');

                MdFunFact.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating fun fact',
                    errors: [{ field: 'createMdFunFact', message: error.message }]
                });

                const result = await mdFunFactResolvers.Mutation.createMdFunFact(null, { input });

                expect(MdFunFact).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Error creating fun fact',
                    { errors: [{ field: 'createMdFunFact', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating fun fact',
                    errors: [{ field: 'createMdFunFact', message: error.message }]
                });
            });

            it('should return validation error response when validation fails', async () => {
                const input = { 
                    funFact: 'Test fun fact',
                    eventTypes: ['invalidId']
                };
                const validationError = {
                    message: 'Validation failed',
                    errors: [
                        { field: 'eventTypes', message: 'Invalid event types reference' }
                    ]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });

                const result = await mdFunFactResolvers.Mutation.createMdFunFact(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, mdFunFactIdSchema, 'MdFunFact');
                expect(MdFunFact.prototype.save).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });
            });
        });

        describe('deleteMdFunFact', () => {
            it('should delete fun fact and clear cache', async () => {
                const id = 'funFactId';
                const deletedFunFact = { _id: id, funFact: 'Deleted fun fact' };

                MdFunFact.findByIdAndDelete.mockResolvedValue(deletedFunFact);
                createResponse.mockReturnValue('successResponse');

                const result = await mdFunFactResolvers.Mutation.deleteMdFunFact(null, { id });

                expect(MdFunFact.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactResponse',
                    'SUCCESS',
                    'Fun fact deleted successfully',
                    { result: { mdFunFact: deletedFunFact } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if fun fact not found', async () => {
                const id = 'nonexistentId';

                MdFunFact.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Mutation.deleteMdFunFact(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Fun fact not found',
                    { errors: [{ field: 'id', message: 'Fun fact not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if an error occurs during deletion', async () => {
                const id = 'funFactId';
                const error = new Error('Database error');

                MdFunFact.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Mutation.deleteMdFunFact(null, { id });

                expect(MdFunFact.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Error deleting fun fact',
                    { errors: [{ field: 'deleteMdFunFact', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('updateMdFunFact', () => {
            it('should update and return the fun fact', async () => {
                const input = { 
                    id: 'funFactId', 
                    funFact: 'Updated fun fact',
                    eventTypes: ['eventType1'] 
                };
                const updatedFunFact = { 
                    _id: 'funFactId', 
                    funFact: 'Updated fun fact',
                    eventTypes: [{ _id: 'eventType1', name: 'Type 1' }] 
                };

                validateReferences.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(updatedFunFact);
                MdFunFact.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await mdFunFactResolvers.Mutation.updateMdFunFact(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, mdFunFactIdSchema, 'MdFunFact');
                expect(MdFunFact.findByIdAndUpdate).toHaveBeenCalledWith(
                    input.id,
                    input,
                    { new: true }
                );
                expect(mockPopulate).toHaveBeenCalledWith('eventTypes');
                expect(clearCacheById).toHaveBeenCalledWith(input.id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactResponse',
                    'SUCCESS',
                    'Fun fact updated successfully',
                    { result: { mdFunFact: updatedFunFact } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if fun fact not found', async () => {
                const input = { 
                    id: 'nonexistentId', 
                    funFact: 'Updated fun fact' 
                };

                validateReferences.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(null);
                MdFunFact.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Mutation.updateMdFunFact(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, mdFunFactIdSchema, 'MdFunFact');
                expect(MdFunFact.findByIdAndUpdate).toHaveBeenCalledWith(
                    input.id,
                    input,
                    { new: true }
                );
                expect(mockPopulate).toHaveBeenCalledWith('eventTypes');
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Fun fact not found',
                    { errors: [{ field: 'id', message: 'Fun fact not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if update fails', async () => {
                const input = { 
                    id: 'funFactId',
                    funFact: 'Updated fun fact',
                    eventTypes: ['eventType1'] 
                };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                MdFunFact.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdFunFactResolvers.Mutation.updateMdFunFact(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, mdFunFactIdSchema, 'MdFunFact');
                expect(MdFunFact.findByIdAndUpdate).toHaveBeenCalledWith(
                    input.id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'MdFunFactErrorResponse',
                    'FAILURE',
                    'Error updating fun fact',
                    { errors: [{ field: 'updateMdFunFact', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});

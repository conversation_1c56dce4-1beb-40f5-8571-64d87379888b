const { User } = require('../../models/User');
const MdRole = require('../../models/MdRole');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildUserQuery = require('./filters/user.filter');
const { clerkClient } = require('@clerk/clerk-sdk-node');
const { updateUserRole } = require('../../utils/clerk.util');
const { requireRole } = require('../../utils/middleware.util');

const userIdSchema = {
    role: { type: 'array', required: true }
};

const userResolvers = {
    Query: {
        getUser: async (_, __, context) => {
            try {
                let user = await getByIdCache(context.user._id);
                if (!user) {
                    user = await User.findById(context.user._id);
                    if (!user) {
                        return createResponse('UserErrorResponse', 'FAILURE', 'User not found', {
                            errors: [{ field: 'id', message: 'User not found' }]
                        });
                    }
                    await setCache(context.user._id, user);
                }
                return createResponse('UserResponse', 'SUCCESS', 'User retrieved successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error retrieving user', {
                    errors: [{ field: 'getUser', message: error.message }]
                });
            }
        },

        getUsers: async (_, { filters, pagination }) => {
            try {
                const query = await buildUserQuery(filters);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(User, query, limit, skip);

                const users = await User.find(query)
                    .skip(skip)
                    .limit(limit);

                if (users.length === 0) {
                    return createResponse('UsersResponse', 'FAILURE', 'No users found', {
                        result: { users },
                        pagination: paginationInfo
                    });
                }

                return createResponse('UsersResponse', 'SUCCESS', 'Users fetched successfully', {
                    result: { users },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error getting users', {
                    errors: [{ field: 'getUsers', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        register: async (_, { input }) => {
            try {
                const role = input.role;
                const existingRoles = await MdRole.find({ name: role });

                if (existingRoles.length === 0) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'Invalid role provided', {
                        errors: [{
                            field: 'role',
                            message: `Role '${role}' does not exist`
                        }]
                    });
                }

                const existingUser = await User.findOne({ phone: input.phone });

                let user;
                if (existingUser && !existingUser.isRegistered) {
                    user = await User.findByIdAndUpdate(
                        existingUser._id,
                        { ...input, isRegistered: true, phoneVerified: true },
                        { new: true }
                    );
                } else if (existingUser) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'User already exists', {
                        errors: [{ field: 'phone', message: 'A user with this phone number already exists' }]
                    });
                } else {
                    user = new User({ ...input, phoneVerified: true });
                    await user.save();
                }

                return createResponse('UserResponse', 'SUCCESS', 'User registered successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error registering user', {
                    errors: [{ field: 'register', message: error.message }]
                });
            }
        },

        verifyUser: async (_, { input }) => {
            try {
                if (!input.email && !input.phone) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'Validation failed', {
                        errors: [{
                            field: 'input',
                            message: 'At least one of email or phone must be provided'
                        }]
                    });
                }

                const query = {};
                if (input.email) {
                    query.email = input.email;
                }
                if (input.phone) {
                    query.phone = input.phone;
                }

                const user = await User.findOne(query);
                if (!user) {
                    const field = input.email && input.phone ? 'input' : (input.email ? 'email' : 'phone');
                    return createResponse('UserErrorResponse', 'FAILURE', 'User not found', {
                        errors: [{ field, message: 'User not found' }]
                    });
                }

                if (input.email) {
                    user.emailVerified = true;
                }
                if (input.phone) {
                    user.phoneVerified = true;
                }

                await user.save();
                await clearCacheById(user.id);

                return createResponse('UserResponse', 'SUCCESS', 'User verified successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error verifying user', {
                    errors: [{ field: 'verifyUser', message: error.message }]
                });
            }
        },

        updateUser: async (_, { input }, context) => {
            try {
                if (input.role) {
                    const existingRoles = await MdRole.find({ name: input.role });

                    if (existingRoles.length === 0) {
                        return createResponse('UserErrorResponse', 'FAILURE', 'Invalid role provided', {
                            errors: [{
                                field: 'role',
                                message: `Role '${input.role}' does not exist`
                            }]
                        });
                    }
                }

                const user = await User.findByIdAndUpdate(context.user._id, input, { new: true });
                if (!user) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'User not found', {
                        errors: [{ field: 'id', message: 'User not found' }]
                    });
                }

                await clearCacheById(context.user._id);
                return createResponse('UserResponse', 'SUCCESS', 'User updated successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error updating user', {
                    errors: [{ field: 'updateUser', message: error.message }]
                });
            }
        },

        updateUserRole: requireRole('super_admin')(async (_, { input }) => {
            try {
                const { userId, role } = input;

                // Validate that roles are one of the allowed values
                const allowedRoles = ['admin', 'user', 'super_admin'];
                const invalidRoles = role.filter(r => !allowedRoles.includes(r));

                if (invalidRoles.length > 0) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'Invalid role(s) provided', {
                        errors: [{
                            field: 'role',
                            message: `Role(s) '${invalidRoles.join(', ')}' are not allowed. Allowed roles are: ${allowedRoles.join(', ')}`
                        }]
                    });
                }

                // Find the user first to check if they have an externalId
                const existingUser = await User.findById(userId);
                if (!existingUser) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'User not found', {
                        errors: [{ field: 'userId', message: 'User not found' }]
                    });
                }

                // Update Clerk role if user has an externalId
                if (existingUser.externalId) {
                    try {
                        await updateUserRole(existingUser.externalId, role);
                    } catch (clerkError) {
                        console.error('Error updating Clerk role:', clerkError);
                        return createResponse('UserErrorResponse', 'FAILURE', 'Error updating role in authentication service', {
                            errors: [{ field: 'externalId', message: clerkError.message }]
                        });
                    }
                }

                // Update the user in our database
                const user = await User.findByIdAndUpdate(
                    userId,
                    { role },
                    { new: true }
                );

                // Clear cache for the updated user
                await clearCacheById(userId);

                return createResponse('UserResponse', 'SUCCESS', 'User role updated successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error updating user role', {
                    errors: [{ field: 'updateUserRole', message: error.message }]
                });
            }
        }),

        deleteUser: async (_, __, context) => {
            try {
                const user = await User.findById(context.user._id);

                if (!user) {
                    return createResponse('UserErrorResponse', 'FAILURE', 'User not found', {
                        errors: [{ field: 'id', message: 'User not found' }]
                    });
                }

                if (user.externalId) {
                    try {
                        await clerkClient.users.deleteUser(user.externalId);
                    } catch (clerkError) {
                        console.error('Error deleting user from Clerk:', clerkError);
                        return createResponse('UserErrorResponse', 'FAILURE', 'Error deleting user from authentication service', {
                            errors: [{ field: 'externalId', message: clerkError.message }]
                        });
                    }
                }

                await User.findByIdAndDelete(context.user._id);

                await clearCacheById(context.user._id);

                return createResponse('UserResponse', 'SUCCESS', 'User deleted successfully', {
                    result: { user }
                });
            } catch (error) {
                console.error(error);
                return createResponse('UserErrorResponse', 'FAILURE', 'Error deleting user', {
                    errors: [{ field: 'deleteUser', message: error.message }]
                });
            }
        }
    }
};

module.exports = userResolvers;

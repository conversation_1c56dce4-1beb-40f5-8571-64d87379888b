const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Reaction = require('../../../src/models/Reaction');
const MdReaction = require('../../../src/models/MdReaction');
const User = require('../../../src/models/User').User;

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

beforeEach(async () => {
    await Reaction.deleteMany({});
    await MdReaction.deleteMany({});
    await User.deleteMany({});
});

describe('Reaction Model Test', () => {
    it('should create and save a Reaction successfully', async () => {
        const mockUser = new User({
            role: ['user'],
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        });
        await mockUser.save();

        const mockMdReaction = new MdReaction({
            name: 'Like',
            icon: new mongoose.Types.ObjectId(),
            active: true
        });
        await mockMdReaction.save();

        const validReaction = new Reaction({
            type: mockMdReaction._id,
            user: mockUser._id,
            timestamp: new Date()
        });

        const savedReaction = await validReaction.save();

        expect(savedReaction._id).toBeDefined();
        expect(savedReaction.type).toEqual(mockMdReaction._id);
        expect(savedReaction.user).toEqual(mockUser._id);
        expect(savedReaction.timestamp).toBeInstanceOf(Date);
        expect(savedReaction.createdAt).toBeDefined();
        expect(savedReaction.updatedAt).toBeDefined();
    });

    it('should fail to create a Reaction without required fields', async () => {
        const reaction = new Reaction();

        let err;
        try {
            await reaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.type).toBeDefined();
        expect(err.errors.user).toBeDefined();
        expect(err.errors.timestamp).toBeDefined();
    });

    it('should fail to create a Reaction with invalid references', async () => {
        const invalidReaction = new Reaction({
            type: 'invalid_id',
            user: 'invalid_id',
            timestamp: new Date()
        });

        let err;
        try {
            await invalidReaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.type).toBeDefined();
        expect(err.errors.user).toBeDefined();
    });
}); 
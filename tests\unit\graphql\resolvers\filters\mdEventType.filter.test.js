const buildMdEventTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdEventType.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util', () => ({
    isValidObjectId: jest.fn()
}));

describe('buildMdEventTypeQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query object when no filter is provided', () => {
        const result = buildMdEventTypeQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid id filter', () => {
        const filter = { id: 'validId' };
        isValidObjectId.mockReturnValue(true);

        const result = buildMdEventTypeQuery(filter);

        expect(result).toEqual({ _id: 'validId' });
        expect(isValidObjectId).toHaveBeenCalledWith('validId');
    });

    it('should throw error for invalid id', () => {
        const filter = { id: 'invalidId' };
        isValidObjectId.mockReturnValue(false);

        expect(() => buildMdEventTypeQuery(filter)).toThrow('Invalid id provided');
        expect(isValidObjectId).toHaveBeenCalledWith('invalidId');
    });

    it('should build query with name filter', () => {
        const filter = { name: 'test' };

        const result = buildMdEventTypeQuery(filter);

        expect(result).toEqual({
            name: { $regex: 'test', $options: 'i' }
        });
    });

    it('should build query with valid duration filter', () => {
        const filter = { duration: 'ONE_DAY' };

        const result = buildMdEventTypeQuery(filter);

        expect(result).toEqual({ duration: 'ONE_DAY' });
    });

    it('should throw error for invalid duration value', () => {
        const filter = { duration: 'INVALID_DURATION' };

        expect(() => buildMdEventTypeQuery(filter)).toThrow(
            'Invalid duration value. Must be one of: ONE_DAY, MULTI_DAY'
        );
    });

    it('should build query with description filter', () => {
        const filter = { description: 'test description' };

        const result = buildMdEventTypeQuery(filter);

        expect(result).toEqual({
            description: { $regex: 'test description', $options: 'i' }
        });
    });

    it('should build query with multiple valid filters', () => {
        const filter = {
            name: 'test',
            duration: 'MULTI_DAY',
            description: 'test description'
        };

        const result = buildMdEventTypeQuery(filter);

        expect(result).toEqual({
            name: { $regex: 'test', $options: 'i' },
            duration: 'MULTI_DAY',
            description: { $regex: 'test description', $options: 'i' }
        });
    });
});
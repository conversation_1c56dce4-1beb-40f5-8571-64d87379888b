const { isValidObjectId } = require("../../../utils/validation.util");

const buildMdVendorTypeQuery = (filters) => {
    const query = {};

    if (filters) {
        if (filters.id) {
            if (isValidObjectId(filters.id)) {
                query._id = filters.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }
        if (filters.name && filters.name !== '') {
            query.name = { $regex: String(filters.name), $options: 'i' };
        }
        if (filters.speciality && filters.speciality !== '') {
            query.speciality = { $regex: String(filters.speciality), $options: 'i' };
        }
    }
    
    return query;
};

module.exports = buildMdVendorTypeQuery;
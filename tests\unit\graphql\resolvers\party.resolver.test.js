const mongoose = require('mongoose');
const Party = require('../../../../src/models/Party');
const MdPartyType = require('../../../../src/models/MdPartyType');
const Address = require('../../../../src/models/Address');
const Host = require('../../../../src/models/Host');
const PartyService = require('../../../../src/models/PartyService');
const Guest = require('../../../../src/models/Guest');
const partyResolvers = require('../../../../src/graphql/resolvers/party.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildPartyQuery = require('../../../../src/graphql/resolvers/filters/party.filter');
const { validateReferences } = require('../../../../src/utils/validation.util');
const MdServiceLocation = require('../../../../src/models/MdServiceLocation');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const Event = require('../../../../src/models/Event');
const { User } = require('../../../../src/models/User');
const MdVendorType = require('../../../../src/models/MdVendorType');
const { processCoHosts } = require('../../../../src/utils/coHost.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');

const basePartyIdSchema = {
    partyType: { type: 'single', model: MdPartyType },
    services: { type: 'array', required: false, model: PartyService },
    serviceLocation: { type: 'single', required: false, model: MdServiceLocation },
    eventId: { type: 'single', model: Event }
};

const partyIdSchema = { 
    ...basePartyIdSchema,
    coHosts: { type: 'array', required: false, model: Host }
};

const createPartyIdSchema = {
    ...basePartyIdSchema,
    partyType: { ...basePartyIdSchema.partyType, required: true },
    eventId: { ...basePartyIdSchema.eventId, required: true }
};

jest.mock('../../../../src/models/Party');
jest.mock('../../../../src/models/MdPartyType');
jest.mock('../../../../src/models/Address');
jest.mock('../../../../src/models/Host');
jest.mock('../../../../src/models/PartyService');
jest.mock('../../../../src/models/Guest');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/party.filter');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/models/Event');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/Host');
jest.mock('../../../../src/models/MdVendorType', () => ({
  find: jest.fn(),
  findById: jest.fn()
}));
jest.mock('../../../../src/utils/coHost.util', () => ({
    processCoHosts: jest.fn()
}));
jest.mock('../../../../src/utils/paginationInfo.util', () => ({
    getPaginationInfo: jest.fn()
}));

describe('Party Resolvers', () => {
    
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Party Field Resolvers', () => {
        const mockParent = {
            _id: 'partyId',
            partyType: 'partyTypeId',
            totalBudget: 1000,
            coHosts: ['coHostId1', 'coHostId2'],
            services: ['serviceId1', 'serviceId2'],
            eventId: 'eventId1'
        };

        const fieldResolverTests = [
            {
                name: 'partyType',
                mockData: { _id: 'partyTypeId', name: 'Birthday' },
                model: MdPartyType,
                parentField: 'partyType',
                errorMessage: 'Error getting party type'
            }
        ];

        fieldResolverTests.forEach(test => {
            describe(test.name, () => {
                it('should return data successfully', async () => {
                    test.model.findById.mockResolvedValue(test.mockData);
                    const result = await partyResolvers.Party[test.name](mockParent);
                    expect(test.model.findById).toHaveBeenCalledWith(mockParent[test.parentField]);
                    expect(result).toEqual(test.mockData);
                });

                it('should handle errors', async () => {
                    test.model.findById.mockRejectedValue(new Error('Database error'));
                    await expect(partyResolvers.Party[test.name](mockParent))
                        .rejects
                        .toThrow(test.errorMessage);
                });
            });
        });

        describe('calculations', () => {
            const calculationTests = [
                {
                    name: 'totalExpenditure',
                    mockServices: [{ expenditure: 150 }, { expenditure: 250 }],
                    expectedTotal: 400,
                    errorMessage: 'Error calculating total expenditure'
                }
            ];

            calculationTests.forEach(test => {
                describe(test.name, () => {
                    it('should calculate total correctly', async () => {
                        PartyService.find.mockResolvedValue(test.mockServices);
                        const result = await partyResolvers.Party[test.name](mockParent);
                        expect(result).toBe(test.expectedTotal);
                    });

                    it('should handle database errors', async () => {
                        PartyService.find.mockRejectedValue(new Error('Database error'));
                        await expect(partyResolvers.Party[test.name](mockParent))
                            .rejects
                            .toThrow(test.errorMessage);
                    });
                });
            });
        });

        describe('guest counts', () => {
            it('should return expected guest count', async () => {
                const mockParty = { expectedGuestCount: 5 };
                const result = mockParty.expectedGuestCount;
                expect(result).toBe(5);
            });

            it('should return actual guest count', async () => {
                const parent = { _id: 'partyId' };
                Guest.countDocuments.mockResolvedValue(5);
                
                const result = await partyResolvers.Party.actualGuestCount(parent);
                
                expect(Guest.countDocuments).toHaveBeenCalledWith({ party: 'partyId' });
                expect(result).toBe(5);
            });

            it('should handle error when getting actual guest count', async () => {
                const parent = { _id: 'partyId' };
                const error = new Error('Database error');
                Guest.countDocuments.mockRejectedValue(error);
                
                await expect(partyResolvers.Party.actualGuestCount(parent))
                    .rejects
                    .toThrow('Error counting guests');
            });
        });

        describe('coHosts', () => {
            it('should return co-hosts for a party', async () => {
                const mockCoHosts = [
                    { _id: 'host1', name: 'Host 1' },
                    { _id: 'host2', name: 'Host 2' }
                ];
                const parent = { coHosts: ['host1', 'host2'] };

                Host.find.mockResolvedValue(mockCoHosts);

                const result = await partyResolvers.Party.coHosts(parent);

                expect(Host.find).toHaveBeenCalledWith({ _id: { $in: parent.coHosts } });
                expect(result).toEqual(mockCoHosts);
            });

            it('should handle errors when fetching co-hosts', async () => {
                const parent = { coHosts: ['host1'] };
                const error = new Error('Database error');

                Host.find.mockRejectedValue(error);

                await expect(partyResolvers.Party.coHosts(parent)).rejects.toThrow('Error getting co-hosts');
                expect(Host.find).toHaveBeenCalledWith({ _id: { $in: parent.coHosts } });
            });
        });

        describe('services', () => {
            it('should return services for a party', async () => {
                const mockServices = [
                    { _id: 'service1', name: 'Service 1' },
                    { _id: 'service2', name: 'Service 2' }
                ];
                const parent = { services: ['service1', 'service2'] };

                PartyService.find.mockResolvedValue(mockServices);

                const result = await partyResolvers.Party.services(parent);

                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: parent.services } });
                expect(result).toEqual(mockServices);
            });

            it('should handle errors when fetching services', async () => {
                const parent = { services: ['service1'] };
                const error = new Error('Database error');

                PartyService.find.mockRejectedValue(error);

                await expect(partyResolvers.Party.services(parent)).rejects.toThrow('Error getting party services');
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: parent.services } });
            });
        });

        describe('guests', () => {
            it('should return guests for a party', async () => {
                const mockGuests = [
                    { _id: 'guest1', name: 'Guest 1' },
                    { _id: 'guest2', name: 'Guest 2' }
                ];
                const parent = { _id: 'party1' };

                Guest.find.mockResolvedValue(mockGuests);

                const result = await partyResolvers.Party.guests(parent);

                expect(Guest.find).toHaveBeenCalledWith({ party: parent._id });
                expect(result).toEqual(mockGuests);
            });

            it('should return empty array when no guests found', async () => {
                const parent = { _id: 'party1' };

                Guest.find.mockResolvedValue(null);

                const result = await partyResolvers.Party.guests(parent);

                expect(Guest.find).toHaveBeenCalledWith({ party: parent._id });
                expect(result).toEqual([]);
            });

            it('should handle errors when fetching guests', async () => {
                const parent = { _id: 'party1' };
                const error = new Error('Database error');

                Guest.find.mockRejectedValue(error);

                await expect(partyResolvers.Party.guests(parent)).rejects.toThrow('Error getting guests');
                expect(Guest.find).toHaveBeenCalledWith({ party: parent._id });
            });
        });

        describe('serviceLocation', () => {
            beforeEach(() => {
                jest.clearAllMocks();
                MdServiceLocation.findById = jest.fn();
            });

            it('should return service location successfully', async () => {
                const mockServiceLocation = {
                    _id: 'serviceLocationId',
                    name: 'Test Location',
                    address: 'Test Address'
                };

                const parent = {
                    serviceLocation: 'serviceLocationId'
                };

                MdServiceLocation.findById.mockResolvedValue(mockServiceLocation);

                const result = await partyResolvers.Party.serviceLocation(parent);

                expect(MdServiceLocation.findById).toHaveBeenCalledWith('serviceLocationId');
                expect(result).toEqual(mockServiceLocation);
            });

            it('should throw error when service location retrieval fails', async () => {
                const parent = {
                    serviceLocation: 'serviceLocationId'
                };

                const error = new Error('Database error');
                MdServiceLocation.findById.mockRejectedValue(error);

                await expect(partyResolvers.Party.serviceLocation(parent))
                    .rejects
                    .toThrow('Error getting service location');

                expect(MdServiceLocation.findById).toHaveBeenCalledWith('serviceLocationId');
            });

            it('should return null when service location is not set', async () => {
                const parent = {
                    serviceLocation: null
                };

                MdServiceLocation.findById.mockResolvedValue(null);

                const result = await partyResolvers.Party.serviceLocation(parent);

                expect(MdServiceLocation.findById).toHaveBeenCalledWith(null);
                expect(result).toBeNull();
            });
        });

        describe('event', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return event successfully', async () => {
                const mockEvent = { _id: 'eventId1', name: 'Test Event' };
                Event.findById.mockResolvedValue(mockEvent);

                const result = await partyResolvers.Party.event(mockParent);

                expect(Event.findById).toHaveBeenCalledWith(mockParent.eventId);
                expect(result).toEqual(mockEvent);
            });

            it('should throw error when Event.findById fails', async () => {
                const error = new Error('Database error');
                Event.findById.mockRejectedValue(error);

                await expect(partyResolvers.Party.event(mockParent))
                    .rejects
                    .toThrow('Error getting event');
                expect(Event.findById).toHaveBeenCalledWith(mockParent.eventId);
            });
        });

        describe('vendorTypes', () => {
            it('should return vendor types successfully', async () => {
                const parent = {
                    vendorTypes: ['vendorType1', 'vendorType2']
                };
                
                const mockVendorTypes = [
                    { _id: 'vendorType1', name: 'Type 1' },
                    { _id: 'vendorType2', name: 'Type 2' }
                ];

                MdVendorType.find.mockResolvedValue(mockVendorTypes);

                const result = await partyResolvers.Party.vendorTypes(parent);

                expect(MdVendorType.find).toHaveBeenCalledWith({
                    _id: { $in: parent.vendorTypes }
                });
                expect(result).toEqual(mockVendorTypes);
            });

            it('should handle errors when getting vendor types', async () => {
                const parent = {
                    vendorTypes: ['vendorType1']
                };
                
                const mockError = new Error('Database error');
                MdVendorType.find.mockRejectedValue(mockError);

                await expect(partyResolvers.Party.vendorTypes(parent))
                    .rejects.toThrow('Error getting vendor types');
            });

            it('should return empty array when no vendor types exist', async () => {
                const parent = {
                    vendorTypes: ['vendorType1']
                };

                MdVendorType.find.mockResolvedValue([]);

                const result = await partyResolvers.Party.vendorTypes(parent);

                expect(result).toEqual([]);
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getPartyById', () => {
            it('should return party from cache if available', async () => {
                const mockParty = { _id: 'partyId', name: 'Birthday Party' };
                getByIdCache.mockResolvedValue(mockParty);
                createResponse.mockReturnValue('successResponse');

                const result = await partyResolvers.Query.getPartyById(null, { id: 'partyId' });

                expect(getByIdCache).toHaveBeenCalledWith('partyId');
                expect(Party.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyResponse',
                    'SUCCESS',
                    'Party retrieved successfully',
                    { result: { party: mockParty } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache party if not in cache', async () => {
                const mockParty = { _id: 'partyId', name: 'Birthday Party' };
                getByIdCache.mockResolvedValue(null);
                Party.findById.mockResolvedValue(mockParty);
                createResponse.mockReturnValue('successResponse');

                const result = await partyResolvers.Query.getPartyById(null, { id: 'partyId' });

                expect(getByIdCache).toHaveBeenCalledWith('partyId');
                expect(Party.findById).toHaveBeenCalledWith('partyId');
                expect(setCache).toHaveBeenCalledWith('partyId', mockParty);
                expect(result).toBe('successResponse');
            });

            it('should handle errors when retrieving party', async () => {
                const id = 'testPartyId';
                const error = new Error('Database error');
                
                getByIdCache.mockRejectedValue(error);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving party',
                    errors: [{ field: 'getPartyById', message: error.message }]
                });

                const result = await partyResolvers.Query.getPartyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Error retrieving party',
                    {
                        errors: [{ field: 'getPartyById', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving party',
                    errors: [{ field: 'getPartyById', message: error.message }]
                });
            });

            it('should return error response when party is not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                Party.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Party not found',
                    errors: [{ field: 'id', message: 'Party not found' }]
                });

                const result = await partyResolvers.Query.getPartyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Party.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Party not found',
                    {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Party not found',
                    errors: [{ field: 'id', message: 'Party not found' }]
                });
            });
        });

        describe('getParties', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return parties with pagination successfully', async () => {
                const mockParties = [
                    { _id: '1', name: 'Party 1' },
                    { _id: '2', name: 'Party 2' }
                ];
                const filter = { name: 'Party' };
                const pagination = { limit: 2, skip: 0 };

                buildPartyQuery.mockResolvedValue({
                    query: { name: /Party/i },
                    applyPostQueryFilters: jest.fn().mockResolvedValue(mockParties)
                });

                getPaginationInfo.mockResolvedValue({
                    totalItems: 2,
                    totalPages: 1,
                    currentPage: 1,
                    pageSize: 2,
                    skip: 0
                });

                const mockFind = {
                    skip: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockResolvedValue(mockParties)
                };
                Party.find.mockReturnValue(mockFind);

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Parties retrieved successfully',
                    result: { parties: mockParties },
                    pagination: {
                        totalItems: 2,
                        totalPages: 1,
                        currentPage: 1,
                        pageSize: 2,
                        skip: 0
                    }
                });

                const result = await partyResolvers.Query.getParties(null, { filter, pagination });

                expect(buildPartyQuery).toHaveBeenCalledWith(filter);
                expect(Party.find).toHaveBeenCalledWith({ name: /Party/i });
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(mockFind.limit).toHaveBeenCalledWith(2);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartiesResponse',
                    'SUCCESS',
                    'Parties retrieved successfully',
                    {
                        result: { parties: mockParties },
                        pagination: {
                            totalItems: 2,
                            totalPages: 1,
                            currentPage: 1,
                            pageSize: 2,
                            skip: 0
                        }
                    }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Parties retrieved successfully',
                    result: { parties: mockParties },
                    pagination: {
                        totalItems: 2,
                        totalPages: 1,
                        currentPage: 1,
                        pageSize: 2,
                        skip: 0
                    }
                });
            });

            it('should return no parties found response when no parties match filter', async () => {
                const mockParties = [];
                const filter = { name: 'NonexistentParty' };
                const pagination = { limit: 10, skip: 0 };

                buildPartyQuery.mockResolvedValue({
                    query: { name: /NonexistentParty/i },
                    applyPostQueryFilters: jest.fn().mockResolvedValue(mockParties)
                });

                getPaginationInfo.mockResolvedValue({
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                    pageSize: 10,
                    skip: 0
                });

                const mockFind = {
                    skip: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockResolvedValue(mockParties)
                };
                Party.find.mockReturnValue(mockFind);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'No parties found',
                    result: { parties: [] },
                    pagination: {
                        totalItems: 0,
                        totalPages: 0,
                        currentPage: 1,
                        pageSize: 10,
                        skip: 0
                    }
                });

                const result = await partyResolvers.Query.getParties(null, { filter, pagination });

                expect(buildPartyQuery).toHaveBeenCalledWith(filter);
                expect(Party.find).toHaveBeenCalledWith({ name: /NonexistentParty/i });
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartiesResponse',
                    'FAILURE',
                    'No parties found',
                    {
                        result: { parties: [] },
                        pagination: {
                            totalItems: 0,
                            totalPages: 0,
                            currentPage: 1,
                            pageSize: 10,
                            skip: 0
                        }
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'No parties found',
                    result: { parties: [] },
                    pagination: {
                        totalItems: 0,
                        totalPages: 0,
                        currentPage: 1,
                        pageSize: 10,
                        skip: 0
                    }
                });
            });

            it('should handle errors and return error response', async () => {
                const filter = { name: 'Party' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                buildPartyQuery.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving parties',
                    errors: [{ field: 'getParties', message: error.message }]
                });

                const result = await partyResolvers.Query.getParties(null, { filter, pagination });

                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Error retrieving parties',
                    {
                        errors: [{ field: 'getParties', message: error.message }]
                    }
                );
                expect(result.status).toBe('FAILURE');
                expect(result.message).toBe('Error retrieving parties');
                expect(result.errors[0].message).toBe('Database error');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createParty', () => {
            const mockInput = {
                name: 'Test Party',
                time: new Date('2024-12-25'),
                partyType: 'partyTypeId',
                eventId: 'eventId',
                coHosts: [
                    {
                        name: 'John Doe',
                        email: '<EMAIL>'
                    }
                ]
            };

            const mockParty = {
                _id: 'partyId',
                ...mockInput,
                coHosts: ['coHostId']
            };

            beforeEach(() => {
                validateReferences.mockReset();
                processCoHosts.mockReset();
                Party.mockReset();
                createResponse.mockReset();
            });

            it('should create party successfully', async () => {
                validateReferences.mockResolvedValue(null);
                processCoHosts.mockResolvedValue({ coHostIds: ['coHostId'] });
                
                const mockSave = jest.fn().mockResolvedValue(mockParty);
                Party.mockImplementation(() => ({
                    save: mockSave
                }));

                createResponse.mockReturnValue('successResponse');

                const result = await partyResolvers.Mutation.createParty(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalledWith(mockInput, createPartyIdSchema, 'Party');
                expect(processCoHosts).toHaveBeenCalledWith(mockInput.coHosts);
                expect(Party).toHaveBeenCalledWith({
                    ...mockInput,
                    coHosts: ['coHostId']
                });
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyResponse',
                    'SUCCESS',
                    'Party created successfully',
                    { result: { party: mockParty } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle reference validation errors', async () => {
                const validationError = {
                    field: 'partyType',
                    message: 'Invalid party type reference'
                };
                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.createParty(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalledWith(mockInput, createPartyIdSchema, 'Party');
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Invalid party information',
                    { errors: [validationError] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle input validation errors', async () => {
                validateReferences.mockResolvedValue(null);
                const mockInput = {
                    name: '', 
                    time: new Date('2020-01-01')
                };

                const result = await partyResolvers.Mutation.createParty(null, { input: mockInput });

                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Invalid party information',
                    {
                        errors: expect.arrayContaining([
                            { field: 'name', message: 'Party name is required' },
                            { field: 'time', message: 'Party time cannot be in the past' }
                        ])
                    }
                );
            });

            it('should handle coHost processing errors', async () => {
                validateReferences.mockResolvedValue(null);
                const coHostError = { errors: [{ field: 'email', message: 'Invalid email' }] };
                processCoHosts.mockResolvedValue(coHostError);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.createParty(null, { input: mockInput });

                expect(processCoHosts).toHaveBeenCalledWith(mockInput.coHosts);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Invalid co-host information',
                    { errors: coHostError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors', async () => {
                validateReferences.mockResolvedValue(null);
                processCoHosts.mockResolvedValue({ coHostIds: ['coHostId'] });
                
                const error = new Error('Database error');
                Party.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.createParty(null, { input: mockInput });

                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Error creating party',
                    {
                        errors: [{
                            field: 'party',
                            message: 'Database error'
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('updateParty', () => {
            const mockId = 'partyId';
            const mockInput = {
                name: 'Updated Party',
                time: new Date('2024-12-25'),
                coHosts: [
                    {
                        name: 'John Doe',
                        email: '<EMAIL>',
                        phone: '1234567890'
                    }
                ]
            };

            it('should update party successfully', async () => {
                const mockExistingParty = {
                    _id: mockId,
                    name: 'Original Party'
                };

                const mockUpdatedParty = {
                    _id: mockId,
                    ...mockInput,
                    coHosts: ['coHostId1']
                };

                validateReferences.mockResolvedValue(null);
                Party.findById.mockResolvedValue(mockExistingParty);
                processCoHosts.mockResolvedValue({ coHostIds: ['coHostId1'] });
                Party.findByIdAndUpdate.mockResolvedValue(mockUpdatedParty);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await partyResolvers.Mutation.updateParty(null, {
                    id: mockId,
                    input: mockInput
                });

                expect(validateReferences).toHaveBeenCalledWith(
                    expect.objectContaining({ name: mockInput.name }),
                    partyIdSchema,
                    'Party'
                );
                expect(Party.findById).toHaveBeenCalledWith(mockId);
                expect(processCoHosts).toHaveBeenCalledWith(mockInput.coHosts);
                expect(Party.findByIdAndUpdate).toHaveBeenCalledWith(
                    mockId,
                    expect.objectContaining({
                        name: mockInput.name,
                        coHosts: ['coHostId1']
                    }),
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(mockId);
                expect(result).toEqual({
                    type: 'PartyResponse',
                    status: 'SUCCESS',
                    message: 'Party updated successfully',
                    result: { party: mockUpdatedParty }
                });
            });

            it('should return error when party not found', async () => {
                validateReferences.mockResolvedValue(null);
                Party.findById.mockResolvedValue(null);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await partyResolvers.Mutation.updateParty(null, {
                    id: mockId,
                    input: mockInput
                });

                expect(Party.findById).toHaveBeenCalledWith(mockId);
                expect(Party.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(result).toEqual({
                    type: 'PartyErrorResponse',
                    status: 'FAILURE',
                    message: 'Party not found',
                    errors: [{ field: 'id', message: 'Party not found' }]
                });
            });

            it('should return error when validation fails', async () => {
                const validationError = {
                    message: 'Invalid references',
                    errors: [{ field: 'partyType', message: 'Invalid party type reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await partyResolvers.Mutation.updateParty(null, {
                    id: mockId,
                    input: mockInput
                });

                expect(Party.findById).not.toHaveBeenCalled();
                expect(Party.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(result).toEqual({
                    type: 'PartyErrorResponse',
                    status: 'FAILURE',
                    message: 'Invalid references',
                    errors: validationError.errors
                });
            });

            it('should return error when co-host processing fails', async () => {
                const mockExistingParty = {
                    _id: mockId,
                    name: 'Original Party'
                };

                validateReferences.mockResolvedValue(null);
                Party.findById.mockResolvedValue(mockExistingParty);
                processCoHosts.mockResolvedValue({
                    errors: [{ field: 'coHosts', message: 'Invalid co-host data' }]
                });
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await partyResolvers.Mutation.updateParty(null, {
                    id: mockId,
                    input: mockInput
                });

                expect(Party.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(result).toEqual({
                    type: 'PartyErrorResponse',
                    status: 'FAILURE',
                    message: 'Invalid co-host information',
                    errors: [{ field: 'coHosts', message: 'Invalid co-host data' }]
                });
            });

            it('should handle unexpected errors', async () => {
                const error = new Error('Database error');
                validateReferences.mockRejectedValue(error);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await partyResolvers.Mutation.updateParty(null, {
                    id: mockId,
                    input: mockInput
                });

                expect(result).toEqual({
                    type: 'PartyErrorResponse',
                    status: 'FAILURE',
                    message: 'Error updating party',
                    errors: [{ field: 'updateParty', message: 'Database error' }]
                });
            });
        });

        describe('deleteParty', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should check for references before deletion', async () => {
                const id = 'partyId';
                const references = ['Guest', 'PartyService'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.deleteParty(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Party');
                expect(Party.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Party cannot be deleted',
                    {
                        errors: [{ 
                            field: 'id', 
                            message: 'Party cannot be deleted as it is being used in: Guest, PartyService' 
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should delete party successfully when no references exist', async () => {
                const id = 'partyId';
                const mockParty = { _id: id, name: 'Test Party' };
                
                findReferences.mockResolvedValue([]);
                Party.findByIdAndDelete.mockResolvedValue(mockParty);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await partyResolvers.Mutation.deleteParty(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Party');
                expect(Party.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyResponse',
                    'SUCCESS',
                    'Party deleted successfully',
                    { result: { party: mockParty } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error when party not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                Party.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.deleteParty(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Party');
                expect(Party.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Party not found',
                    { errors: [{ field: 'id', message: 'Party not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors during deletion', async () => {
                const id = 'partyId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Party.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await partyResolvers.Mutation.deleteParty(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Party');
                expect(Party.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'PartyErrorResponse',
                    'FAILURE',
                    'Error deleting party',
                    { errors: [{ field: 'deleteParty', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
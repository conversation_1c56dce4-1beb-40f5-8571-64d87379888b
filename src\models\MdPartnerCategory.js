const { model, Schema } = require('mongoose');

const mdPartnerCategorySchema = new Schema({
    name: { type: String, required: true },
    icon: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    description: { type: String, required: false },
}, { timestamps: true, collection: 'md_partner_categories' });

const MdPartnerCategory = model('MdPartnerCategory', mdPartnerCategorySchema);

module.exports = MdPartnerCategory;
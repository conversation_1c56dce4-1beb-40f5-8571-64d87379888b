const Invitation = require('../../models/Invitation');
const Guest = require('../../models/Guest');
const InvitationRSVP = require('../../models/InvitationRSVP');
const { hasEventPartyLevelAccess, hasEventPartyGuestLevelAccess } = require('../../utils/auth/accessLevels.util');
const Party = require('../../models/Party');
const { createResponse } = require('../../utils/response.util');
const { validateReferences } = require('../../utils/validation.util');
const { setCache } = require('../../utils/cache.util');
const { notifyRSVPUpdate } = require('../../notification/partyNotification');

const invitationRSVPSchema = {
    invitation: { type: 'single', model: Invitation},
    guest: { type: 'single', model: Guest}
}


const invitationRSVPResolvers = {
    InvitationRSVP: {
        invitation: async (parent) => {
            try {
                return await Invitation.findById(parent.invitation);
            } catch (error) {
                throw new Error('Error getting invitation');
            }
        },
        guest: async (parent) => {
            try {
                return await Guest.findById(parent.guest);
            } catch (error) {
                throw new Error('Error getting guest');
            }
        }
    },
    Query: {
        getInvitationRSVPById: async (_, { id }, context) => {
            try {
                const invitationRSVP = await InvitationRSVP.findById(id);
                if (!invitationRSVP) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Invitation RSVP not found', {
                        errors: [{ field: 'id', message: 'Invitation RSVP not found' }]
                    });
                }

                const invitation = await Invitation.findById(invitationRSVP.invitation);
                if (!invitation) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitation', message: 'Invitation not found' }]
                    });
                }

                const party = await Party.findById(invitation.party);

                if (!party) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'party', message: 'Party not found' }]
                    });
                }

                if (!await hasEventPartyLevelAccess(context.user._id, party.eventId, party._id)) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this invitation RSVP' }]
                    });
                }

                return createResponse('InvitationRSVPResponse', 'SUCCESS', 'Invitation RSVP retrieved successfully', {
                    result: { invitationRSVP }
                });

            } catch (error) {
                return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Error getting invitation RSVP', {
                    errors: [{ field: 'getInvitationRSVPById', message: error.message }]
                });
            }
        }
    },
    Mutation: {
        updateInvitationRSVP: async (_, { id, input }, context) => {
            try {
                if (input.additionalGuestsCount && input.additionalGuestsCount < 0) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Additional guests count cannot be negative', {
                        errors: [{ field: 'additionalGuestsCount', message: 'Additional guests count cannot be negative' }]
                    });
                }

                let invitationRSVP = await InvitationRSVP.findById(id);
                if (!invitationRSVP) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Invitation RSVP not found', {
                        errors: [{ field: 'id', message: 'Invitation RSVP not found' }]
                    });
                }

                const invitation = await Invitation.findById(invitationRSVP.invitation);
                if (!invitation) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitation', message: 'Invitation not found' }]
                    });
                }

                const party = await Party.findById(invitation.party);
                if (!party) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'party', message: 'Party not found' }]
                    });
                }

                if (!await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id)) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this invitation RSVP' }]
                    });
                }

                const guest = await Guest.findById(invitationRSVP.guest);
                if (!guest) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Guest not found', {
                        errors: [{ field: 'guest', message: 'Guest not found' }]
                    });
                }

                const validationError = await validateReferences(input, invitationRSVPSchema, 'InvitationRSVP');
                if (validationError) {
                    return createResponse('InvitationRSVPErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                Object.assign(invitationRSVP, input);
                await invitationRSVP.save();

                if (input.status) {
                    await notifyRSVPUpdate(guest, party, input.status, context.user._id);
                }

                if (input.additionalGuestsCount) {
                    guest.additionalGuestsCount = input.additionalGuestsCount;
                    await guest.save();
                }

                await setCache(party._id, party);

                return createResponse('InvitationRSVPResponse', 'SUCCESS', 'Invitation RSVP updated successfully', {
                    result: { invitationRSVP }
                });

            } catch (error) {
                return createResponse('InvitationRSVPErrorResponse', 'FAILURE', 'Error updating invitation RSVP', {
                    errors: [{ field: 'updateInvitationRSVP', message: error.message }]
                });
            }
        }
    }
}

module.exports = invitationRSVPResolvers


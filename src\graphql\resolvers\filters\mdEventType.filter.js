const { isValidObjectId } = require("../../../utils/validation.util");

const VALID_DURATIONS = ['ONE_DAY', 'MULTI_DAY'];

const buildMdEventTypeQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.duration) {
            if (!VALID_DURATIONS.includes(filter.duration)) {
                throw new Error(`Invalid duration value. Must be one of: ${VALID_DURATIONS.join(', ')}`);
            }
            query.duration = filter.duration;
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }
    }

    return query;
};

module.exports = buildMdEventTypeQuery;
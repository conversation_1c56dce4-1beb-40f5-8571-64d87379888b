const buildMediaQuery = require('../../../../src/graphql/resolvers/filters/media.filter');
const mediaResolvers = require('../../../../src/graphql/resolvers/media.resolver');
const findReferences = require('../../../../src/graphql/resolvers/references/media.references');
const Media = require('../../../../src/models/Media');
const Tag = require('../../../../src/models/Tag');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { mongoose } = require('mongoose');

jest.mock('../../../../src/models/Media');
jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/media.filter');
jest.mock('../../../../src/graphql/resolvers/references/media.references');

beforeAll(() => {
    console.error = jest.fn();
});

beforeEach(() => {
    jest.clearAllMocks();
});

describe('Media Resolvers', () => {
    describe('Media', () => {
        describe('tags', () => {
            it('should return tags for the given media', async () => {
                const media = { tags: ['tagId1', 'tagId2'] };
                const mockTags = [
                    { _id: 'tagId1', name: 'Tag 1' },
                    { _id: 'tagId2', name: 'Tag 2' }
                ];
    
                Tag.find.mockResolvedValue(mockTags);
    
                const result = await mediaResolvers.Media.tags(media);
    
                expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: media.tags } });
                expect(result).toEqual(mockTags);
            });

            it('should handle errors and throw them', async () => {
                const media = { tags: ['tagId1'] };
                const errorMessage = 'Database error';
                Tag.find.mockRejectedValue(new Error(errorMessage));

                await expect(mediaResolvers.Media.tags(media)).rejects.toThrow(errorMessage);
                expect(console.error).toHaveBeenCalledWith(expect.any(Error));
            });
        });
    });

    describe('Query', () => {
        describe('getMediaById', () => {
            it('should fetch media and set cache', async () => {
                const mediaId = 'someMediaId';
                const mediaData = { _id: mediaId, title: 'Test Media' };

                getByIdCache.mockResolvedValue(null);
                Media.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(mediaData)
                });
                setCache.mockResolvedValue();

                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Media fetched successfully',
                    result: { media: mediaData }
                });

                const result = await mediaResolvers.Query.getMediaById(null, { id: mediaId });

                expect(Media.findById).toHaveBeenCalledWith(mediaId);
                expect(setCache).toHaveBeenCalledWith(mediaId, mediaData);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Media fetched successfully',
                    result: { media: mediaData }
                });
            });

            it('should return error response if media not found', async () => {
                const id = 'nonexistentId';
                getByIdCache.mockResolvedValue(null);
                Media.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(null) 
                });
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Media not found',
                    errors: [{ field: 'id', message: 'Media not found' }]
                });

                const result = await mediaResolvers.Query.getMediaById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Media.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MediaErrorResponse',
                    'FAILURE',
                    'Media not found',
                    { errors: [{ field: 'id', message: 'Media not found' }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Media not found',
                    errors: [{ field: 'id', message: 'Media not found' }]
                });
            });

            it('should return error response if an error occurs', async () => {
                const id = 'mediaId';
                const error = new Error('Error retrieving media');
                
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving media',
                    errors: [{ field: 'getMediaById', message: error.message }]
                });

                const result = await mediaResolvers.Query.getMediaById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MediaErrorResponse',
                    'FAILURE',
                    'Error retrieving media',
                    { errors: [{ field: 'getMediaById', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving media',
                    errors: [{ field: 'getMediaById', message: error.message }]
                });
            });
        });

        describe('getMedias', () => {
            it('should return medias with pagination', async () => {
                const mockMedias = [{ _id: '1', title: 'Test Media 1' }, { _id: '2', title: 'Test Media 2' }];
                Media.find.mockResolvedValue(mockMedias);
                const result = await mediaResolvers.Query.getMedias(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
                expect(result).toEqual(createResponse('MediasResponse', 'SUCCESS', 'Media fetched successfully', { result: { medias: mockMedias }, pagination: expect.any(Object) }));
            });

            it('should return failure response if no media found', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
        
                Media.find.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockResolvedValue([]),
                });
                getPaginationInfo.mockResolvedValue(paginationInfo);
        
                const result = await mediaResolvers.Query.getMedias(null, { filter, pagination });
        
                const expectedResponse = createResponse('MediasResponse', 'FAILURE', 'No media found', { result: { medias: [] }, pagination: paginationInfo });
                expect(result).toEqual(expectedResponse);
            });

            it('should return media successfully with pagination info', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const query = {};
                const paginationInfo = { total: 5, limit: 10, skip: 0 };
                const medias = [{ _id: '1', title: 'Media 1' }, { _id: '2', title: 'Media 2' }];
            
                buildMediaQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                Media.find.mockReturnValue({
                    populate: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockResolvedValue(medias),
                });
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Media fetched successfully',
                    result: { medias },
                    pagination: paginationInfo,
                });
            
                const result = await mediaResolvers.Query.getMedias(null, { filter, pagination });
            
                expect(buildMediaQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(Media, query, pagination.limit, pagination.skip);
                expect(Media.find).toHaveBeenCalledWith(query);
                expect(createResponse).toHaveBeenCalledWith(
                    'MediasResponse',
                    'SUCCESS',
                    'Media fetched successfully',
                    { result: { medias }, pagination: paginationInfo }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Media fetched successfully',
                    result: { medias },
                    pagination: paginationInfo,
                });
            });
        });
    });

    describe('Mutation', () => {
        describe('createMedia', () => {
            it('should create and return the media successfully', async () => {
                const input = { title: 'New Media', tagIds: ['validTagId1', 'validTagId2'] };
                const media = { _id: 'mediaId', ...input, tags: ['validTagId1', 'validTagId2'] };

                mongoose.Types.ObjectId.isValid = jest.fn().mockReturnValue(true);
                Media.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(media),
                    populate: jest.fn().mockResolvedValue(media),
                }));
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Media created successfully',
                    result: { media },
                });

                const result = await mediaResolvers.Mutation.createMedia(null, { input });

                expect(Media).toHaveBeenCalledWith({ ...input, tags: ['validTagId1', 'validTagId2'] });
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Media created successfully',
                    result: { media },
                });
            });

            it('should return an error response if creation fails', async () => {
                const input = { title: 'New Media', tagIds: ['validTagId1', 'validTagId2'] };
                const error = new Error('Error creating media');

                mongoose.Types.ObjectId.isValid = jest.fn().mockReturnValue(true);
                Media.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating media',
                    errors: [{ field: 'createMedia', message: error.message }],
                });

                const result = await mediaResolvers.Mutation.createMedia(null, { input });

                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating media',
                    errors: [{ field: 'createMedia', message: error.message }],
                });
            });

            it('should return an error response for invalid tag IDs', async () => {
                const input = { title: 'New Media', tagIds: ['validTagId1', 'invalidTagId'] };
                const validTagId = 'validTagId1';
                const invalidTagId = 'invalidTagId';
            
                mongoose.Types.ObjectId.isValid = jest.fn(tagId => tagId === validTagId);
            
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Invalid tag IDs provided',
                    errors: [{ field: 'tags', message: 'Invalid tag IDs', invalidIds: [invalidTagId] }],
                });
            
                const result = await mediaResolvers.Mutation.createMedia(null, { input });
            
                expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith(validTagId);
                expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith(invalidTagId);
                expect(createResponse).toHaveBeenCalledWith(
                    'MediaErrorResponse',
                    'FAILURE',
                    'Invalid tag IDs provided',
                    { errors: [{ field: 'tags', message: 'Invalid tag IDs', invalidIds: [invalidTagId] }] }
                );
                
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Invalid tag IDs provided',
                    errors: [{ field: 'tags', message: 'Invalid tag IDs', invalidIds: [invalidTagId] }],
                });
            });
        });

        describe('updateMedia', () => {
            it('should update media successfully', async () => {
                const input = { title: 'Updated Media', tagIds: ['validTagId'] };
                const mockMedia = { _id: '1', title: 'Updated Media', tags: [] };
                Media.findByIdAndUpdate.mockResolvedValue(mockMedia);
                Tag.find.mockResolvedValue([{ _id: 'validTagId' }]);
                const result = await mediaResolvers.Mutation.updateMedia(null, { id: '1', input });
                expect(result).toEqual(createResponse('MediaResponse', 'SUCCESS', 'Media updated successfully', { result: { media: mockMedia } }));
            });

            it('should return error when media not found', async () => {
                Media.findByIdAndUpdate.mockResolvedValue(null);
                const result = await mediaResolvers.Mutation.updateMedia(null, { id: '1', input: {} });
                expect(result).toEqual(createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { errors: [{ field: 'id', message: 'Media not found' }] }));
            });

            it('should return an error if media not found', async () => {
                Media.findByIdAndUpdate.mockResolvedValue(null);
                const result = await mediaResolvers.Mutation.updateMedia(null, { id: 'mediaId', input: { tagIds: ['validTagId'] } });

                expect(result).toEqual(createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { errors: [{ field: 'id', message: 'Media not found' }] }));
            });

            it('should handle errors during the update process', async () => {
                const errorMessage = 'Database error';
                Media.findByIdAndUpdate.mockRejectedValue(new Error(errorMessage));
                const result = await mediaResolvers.Mutation.updateMedia(null, { id: 'mediaId', input: { tagIds: ['validTagId'] } });

                expect(result).toEqual(createResponse('MediaErrorResponse', 'FAILURE', 'Error updating media', { errors: [{ field: 'updateMedia', message: errorMessage }] }));
            });
        });

        describe('deleteMedia', () => {
            it('should delete media successfully', async () => {
                const mockMedia = { _id: '1', title: 'Media to delete' };
                Media.findByIdAndDelete.mockResolvedValue(mockMedia);
                const result = await mediaResolvers.Mutation.deleteMedia(null, { id: '1' });
                expect(result).toEqual(createResponse('MediaResponse', 'SUCCESS', 'Media deleted successfully', { result: { media: mockMedia } }));
            });

            it('should return error when media not found', async () => {
                Media.findByIdAndDelete.mockResolvedValue(null);
                const result = await mediaResolvers.Mutation.deleteMedia(null, { id: '1' });
                expect(result).toEqual(createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { errors: [{ field: 'id', message: 'Media not found' }] }));
            });

            it('should return an error response if an error occurs during deletion', async () => {
                const id = 'mediaId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                Media.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting media',
                    errors: [{ field: 'deleteMedia', message: error.message }]
                });

                const result = await mediaResolvers.Mutation.deleteMedia(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Media.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MediaErrorResponse',
                    'FAILURE',
                    'Error deleting media',
                    { errors: [{ field: 'deleteMedia', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting media',
                    errors: [{ field: 'deleteMedia', message: error.message }]
                });
            });
        });
    });
});


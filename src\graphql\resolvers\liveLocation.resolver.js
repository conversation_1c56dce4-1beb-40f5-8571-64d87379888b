const Guest = require('../../models/Guest');
const LiveLocation = require('../../models/liveLocation');
const { createResponse } = require('../../utils/response.util');
const { setCache, clearCacheById } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const buildLiveLocationQuery = require('./filters/liveLocation.filter');
const { requestLiveLocation } = require('../../utils/socket.util');
const { hasEventPartyLevelAccess, hasGuestLevelAccess } = require('../../utils/auth/accessLevels.util');
const Party = require('../../models/Party');
const { addMessageToActivityQueue } = require('../../utils/messageQueue.util');
const Message = require('../../models/Message');

const liveLocationSchema = {
    guestId: { type: 'single', model: Guest, required: true }
}

const getDHMFromTimestamp = (timestamp) => {
    // Calculate the difference in milliseconds
    const diffMs = new Date(timestamp) - new Date();
    const diffSeconds = Math.floor(diffMs / 1000);

    // Convert to days, hours, minutes
    const days = Math.floor(diffSeconds / (24 * 60 * 60));
    const hours = Math.floor((diffSeconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);

    // Build the string format
    let etaString = '';
    if (days > 0) etaString += `${days}d `;
    if (hours > 0) etaString += `${hours}h `;
    if (minutes > 0) etaString += `${minutes}m`;
    
    // Trim any trailing space
    return etaString.trim();
}

const liveLocationResolvers = {
    LiveLocation: {
        ETA: async (parent) => {
            try {
                if (parent.allowLiveTracking != 'ALLOW') {
                    return null;
                }
                
                // Execute queries in parallel using Promise.all
                const [guest, party] = await Promise.all([
                    Guest.findById(parent.guestId),
                    Guest.findById(parent.guestId).populate({
                        path: 'party',
                        populate: {
                            path: 'venueAddress'
                        }
                    })
                ]);

                if (!guest || !party || !party.party) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Required data not found', { errors: [{ field: 'guestId', message: 'Required data not found' }] });
                }
                
                if (!party.party.venueAddress) {
                    return "Venue location not set - ETA cannot be calculated";
                }
                
                if (!party.party.venueAddress.placeId) {
                    return "Venue address is missing place ID - ETA cannot be calculated";
                }
                
                const etaTimestamp = await parent.calculateETA(party.party.venueAddress.placeId);
                return getDHMFromTimestamp(etaTimestamp);    
            } catch (error) {
                console.error(error);
                // Return the specific error message instead of a generic one
                if (error.message.includes('ETA cannot be calculated') || 
                    error.message.includes('No location data available')) {
                    return error.message;
                }
                return "Error calculating ETA";
            }
        },
        guest: async (parent) => {
            try {
                return await Guest.findById(parent.guestId);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting guest');
            }
        }
    },
    Query: {
        getLiveLocationByGuestId: async (_, { guestId }, context) => {
            try {
                const liveLocation = await LiveLocation.findOne({ guestId });
                if (!liveLocation) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Live location not found', { errors: [{ field: 'guestId', message: 'Live location not found' }] });
                }

                if (liveLocation.locationId) 
                    await liveLocation.updateCoordinatesFromPlaceId();

                await setCache(guestId, liveLocation);
                return createResponse('LiveLocationResponse', 'SUCCESS', 'Live location fetched successfully', { result: { liveLocation } });
            } catch (error) {
                console.error(error);
                return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Error getting live location', { errors: [{ field: 'getLiveLocationByGuestId', message: error.message }] });
            }
        },
        getLiveLocationsByPartyId: async (_, { partyId, filter, pagination }, context) => {
            try {
                const party = await Party.findById(partyId);
                if (!party) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Party not found', { 
                        errors: [{ field: 'partyId', message: 'Party not found' }] 
                    });
                }
                if (!await hasGuestLevelAccess(context.user._id, party.eventId)) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'You do not have access to this party', { 
                        errors: [{ field: 'getLiveLocationsByPartyId', message: 'You do not have access to this party' }] 
                    });
                }
                const guests = await Guest.find({ party: partyId });
                const query = await buildLiveLocationQuery(filter, guests);
                const liveLocations = await LiveLocation.find(query);

                if (!liveLocations) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Live locations not found', { errors: [{ field: 'partyId', message: 'Live locations not found' }] });
                }

                for (const liveLocation of liveLocations) {
                    if (liveLocation.locationId) 
                        await liveLocation.updateCoordinatesFromPlaceId();
                }

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(LiveLocation, query, limit, skip);

                return createResponse('LiveLocationsResponse', 'SUCCESS', 'Live locations fetched successfully', { result: { liveLocations }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Error getting live locations', { errors: [{ field: 'getLiveLocationsByPartyId', message: error.message }] });
            }

        },
        requestLiveLocationFormClient: async (_, { eventId, partyId }, context) => {
            try {
                if (!await hasEventPartyLevelAccess(context.user._id, eventId, partyId)) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'You do not have access to this event', { 
                        errors: [{ field: 'requestLiveLocationFormClient', message: 'You do not have access to this event' }] 
                    });
                }

                await requestLiveLocation({ eventId, partyId });
                return createResponse('LiveLocationMessageResponse', 'SUCCESS', 'Live location requested successfully', {
                    result: { message: "Live location requested successfully" }
                });
            } catch (error) {
                console.error(error);
                return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Error requesting live location', { 
                    errors: [{ field: 'requestLiveLocationForClient', message: error.message }] 
                });
            }
        }
    },
    Mutation: {
        updateLiveLocation: async (_, { guestId, input }, context) => {
            try {
                const guest = await Guest.findById(guestId).populate('party');
                if (!guest) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Guest not found', { 
                        errors: [{ field: 'guestId', message: 'Guest not found' }] 
                    });
                }
                if (!await hasGuestLevelAccess(context.user._id, guest.party.eventId)) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'You do not have access to this guest', { 
                        errors: [{ field: 'updateLiveLocation', message: 'You do not have access to this guest' }] 
                    });
                }
                
                const VALID_TRACKING_STATES = ['ALLOW', 'DISALLOW'];
                if (input.allowLiveTracking && !VALID_TRACKING_STATES.includes(input.allowLiveTracking)) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Invalid allowLiveTracking value', { 
                        errors: [{ field: 'allowLiveTracking', message: 'Invalid allowLiveTracking value' }] 
                    });
                }
                
                let liveLocation = await LiveLocation.findOne({ guestId });
                
                if (!liveLocation) 
                    liveLocation = new LiveLocation({ guestId });
                
                if (input.locationId !== undefined) {
                    liveLocation.locationId = input.locationId;
                    await liveLocation.updateCoordinatesFromPlaceId();
                }
                
                if (input.allowLiveTracking !== undefined)
                    liveLocation.allowLiveTracking = input.allowLiveTracking;
                
                if (input.coordinates) {
                    liveLocation.coordinates = {
                        latitude: input.coordinates.latitude,
                        longitude: input.coordinates.longitude
                    };
                        
                    if (input.coordinates.latitude && input.coordinates.longitude)
                        liveLocation.locationId = null;
                }
                
                await liveLocation.save();
                
                await setCache(guestId, liveLocation);
                
                return createResponse('LiveLocationResponse', 'SUCCESS', 'Live location updated successfully', { 
                    result: { liveLocation } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Error updating live location', { 
                    errors: [{ field: 'updateLiveLocation', message: error.message }] 
                });
            }
        },

        sendLiveLocationReminder: async (_, { partyId }, context) => {
            try {
                const party = await Party.findById(partyId).populate('eventId');
                if (!party) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Party not found', { errors: [{ field: 'partyId', message: 'Party not found' }] });
                }
                if (!await hasEventPartyLevelAccess(context.user._id, party.eventId, partyId)) {
                    return createResponse('LiveLocationErrorResponse', 'FAILURE', 'You do not have access to this event', { errors: [{ field: 'sendRemainder', message: 'You do not have access to this event' }] });
                }

                const guests = await Guest.find({ party: partyId });
                const liveLocations = await LiveLocation.find({ guestId: { $in: guests.map(guest => guest._id) } });

                const message = new Message({
                    type: 'SYSTEM',
                    text: 'Please share your live location to help us track your arrival time. <button>',
                    receivers: guests.filter(guest => 
                        liveLocations.some(loc => 
                            loc.guestId.toString() === guest._id.toString() && 
                            (loc.allowLiveTracking === 'PENDING' || loc.allowLiveTracking === 'DISALLOW')
                        )
                    ).map(guest => guest.user)
                })

                await message.save();
                party.activity.push(message._id);
                await party.save();
                await clearCacheById(party._id);
                await addMessageToActivityQueue(partyId, message);

                return createResponse('SendLiveLocationReminderResponse', 'SUCCESS', 'Reminder sent successfully', { result: true });
            } catch (error) {
                console.error(error);
                return createResponse('LiveLocationErrorResponse', 'FAILURE', 'Error sending reminder', { errors: [{ field: 'sendReminder', message: error.message }] });
            }
        }


    }

};




module.exports = liveLocationResolvers;


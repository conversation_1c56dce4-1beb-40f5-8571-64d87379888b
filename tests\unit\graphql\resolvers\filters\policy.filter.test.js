const mongoose = require('mongoose');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');
const buildPolicyQuery = require('../../../../../src/graphql/resolvers/filters/policy.filter');
const buildVendorQuery = require('../../../../../src/graphql/resolvers/filters/vendor.filter');
const buildTagQuery = require('../../../../../src/graphql/resolvers/filters/tag.filter');
const Vendor = require('../../../../../src/models/Vendor');
const Tag = require('../../../../../src/models/Tag');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/vendor.filter');
jest.mock('../../../../../src/graphql/resolvers/filters/tag.filter');
jest.mock('../../../../../src/models/Vendor');
jest.mock('../../../../../src/models/Tag');

describe('buildPolicyQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', async () => {
        const result = await buildPolicyQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid ID filter', async () => {
        isValidObjectId.mockReturnValue(true);
        const validId = new mongoose.Types.ObjectId().toString();

        const result = await buildPolicyQuery({ id: validId });
        expect(result).toEqual({ _id: validId });
    });

    it('should throw error with invalid ID filter', async () => {
        isValidObjectId.mockReturnValue(false);
        
        await expect(buildPolicyQuery({ id: 'invalid-id' }))
            .rejects
            .toThrow('Invalid id provided');
    });

    it('should build query with name filter', async () => {
        const result = await buildPolicyQuery({ name: 'test policy' });
        expect(result).toEqual({
            name: { $regex: 'test policy', $options: 'i' }
        });
    });

    it('should build query with description filter', async () => {
        const result = await buildPolicyQuery({ description: 'test description' });
        expect(result).toEqual({
            description: { $regex: 'test description', $options: 'i' }
        });
    });

    it('should build query with active filter', async () => {
        const result = await buildPolicyQuery({ active: true });
        expect(result).toEqual({ active: true });
    });

    describe('vendor filter', () => {
        it('should build query with valid vendor ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const validVendorId = new mongoose.Types.ObjectId().toString();

            const result = await buildPolicyQuery({ vendor: validVendorId });
            expect(result).toEqual({ vendor: validVendorId });
        });

        it('should throw error with invalid vendor ID', async () => {
            isValidObjectId.mockReturnValue(false);
            
            await expect(buildPolicyQuery({ vendor: 'invalid-id' }))
                .rejects
                .toThrow('Invalid vendor ID provided');
        });

        it('should build query with vendor object filter', async () => {
            const vendorFilter = { name: 'test vendor' };
            const vendorPipeline = [{ $match: { name: 'test vendor' } }];
            const matchingVendors = [{ _id: 'vendor1' }, { _id: 'vendor2' }];

            buildVendorQuery.mockReturnValue(vendorPipeline);
            Vendor.aggregate.mockResolvedValue(matchingVendors);

            const result = await buildPolicyQuery({ vendor: vendorFilter });
            expect(result).toEqual({
                vendor: { $in: ['vendor1', 'vendor2'] }
            });
        });

        it('should handle empty vendor results', async () => {
            buildVendorQuery.mockReturnValue([{ $match: {} }]);
            Vendor.aggregate.mockResolvedValue([]);

            const result = await buildPolicyQuery({ vendor: { name: 'nonexistent' } });
            expect(result).toEqual({
                vendor: { $in: [] }
            });
        });
    });

    describe('tag filter', () => {
        it('should build query with valid tag ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const validTagId = new mongoose.Types.ObjectId().toString();

            const result = await buildPolicyQuery({ tag: validTagId });
            expect(result).toEqual({ tags: validTagId });
        });

        it('should throw error with invalid tag ID', async () => {
            isValidObjectId.mockReturnValue(false);
            
            await expect(buildPolicyQuery({ tag: 'invalid-id' }))
                .rejects
                .toThrow('Invalid tag ID provided');
        });

        it('should build query with tag object filter', async () => {
            const tagFilter = { name: 'test tag' };
            const tagQuery = { name: 'test tag' };
            const matchingTags = [{ _id: 'tag1' }, { _id: 'tag2' }];

            buildTagQuery.mockResolvedValue(tagQuery);
            Tag.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingTags)
            });

            const result = await buildPolicyQuery({ tag: tagFilter });
            expect(result).toEqual({
                tags: { $in: ['tag1', 'tag2'] }
            });
        });

        it('should handle empty tag results', async () => {
            buildTagQuery.mockResolvedValue({ name: 'nonexistent' });
            Tag.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });

            const result = await buildPolicyQuery({ tag: { name: 'nonexistent' } });
            expect(result).toEqual({
                tags: { $in: [] }
            });
        });
    });

    it('should build query with multiple filters', async () => {
        isValidObjectId.mockReturnValue(true);
        const validId = new mongoose.Types.ObjectId().toString();

        const result = await buildPolicyQuery({
            id: validId,
            name: 'test policy',
            description: 'test description',
            active: true
        });

        expect(result).toEqual({
            _id: validId,
            name: { $regex: 'test policy', $options: 'i' },
            description: { $regex: 'test description', $options: 'i' },
            active: true
        });
    });
}); 
const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const transactionSchema = new Schema({
    description: { type: String, required: true },
    amount: { type: Number, required: true, default: 0.0 },
    type: { 
        type: String, 
        enum: ['CREDIT', 'DEBIT'], 
        required: true 
    },
    method: { 
        type: String, 
        enum: ['CASH', 'UPI', 'CREDIT_CARD', 'DEBIT_CARD'], 
        required: true 
    },
    timeStamp: { type: Date, default: Date.now },
    documents: [{ type: Schema.Types.ObjectId, ref: 'Document' }]
}, { timestamps: true });

const Transaction = model('Transaction', transactionSchema);

module.exports = Transaction;
const mongoose = require('mongoose');
const buildMdServiceLocationQuery = require('../../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');

jest.mock('mongoose');

describe('buildMdServiceLocationQuery', () => {
    it('should return an empty query when no filter is provided', () => {
        const result = buildMdServiceLocationQuery();
        expect(result).toEqual({});
    });

    it('should build a query with valid id filter', () => {
        const validId = '507f1f77bcf86cd799439011';
        mongoose.Types.ObjectId.isValid.mockReturnValue(true);
        const result = buildMdServiceLocationQuery({ id: validId });
        expect(result).toEqual({ _id: validId });
    });

    it('should throw an error with invalid id filter', () => {
        const invalidId = 'invalid-id';
        mongoose.Types.ObjectId.isValid.mockReturnValue(false);
        expect(() => buildMdServiceLocationQuery({ id: invalidId })).toThrow('Invalid id provided');
    });

    it('should build a query with name filter', () => {
        const result = buildMdServiceLocationQuery({ name: 'Test Location' });
        expect(result).toEqual({ name: { $regex: /Test Location/i } });
    });

    it('should build a query with city filter', () => {
        const result = buildMdServiceLocationQuery({ city: 'New York' });
        expect(result).toEqual({ city: { $regex: /New York/i } });
    });

    it('should build a query with areas filter', () => {
        const result = buildMdServiceLocationQuery({ areas: ['Downtown', 'Suburb'] });
        expect(result).toEqual({ areas: { $in: [/Downtown/i, /Suburb/i] } });
    });

    it('should filter by state case-insensitively', () => {
        const filter = { state: 'California' };
        const result = buildMdServiceLocationQuery(filter);
        
        expect(result).toEqual({
            state: { $regex: new RegExp('California', 'i') }
        });
    });

    it('should match state partially', () => {
        const filter = { state: 'Cali' };
        const result = buildMdServiceLocationQuery(filter);
        
        expect(result).toEqual({
            state: { $regex: new RegExp('Cali', 'i') }
        });
    });

    it('should build a query with multiple filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        mongoose.Types.ObjectId.isValid.mockReturnValue(true);
        const result = buildMdServiceLocationQuery({
            id: validId,
            name: 'Test Location',
            city: 'New York',
            areas: ['Downtown', 'Suburb']
        });
        expect(result).toEqual({
            _id: validId,
            name: { $regex: /Test Location/i },
            city: { $regex: /New York/i },
            areas: { $in: [/Downtown/i, /Suburb/i] }
        });
    });

    it('should ignore empty areas array', () => {
        const result = buildMdServiceLocationQuery({ areas: [] });
        expect(result).toEqual({});
    });
});
const mongoose = require('mongoose');
const Document = require('../../../../src/models/Document');
const { User } = require('../../../../src/models/User');
const documentResolvers = require('../../../../src/graphql/resolvers/document.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const buildDocumentQuery = require('../../../../src/graphql/resolvers/filters/document.filter');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const findReferences = require('../../../../src/graphql/resolvers/references/document.references');

jest.mock('../../../../src/models/Document');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/document.filter');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/references/document.references');

describe('Document Resolvers', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Document.createdBy', () => {
    it('should return user when createdBy is an object with id', async () => {
      const mockUser = { id: 'user123', name: 'John Doe' };
      User.findById.mockResolvedValue(mockUser);
  
      const parent = { createdBy: { id: 'user123' } };
      const result = await documentResolvers.Document.createdBy(parent);
  
      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(result).toEqual(mockUser);
    });
  
    it('should return user when createdBy is a string', async () => {
      const mockUser = { id: 'user456', name: 'Jane Doe' };
      User.findById.mockResolvedValue(mockUser);
  
      const parent = { createdBy: 'user456' };
      const result = await documentResolvers.Document.createdBy(parent);
  
      expect(User.findById).toHaveBeenCalledWith('user456');
      expect(result).toEqual(mockUser);
    });
  
    it('should throw an error when createdBy is invalid', async () => {
      const parent = { createdBy: 123 };
      
      await expect(documentResolvers.Document.createdBy(parent)).rejects.toThrow('Invalid createdBy field');
    });
    
    it('should throw an error when User.findById fails', async () => {
      User.findById.mockRejectedValue(new Error('Database error'));
      
      const parent = { createdBy: 'user789' };
      
      console.error = jest.fn();
      
      await expect(documentResolvers.Document.createdBy(parent)).rejects.toThrow('Error getting created by');
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('Query.getDocumentById', () => {
    it('should return a document if found in cache', async () => {
      const mockDocument = { id: 'docId', title: 'Test Document' };
      getByIdCache.mockResolvedValue(mockDocument);
      createResponse.mockReturnValue('success response');

      const result = await documentResolvers.Query.getDocumentById(null, { id: 'docId' });

      expect(getByIdCache).toHaveBeenCalledWith('docId');
      expect(setCache).toHaveBeenCalledWith('docId', mockDocument);
      expect(createResponse).toHaveBeenCalledWith('DocumentResponse', 'SUCCESS', 'Document fetched successfully', { result: { document: mockDocument } });
      expect(result).toBe('success response');
    });

    it('should return a document if found in database but not in cache', async () => {
        const mockDocument = { id: 'docId', title: 'Test Document' };
        getByIdCache.mockResolvedValue(null);
        Document.findById = jest.fn().mockReturnValue({
            populate: jest.fn().mockResolvedValue(mockDocument)
        });
        createResponse.mockReturnValue('success response');

        const result = await documentResolvers.Query.getDocumentById(null, { id: 'docId' });

        expect(getByIdCache).toHaveBeenCalledWith('docId');
        expect(Document.findById).toHaveBeenCalledWith('docId');
        expect(setCache).toHaveBeenCalledWith('docId', mockDocument);
        expect(createResponse).toHaveBeenCalledWith('DocumentResponse', 'SUCCESS', 'Document fetched successfully', { result: { document: mockDocument } });
        expect(result).toBe('success response');
    });

    it('should return an error response if document is not found', async () => {
      getByIdCache.mockResolvedValue(null);
      Document.findById.mockReturnValue({
        populate: jest.fn().mockResolvedValue(null)
      });
      createResponse.mockReturnValue('error response');

      const result = await documentResolvers.Query.getDocumentById(null, { id: 'docId' });

      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Document not found', { errors: [{ field: 'id', message: 'Document not found' }] });
      expect(result).toBe('error response');
    });
  });

  describe('Query.getDocuments', () => {
    it('should fetch documents successfully', async () => {
      const mockFilter = { name: 'Test' };
      const mockPagination = { limit: 10, skip: 0 };
      const mockPipeline = [{ $match: { name: 'Test' } }];
      const mockDocuments = [
        { _id: '1', name: 'Doc1', createdByUser: { _id: 'user1', name: 'User1' } },
        { _id: '2', name: 'Doc2', createdByUser: { _id: 'user2', name: 'User2' } },
      ];
  
      buildDocumentQuery.mockReturnValue(mockPipeline);
      Document.aggregate.mockImplementation((pipeline) => {
        if (pipeline.some(stage => '$count' in stage)) {
          return Promise.resolve([{ total: 2 }]);
        }
        return Promise.resolve(mockDocuments);
      });
  
      createResponse.mockReturnValue({ status: 'SUCCESS', message: 'Documents fetched successfully' });
  
      const result = await documentResolvers.Query.getDocuments(null, { filter: mockFilter, pagination: mockPagination });
  
      expect(buildDocumentQuery).toHaveBeenCalledWith(mockFilter);
      expect(Document.aggregate).toHaveBeenCalledTimes(2);
      expect(createResponse).toHaveBeenCalledWith(
        'DocumentsResponse',
        'SUCCESS',
        'Documents fetched successfully',
        expect.objectContaining({
          result: {
            documents: expect.arrayContaining([
              expect.objectContaining({ id: '1', name: 'Doc1', createdBy: expect.objectContaining({ id: 'user1', name: 'User1' }) }),
              expect.objectContaining({ id: '2', name: 'Doc2', createdBy: expect.objectContaining({ id: 'user2', name: 'User2' }) }),
            ])
          },
          pagination: expect.objectContaining({
            totalItems: 2,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10,
            hasNextPage: false,
            hasPreviousPage: false
          })
        })
      );
      expect(result).toEqual({ status: 'SUCCESS', message: 'Documents fetched successfully' });
    });
  
    it('should return failure response when no documents are found', async () => {
      const mockFilter = { name: 'NonExistent' };
      const mockPagination = { limit: 10, skip: 0 };
      const mockPipeline = [{ $match: { name: 'NonExistent' } }];
  
      buildDocumentQuery.mockReturnValue(mockPipeline);
      Document.aggregate.mockResolvedValueOnce([{ total: 0 }]).mockResolvedValueOnce([]);
  
      createResponse.mockReturnValue({ status: 'FAILURE', message: 'No documents found' });
  
      const result = await documentResolvers.Query.getDocuments(null, { filter: mockFilter, pagination: mockPagination });
  
      expect(createResponse).toHaveBeenCalledWith(
        'DocumentsResponse',
        'FAILURE',
        'No documents found',
        expect.objectContaining({
          result: { documents: [] },
          pagination: expect.objectContaining({ totalItems: 0 })
        })
      );
      expect(result).toEqual({ status: 'FAILURE', message: 'No documents found' });
    });
  
    it('should handle errors and return error response', async () => {
      const mockFilter = { name: 'Test' };
      const mockPagination = { limit: 10, skip: 0 };
      const mockError = new Error('Database error');
  
      buildDocumentQuery.mockImplementation(() => {
        throw mockError;
      });
  
      createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error fetching documents' });
  
      const result = await documentResolvers.Query.getDocuments(null, { filter: mockFilter, pagination: mockPagination });
  
      expect(createResponse).toHaveBeenCalledWith(
        'DocumentErrorResponse',
        'FAILURE',
        'Error fetching documents',
        { errors: [{ field: 'getDocuments', message: 'Database error' }] }
      );
      expect(result).toEqual({ status: 'FAILURE', message: 'Error fetching documents' });
    });
  });

  describe('Mutation.createDocument', () => {
    it('should create a new document successfully', async () => {
        const mockInput = { title: 'New Document' };
        const documentMock = {
            _id: '123',
            ...mockInput,
            save: jest.fn().mockResolvedValue(true)
        };
        Document.mockImplementation(() => documentMock);
        const createResponseMock = {
            status: 'SUCCESS',
            message: 'Document created successfully',
            result: { document: documentMock }
        };
        createResponse.mockReturnValue(createResponseMock);

        const result = await documentResolvers.Mutation.createDocument(null, { input: mockInput });

        expect(Document).toHaveBeenCalledWith(mockInput);
        expect(documentMock.save).toHaveBeenCalled();
        expect(createResponse).toHaveBeenCalledWith('DocumentResponse', 'SUCCESS', 'Document created successfully', { result: { document: documentMock } });
        expect(result).toBe(createResponseMock);
      });

    it('should return an error response if document creation fails', async () => {
      const mockInput = { title: 'New Document' };
      Document.mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Creation failed'))
      }));
      createResponse.mockReturnValue('error response');

      const result = await documentResolvers.Mutation.createDocument(null, { input: mockInput });

      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Error creating document', { errors: [{ field: 'createDocument', message: 'Creation failed' }] });
      expect(result).toBe('error response');
    });
  });

  describe('Mutation.updateDocument', () => {
    it('should update a document successfully', async () => {
      const mockInput = { title: 'Updated Document' };
      const mockUpdatedDocument = { id: 'docId', ...mockInput };
      Document.findByIdAndUpdate = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockUpdatedDocument)
      });
      setCache.mockResolvedValue();
      createResponse.mockReturnValue('success response');

      const result = await documentResolvers.Mutation.updateDocument(null, { id: 'docId', input: mockInput });

      expect(Document.findByIdAndUpdate).toHaveBeenCalledWith('docId', mockInput, { new: true });
      expect(setCache).toHaveBeenCalledWith('docId', mockUpdatedDocument);
      expect(createResponse).toHaveBeenCalledWith('DocumentResponse', 'SUCCESS', 'Document updated successfully', { result: { document: mockUpdatedDocument } });
      expect(result).toBe('success response');
    });

    it('should return an error response if document is not found', async () => {
      Document.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockResolvedValue(null)
      });
      createResponse.mockReturnValue('error response');

      const result = await documentResolvers.Mutation.updateDocument(null, { id: 'docId', input: {} });

      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Document not found', { errors: [{ field: 'id', message: 'Document not found' }] });
      expect(result).toBe('error response');
    });
  });

  describe('Mutation.deleteDocument', () => {
    it('should delete a document successfully if there are no references', async () => {
      const mockDeletedDocument = { id: 'docId', title: 'Deleted Document' };
      Document.findByIdAndDelete = jest.fn().mockResolvedValue(mockDeletedDocument);
      clearCacheById.mockResolvedValue();
      createResponse.mockReturnValue('success response');
      findReferences.mockResolvedValue([]);

      const result = await documentResolvers.Mutation.deleteDocument(null, { id: 'docId' });

      expect(findReferences).toHaveBeenCalledWith('docId');
      expect(Document.findByIdAndDelete).toHaveBeenCalledWith('docId');
      expect(clearCacheById).toHaveBeenCalledWith('docId');
      expect(createResponse).toHaveBeenCalledWith('DocumentResponse', 'SUCCESS', 'Document deleted successfully', { result: { document: mockDeletedDocument } });
      expect(result).toBe('success response');
    });

    it('should return an error response if document is not found', async () => {
      Document.findByIdAndDelete.mockResolvedValue(null);
      createResponse.mockReturnValue('error response');
      findReferences.mockResolvedValue([]);

      const result = await documentResolvers.Mutation.deleteDocument(null, { id: 'docId' });

      expect(findReferences).toHaveBeenCalledWith('docId');
      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Document not found', { errors: [{ field: 'id', message: 'Document not found' }] });
      expect(result).toBe('error response');
    });

    it('should return an error response if the document has references', async () => {
      const mockReferences = ['Collection1', 'Collection2'];
      findReferences.mockResolvedValue(mockReferences);
      createResponse.mockReturnValue('error response');

      const result = await documentResolvers.Mutation.deleteDocument(null, { id: 'docId' });

      expect(findReferences).toHaveBeenCalledWith('docId');
      expect(Document.findByIdAndDelete).not.toHaveBeenCalled();
      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Document cannot be deleted', {
        errors: [{ field: 'deleteDocument', message: `Document cannot be deleted as it has references in the following collections: Collection1, Collection2` }],
      });
      expect(result).toBe('error response');
    });

    it('should return an error response if an exception occurs', async () => {
      const errorMessage = 'Some error';
      Document.findByIdAndDelete.mockRejectedValue(new Error(errorMessage));
      findReferences.mockResolvedValue([]); 
      createResponse.mockReturnValue('error response');

      const result = await documentResolvers.Mutation.deleteDocument(null, { id: 'docId' });

      expect(findReferences).toHaveBeenCalledWith('docId');
      expect(createResponse).toHaveBeenCalledWith('DocumentErrorResponse', 'FAILURE', 'Error deleting document', {
        errors: [{ field: 'deleteDocument', message: errorMessage }],
      });
      expect(result).toBe('error response');
    });
  });
});
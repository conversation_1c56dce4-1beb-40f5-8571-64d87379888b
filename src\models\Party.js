const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const partySchema = new Schema({
    name: { type: String, required: true },
    partyType: { type: Schema.Types.ObjectId, ref: 'MdPartyType', required: false },
    time: { type: Date, required: true },
    serviceLocation: { type: Schema.Types.ObjectId, ref: 'MdServiceLocation', required: false },
    expectedGuestCount: { type: Number, required: true, default: 0 },
    totalBudget: { type: Number, required: true, default: 0 },
    coHosts: [{ type: Schema.Types.ObjectId, ref: 'Host' }],
    services: [{ type: Schema.Types.ObjectId, ref: 'PartyService' }],
    vendorTypes: [{ type: Schema.Types.ObjectId, ref: 'MdVendorType' }],
    eventId: { type: Schema.Types.ObjectId, ref: 'Event' },
    venueAddress: { type: Schema.Types.ObjectId, ref: 'VenueAddress' },
    guests: [{ type: Schema.Types.ObjectId, ref: 'Guest' }],
    invitation: { type: Schema.Types.ObjectId, ref: 'Invitation' },
    rsvps: [{ type: Schema.Types.ObjectId, ref: 'InvitationRSVP' }],
    invitationSettings: { type: Schema.Types.ObjectId, ref: 'MdInvitationSettings' },
    activity: [{ type: Schema.Types.ObjectId, ref: 'Message' }],
    reminders: [{ type: Schema.Types.ObjectId, ref: 'Reminder' }]
}, { timestamps: true, collection: 'parties' });

const Party = model('Party', partySchema);

module.exports = Party; 
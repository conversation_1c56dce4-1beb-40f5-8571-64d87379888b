const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const VendorUser = require('../../../src/models/VendorUser');
const { User } = require('../../../src/models/User');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('VendorUser Model Test', () => {
    it('should create and save a VendorUser successfully', async () => {
        const mockUser = new User({
            role: ['user'],
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        });
        await mockUser.save();

        const validVendorUser = new VendorUser({
            user: mockUser._id
        });

        const savedVendorUser = await validVendorUser.save();

        expect(savedVendorUser._id).toBeDefined();
        expect(savedVendorUser.user.toString()).toBe(mockUser._id.toString());
        expect(savedVendorUser.createdAt).toBeDefined();
        expect(savedVendorUser.updatedAt).toBeDefined();
    });

    it('should fail to create a VendorUser without required fields', async () => {
        const invalidVendorUser = new VendorUser();

        let err;
        try {
            await invalidVendorUser.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.user).toBeDefined();
    });

    it('should fail to create a VendorUser with an invalid user reference', async () => {
        const invalidVendorUser = new VendorUser({
            user: 'invalid_id'
        });

        let err;
        try {
            await invalidVendorUser.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.user).toBeDefined();
    });
});
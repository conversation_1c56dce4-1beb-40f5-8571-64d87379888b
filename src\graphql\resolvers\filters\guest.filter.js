const { isValidObjectId } = require("../../../utils/validation.util");
const buildUserQuery = require("./user.filter");
const buildPartyQuery = require("./party.filter");
const User = require("../../../models/User");
const Party = require("../../../models/Party");

const buildGuestQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.userId) {
            if (isValidObjectId(filter.userId)) {
                query.user = filter.userId;
            } else {
                throw new Error('Invalid user ID provided');
            }
        }
        
        if (filter.user) {
            if (typeof filter.user === 'string') {
                if (isValidObjectId(filter.user)) {
                    query.user = filter.user;
                } else {
                    throw new Error('Invalid user ID provided');
                }
            } else if (typeof filter.user === 'object') {
                const userQuery = await buildUserQuery(filter.user);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.user = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.user = { $in: [] };
                    }
                }
            }
        }

        if (filter.partyId) {
            if (isValidObjectId(filter.partyId)) {
                query.party = filter.partyId;
            } else {
                throw new Error('Invalid party ID provided');
            }
        }

        if (filter.party) {
            if (typeof filter.party === 'string') {
                if (isValidObjectId(filter.party)) {
                    query.party = filter.party;
                } else {
                    throw new Error('Invalid party ID provided');
                }
            } else if (typeof filter.party === 'object') {
                const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filter.party);
                if (Object.keys(partyQuery).length > 0) {
                    const matchingParties = await Party.find(partyQuery);
                    const filteredParties = await applyPostQueryFilters(matchingParties);
                    
                    if (filteredParties.length > 0) {
                        query.party = { $in: filteredParties.map(party => party._id) };
                    } else {
                        query.party = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildGuestQuery; 
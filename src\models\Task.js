const { model, Schema } = require('mongoose');

const taskSchema = new Schema({
    title: { type: String, required: true },
    description: { type: String, required: false },
    status: { 
        type: String, 
        enum: ['IN_PROGRESS', 'COMPLETED', 'CLOSED'], 
        default: 'IN_PROGRESS', 
        required: true 
    },
    dueDate: { type: Date, required: false },
    comments: [{ type: Schema.Types.ObjectId, ref: 'Comment' }],
    attachments: [{ type: Schema.Types.ObjectId, ref: 'Document' }],
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: true },
    collaborators: [{ type: Schema.Types.ObjectId, ref: 'TaskCollaborator' }],
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    assignedTo: [{ type: Schema.Types.ObjectId, ref: 'User' }]
}, { timestamps: true, collection: 'tasks' });

const Task = model('Task', taskSchema);

module.exports = Task;
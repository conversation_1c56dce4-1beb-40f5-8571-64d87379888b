const Address = require('../../models/Address');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildAddressQuery = require('./filters/address.filter');
const { findReferences } = require('../../utils/referenceCheck.util');

const addressResolvers = {
    Query: {
        getAddressById: async (_, { id }) => {
            try {
                let address = await getByIdCache(id);
                if (!address) {
                    address = await Address.findById(id);
                    if (!address) {
                        return createResponse('AddressErrorResponse', 'FAILURE', 'Address not found', {
                            errors: [{ field: 'id', message: 'Address not found' }]
                        });
                    }
                    await setCache(id, address);
                }
                return createResponse('AddressResponse', 'SUCCESS', 'Address retrieved successfully', {
                    result: { address }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressErrorResponse', 'FAILURE', 'Error retrieving address', {
                    errors: [{ field: 'getAddressById', message: error.message }]
                });
            }
        },

        getAddresses: async (_, { filter, pagination }) => {
            try {
                const query = buildAddressQuery(filter);
                
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Address, query, limit, skip);

                const addresses = await Address.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (addresses.length === 0) {
                    return createResponse('AddressesResponse', 'FAILURE', 'No addresses found', {
                        result: { addresses },
                        pagination: paginationInfo
                    });
                }

                return createResponse('AddressesResponse', 'SUCCESS', 'Addresses fetched successfully', {
                    result: { addresses },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressErrorResponse', 'FAILURE', 'Error retrieving addresses', {
                    errors: [{ field: 'getAddresses', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createAddress: async (_, { input }) => {
            try {
                const address = new Address(input);
                await address.save();
                
                return createResponse('AddressResponse', 'SUCCESS', 'Address created successfully', {
                    result: { address }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressErrorResponse', 'FAILURE', 'Error creating address', {
                    errors: [{ field: 'createAddress', message: error.message }]
                });
            }
        },

        updateAddress: async (_, { id, input }) => {
            try {
                const address = await Address.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!address) {
                    return createResponse('AddressErrorResponse', 'FAILURE', 'Address not found', {
                        errors: [{ field: 'id', message: 'Address not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('AddressResponse', 'SUCCESS', 'Address updated successfully', {
                    result: { address }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressErrorResponse', 'FAILURE', 'Error updating address', {
                    errors: [{ field: 'updateAddress', message: error.message }]
                });
            }
        },

        deleteAddress: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Address');
                if (references.length > 0) {
                    return createResponse('AddressErrorResponse', 'FAILURE', 'Address cannot be deleted', {
                        errors: [{ field: 'deleteAddress', message: `Address cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const address = await Address.findByIdAndDelete(id);

                if (!address) {
                    return createResponse('AddressErrorResponse', 'FAILURE', 'Address not found', {
                        errors: [{ field: 'id', message: 'Address not found' }]
                    });
                }

                await clearCacheById(id);

                return createResponse('AddressResponse', 'SUCCESS', 'Address deleted successfully', {
                    result: { address }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressErrorResponse', 'FAILURE', 'Error deleting address', {
                    errors: [{ field: 'deleteAddress', message: error.message }]
                });
            }
        }
    }
};

module.exports = addressResolvers;
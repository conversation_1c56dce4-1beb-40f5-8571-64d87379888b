const mongoose = require('mongoose');
const MdRole = require('../../../models/MdRole');
const { isValidObjectId } = require('../../../utils/validation.util');
const { buildRoleQuery } = require('./mdRole.filter');

const buildUserQuery = async (filters) => {
    if (!filters) return {};

    const query = {};

    if (filters.firstName) {
        query.firstName = { $regex: filters.firstName, $options: 'i' };
    }

    if (filters.lastName) {
        query.lastName = { $regex: filters.lastName, $options: 'i' };
    }

    if (filters.email) {
        query.email = { $regex: filters.email, $options: 'i' };
    }

    if (filters.phone) {
        query.phone = { $regex: new RegExp(filters.phone.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')), $options: 'i' };
    }

    if (filters.isRegistered !== undefined) {
        query.isRegistered = filters.isRegistered;
    }

    if (filters.emailVerified !== undefined) {
        query.emailVerified = filters.emailVerified;
    }

    if (filters.phoneVerified !== undefined) {
        query.phoneVerified = filters.phoneVerified;
    }

    if (filters.role) {
        query.role = { $regex: filters.role, $options: 'i' };
    }

    return query;
}

module.exports = buildUserQuery;
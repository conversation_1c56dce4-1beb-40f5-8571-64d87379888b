const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
    type: {
        type: String,
        enum: ['SYSTEM', 'USER'],
        required: true,
    },
    parentMessageId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Message',
        required: false,
    },
    media:{
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Media',
        required: false,
    },
    text: {
        type: String,
        required: false,
    },
    reactions: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'Reaction',
        required: false,
    },
    receivers: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: 'User',
        required: false,
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },  
}, { timestamps: true });

const Message = mongoose.model('Message', messageSchema);

module.exports = Message;
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdReaction = require('../../../src/models/MdReaction');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdReaction Model Test', () => {
    it('should create and save a MdReaction successfully', async () => {
        const mockIcon = new MdIcon({
            name: 'Test Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const validMdReaction = new MdReaction({
            name: 'Test Reaction',
            icon: mockIcon._id,
            active: true
        });

        const savedMdReaction = await validMdReaction.save();

        expect(savedMdReaction._id).toBeDefined();
        expect(savedMdReaction.name).toBe('Test Reaction');
        expect(savedMdReaction.icon).toEqual(mockIcon._id);
        expect(savedMdReaction.active).toBe(true);
        expect(savedMdReaction.createdAt).toBeDefined();
        expect(savedMdReaction.updatedAt).toBeDefined();
    });

    it('should fail to create a MdReaction without required fields', async () => {
        const mdReaction = new MdReaction();

        let err;
        try {
            await mdReaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.icon).toBeDefined();
        expect(err.errors.active).toBeDefined();
    });

    it('should fail to create a MdReaction with invalid icon reference', async () => {
        const invalidMdReaction = new MdReaction({
            name: 'Invalid Reaction',
            icon: 'invalid_id',
            active: true
        });

        let err;
        try {
            await invalidMdReaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.icon).toBeDefined();
    });
}); 
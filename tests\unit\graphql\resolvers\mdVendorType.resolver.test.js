const MdVendorType = require('../../../../src/models/MdVendorType');
const MdFeature = require('../../../../src/models/MdFeature');
const MdIcon = require('../../../../src/models/MdIcon');
const MdPartner = require('../../../../src/models/MdPartner');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildMdVendorTypeQuery = require('../../../../src/graphql/resolvers/filters/mdVendorType.filter');
const mdVendorTypeResolvers = require('../../../../src/graphql/resolvers/mdVendorType.resolver');
const findReferences = require('../../../../src/graphql/resolvers/references/mdVendorType.references');

jest.mock('../../../../src/models/MdVendorType');
jest.mock('../../../../src/models/MdFeature');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/models/MdPartner');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdVendorType.filter');
jest.mock('../../../../src/graphql/resolvers/references/mdVendorType.references');

describe('MdVendorType Resolvers', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('MdVendorType field resolvers', () => {
        it('should resolve primaryFeature', async () => {
            const parent = { primaryFeature: 'featureId' };
            const mockFeature = { _id: 'featureId', name: 'Feature' };
            MdFeature.findById.mockResolvedValue(mockFeature);

            const result = await mdVendorTypeResolvers.MdVendorType.primaryFeature(parent);

            expect(MdFeature.findById).toHaveBeenCalledWith('featureId');
            expect(result).toEqual(mockFeature);
        });

        it('should resolve features', async () => {
            const parent = { features: ['feature1', 'feature2'] };
            const mockFeatures = [{ _id: 'feature1' }, { _id: 'feature2' }];
            MdFeature.find.mockResolvedValue(mockFeatures);

            const result = await mdVendorTypeResolvers.MdVendorType.features(parent);

            expect(MdFeature.find).toHaveBeenCalledWith({ _id: { $in: parent.features } });
            expect(result).toEqual(mockFeatures);
        });

        it('should resolve mainFeatures', async () => {
            const parent = { mainFeatures: ['feature1', 'feature2'] };
            const mockFeatures = [{ _id: 'feature1' }, { _id: 'feature2' }];
            MdFeature.find.mockResolvedValue(mockFeatures);

            const result = await mdVendorTypeResolvers.MdVendorType.mainFeatures(parent);

            expect(MdFeature.find).toHaveBeenCalledWith({ _id: { $in: parent.mainFeatures } });
            expect(result).toEqual(mockFeatures);
        });

        it('should resolve icon', async () => {
            const parent = { icon: 'iconId' };
            const mockIcon = { _id: 'iconId', name: 'Icon' };
            MdIcon.findById.mockResolvedValue(mockIcon);

            const result = await mdVendorTypeResolvers.MdVendorType.icon(parent);

            expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
            expect(result).toEqual(mockIcon);
        });

        it('should resolve partners', async () => {
            const parent = { partners: ['partner1', 'partner2'] };
            const mockPartners = [{ _id: 'partner1' }, { _id: 'partner2' }];
            MdPartner.find.mockResolvedValue(mockPartners);

            const result = await mdVendorTypeResolvers.MdVendorType.partners(parent);

            expect(MdPartner.find).toHaveBeenCalledWith({ _id: { $in: parent.partners } });
            expect(result).toEqual(mockPartners);
        });
    });

    describe('Query resolvers', () => {
        describe('getMdVendorTypeById', () => {
            it('should return MdVendorType if found in cache', async () => {
                const id = 'vendorTypeId';
                const cachedVendorType = { _id: id, name: 'Cached VendorType' };
                getByIdCache.mockResolvedValue(cachedVendorType);
                createResponse.mockReturnValue('success response');

                const result = await mdVendorTypeResolvers.Query.getMdVendorTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, cachedVendorType);
                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType fetched successfully', { result: { mdVendorType: cachedVendorType } });
                expect(result).toBe('success response');
            });

            it('should handle errors when fetching from cache', async () => {
                const id = 'vendorTypeId';
                const error = new Error('Cache error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');
            
                const result = await mdVendorTypeResolvers.Query.getMdVendorTypeById(null, { id });
            
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeErrorResponse',
                    'FAILURE',
                    'Error getting MdVendorType',
                    { errors: [{ field: 'getMdVendorTypeById', message: error.message }] }
                );
                expect(result).toBe('error response');
            });

            it('should return MdVendorType if found in database', async () => {
                const id = 'vendorTypeId';
                const dbVendorType = { _id: id, name: 'DB VendorType' };
                getByIdCache.mockResolvedValue(null);
                MdVendorType.findById.mockResolvedValue(dbVendorType);
                createResponse.mockReturnValue('success response');

                const result = await mdVendorTypeResolvers.Query.getMdVendorTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdVendorType.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, dbVendorType);
                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType fetched successfully', { result: { mdVendorType: dbVendorType } });
                expect(result).toBe('success response');
            });

            it('should return error response if MdVendorType not found', async () => {
                const id = 'nonExistentId';
                getByIdCache.mockResolvedValue(null);
                MdVendorType.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Query.getMdVendorTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdVendorType.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType not found', { errors: [{ field: 'id', message: 'MdVendorType not found' }] });
                expect(result).toBe('error response');
            });
        });

        describe('getMdVendorTypes', () => {
            it('should return MdVendorTypes if found', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'Test', $options: 'i' } };
                const paginationInfo = { totalCount: 1, totalPages: 1 };
                const mdVendorTypes = [{ _id: '1', name: 'Test VendorType' }];

                buildMdVendorTypeQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdVendorType.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mdVendorTypes),
                });
                createResponse.mockReturnValue('success response');

                const result = await mdVendorTypeResolvers.Query.getMdVendorTypes(null, { filter, pagination });

                expect(buildMdVendorTypeQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdVendorType, query, 10, 0);
                expect(MdVendorType.find).toHaveBeenCalledWith(query);
                expect(createResponse).toHaveBeenCalledWith('MdVendorTypesResponse', 'SUCCESS', 'MdVendorTypes fetched successfully', { result: { mdVendorTypes }, pagination: paginationInfo });
                expect(result).toBe('success response');
            });

            it('should handle errors when fetching MdVendorTypes', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
        
                buildMdVendorTypeQuery.mockReturnValue({});
                getPaginationInfo.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');
        
                const result = await mdVendorTypeResolvers.Query.getMdVendorTypes(null, { filter, pagination });
        
                expect(buildMdVendorTypeQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdVendorType, {}, 10, 0);
                expect(createResponse).toHaveBeenCalledWith(
                  'MdVendorTypeErrorResponse',
                  'FAILURE',
                  'Error getting MdVendorTypes',
                  { errors: [{ field: 'getMdVendorTypes', message: error.message }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if no MdVendorTypes found', async () => {
                const filter = { name: 'NonExistent' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'NonExistent', $options: 'i' } };
                const paginationInfo = { totalCount: 0, totalPages: 0 };

                buildMdVendorTypeQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdVendorType.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([]),
                });
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Query.getMdVendorTypes(null, { filter, pagination });

                expect(createResponse).toHaveBeenCalledWith('MdVendorTypesResponse', 'FAILURE', 'No MdVendorTypes found', { result: { mdVendorTypes: [] }, pagination: paginationInfo });
                expect(result).toBe('error response');
            });
        });
    });

    describe('Mutation resolvers', () => {
        describe('createMdVendorType', () => {
            it('should create and return the MdVendorType', async () => {
                const input = {
                    name: 'Test Vendor Type',
                    description: 'This is a test vendor type.'
                };
                
                const mdVendorTypeMock = {
                    _id: '123',
                    name: 'Test Vendor Type',
                    description: 'This is a test vendor type.',
                    save: jest.fn().mockResolvedValue(true)
                };
        
                MdVendorType.mockImplementation(() => mdVendorTypeMock);
                const createResponseMock = {
                    status: 'SUCCESS',
                    message: 'MdVendorType created successfully',
                    result: { mdVendorType: mdVendorTypeMock }
                };
                createResponse.mockReturnValue(createResponseMock);
        
                const result = await mdVendorTypeResolvers.Mutation.createMdVendorType(null, { input });
        
                expect(MdVendorType).toHaveBeenCalledWith(input);
                expect(mdVendorTypeMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeResponse',
                    'SUCCESS',
                    'MdVendorType created successfully',
                    { result: { mdVendorType: mdVendorTypeMock } }
                );
                expect(result).toEqual(createResponseMock);
            });

            it('should return error response on exception', async () => {
                const input = { name: 'New VendorType', description: 'Description' };
                const error = new Error('Database error');

                MdVendorType.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Mutation.createMdVendorType(null, { input });

                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeErrorResponse', 'FAILURE', 'Error creating MdVendorType', { errors: [{ field: 'createMdVendorType', message: error.message }] });
                expect(result).toBe('error response');
            });
        });

        describe('updateMdVendorType', () => {
            it('should update and return the MdVendorType', async () => {
                const id = 'vendorTypeId';
                const input = { name: 'Updated VendorType' };
                const updatedVendorType = { _id: id, ...input };

                MdVendorType.findByIdAndUpdate.mockResolvedValue(updatedVendorType);
                createResponse.mockReturnValue('success response');

                const result = await mdVendorTypeResolvers.Mutation.updateMdVendorType(null, { id, input });

                expect(MdVendorType.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeResponse', 'SUCCESS', 'MdVendorType updated successfully', { result: { mdVendorType: updatedVendorType } });
                expect(result).toBe('success response');
            });

            it('should handle errors when updating MdVendorType', async () => {
                const id = 'vendorTypeId';
                const input = { name: 'Updated VendorType' };
                const error = new Error('Database error');
        
                MdVendorType.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');
        
                const result = await mdVendorTypeResolvers.Mutation.updateMdVendorType(null, { id, input });
        
                expect(MdVendorType.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith(
                  'MdVendorTypeErrorResponse',
                  'FAILURE',
                  'Error updating MdVendorType',
                  { errors: [{ field: 'updateMdVendorType', message: error.message }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if MdVendorType not found', async () => {
                const id = 'nonExistentId';
                const input = { name: 'Updated VendorType' };

                MdVendorType.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Mutation.updateMdVendorType(null, { id, input });

                expect(createResponse).toHaveBeenCalledWith('MdVendorTypeErrorResponse', 'FAILURE', 'MdVendorType not found', { errors: [{ field: 'updateMdVendorType', message: 'MdVendorType not found' }] });
                expect(result).toBe('error response');
            });
        });

        describe('deleteMdVendorType', () => {
            it('should check for references before deleting', async () => {
                const id = 'vendorTypeId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Mutation.deleteMdVendorType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(MdVendorType.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeErrorResponse',
                    'FAILURE',
                    'MdVendorType cannot be deleted',
                    {
                        errors: [{ 
                            field: 'deleteMdVendorType', 
                            message: `MdVendorType cannot be deleted as it has references in the following collections: ${references.join(', ')}` 
                        }]
                    }
                );
                expect(result).toBe('error response');
            });

            it('should delete and return the MdVendorType when no references exist', async () => {
                const id = 'vendorTypeId';
                const deletedVendorType = { _id: id, name: 'Deleted VendorType' };

                findReferences.mockResolvedValue([]);
                MdVendorType.findByIdAndDelete.mockResolvedValue(deletedVendorType);
                createResponse.mockReturnValue('success response');

                const result = await mdVendorTypeResolvers.Mutation.deleteMdVendorType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(MdVendorType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeResponse',
                    'SUCCESS',
                    'MdVendorType deleted successfully',
                    { result: { mdVendorType: deletedVendorType } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response if MdVendorType not found', async () => {
                const id = 'nonExistentId';

                findReferences.mockResolvedValue([]);
                MdVendorType.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Mutation.deleteMdVendorType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeErrorResponse',
                    'FAILURE',
                    'MdVendorType not found',
                    { errors: [{ field: 'deleteMdVendorType', message: 'MdVendorType not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should handle errors when deleting MdVendorType', async () => {
                const id = 'vendorTypeId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdVendorType.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorTypeResolvers.Mutation.deleteMdVendorType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(MdVendorType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorTypeErrorResponse',
                    'FAILURE',
                    'Error deleting MdVendorType',
                    { errors: [{ field: 'deleteMdVendorType', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });
    });
});

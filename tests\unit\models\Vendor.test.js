const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Vendor = require('../../../src/models/Vendor');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
}, 10000);

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
}, 10000);

describe('Vendor Schema', () => {
    it('should create a vendor document with valid fields', async () => {
        const validVendor = new Vendor({
            name: 'Test Vendor',
            primaryContact: new mongoose.Types.ObjectId(),
            contacts: [new mongoose.Types.ObjectId()],
            businessAddress: new mongoose.Types.ObjectId(),
            serviceLocations: [new mongoose.Types.ObjectId()],
            servicesProvided: [new mongoose.Types.ObjectId()]
        });

        const savedVendor = await validVendor.save();
        expect(savedVendor._id).toBeDefined();
        expect(savedVendor.name).toBe('Test Vendor');
        expect(savedVendor.primaryContact).toBeDefined();
    });

    it('should fail to create a vendor document without required fields', async () => {
        const invalidVendor = new Vendor({});

        let err;
        try {
            await invalidVendor.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.primaryContact).toBeDefined();
        expect(err.errors.businessAddress).toBeDefined();
    });

    it('should enforce correct type for each field', async () => {
        const invalidVendor = new Vendor({
            name: 12345,
            primaryContact: 'not_an_object_id', 
            businessAddress: 'not_an_object_id', 
        });

        let err;
        try {
            await invalidVendor.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.primaryContact).toBeDefined();
        expect(err.errors.businessAddress).toBeDefined();
    });
});

const { gql } = require('graphql-tag');
const mdIconTypeDef = require('../../../../src/graphql/typedefs/mdIcon.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mdIconTypeDef', () => {
  it('should contain the MdIcon type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type MdIcon {
        id: ID!
        name: String!
        iconSrc: String!
        createdAt: Date!
        updatedAt: Date!
      }

      input MdIconFilterInput {
        id: String!
        name: String
        iconSrc: String
      }

      type MdIconWrapper {
        mdIcon: MdIcon!
      }

      type MdIconsWrapper {
        mdIcons: [MdIcon]!
      }

      type MdIconsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdIconsWrapper!
        pagination: PaginationInfo!
      }

      type MdIconResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdIconWrapper!
      }

      type MdIconErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union MdIconResult = MdIconResponse | MdIconErrorResponse
      union MdIconsResult = MdIconsResponse | MdIconErrorResponse

      type Query {
        getMdIconById(id: ID!): MdIconResult!
        getMdIcons(filter: MdIconFilterInput, pagination: PaginationInput): MdIconsResult!
      }

      type Mutation {
        createMdIcon(name: String!, iconSrc: String!): MdIconResult!
        updateMdIcon(id: ID!, name: String, iconSrc: String): MdIconResult!
        deleteMdIcon(id: ID!): MdIconResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(mdIconTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
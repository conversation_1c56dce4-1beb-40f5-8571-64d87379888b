const findReferences = require('../../../../../src/graphql/resolvers/references/mdFeature.references');
const VendorService = require('../../../../../src/models/VendorService');

jest.mock('../../../../../src/models/VendorService');

describe('mdFeature References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        VendorService.find.mockResolvedValue([]);

        const result = await findReferences('featureId');

        expect(result).toEqual([]);
        expect(VendorService.find).toHaveBeenCalledWith({ 'features': 'featureId' });
    });

    it('should return VendorService when references exist', async () => {
        VendorService.find.mockResolvedValue([
            { _id: 'vendorService1' },
            { _id: 'vendorService2' }
        ]);

        const result = await findReferences('featureId');

        expect(result).toEqual(['VendorService']);
        expect(VendorService.find).toHaveBeenCalledWith({ 'features': 'featureId' });
    });

    it('should handle invalid input gracefully', async () => {
        VendorService.find.mockResolvedValue(null);

        const result = await findReferences(null);

        expect(result).toEqual([]);
        expect(VendorService.find).toHaveBeenCalledWith({ 'features': null });
    });

    it('should handle database errors', async () => {
        const error = new Error('Database error');
        VendorService.find.mockRejectedValue(error);

        await expect(findReferences('featureId')).rejects.toThrow('Database error');
        expect(VendorService.find).toHaveBeenCalledWith({ 'features': 'featureId' });
    });
});
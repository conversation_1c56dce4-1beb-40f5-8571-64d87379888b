const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mdServiceLocationSchema = new Schema({
    name: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    areas: [{ type: String }]
}, { timestamps: true, collection: 'md_service_locations' });

const MdServiceLocation = model('MdServiceLocation', mdServiceLocationSchema);

module.exports = MdServiceLocation;
const buildVendorQuery = require('../../../../../src/graphql/resolvers/filters/vendor.filter');

describe('buildVendorQuery', () => {
    it('should return an empty pipeline when no filters are provided', () => {
        const result = buildVendorQuery();
        expect(result).toEqual([]);
    });

    it('should build a pipeline with name filter', () => {
        const filters = { name: 'test vendor' };
        const result = buildVendorQuery(filters);
        
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
            $match: { name: { $regex: /test vendor/i } }
        });
    });

    it('should build a pipeline with minRating filter', () => {
        const filters = { minRating: 4 };
        const result = buildVendorQuery(filters);
        
        expect(result).toHaveLength(4);
        expect(result[0]).toEqual({
            $lookup: {
                from: 'vendor_ratings',
                localField: '_id',
                foreignField: 'vendor',
                as: 'ratings'
            }
        });
        expect(result[2]).toEqual({
            $match: {
                'ratingStats.averageRating': { $gte: 4 }
            }
        });
    });

    it('should build a pipeline with minTotalReviews filter', () => {
        const filters = { minTotalReviews: 10 };
        const result = buildVendorQuery(filters);
        
        expect(result).toHaveLength(4);
        expect(result[2]).toEqual({
            $match: {
                'ratingStats.totalRating': { $gte: 10 }
            }
        });
    });

    it('should build a pipeline with both rating filters', () => {
        const filters = { 
            minRating: 4,
            minTotalReviews: 10
        };
        const result = buildVendorQuery(filters);
        
        expect(result).toHaveLength(4);
        expect(result[2]).toEqual({
            $match: {
                'ratingStats.averageRating': { $gte: 4 },
                'ratingStats.totalRating': { $gte: 10 }
            }
        });
    });

    it('should build a pipeline with name and rating filters', () => {
        const filters = {
            name: 'test vendor',
            minRating: 4,
            minTotalReviews: 10
        };
        const result = buildVendorQuery(filters);
        
        expect(result).toHaveLength(5);
        expect(result[0]).toEqual({
            $match: { name: { $regex: /test vendor/i } }
        });
        expect(result[3]).toEqual({
            $match: {
                'ratingStats.averageRating': { $gte: 4 },
                'ratingStats.totalRating': { $gte: 10 }
            }
        });
    });

    it('should include rating aggregation stages when rating filters are present', () => {
        const filters = { minRating: 4 };
        const result = buildVendorQuery(filters);
        
        expect(result[1]).toEqual({
            $addFields: {
                ratingStats: {
                    totalRating: { $size: '$ratings' },
                    averageRating: {
                        $cond: {
                            if: { $eq: [{ $size: '$ratings' }, 0] },
                            then: 0,
                            else: {
                                $round: [{ $avg: '$ratings.rating' }, 1]
                            }
                        }
                    }
                }
            }
        });
        
        expect(result[3]).toEqual({
            $addFields: {
                ratingAggregates: {
                    averageRating: '$ratingStats.averageRating',
                    totalRating: '$ratingStats.totalRating'
                }
            }
        });
    });
});

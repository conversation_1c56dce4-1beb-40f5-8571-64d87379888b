const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type InAppNotification {
        id: ID!
        user: User!
        sender: User!
        message: String!
        media: String
        link: String
        read: Boolean!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input InAppNotificationFilterInput {
        id: ID
        userId: ID
        senderId: ID
        read: Boolean
        message: String
        createdAt: DateTimeRangeInput
    }

    type InAppNotificationWrapper {
        inAppNotification: InAppNotification!
    }

    type InAppNotificationsWrapper {
        inAppNotifications: [InAppNotification]!
    }

    type CreateInAppNotificationsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: InAppNotificationsWrapper!
    }

    type InAppNotificationResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: InAppNotificationWrapper!
    }

    type InAppNotificationsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: InAppNotificationsWrapper!
        pagination: PaginationInfo!
    }

    type InAppNotificationErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union InAppNotificationResult = InAppNotificationResponse | InAppNotificationErrorResponse
    union InAppNotificationsResult = InAppNotificationsResponse | InAppNotificationErrorResponse
    union CreateInAppNotificationsResult = CreateInAppNotificationsResponse | InAppNotificationErrorResponse

    type Query {
        getInAppNotificationById(id: ID!): InAppNotificationResult!
        getInAppNotifications(filter: InAppNotificationFilterInput, pagination: PaginationInput): InAppNotificationsResult!
        getInAppNotificationCount: Int!
    }

    input InAppNotificationInput {
        userIds: [ID!]!
        senderId: ID!
        message: String!
        media: String
        link: String
        read: Boolean!
    }

    input InAppNotificationUpdateInput {
        message: String
        media: String
        link: String
        read: Boolean
    }

    type Mutation {
        createInAppNotification(input: InAppNotificationInput!): CreateInAppNotificationsResult!
        updateInAppNotification(id: ID!, input: InAppNotificationUpdateInput!): InAppNotificationResult!
        deleteInAppNotification(id: ID!): InAppNotificationResult!
        markNotificationAsRead(id: ID!): InAppNotificationResult!
        markAllNotificationsAsRead: Boolean!
    }
`;

const { gql } = require('graphql-tag');
const vendorUserTypeDef = require('../../../../src/graphql/typedefs/vendorUser.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('vendorUserTypeDef', () => {
  it('should contain the VendorUser type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type VendorUser {
        id: ID!
        user: User!
      }

      input VendorUserFilterInput {
        vendorFirstName: String
        vendorLastName: String
        vendorEmail: String
      }

      type VendorUserWrapper {
        vendorUser: VendorUser!
      }

      type VendorUsersWrapper {
        vendorUsers: [VendorUser]!
      }

      type VendorUserResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorUserWrapper!
      }

      type VendorUsersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VendorUsersWrapper!
        pagination: PaginationInfo!
      }

      type VendorUserErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union VendorUserResult = VendorUserResponse | VendorUserErrorResponse
      union VendorUsersResult = VendorUsersResponse | VendorUserErrorResponse

      type Query {
        getVendorUserById(id: ID!): VendorUserResult!
        getVendorUsers(filter: VendorUserFilterInput, pagination: PaginationInput): VendorUsersResult!
      }

      type Mutation {
        createVendorUser(userId: ID!): VendorUserResult!
        deleteVendorUser(id: ID!): VendorUserResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(vendorUserTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
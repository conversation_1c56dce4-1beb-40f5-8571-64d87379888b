const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    scalar Date

    scalar DateTime

    scalar PhoneNumber

    scalar JSON

    enum ResponseStatus {
        SUCCESS
        FAILURE
    }

    interface Response {
        status: ResponseStatus!
        message: String!
    }
    
    type Error {
        field: String!
        message: String!
    }

    input PaginationInput {
        limit: Int
        skip: Int
    }

    type PaginationInfo {
        totalItems: Int!
        totalPages: Int!
        pageSize: Int!
        currentPage: Int!
    }

    input DateRangeInput {
        start: Date
        end: Date
    }

    input DateTimeRangeInput {
        start: DateTime
        end: DateTime
    }
`;
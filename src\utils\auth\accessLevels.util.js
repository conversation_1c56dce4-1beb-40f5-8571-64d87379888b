const Event = require('../../models/Event');
const Host = require('../../models/Host');
const Party = require('../../models/Party');
const Guest = require('../../models/Guest');
const Task = require('../../models/Task');
const TaskCollaborator = require('../../models/TaskCollaborator');
const Media = require('../../models/Media');
const MediaFolder = require('../../models/MediaFolder');
const EventGroup = require('../../models/EventGroup');

const hasEventLevelAccess = async (userId, eventId) => {
    try {
        const event = await Event.findById(eventId);
        if (!event) {
            return false;
        }

        // Check if user is the main host
        const isMainHost = await Host.exists({ 
            _id: event.mainHost, 
            userId 
        });

        if (isMainHost) {
            return true;
        }

        // Get all parties for this event to check co-hosts
        const parties = await Party.find({ eventId: event._id });
        const allCoHostIds = [
            ...(event.coHosts || []),
            ...parties.reduce((ids, party) => [...ids, ...(party.coHosts || [])], [])
        ];

        // Check if user is a co-host
        if (allCoHostIds.length > 0) {
            const isCoHost = await Host.exists({ 
                _id: { $in: allCoHostIds }, 
                userId 
            });
            
            if (isCoHost) {
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error in hasEventLevelAccess:', error);
        return false;
    }
};

const hasEventPartyLevelAccess = async (userId, eventId, partyId) => {
    try {
        // First check event level access
        const hasEventAccess = await hasEventLevelAccess(userId, eventId);
        if (hasEventAccess) {
            return true;
        }

        // If no event level access, check party level access
        const party = await Party.findById(partyId);
        if (!party) {
            return false;
        }

        // Check if user is a co-host of the specific party
        if (party.coHosts && party.coHosts.length > 0) {
            const isPartyCoHost = await Host.exists({ 
                _id: { $in: party.coHosts }, 
                userId 
            });
            
            if (isPartyCoHost) {
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error in hasEventPartyLevelAccess:', error);
        return false;
    }
};

const hasEventPartyGuestLevelAccess = async (userId, eventId, partyId) => {
    try {
        const hasEventAccess = await hasEventLevelAccess(userId, eventId);
        if (hasEventAccess) {
            return { hasAccess: true, isHost: true };
        }

        const hasEventPartyAccess = await hasEventPartyLevelAccess(userId, eventId, partyId);
        if (hasEventPartyAccess) {
            return { hasAccess: true, isHost: true };
        }

        const parties = await Party.find({ eventId });
        if (parties.length === 0) {
            return { hasAccess: false, isHost: false };
        }

        // Check if user is a guest in any of the event's parties
        const isGuest = await Guest.exists({
            party: { $in: parties.map(p => p._id) },
            user: userId
        });

        return { hasAccess: Boolean(isGuest), isHost: false };
    } catch (error) {
        console.error('Error in hasPartyGuestLevelAccess:', error);
        return { hasAccess: false, isHost: false };
    }
};

const hasGuestLevelAccess = async (userId, eventId) => {
    try {
        // First check if user has host/co-host level access
        const hasEventAccess = await hasEventLevelAccess(userId, eventId);
        if (hasEventAccess) {
            return true;
        }

        // If no host level access, check guest access
        const parties = await Party.find({ eventId });
        if (parties.length === 0) {
            return false;
        }

        // Check if user is a guest in any of the event's parties
        const isGuest = await Guest.exists({
            party: { $in: parties.map(p => p._id) },
            user: userId
        });

        return Boolean(isGuest);
    } catch (error) {
        console.error('Error in hasGuestLevelAccess:', error);
        return false;
    }
};

const hasTaskCollaboratorLevelAccess = async (userId, taskId) => {
    try {
        const task = await Task.findById(taskId);
        if (!task) {
            return false;
        }

        const party = await Party.findById(task.party);
        if (!party) {
            return false;
        }

        // Check event level access first
        const hasEventAccess = await hasEventLevelAccess(userId, party.eventId);
        if (hasEventAccess) {
            return true;
        }

        // If no event level access, check if user is a task collaborator
        const isTaskCollaborator = await TaskCollaborator.exists({
            task: taskId,
            user: userId
        });

        return Boolean(isTaskCollaborator);
    } catch (error) {
        console.error('Error in hasTaskCollaboratorLevelAccess:', error);
        return false;
    }
};

const hasMediaOwnerLevelAccess = async (userId, mediaId) => {
    try {
        const media = await Media.findById(mediaId);
        if (!media) {
            return false;
        }

        // Check if user is the direct owner of the media
        return media.owner.toString() === userId.toString();
    } catch (error) {
        console.error('Error in hasMediaOwnerLevelAccess:', error);
        return false;
    }
};

const hasMediaFolderLevelAccess = async (userId, mediaFolderId) => {
    try {
        const mediaFolder = await MediaFolder.findById(mediaFolderId);
        if (!mediaFolder) {
            return false;
        }

        // Check if user is a folder owner or contributor
        const isFolderOwner = mediaFolder.owner.some(ownerId => 
            ownerId.toString() === userId.toString()
        );
        const isFolderContributor = mediaFolder.contributors?.some(
            contributorId => contributorId.toString() === userId.toString()
        );

        return Boolean(isFolderOwner || isFolderContributor);
    } catch (error) {
        console.error('Error in hasMediaFolderLevelAccess:', error);
        return false;
    }
};

const hasMediaContributorLevelAccess = async (userId, mediaFolderId) => {
    try {
        const mediaFolder = await MediaFolder.findById(mediaFolderId);
        if (!mediaFolder) {
            return false;
        }

        // Check if user is a direct owner or contributor
        const isFolderOwner = mediaFolder.owner.some(ownerId => 
            ownerId.toString() === userId.toString()
        );
        const isFolderContributor = mediaFolder.contributors?.some(
            contributorId => contributorId.toString() === userId.toString()
        );

        if (isFolderOwner || isFolderContributor) {
            return true;
        }

        // If folder is associated with an event, check event-level access
        if (mediaFolder.event) {
            const hasEventAccess = await hasEventLevelAccess(userId, mediaFolder.event);
            if (hasEventAccess) {
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error in hasMediaContributorLevelAccess:', error);
        return false;
    }
};

const hasEventGroupMemberLevelAccess = async (userId, eventGroupId) => {
    try {
        const eventGroup = await EventGroup.findById(eventGroupId);
        if (!eventGroup) {
            return false;
        }

        // Check if user is a member
        const isMember = eventGroup.members.some(memberId => 
            memberId.toString() === userId.toString()
        );

        // Check if user is an organizer
        const isOrganizer = eventGroup.organizers.some(organizerId => 
            organizerId.toString() === userId.toString()
        );

        // Check if user is the creator
        const isCreator = eventGroup.createdBy.toString() === userId.toString();

        return Boolean(isMember || isOrganizer || isCreator);
    } catch (error) {
        console.error('Error in hasEventGroupMemberLevelAccess:', error);
        return false;
    }
};

const hasEventGroupOrganizerLevelAccess = async (userId, eventGroupId) => {
    try {
        const eventGroup = await EventGroup.findById(eventGroupId);
        if (!eventGroup) {
            return false;
        }

        // Check if user is an organizer
        const isOrganizer = eventGroup.organizers.some(organizerId => 
            organizerId.toString() === userId.toString()
        );

        return Boolean(isOrganizer);
    } catch (error) {
        console.error('Error in hasEventGroupOrganizerLevelAccess:', error);
        return false;
    }
};

const hasEventGroupCoOrganizerLevelAccess = async (userId, eventGroupId) => {
    try {
        const eventGroup = await EventGroup.findById(eventGroupId);
        if (!eventGroup) {
            return false;
        }

        // Check if user is the creator and also an organizer
        const isCreator = eventGroup.createdBy.toString() === userId.toString();
        const isOrganizer = eventGroup.organizers.some(organizerId => 
            organizerId.toString() === userId.toString()
        );

        return Boolean(isCreator && isOrganizer);
    } catch (error) {
        console.error('Error in hasEventGroupCoOrganizerLevelAccess:', error);
        return false;
    }
};

module.exports = {
    hasEventLevelAccess,
    hasEventPartyLevelAccess,
    hasGuestLevelAccess,
    hasTaskCollaboratorLevelAccess,
    hasMediaOwnerLevelAccess,
    hasMediaFolderLevelAccess,
    hasMediaContributorLevelAccess,
    hasEventPartyGuestLevelAccess,
    hasEventGroupMemberLevelAccess,
    hasEventGroupOrganizerLevelAccess,
    hasEventGroupCoOrganizerLevelAccess
};

const mongoose = require('mongoose');
const { isValidObjectId, validateIds, validateReferences } = require('../../../src/utils/validation.util');
const { createResponse } = require('../../../src/utils/response.util');

jest.mock('../../../src/utils/response.util', () => ({
    createResponse: jest.fn()
}));

describe('Validation Utility Tests', () => {
    describe('isValidObjectId', () => {
        it('should return true for valid ObjectId string', () => {
            const validId = new mongoose.Types.ObjectId().toString();
            expect(isValidObjectId(validId)).toBe(true);
        });

        it('should return false for invalid ObjectId string', () => {
            expect(isValidObjectId('invalid-id')).toBe(false);
        });

        it('should return false for non-string input', () => {
            expect(isValidObjectId(123)).toBe(false);
            expect(isValidObjectId({})).toBe(false);
            expect(isValidObjectId(null)).toBe(false);
        });
    });

    describe('validateIds', () => {
        const validId = new mongoose.Types.ObjectId().toString();

        it('should validate single ID fields', () => {
            const input = { userId: validId };
            const schema = { userId: { type: 'single', required: true } };
            
            const errors = validateIds(input, schema);
            expect(errors).toHaveLength(0);
        });

        it('should validate array ID fields', () => {
            const input = { roleIds: [validId, validId] };
            const schema = { roleIds: { type: 'array', required: true } };
            
            const errors = validateIds(input, schema);
            expect(errors).toHaveLength(0);
        });

        it('should return errors for invalid single ID', () => {
            const input = { userId: 'invalid-id' };
            const schema = { userId: { type: 'single', required: true } };
            
            const errors = validateIds(input, schema);
            expect(errors).toHaveLength(1);
            expect(errors[0]).toEqual({
                field: 'userId',
                message: 'Invalid userId ID format'
            });
        });

        it('should return errors for invalid array IDs', () => {
            const input = { roleIds: [validId, 'invalid-id'] };
            const schema = { roleIds: { type: 'array', required: true } };
            
            const errors = validateIds(input, schema);
            expect(errors).toHaveLength(1);
            expect(errors[0]).toEqual({
                field: 'roleIds',
                message: 'One or more roleIds IDs are invalid'
            });
        });

        it('should skip validation for non-required empty fields', () => {
            const input = {};
            const schema = { userId: { type: 'single', required: false } };
            
            const errors = validateIds(input, schema);
            expect(errors).toHaveLength(0);
        });
    });

    describe('validateReferences', () => {
        const mockModel = {
            findById: jest.fn(),
            find: jest.fn()
        };

        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should validate single reference successfully', async () => {
            const input = { userId: 'validId' };
            const schema = {
                userId: { type: 'single', model: mockModel, required: true }
            };

            mockModel.findById.mockResolvedValue({ _id: 'validId' });
            
            const result = await validateReferences(input, schema, 'User');
            expect(result).toBeNull();
        });

        it('should validate array references successfully', async () => {
            const input = { roleIds: ['id1', 'id2'] };
            const schema = {
                roleIds: { type: 'array', model: mockModel, required: true }
            };

            mockModel.find.mockResolvedValue([{ _id: 'id1' }, { _id: 'id2' }]);
            
            const result = await validateReferences(input, schema, 'User');
            expect(result).toBeNull();
        });

        it('should handle nested field paths', async () => {
            const input = { user: { id: 'validId' } };
            const schema = {
                'user.id': { type: 'single', model: mockModel, required: true }
            };

            mockModel.findById.mockResolvedValue({ _id: 'validId' });
            
            const result = await validateReferences(input, schema, 'User');
            expect(result).toBeNull();
        });
    });
});
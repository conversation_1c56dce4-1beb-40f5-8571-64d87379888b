const buildAddressQuery = (filters) => {
    const query = {};

    if (filters) {
        if (filters.addressType) {
            query.addressType = filters.addressType;
        }

        if (filters.city) {
            query.city = { $regex: filters.city, $options: 'i' };
        }

        if (filters.state) {
            query.state = { $regex: filters.state, $options: 'i' };
        }

        if (filters.country) {
            query.country = { $regex: filters.country, $options: 'i' };
        }

        if (filters.isPrimary !== undefined) {
            query.isPrimary = filters.isPrimary;
        }
    }

    return query;
};

module.exports = buildAddressQuery;
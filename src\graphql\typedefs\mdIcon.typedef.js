const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdIcon {
        id: ID!
        name: String!
        iconSrc: String!
        createdAt: Date!
        updatedAt: Date!
    }

    input MdIconFilterInput {
        id: String!
        name: String
        iconSrc: String
    }

    type MdIconWrapper {
        mdIcon: MdIcon!
    }

    type MdIconsWrapper {
        mdIcons: [MdIcon]!
    }

    type MdIconsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdIconsWrapper!
        pagination: PaginationInfo!
    }

    type MdIconResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdIconWrapper!
    }

    type MdIconErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdIconResult = MdIconResponse | MdIconErrorResponse
    union MdIconsResult = MdIconsResponse | MdIconErrorResponse

    type Query {
        getMdIconById(id: ID!): MdIconResult!
        getMdIcons(filter: MdIconFilterInput, pagination: PaginationInput): MdIconsResult!
    }

    type Mutation {
        createMdIcon(name: String!, iconSrc: String!): MdIconResult!
        updateMdIcon(id: ID!, name: String, iconSrc: String): MdIconResult!
        deleteMdIcon(id: ID!): MdIconResult!
    }
`;
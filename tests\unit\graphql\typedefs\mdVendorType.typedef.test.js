const { gql } = require('graphql-tag');
const mdVendorTypeTypeDef = require('../../../../src/graphql/typedefs/mdVendorType.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mdVendorTypeTypeDef', () => {
    it('should contain the MdVendorType type definitions', () => {
        const expectedTypeDefs = gql`
            ${sharedTypeDef}
    
            type MdVendorType {
                id: ID!
                name: String!
                description: String!
                primaryFeature: MdFeature
                features: [MdFeature!]
                mainFeatures: [MdFeature!]
                bannerImage: String
                speciality: String
                icon: MdIcon
                partners: [MdPartner!]
                createdAt: Date!
                updatedAt: Date!
            }
    
            input MdVendorTypeFilterInput {
                id: String
                name: String
                speciality: String
            }
    
            type MdVendorTypeWrapper {
                mdVendorType: MdVendorType!
            }
    
            type MdVendorTypesWrapper {
                mdVendorTypes: [MdVendorType]!
            }
    
            type MdVendorTypesResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: MdVendorTypesWrapper!
                pagination: PaginationInfo!
            }
    
            type MdVendorTypeResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: MdVendorTypeWrapper!
            }
    
            type MdVendorTypeErrorResponse implements Response {
                status: ResponseStatus!
                message: String!
                errors: [Error!]!
            }
    
            union MdVendorTypeResult = MdVendorTypeResponse | MdVendorTypeErrorResponse
            union MdVendorTypesResult = MdVendorTypesResponse | MdVendorTypeErrorResponse
    
            type Query {
                getMdVendorTypeById(id: ID!): MdVendorTypeResult!
                getMdVendorTypes(filter: MdVendorTypeFilterInput, pagination: PaginationInput): MdVendorTypesResult!
            }
    
            input MdVendorTypeInput {
                name: String!
                description: String!
                primaryFeature: ID
                features: [ID!]
                mainFeatures: [ID!]
                bannerImage: String
                speciality: String
                icon: ID
                partners: [ID!]
            }
    
            input MdVendorTypeUpdateInput {
                name: String
                description: String
                primaryFeature: ID
                features: [ID!]
                mainFeatures: [ID!]
                bannerImage: String
                speciality: String
                icon: ID
                partners: [ID!]
            }
    
            type Mutation {
                createMdVendorType(input: MdVendorTypeInput!): MdVendorTypeResult!
                updateMdVendorType(id: ID!, input: MdVendorTypeUpdateInput!): MdVendorTypeResult!
                deleteMdVendorType(id: ID!): MdVendorTypeResult!
            }
        `;
    
        const normalize = str => str.replace(/\s+/g, ' ').trim();
    
        expect(normalize(mdVendorTypeTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});
const MdPromotion = require('../../models/MdPromotion');
const MdPartner = require('../../models/MdPartner');
const Tag = require('../../models/Tag');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMdPromotionQuery = require('./filters/mdPromotion.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');

const baseMdPromotionIdSchema = {
    partner: { type: 'single', model: MdPartner },
    tags: { type: 'array', required: false, model: Tag }
};

const mdPromotionIdSchema = { ...baseMdPromotionIdSchema };

const createMdPromotionIdSchema = {
    ...baseMdPromotionIdSchema,
    partner: { ...baseMdPromotionIdSchema.partner, required: true }
};

const mdPromotionResolvers = {
    MdPromotion: {
        partner: async (parent) => {
            try {
                return await MdPartner.findById(parent.partner);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting partner');
            }
        },
        tags: async (parent) => {
            try {
                return await Tag.find({ _id: { $in: parent.tags } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting tags');
            }
        }
    },

    Query: {
        getMdPromotionById: async (_, { id }) => {
            try {
                let mdPromotion = await getByIdCache(id);
                if (!mdPromotion) {
                    mdPromotion = await MdPromotion.findById(id)
                        .populate('partner')
                        .populate('tags');
                    
                    if (!mdPromotion) {
                        return createResponse('MdPromotionErrorResponse', 'FAILURE', 'MdPromotion not found', {
                            errors: [{ field: 'id', message: 'MdPromotion not found' }]
                        });
                    }
                    await setCache(id, mdPromotion);
                }
                return createResponse('MdPromotionResponse', 'SUCCESS', 'MdPromotion fetched successfully', {
                    result: { mdPromotion }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Error getting MdPromotion', {
                    errors: [{ field: 'getMdPromotionById', message: error.message }]
                });
            }
        },

        getMdPromotions: async (_, { filter, pagination }) => {
            try {
                const query = await buildMdPromotionQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdPromotion, query, limit, skip);

                const mdPromotions = await MdPromotion.find(query)
                    .populate('partner')
                    .populate('tags')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (mdPromotions.length === 0) {
                    return createResponse('MdPromotionsResponse', 'FAILURE', 'No promotions found', {
                        result: { mdPromotions },
                        pagination: paginationInfo
                    });
                }

                return createResponse('MdPromotionsResponse', 'SUCCESS', 'Promotions fetched successfully', {
                    result: { mdPromotions },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Error getting promotions', {
                    errors: [{ field: 'getMdPromotions', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdPromotion: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createMdPromotionIdSchema, 'MdPromotion');
                if (validationError) {
                    return createResponse('MdPromotionErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdPromotion = new MdPromotion(input);
                await mdPromotion.save();

                const populatedPromotion = await MdPromotion.findById(mdPromotion._id)
                    .populate('partner')
                    .populate('tags');

                return createResponse('MdPromotionResponse', 'SUCCESS', 'Promotion created successfully', {
                    result: { mdPromotion: populatedPromotion }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Error creating promotion', {
                    errors: [{ field: 'createMdPromotion', message: error.message }]
                });
            }
        },

        updateMdPromotion: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdPromotionIdSchema, 'MdPromotion');
                if (validationError) {
                    return createResponse('MdPromotionErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdPromotion = await MdPromotion.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                )
                .populate('partner')
                .populate('tags');

                if (!mdPromotion) {
                    return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Promotion not found', {
                        errors: [{ field: 'id', message: 'Promotion not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdPromotionResponse', 'SUCCESS', 'Promotion updated successfully', {
                    result: { mdPromotion }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Error updating promotion', {
                    errors: [{ field: 'updateMdPromotion', message: error.message }]
                });
            }
        },

        deleteMdPromotion: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdPromotion');
                if (references.length > 0) {
                    return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Promotion cannot be deleted', {
                        errors: [{ field: 'id', message: `Promotion cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const mdPromotion = await MdPromotion.findByIdAndDelete(id);
                
                if (!mdPromotion) {
                    return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Promotion not found', {
                        errors: [{ field: 'id', message: 'Promotion not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdPromotionResponse', 'SUCCESS', 'Promotion deleted successfully', {
                    result: { mdPromotion }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPromotionErrorResponse', 'FAILURE', 'Error deleting promotion', {
                    errors: [{ field: 'deleteMdPromotion', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdPromotionResolvers; 
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdPartner = require('../../../src/models/MdPartner');
const Tag = require('../../../src/models/Tag');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdPartner Model Test', () => {
    beforeEach(async () => {
        await MdPartner.deleteMany({});
        await Tag.deleteMany({});
        await MdIcon.deleteMany({});
    });

    it('should create and save a MdPartner successfully', async () => {
        const mockIcon = new MdIcon({
            name: 'Test Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const mockTag = new Tag({
            name: 'Test Tag',
            slug: 'test-tag'
        });
        await mockTag.save();

        const validMdPartner = new MdPartner({
            name: 'Test Partner',
            categories: [mockTag._id],
            description: 'Test Description',
            icon: mockIcon._id,
            banner_image_url: 'https://example.com/banner.jpg'
        });

        const savedMdPartner = await validMdPartner.save();

        expect(savedMdPartner._id).toBeDefined();
        expect(savedMdPartner.name).toBe('Test Partner');
        expect(savedMdPartner.categories[0]).toEqual(mockTag._id);
        expect(savedMdPartner.description).toBe('Test Description');
        expect(savedMdPartner.icon).toEqual(mockIcon._id);
        expect(savedMdPartner.banner_image_url).toBe('https://example.com/banner.jpg');
        expect(savedMdPartner.createdAt).toBeDefined();
        expect(savedMdPartner.updatedAt).toBeDefined();
    });

    it('should fail to create a MdPartner without required fields', async () => {
        const mdPartner = new MdPartner({});

        let err;
        try {
            await mdPartner.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.icon).toBeDefined();
    });

    it('should create a MdPartner with only required fields', async () => {
        const mockIcon = new MdIcon({
            name: 'Required Icon',
            iconSrc: '<iconSrc></iconSrc>'
        });
        await mockIcon.save();

        const mockTag = new Tag({
            name: 'Required Tag',
            slug: 'required-tag'
        });
        await mockTag.save();

        const minimalMdPartner = new MdPartner({
            name: 'Minimal Partner',
            categories: [mockTag._id],
            icon: mockIcon._id
        });

        const savedMdPartner = await minimalMdPartner.save();

        expect(savedMdPartner._id).toBeDefined();
        expect(savedMdPartner.name).toBe('Minimal Partner');
        expect(savedMdPartner.categories[0]).toEqual(mockTag._id);
        expect(savedMdPartner.icon).toEqual(mockIcon._id);
        expect(savedMdPartner.description).toBeUndefined();
        expect(savedMdPartner.banner_image_url).toBeUndefined();
    });

    it('should fail to create a MdPartner with invalid references', async () => {
        const invalidMdPartner = new MdPartner({
            name: 'Invalid Partner',
            categories: ['invalid_id'],
            icon: 'invalid_id'
        });

        let err;
        try {
            await invalidMdPartner.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors['categories.0']).toBeDefined();
        expect(err.errors.icon).toBeDefined();
    });
});
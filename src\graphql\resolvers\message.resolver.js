const Message = require('../../models/Message');
const Media = require('../../models/Media');
const Reaction = require('../../models/Reaction');
const { User } = require('../../models/User');
const { hasEventPartyGuestLevelAccess } = require('../../utils/auth/accessLevels.util');
const Party = require('../../models/Party');
const { createResponse } = require('../../utils/response.util');
const buildMessageQuery = require('./filters/message.filter');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');
const { validateReferences } = require('../../utils/validation.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const MediaFolder = require('../../models/MediaFolder');
const { clearCacheById } = require('../../utils/cache.util');
const { addMessageToActivityQueue } = require('../../utils/messageQueue.util');
const { notifyMessageAdded, notifyPhotoAddedInMessage } = require('../../notification/activityNotification');

const messageSchema = {
    partyId: { type: 'single', required: true , model: Party },
    parentMessageId: { type: 'single', required: false , model: Message },
    media: { type: 'array', required: false , model: Media },
    receivers: { type: 'array', required: false , model: User },
}

const validateReceivers = async (receivers, eventId, partyId) => {
    if (!receivers || receivers.length === 0) return null;

    const unauthorizedReceivers = [];
    // Use Promise.all to properly handle async operations in array
    await Promise.all(receivers.map(async (receiver) => {
        const { hasAccess } = await hasEventPartyGuestLevelAccess(receiver, eventId, partyId);
        if (!hasAccess) {
            unauthorizedReceivers.push(receiver);
        }
    }));

    if (unauthorizedReceivers.length > 0) {
        return {
            field: `receivers: ${unauthorizedReceivers.join(', ')}`,
            message: 'You do not have access to these receivers'
        };
    }
    return null;
};

const messageResolvers = {
    Message: {
        media: async (parent) => {
            try {
                return await Media.find({ _id: { $in: parent.media } });
            } catch (error) {
                throw new Error('Error fetching media');
            }
        },

        reactions: async (parent) => {
            try {
                return await Reaction.find({ _id: { $in: parent.reactions } });
            } catch (error) {
                throw new Error('Error fetching reactions');
            }
        },
        createdBy: async (parent) => {
            try {
                return await User.findById(parent.createdBy);
            } catch (error) {
                throw new Error('Error fetching createdBy');
            }
        },
        receivers: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.receivers } });
            } catch (error) {
                throw new Error('Error fetching receivers');
            }
        }
    },

    Query: {
        getMessageById: async (_, { id }, context) => {
            try {
                const party = await Party.findOne({ activity: { $in: [id] } });
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                    });
                }
                const message = await Message.findById(id);
                if (!message) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Message not found', {
                        errors: [{ field: 'id', message: 'Message not found' }]
                    });
                }

                if (message.receivers.length != 0) {
                    if (!message.receivers.includes(context.user._id)) {
                        return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                            errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                        });
                    }
                }

                return createResponse('MessageResponse', 'SUCCESS', 'Message retrieved successfully', {
                    result: { message }
                });

            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error fetching message', {
                    errors: [{ field: 'getMessageById', message: error.message }]
                });
            }
        },

        getMessagesByPartyId: async (_, { partyId, filter, pagination }, context) => {
            try {
                const party = await Party.findById(partyId);
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                    });
                }

                const query  = await buildMessageQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const messageQuery = {
                    ...query,
                    _id: { $in: party.activity }
                };

                const paginationInfo = await getPaginationInfo(Message, messageQuery, limit, skip);

                const messages = await Message.find(messageQuery)
                    .skip(skip)
                    .limit(limit);

                const filteredMessages = messages.filter(message => 
                    !message.receivers?.length ||
                    message.receivers.includes(context.user._id)
                );

                return createResponse('MessagesResponse', 'SUCCESS', 'Messages retrieved successfully', {
                    result: { messages: filteredMessages },
                    pagination: paginationInfo
                });


            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error fetching messages', {
                    errors: [{ field: 'getMessagesByPartyId', message: error.message }]
                });
            }
        },
    },

    Mutation: {
        createMessage: async (_, { input }, context) => {
            try {
                const party = await Party.findById(input.partyId);
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'partyId', message: 'Party not found' }]
                    });
                }
                
                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                    });
                }

                const receiverValidationError = await validateReceivers(input.receivers, party.eventId, party._id);
                if (receiverValidationError) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [receiverValidationError]
                    });
                }

                const referenceValidationErrors = await validateReferences(input, messageSchema, 'Message');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createMessage',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('MessageErrorResponse', 'FAILURE', 'Invalid message information', {
                        errors
                    });
                }

                const message = await new Message({
                    ...input,
                    createdBy: context.user._id
                }).save();

                const mediaFolder = await MediaFolder.findOne({ event: party.eventId });

                if (mediaFolder && input.media?.length) {
                    const newMedia = input.media.filter(mediaId => 
                        !mediaFolder.media.includes(mediaId)
                    );
                    if (newMedia.length) {
                        mediaFolder.media.push(...newMedia);
                        await mediaFolder.save();

                        // Send photo notification
                        const user = await User.findById(context.user._id);
                        const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
                        await notifyPhotoAddedInMessage(
                            party.name,
                            userName,
                            party._id.toString(),
                            context.user._id
                        );
                    }
                }

                party.activity.push(message._id);
                await party.save();
                await clearCacheById(party._id);

                // Existing message notification
                const user = await User.findById(context.user._id);
                const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
                await notifyMessageAdded(
                    party.name,
                    userName,
                    input.text,
                    party._id.toString(),
                    context.user._id
                );

                addMessageToActivityQueue(party._id, { eventId: party.eventId, message, sub_type: 'MESSAGE_CREATED' });

                return createResponse('MessageResponse', 'SUCCESS', 'Message created successfully', {
                    result: { message }
                });
            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error creating message', {
                    errors: [{ field: 'createMessage', message: error.message }]
                });
            }
        },

        editMessage: async (_, { id, input }, context) => {
            try {
                const party = await Party.findOne({ activity: { $in: [id] } });
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                    });
                }

                const receiverValidationError = await validateReceivers(input.receivers, party.eventId, party._id);
                if (receiverValidationError) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [receiverValidationError]
                    });
                }

                let message = await Message.findById(id);
                if (!message) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Message not found', {
                        errors: [{ field: 'id', message: 'Message not found' }]
                    });
                }

                const updatedMessage = await Message.findByIdAndUpdate(id, input, { new: true });

                addMessageToActivityQueue(party._id, { eventId: party.eventId, message: updatedMessage, sub_type: 'MESSAGE_UPDATED' });

                const mediaFolder = await MediaFolder.findOne({ event: party.eventId });

                if (mediaFolder && input.media?.length) {
                    const newMedia = input.media.filter(mediaId => 
                        !mediaFolder.media.includes(mediaId)
                    );

                    if (newMedia.length) {
                        mediaFolder.media.push(...newMedia);
                        await mediaFolder.save();
                    }
                }

                await clearCacheById(party._id);

                return createResponse('MessageResponse', 'SUCCESS', 'Message updated successfully', {
                    result: { message: updatedMessage }
                });


            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error updating message', {
                    errors: [{ field: 'updateMessage', message: error.message }]
                });
            }
        },

        deleteMessage: async (_, { id }, context) => {
            try {
                const party = await Party.findOne({ activity: { $in: [id] } });
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                const { hasAccess, isHost } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                    });
                }

                const message = await Message.findById(id);
                if (!message) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Message not found', {
                        errors: [{ field: 'id', message: 'Message not found' }]
                    });
                }

                // Check if user is authorized to delete the message
                if (!isHost && message.createdBy.toString() !== context.user._id.toString()) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You can only delete your own messages' }]
                    });
                }

                // Use cascade delete utility
                const { success, error } = await cascadeDelete('Message', id);
                if (!success) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Error deleting message', {
                        errors: [{ field: 'deleteMessage', message: error?.message || 'Failed to delete message and its dependencies' }]
                    });
                }

                // Remove message from party activity
                party.activity = party.activity.filter(activityId => activityId.toString() !== id.toString());
                await party.save();
                await clearCacheById(party._id);

                addMessageToActivityQueue(party._id, { eventId: party.eventId, message, sub_type: 'MESSAGE_DELETED' });

                return createResponse('MessageResponse', 'SUCCESS', 'Message and its dependencies deleted successfully', {
                    result: { message }
                });

            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error deleting message', {
                    errors: [{ field: 'deleteMessage', message: error.message }]
                });
            }
        },

        addReactionToMessage: async (_, { messageId, reactionId }, context) => {
            try {
                const party = await Party.findOne({ activity: { $in: [messageId] } });
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'messageId', message: 'Party not found' }]
                    });
                }

                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                    });
                }

                const message = await Message.findById(messageId);
                if (!message) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Message not found', {
                        errors: [{ field: 'messageId', message: 'Message not found' }]
                    });
                }

                // Get the new reaction with user validation
                const newReaction = await Reaction.findOne({
                    _id: reactionId,
                    user: context.user._id
                });
                
                if (!newReaction) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Reaction not found or not owned by user', {
                        errors: [{ field: 'reactionId', message: 'Invalid reaction' }]
                    });
                }

                // Find existing user reaction on this message
                const existingReactions = await Reaction.find({
                    _id: { $in: message.reactions },
                    user: context.user._id
                });

                // Remove existing reactions
                if (existingReactions.length > 0) {
                    // Delete reaction documents
                    await Reaction.deleteMany({ 
                        _id: { $in: existingReactions.map(r => r._id) } 
                    });
                    
                    // Remove from message's reactions array
                    message.reactions = message.reactions.filter(rid => 
                        !existingReactions.some(r => r._id.equals(rid))
                    );
                }

                // Add new reaction
                message.reactions.push(newReaction._id);
                await message.save();
                await clearCacheById(party._id);

                addMessageToActivityQueue(party._id, { eventId: party.eventId, message, sub_type: 'MESSAGE_REACTION_ADDED' });

                return createResponse('MessageResponse', 'SUCCESS', 
                    existingReactions.length > 0 
                        ? 'Reaction replaced successfully' 
                        : 'Reaction added successfully', 
                    { result: { message } }
                );

            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error updating reaction', {
                    errors: [{ field: 'addReactionToMessage', message: error.message }]
                });
            }
        },

        removeReactionFromMessage: async (_, { messageId, reactionId }, context) => {
            try {
                const party = await Party.findOne({ activity: { $in: [messageId] } });
                if (!party) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'messageId', message: 'Party not found' }]
                    });
                }

                const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
                if (!hasAccess) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this message' }]
                    });
                }

                // Get the message first
                const message = await Message.findOne({
                    _id: messageId
                });

                if (!message) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Message not found', {
                        errors: [{ field: 'messageId', message: 'Invalid message' }]
                    });
                }

                const reaction = await Reaction.findOneAndDelete({_id: reactionId, user: context.user._id});
                if (!reaction) {
                    return createResponse('MessageErrorResponse', 'FAILURE', 'Reaction not found', {
                        errors: [{ field: 'reactionId', message: 'Reaction not found' }]
                    });
                }

                // Remove reaction from message
                message.reactions = message.reactions.filter(rid => 
                    !reaction._id.equals(rid)
                );

                await message.save();
                await clearCacheById(party._id);
                addMessageToActivityQueue(party._id, { eventId: party.eventId, message, sub_type: 'MESSAGE_REACTION_REMOVED' });

                return createResponse('MessageResponse', 'SUCCESS', 'Reaction removed successfully', {
                    result: { message }
                });

            } catch (error) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Error removing reaction', {
                    errors: [{ field: 'removeReactionFromMessage', message: error.message }]
                });
            }
        }
    }
}

module.exports = messageResolvers


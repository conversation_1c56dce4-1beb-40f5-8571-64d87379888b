const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const reminderSchema = new Schema({
    message: { type: String, required: true },
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: true },
    datetime: { type: Date, required: true }
}, { timestamps: true, collection: 'reminders' });

const Reminder = model('Reminder', reminderSchema);

module.exports = Reminder;

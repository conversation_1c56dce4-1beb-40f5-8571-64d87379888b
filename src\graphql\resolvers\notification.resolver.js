const DeviceToken = require('../../models/DeviceTokenModel');
const { sendMultiUserNotification } = require('../../services/notification.service');
const { AuthenticationError } = require('apollo-server-express');

const notificationResolvers = {
  Mutation: {
    sendNotification: async (_, { input }, { user }) => {
      try {
        if (!user) {
          throw new AuthenticationError('You must be logged in to send notifications');
        }

        const targetUserId = input.userId || user._id.toString();
        
        // Find device tokens for the user
        const deviceTokens = await DeviceToken.find({ userId: targetUserId });
        
        if (!deviceTokens || deviceTokens.length === 0) {
          return {
            __typename: 'NotificationError',
            message: `No device tokens found for user ${targetUserId}`
          };
        }

        // Create notification content object
        const content = {
          title: input.title,
          body: input.body,
          data: input.data || {},
          sound: input.sound || 'default',
          badge: input.badge,
          channelId: input.channelId
        };

        // For each token, send the notification
        const userTokens = deviceTokens.map(device => ({
          userId: targetUserId,
          token: device.token
        }));

        const result = await sendMultiUserNotification(userTokens, content);
        
        if (!result.success) {
          return {
            __typename: 'NotificationError',
            message: result.message || 'Failed to send notification'
          };
        }

        return {
          __typename: 'NotificationResult',
          message: 'Notification sent successfully',
          notification: {
            id: result.receiptIds.length > 0 ? result.receiptIds[0].receiptId : null,
            userId: targetUserId,
            title: input.title,
            body: input.body,
            data: input.data,
            createdAt: new Date().toISOString()
          },
          receipts: result.receiptIds.map(r => r.receiptId),
          deviceTokens: deviceTokens.map(dt => dt.token)
        };
      } catch (error) {
        console.error('Error in sendNotification resolver:', error);
        return {
          __typename: 'NotificationError',
          message: error.message || 'An error occurred while sending the notification'
        };
      }
    }
  }
};

module.exports = notificationResolvers; 
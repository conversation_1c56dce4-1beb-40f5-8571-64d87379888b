const buildMdPartyTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdPartyType.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdPartyTypeQuery', () => {
    it('should return an empty query object when no filter is provided', () => {
        const result = buildMdPartyTypeQuery();
        expect(result).toEqual({});
    });

    it('should build a query with a valid id', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const result = buildMdPartyTypeQuery({ id: validId });
        expect(result).toEqual({ _id: validId });
    });

    it('should throw an error for an invalid id', () => {
        const invalidId = 'invalid_id';
        isValidObjectId.mockReturnValue(false);
        expect(() => buildMdPartyTypeQuery({ id: invalidId })).toThrow('Invalid id provided');
    });

    it('should build a query with a name filter', () => {
        const nameFilter = 'Party Type';
        const result = buildMdPartyTypeQuery({ name: nameFilter });
        expect(result).toEqual({ name: { $regex: nameFilter, $options: 'i' } });
    });

    it('should build a query with multiple filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const nameFilter = 'Party Type';
        const result = buildMdPartyTypeQuery({ id: validId, name: nameFilter });
        expect(result).toEqual({
            _id: validId,
            name: { $regex: nameFilter, $options: 'i' }
        });
    });
});
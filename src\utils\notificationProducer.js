const AWS = require('aws-sdk');
const Party = require('../models/Party');
const Host = require('../models/Host');
const Guest = require('../models/Guest');
const NotificationConfig = require('./notificationConfig');
const Event = require('../models/Event');
const EventGroup = require('../models/EventGroup');
const Message = require('../models/Message');
const { clearCacheById } = require('./cache.util');
const { addMessageToActivityQueue } = require('./messageQueue.util');
const InAppNotification = require('../models/InAppNotification');

class NotificationProducer {
    constructor() {
        this.sns = new AWS.SNS({ region: process.env.AWS_REGION });
        this.topicArn = process.env.SNS_NOTIFICATION_TOPIC_ARN;
        this.config = new NotificationConfig();
    }

    replaceTemplateVars(template, data) {
        let message = template;
        Object.entries(data).forEach(([key, value]) => {
            message = message.replace(`{${key}}`, value?.toString() || '');
        });
        return message;
    }

    async getRecipients(model, modelId, receiverTypes) {
        try {
            const recipients = new Set();
            let party, event, circle;

            // Fetch and validate the appropriate model
            if (model === Party) {
                party = await Party.findById(modelId).populate('eventId');
                if (!party) throw new Error('Party not found');
                
                event = await Event.findById(party.eventId).populate('mainHost');
                if (!event) throw new Error('Event not found');
            } else if (model === EventGroup) {
                circle = await EventGroup.findById(modelId)
                    .populate('members')
                    .populate('organizers');
                if (!circle) throw new Error('Circle not found');
            } else {
                throw new Error('Unsupported model type');
            }

            // Process each receiver type
            for (const type of receiverTypes) {
                switch (type) {
                    case 'Host':
                        if (event?.mainHost) {
                            const mainHost = await Host.findById(event.mainHost).populate('userId');
                            if (mainHost?.userId) {
                                recipients.add({
                                    userId: mainHost.userId._id,
                                    email: mainHost.userId.email,
                                    phone: mainHost.userId.phone,
                                    role: 'Host'
                                });
                            }
                        }
                        break;
                    case 'Cohost':
                        if (party?.coHosts?.length) {
                            const cohosts = await Host.find({ _id: { $in: party.coHosts } }).populate('userId');
                            cohosts.forEach(cohost => {
                                if (cohost?.userId) {
                                    recipients.add({
                                        userId: cohost.userId._id,
                                        email: cohost.userId.email,
                                        phone: cohost.userId.phone,
                                        role: 'Cohost'
                                    });
                                }
                            });
                        }
                        break;
                    case 'Guest':
                        if (party) {
                            const guests = await Guest.find({ party: party._id }).populate('user');
                            guests.forEach(guest => {
                                if (guest?.user) {
                                    recipients.add({
                                        userId: guest.user._id,
                                        email: guest.user.email,
                                        phone: guest.user.phone,
                                        role: 'Guest'
                                    });
                                }
                            });
                        }
                        break;
                    case 'Member':
                        if (circle?.members?.length) {
                            circle.members.forEach(member => {
                                if (member?._id) {
                                    recipients.add({
                                        userId: member._id,
                                        email: member.email,
                                        phone: member.phone,
                                        role: 'Member'
                                    });
                                }
                            });
                        }
                        break;
                    case 'Organizer':
                        if (circle?.organizers?.length) {
                            circle.organizers.forEach(organizer => {
                                if (organizer?.id) {
                                    recipients.add({
                                        userId: organizer.id,
                                        email: organizer.email,
                                        phone: organizer.phone,
                                        role: 'Organizer'
                                    });
                                }
                            });
                        }
                        break;  
                }
            }
            return Array.from(recipients);
        } catch (error) {
            console.error('Error getting recipients:', error);
            throw error;
        }
    }

    async handleActivityNotification(partyId, messageText) {
        try {
            const activityMessage = new Message({
                type: 'SYSTEM',
                text: messageText.replace(/[\"\\]/g, ''),
                partyId
            });

            // Update party and save message in parallel
            const [party] = await Promise.all([
                Party.findByIdAndUpdate(
                    partyId,
                    { $push: { activity: activityMessage._id } },
                    { new: true }
                ),
                activityMessage.save()
            ]);

            if (!party) {
                throw new Error('Party not found');
            }

            // Execute cache clear and queue update in parallel
            await Promise.all([
                clearCacheById(partyId),
                addMessageToActivityQueue(partyId, activityMessage)
            ]);

            return true;
        } catch (error) {
            console.error('Error handling activity notification:', error);
            throw error;
        }
    }

    async createInAppNotifications(message, recipients, entityType, entityId, senderId) {
        try {
            const notifications = await Promise.all(
                recipients
                    .filter(recipient => recipient.userId) // Ensure we have valid userIds
                    .map(async (recipient) => {
                        const notification = new InAppNotification({
                            userId: recipient.userId,
                            sender: senderId,
                            message,
                            read: false,
                            // Optionally add link based on entity
                            link: entityId ? `/${entityType}/${entityId}` : undefined
                        });
                        await notification.save();
                        return notification;
                    })
            );
            
            console.log(`Created ${notifications.length} in-app notifications`);
            return notifications;
        } catch (error) {
            console.error('Error creating in-app notifications:', error);
            throw error;
        }
    }

    async publishToSNS(eventType, data, recipients) {
        try {
            const config = await this.config.getEventConfig(eventType);
            const message = this.config.replaceWildcards(config.templates.message, data);
            const subject = this.config.replaceWildcards(config.templates.subject, data);

            if (config.channels.activity) {
                await this.handleActivityNotification(data.partyId, message);
            }

            // Get entity information - use provided values if they exist
            let entityType = data.entityType || 'party';
            let entityId = data.entityId || data.partyId;
            
            if (!entityId && data.eventGroupId) {
                entityType = 'eventGroup';
                entityId = data.eventGroupId;
            }
            
            if (!entityId) {
                entityId = 'custom-' + Date.now();
            }

            // Handle in-app notifications if enabled
            if (config.channels.inApp) {
                await this.createInAppNotifications(message, recipients, entityType, entityId, data.senderId);
            }

            // Check if we need to continue with other channels
            const hasOtherChannels = Object.entries(config.channels)
                .some(([channel, enabled]) => enabled && channel !== 'activity' && channel !== 'inApp');

            if (!hasOtherChannels) {
                return true;
            }

            const channelConfig = {
                email: { 
                    recipientKey: 'email', 
                    recipientListKey: 'recipientEmails' 
                },
                sms: { 
                    recipientKey: 'phone', 
                    recipientListKey: 'recipientPhones',
                    transform: phone => phone ? phone.replace(/-/g, '') : phone 
                },
                whatsapp: { 
                    recipientKey: 'phone', 
                    recipientListKey: 'recipientPhones',
                    transform: phone => phone ? phone.replace(/-/g, '') : phone 
                },
                inApp: { 
                    recipientKey: 'userId', 
                    recipientListKey: 'recipientIds', 
                    transform: id => id.toString() 
                }
            };

            const payload = {
                metadata: {
                    eventType,
                    entityType,
                    entityId: entityId.toString(),
                    timestamp: new Date().toISOString()
                }
            };

            // Initialize enabledChannels array
            const enabledChannels = [];

            // Generate payload for each enabled channel
            Object.entries(channelConfig).forEach(([channel, cfg]) => {
                if (config.channels[channel] === true) {
                    enabledChannels.push(channel);
                    payload[channel] = {
                        messageText: message,
                        ...(channel === 'email' && { subject }),
                        ...(cfg.skipRecipients ? {} : {
                            [cfg.recipientListKey]: recipients
                                .filter(r => r[cfg.recipientKey])
                                .map(r => cfg.transform ? cfg.transform(r[cfg.recipientKey]) : r[cfg.recipientKey])
                        })
                    };
                }
            });

            const messageAttributes = {
                eventType: { DataType: 'String', StringValue: eventType },
                entityType: { DataType: 'String', StringValue: entityType }
            };

            enabledChannels.forEach(channel => {
                messageAttributes[`channel-${channel}`] = { 
                    DataType: 'String', 
                    StringValue: 'true' 
                };
            });

            await this.sns.publish({
                TopicArn: this.topicArn,
                Message: JSON.stringify(payload),
                MessageAttributes: messageAttributes
            }).promise();

            return true;
        } catch (error) {
            console.error('Error publishing to SNS:', error);
            throw error;
        }
    }

    async sendNotification(eventType, data) {
        try {
            if (!eventType || !data?.partyId) {
                throw new Error('Missing required parameters');
            }

            const config = await this.config.getEventConfig(eventType);
            if (!config) {
                throw new Error(`Event type "${eventType}" not found in configuration`);
            }

            if (!this.config.validateRequiredData(eventType, data)) {
                throw new Error('Missing required data fields for this event type');
            }

            // Only fetch recipients if there are non-activity channels enabled
            const hasNonActivityChannels = Object.entries(config.channels)
                .some(([channel, enabled]) => enabled && channel !== 'activity');
            
            const recipients = hasNonActivityChannels ? 
                await this.getRecipients(Party, data.partyId, config.receivers) : 
                [];

            await this.publishToSNS(eventType, data, recipients);
        } catch (error) {
            console.error('Error sending notification:', error);
            throw error;
        }
    }

    async sendCircleNotification(eventType, data) {
        try {
            if (!eventType || !data?.eventGroupId) {
                throw new Error('Missing required parameters');
            }

            const config = await this.config.getEventConfig(eventType);
            if (!config) {
                throw new Error(`Event type "${eventType}" not found in configuration`);
            }

            if (!this.config.validateRequiredData(eventType, data)) {
                throw new Error('Missing required data fields for this event type');
            }

            // Only fetch recipients if there are non-activity channels enabled
            const hasNonActivityChannels = Object.entries(config.channels)
                .some(([channel, enabled]) => enabled && channel !== 'activity');
            
            const recipients = hasNonActivityChannels ? 
                await this.getRecipients(EventGroup, data.eventGroupId, config.receivers) : 
                [];

            await this.publishToSNS(eventType, data, recipients);
        } catch (error) {
            console.error('Error sending circle notification:', error);
            throw error;
        }
    }

    async sendNotificationToSpecificRecipients(eventType, data, recipients) {
        try {
            if (!eventType) {
                throw new Error('Missing required parameters');
            }

            const config = await this.config.getEventConfig(eventType);
            if (!config) {
                throw new Error(`Event type "${eventType}" not found in configuration`);
            }

            if (!this.config.validateRequiredData(eventType, data)) {
                throw new Error('Missing required data fields for this event type');
            }

            await this.publishToSNS(eventType, data, recipients);
        } catch (error) {
            console.error('Error sending notification:', error);
            throw error;
        }
    }
}

module.exports = new NotificationProducer();
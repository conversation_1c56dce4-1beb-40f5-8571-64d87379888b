const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

const mdInterestCategoryTypeDefs = gql`
    ${sharedTypeDef}

    type MdInterestCategory {
        id: ID!
        title: String!
        interests: [MdInterest!]
    }
    
    type MdInterestCategoryWrapper {
        mdInterestCategory: MdInterestCategory!
    }

    type MdInterestCategoriesWrapper {
        mdInterestCategories: [MdInterestCategory!]!
    }

    type MdInterestCategoryResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInterestCategoryWrapper!
    }

    type MdInterestCategoriesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInterestCategoriesWrapper!
        pagination: PaginationInfo!
    }

    type MdInterestCategoryErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    input MdInterestCategoryFilterInput {
        id: ID
        title: String
        interests: [MdInterestFilterInput!]
    }

    input MdInterestCategoryInput {
        title: String!
        interests: [ID!]
    }

    input MdInterestCategoryUpdateInput {
        title: String
        interests: [ID!]
    }

    union MdInterestCategoryResult = MdInterestCategoryResponse | MdInterestCategoryErrorResponse
    union MdInterestCategoriesResult = MdInterestCategoriesResponse | MdInterestCategoryErrorResponse
    
    type Query {
        getMdInterestCategoryById(id: ID!): MdInterestCategoryResult!
        getMdInterestCategories(filter: MdInterestCategoryFilterInput, pagination: PaginationInput): MdInterestCategoriesResult!
    }

    type Mutation {
        createMdInterestCategory(input: MdInterestCategoryInput!): MdInterestCategoryResult!
        updateMdInterestCategory(id: ID!, input: MdInterestCategoryUpdateInput!): MdInterestCategoryResult!
        deleteMdInterestCategory(id: ID!): MdInterestCategoryResult!
    }
`;

module.exports = mdInterestCategoryTypeDefs;
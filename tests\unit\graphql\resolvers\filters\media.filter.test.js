const buildMediaQuery = require('../../../../../src/graphql/resolvers/filters/media.filter');
const Tag = require('../../../../../src/models/Tag');

jest.mock('../../../../../src/models/Tag');

describe('buildMediaQuery', () => {
    it('should return an empty query when no filters are provided', async () => {
        const result = await buildMediaQuery();
        expect(result).toEqual({});
    });

    it('should build a query with title filter', async () => {
        const filters = { title: 'test title' };
        const result = await buildMediaQuery(filters);
        expect(result).toEqual({ title: { $regex: 'test title', $options: 'i' } });
    });

    it('should build a query with uploadedAt filter', async () => {
        const filters = { uploadedAt: { start: '2023-01-01', end: '2023-01-31' } };
        const result = await buildMediaQuery(filters);
        expect(result).toEqual({
            uploadedAt: {
                $gte: new Date('2023-01-01'),
                $lte: new Date('2023-01-31'),
            },
        });
    });

    it('should build a query with tags filter', async () => {
        const filters = { tags: ['tag1', 'tag2'] };
        Tag.find.mockReturnValueOnce({
            distinct: jest.fn().mockResolvedValue(['tagId1', 'tagId2']),
        });
        const result = await buildMediaQuery(filters);
        expect(Tag.find).toHaveBeenCalledWith({ name: { $regex: expect.any(RegExp) } });
        expect(result).toEqual({ tags: { $in: ['tagId1', 'tagId2'] } });
    });

    it('should combine multiple filters', async () => {
        const filters = {
            title: 'test title',
            uploadedAt: { start: '2023-01-01', end: '2023-01-31' },
            tags: ['tag1', 'tag2'],
        };
        Tag.find.mockReturnValueOnce({
            distinct: jest.fn().mockResolvedValue(['tagId1', 'tagId2']),
        });
        const result = await buildMediaQuery(filters);
        expect(result).toEqual({
            title: { $regex: 'test title', $options: 'i' },
            uploadedAt: {
                $gte: new Date('2023-01-01'),
                $lte: new Date('2023-01-31'),
            },
            tags: { $in: ['tagId1', 'tagId2'] },
        });
    });
});
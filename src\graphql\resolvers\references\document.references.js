const Transaction = require("../../../models/Transaction");

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];
    
    const transactionReferences = await Transaction.find({ 'documents': id });
    if (hasReferences(transactionReferences)) {
        references.push('Transaction');
    }

    return references;
}

module.exports = findReferences;
const mongoose = require('mongoose');
const Guest = require('../../../../src/models/Guest');
const { User } = require('../../../../src/models/User');
const Party = require('../../../../src/models/Party');
const guestResolvers = require('../../../../src/graphql/resolvers/guest.resolver');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { validateReferences } = require('../../../../src/utils/validation.util');

jest.mock('../../../../src/models/Guest');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/Party');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/validation.util');

const guestIdSchema = {
    userId: { type: 'single', required: true, model: User },
    partyId: { type: 'single', required: true, model: Party }
};

describe('Guest Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Guest Field Resolvers', () => {
        describe('user', () => {
            it('should return user data successfully', async () => {
                const mockUser = { _id: 'userId', name: 'Test User' };
                User.findById.mockResolvedValue(mockUser);

                const parent = { user: 'userId' };
                const result = await guestResolvers.Guest.user(parent);

                expect(User.findById).toHaveBeenCalledWith('userId');
                expect(result).toEqual(mockUser);
            });

            it('should throw error when user fetch fails', async () => {
                const error = new Error('Database error');
                User.findById.mockRejectedValue(error);

                const parent = { user: 'userId' };
                await expect(guestResolvers.Guest.user(parent))
                    .rejects
                    .toThrow('Error getting user');
            });
        });

        describe('party', () => {
            it('should return party data successfully', async () => {
                const mockParty = { _id: 'partyId', name: 'Test Party' };
                Party.findById.mockResolvedValue(mockParty);

                const parent = { party: 'partyId' };
                const result = await guestResolvers.Guest.party(parent);

                expect(Party.findById).toHaveBeenCalledWith('partyId');
                expect(result).toEqual(mockParty);
            });

            it('should throw error when party fetch fails', async () => {
                const error = new Error('Database error');
                Party.findById.mockRejectedValue(error);

                const parent = { party: 'partyId' };
                await expect(guestResolvers.Guest.party(parent))
                    .rejects
                    .toThrow('Error getting party');
            });
        });
    });

    describe('Query', () => {
        describe('getGuestById', () => {
            it('should return cached guest if available', async () => {
                const mockGuest = { _id: 'guestId', name: 'Test Guest' };
                getByIdCache.mockResolvedValue(mockGuest);
                createResponse.mockReturnValue('successResponse');

                const result = await guestResolvers.Query.getGuestById(null, { id: 'guestId' });

                expect(getByIdCache).toHaveBeenCalledWith('guestId');
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestResponse',
                    'SUCCESS',
                    'Guest retrieved successfully from cache',
                    { result: { guest: mockGuest } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache guest if not in cache', async () => {
                const mockGuest = { _id: 'guestId', name: 'Test Guest' };
                getByIdCache.mockResolvedValue(null);
                Guest.findById.mockResolvedValue(mockGuest);
                createResponse.mockReturnValue('successResponse');

                const result = await guestResolvers.Query.getGuestById(null, { id: 'guestId' });

                expect(getByIdCache).toHaveBeenCalledWith('guestId');
                expect(Guest.findById).toHaveBeenCalledWith('guestId');
                expect(setCache).toHaveBeenCalledWith('guestId', mockGuest);
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestResponse',
                    'SUCCESS',
                    'Guest retrieved successfully',
                    { result: { guest: mockGuest } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when guest not found', async () => {
                getByIdCache.mockResolvedValue(null);
                Guest.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await guestResolvers.Query.getGuestById(null, { id: 'guestId' });

                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Guest not found',
                    { errors: [{ field: 'id', message: 'Guest not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors when retrieving guest', async () => {
                const id = 'guestId';
                const error = new Error('Database error');
                
                getByIdCache.mockResolvedValue(null);
                
                Guest.findById.mockRejectedValue(error);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving guest',
                    errors: [{ field: 'id', message: error.message }]
                });

                const result = await guestResolvers.Query.getGuestById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Guest.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Error retrieving guest',
                    {
                        errors: [{ field: 'id', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving guest',
                    errors: [{ field: 'id', message: error.message }]
                });
            });
        });

        describe('getGuests', () => {
            it('should return guests with pagination', async () => {
                const mockGuests = [{ _id: 'guest1' }, { _id: 'guest2' }];
                const mockPaginationInfo = { totalPages: 1, totalItems: 2 };

                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockResolvedValue(mockGuests)
                };
                Guest.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('successResponse');

                const result = await guestResolvers.Query.getGuests(null, { 
                    pagination: { limit: 10, skip: 0 } 
                });

                expect(getPaginationInfo).toHaveBeenCalledWith(Guest, {}, 10, 0);
                expect(Guest.find).toHaveBeenCalledWith({});
                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestsResponse',
                    'SUCCESS',
                    'Guests fetched successfully',
                    { result: { guests: mockGuests }, pagination: mockPaginationInfo }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors when getting guests', async () => {
                const error = new Error('Database error');
                Guest.find.mockImplementation(() => {
                    throw error;
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await guestResolvers.Query.getGuests(null, { 
                    pagination: { limit: 10, skip: 0 } 
                });

                expect(Guest.find).toHaveBeenCalledWith({});
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Error getting guests',
                    { errors: [{ field: 'getGuests', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });

    describe('Mutation', () => {
        describe('createGuest', () => {
            it('should create guest successfully', async () => {
                const input = { userId: 'userId', partyId: 'partyId' };
                validateReferences.mockResolvedValue(null);
                
                const mockGuest = {
                    _id: 'newId',
                    ...input,
                    save: jest.fn().mockResolvedValue({ _id: 'newId', ...input })
                };
                
                Guest.mockImplementation(() => mockGuest);
                createResponse.mockReturnValue('successResponse');

                const result = await guestResolvers.Mutation.createGuest(null, input);

                expect(validateReferences).toHaveBeenCalledWith(
                    input,
                    expect.any(Object),
                    'Guest'
                );
                expect(Guest).toHaveBeenCalledWith({ 
                    user: input.userId, 
                    party: input.partyId 
                });
                expect(mockGuest.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestResponse',
                    'SUCCESS',
                    'Guest created successfully',
                    { result: { guest: mockGuest } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if validation fails', async () => {
                const input = { userId: 'userId', partyId: 'partyId' };
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'userId', message: 'Invalid user ID' }]
                };
                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('errorResponse');

                const result = await guestResolvers.Mutation.createGuest(null, input);

                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when guest creation fails', async () => {
                const input = {
                    userId: 'testUserId',
                    partyId: 'testPartyId'
                };
                
                const error = new Error('Database error');
                
                validateReferences.mockResolvedValue(null);
                
                Guest.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating guest',
                    errors: [{ field: 'userId', message: error.message }]
                });

                const result = await guestResolvers.Mutation.createGuest(null, { 
                    userId: input.userId, 
                    partyId: input.partyId 
                });

                expect(validateReferences).toHaveBeenCalledWith(
                    { userId: input.userId, partyId: input.partyId },
                    guestIdSchema,
                    'Guest'
                );
                expect(Guest).toHaveBeenCalledWith({ 
                    user: input.userId, 
                    party: input.partyId 
                });
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Error creating guest',
                    { errors: [{ field: 'userId', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating guest',
                    errors: [{ field: 'userId', message: error.message }]
                });
            });
        });

        describe('deleteGuest', () => {
            it('should delete guest successfully', async () => {
                const mockGuest = { _id: 'guestId' };
                Guest.findByIdAndDelete.mockResolvedValue(mockGuest);
                createResponse.mockReturnValue('successResponse');

                const result = await guestResolvers.Mutation.deleteGuest(null, { id: 'guestId' });

                expect(Guest.findByIdAndDelete).toHaveBeenCalledWith('guestId');
                expect(clearCacheById).toHaveBeenCalledWith('guestId');
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestResponse',
                    'SUCCESS',
                    'Guest deleted successfully',
                    { result: { guest: mockGuest } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error when guest not found', async () => {
                Guest.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await guestResolvers.Mutation.deleteGuest(null, { id: 'guestId' });

                expect(Guest.findByIdAndDelete).toHaveBeenCalledWith('guestId');
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Guest not found',
                    { errors: [{ field: 'id', message: 'Guest not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when deletion fails', async () => {
                const id = 'guestId';
                const error = new Error('Database error');

                Guest.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await guestResolvers.Mutation.deleteGuest(null, { id });

                expect(Guest.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'GuestErrorResponse',
                    'FAILURE',
                    'Error deleting guest',
                    { errors: [{ field: 'id', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});

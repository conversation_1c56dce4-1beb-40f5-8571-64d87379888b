const { getByIdCache } = require('./cache.util');

class NotificationConfig {
    constructor() {
        this.ALL_CONFIGS_CACHE_KEY = 'all_notification_configs';
    }

    async getEventConfig(eventType) {
        try {
            const allConfigs = await getByIdCache(this.ALL_CONFIGS_CACHE_KEY);
            if (!allConfigs) {
                throw new Error('Notification configs not found in cache');
            }

            const template = allConfigs.find(t => t.eventType === eventType);
            if (!template) {
                throw new Error(`Event type "${eventType}" not found in configuration`);
            }

            // Ensure receivers is always an array
            const receivers = Array.isArray(template.config.receivers) 
                ? template.config.receivers 
                : [];

            return {
                channels: template.config.channels || {},
                templates: template.config.templates || {
                    subject: '',
                    message: ''
                },
                receivers: receivers,
                requiredData: template.config.requiredData || []
            };
        } catch (error) {
            console.error('Error getting notification config:', error);
            // Return a default config instead of throwing
            return {
                channels: {},
                templates: {
                    subject: '',
                    message: ''
                },
                receivers: [],
                requiredData: []
            };
        }
    }

    replaceWildcards(template, data) {
        if (!template) return '';
        let message = template;
        Object.entries(data || {}).forEach(([key, value]) => {
            const regex = new RegExp(`<${key}>`, 'g');
            message = message.replace(regex, value?.toString() || '');
        });
        return message;
    }

    async validateRequiredData(eventType, data) {
        try {
            const allConfigs = await getByIdCache(this.ALL_CONFIGS_CACHE_KEY);
            if (!allConfigs) {
                return false;
            }

            const template = allConfigs.find(t => t.eventType === eventType);
            if (!template || !template.config.requiredData) return false;
            
            return template.config.requiredData.every(field => 
                data && data.hasOwnProperty(field)
            );
        } catch (error) {
            console.error('Error validating required data:', error);
            return false;
        }
    }
}

module.exports = NotificationConfig;
const { gql } = require('graphql-tag');
const tempTypeDef = require('../../../../src/graphql/typedefs/temp.typedef');

describe('tempTypeDef', () => {
  it('should contain the Temp type definitions', () => {
    const expectedTypeDefs = gql`
      type TempResponse {
        message: String!
      }

      extend type Query {
        tempQuery: TempResponse!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(tempTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
const mongoose = require('mongoose');
const findReferences = require('../../../../../src/graphql/resolvers/references/task.references');
const PartyService = require('../../../../../src/models/PartyService');

jest.mock('../../../../../src/models/PartyService');

describe('Task References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        PartyService.find.mockResolvedValue([]);

        const result = await findReferences('taskId');

        expect(result).toEqual([]);
        expect(PartyService.find).toHaveBeenCalledWith({ tasks: 'taskId' });
    });

    it('should return PartyService when references exist', async () => {
        PartyService.find.mockResolvedValue([
            { _id: 'partyService1' },
            { _id: 'partyService2' }
        ]);

        const result = await findReferences('taskId');

        expect(result).toEqual(['PartyService']);
        expect(PartyService.find).toHaveBeenCalledWith({ tasks: 'taskId' });
    });

    it('should handle errors gracefully', async () => {
        PartyService.find.mockRejectedValue(new Error('Database error'));

        await expect(findReferences('taskId')).rejects.toThrow('Database error');
        expect(PartyService.find).toHaveBeenCalledWith({ tasks: 'taskId' });
    });
}); 
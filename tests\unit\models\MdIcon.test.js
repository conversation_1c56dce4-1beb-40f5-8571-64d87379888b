const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdIcon Model Test', () => {
    it('should create and save a MdIcon successfully', async () => {
        const validMdIcon = new MdIcon({
            name: 'Icon Name',
            iconSrc: '<iconSrc></iconSrc>',
        });

        const savedMdIcon = await validMdIcon.save();

        expect(savedMdIcon._id).toBeDefined();
        expect(savedMdIcon.name).toBe('Icon Name');
        expect(savedMdIcon.iconSrc).toBe('<iconSrc></iconSrc>');
    });

    it('should fail to create a MdIcon without required fields', async () => {
        const mdIcon = new MdIcon();

        let err;
        try {
            await mdIcon.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.iconSrc).toBeDefined();
    });
});
const { isValidObjectId } = require("../../../utils/validation.util");

const buildMdRoleQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }
        
        if (filter.name) {
            query.name = { $regex: new RegExp(filter.name, 'i') };
        }
    }

    return query;
};

module.exports = buildMdRoleQuery;
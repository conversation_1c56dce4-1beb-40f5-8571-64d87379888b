const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Document = require('../../../src/models/Document');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Document Model Test', () => {
    it('should create and save a document successfully', async () => {
        const validDocument = new Document({
            name: 'Test Document',
            documentType: 'RECEIPT',
            documentUrl: 'https://example.com/document.pdf',
            description: 'A test document',
            createdBy: new mongoose.Types.ObjectId()
        });

        const savedDocument = await validDocument.save();

        expect(savedDocument._id).toBeDefined();
        expect(savedDocument.name).toBe('Test Document');
        expect(savedDocument.documentType).toBe('RECEIPT');
        expect(savedDocument.documentUrl).toBe('https://example.com/document.pdf');
        expect(savedDocument.description).toBe('A test document');
        expect(savedDocument.createdBy).toBeDefined();
        expect(savedDocument.createdAt).toBeDefined();
        expect(savedDocument.updatedAt).toBeDefined();
    });

    it('should fail to create a document without required fields', async () => {
        const invalidDocument = new Document({
            name: 'Invalid Document'
        });

        let err;
        try {
            await invalidDocument.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.documentType).toBeDefined();
        expect(err.errors.documentUrl).toBeDefined();
        expect(err.errors.createdBy).toBeDefined();
    });

    it('should fail to create a document with an invalid documentType', async () => {
        const invalidDocument = new Document({
            name: 'Invalid Document',
            documentType: 'INVALID_TYPE',
            documentUrl: 'https://example.com/document.pdf',
            createdBy: new mongoose.Types.ObjectId()
        });

        let err;
        try {
            await invalidDocument.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.documentType).toBeDefined();
    });
});
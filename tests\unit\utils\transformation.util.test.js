const { transformIcon } = require('../../../src/utils/transformation.util');

describe('Transformation Utility Tests', () => {
    describe('transformIcon', () => {
        it('should transform a valid icon object', () => {
            const mockIcon = {
                _id: '123456789',
                name: 'Test Icon',
                iconSrc: '<svg>test</svg>',
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date('2024-01-02')
            };

            const result = transformIcon(mockIcon);

            expect(result).toEqual({
                _id: '123456789',
                id: '123456789',
                name: 'Test Icon',
                iconSrc: '<svg>test</svg>',
                createdAt: mockIcon.createdAt,
                updatedAt: mockIcon.updatedAt
            });
        });

        it('should handle missing optional fields', () => {
            const mockIcon = {
                _id: '123456789',
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date('2024-01-02')
            };

            const result = transformIcon(mockIcon);

            expect(result).toEqual({
                _id: '123456789',
                id: '123456789',
                name: '',
                iconSrc: '',
                createdAt: mockIcon.createdAt,
                updatedAt: mockIcon.updatedAt
            });
        });

        it('should return null for null input', () => {
            const result = transformIcon(null);
            expect(result).toBeNull();
        });

        it('should return undefined for undefined input', () => {
            const result = transformIcon(undefined);
            expect(result).toBeUndefined();
        });

        it('should return original icon object if _id is missing', () => {
            const mockIcon = {
                name: 'Test Icon',
                iconSrc: '<svg>test</svg>'
            };

            const result = transformIcon(mockIcon);
            expect(result).toBe(mockIcon);
        });

        it('should handle error and return original icon', () => {
            const mockIcon = {
                _id: {
                    toString: () => { throw new Error('Test error'); }
                }
            };

            console.error = jest.fn();
            const result = transformIcon(mockIcon);

            expect(console.error).toHaveBeenCalledWith(
                'Error transforming icon:',
                expect.any(Error)
            );
            expect(result).toBe(mockIcon);
        });
    });
});

const mdPartyTypeResolvers = require('../../../../src/graphql/resolvers/mdPartyType.resolver');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const buildMdPartyTypeQuery = require('../../../../src/graphql/resolvers/filters/mdPartyType.filter');
const MdPartyType = require('../../../../src/models/MdPartyType');
const MdVendorType = require('../../../../src/models/MdVendorType');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/models/MdPartyType');
jest.mock('../../../../src/models/MdVendorType');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdPartyType.filter');

describe('mdPartyTypeResolvers', () => {
    describe('MdPartyType', () => {
        describe('vendorTypes', () => {
            it('should return vendor types for a given mdPartyType', async () => {
                const mockVendorTypes = [
                    { _id: 'vendor1', name: 'Vendor 1' },
                    { _id: 'vendor2', name: 'Vendor 2' },
                ];
                const mockMdPartyType = {
                    vendorTypes: ['vendor1', 'vendor2'],
                };
    
                MdVendorType.find.mockResolvedValue(mockVendorTypes);
    
                const result = await mdPartyTypeResolvers.MdPartyType.vendorTypes(mockMdPartyType);
    
                expect(result).toEqual(mockVendorTypes);
                expect(MdVendorType.find).toHaveBeenCalledWith({ _id: { $in: mockMdPartyType.vendorTypes } });
            });
    
            it('should throw an error if there is a problem fetching vendor types', async () => {
                const mockMdPartyType = {
                    vendorTypes: ['vendor1', 'vendor2'],
                };
                const mockError = new Error('Database error');
    
                MdVendorType.find.mockRejectedValue(mockError);
    
                await expect(mdPartyTypeResolvers.MdPartyType.vendorTypes(mockMdPartyType)).rejects.toThrow('Error getting vendor types');
                expect(MdVendorType.find).toHaveBeenCalledWith({ _id: { $in: mockMdPartyType.vendorTypes } });
            });
        });
    });
    describe('Query', () => {
        describe('getMdPartyTypeById', () => {
            it('should return MdPartyType if found', async () => {
                const mdPartyType = { id: '1', name: 'Party Type Name' };
                getByIdCache.mockResolvedValue(null);
                MdPartyType.findById.mockResolvedValue(mdPartyType);
                setCache.mockResolvedValue();

                const result = await mdPartyTypeResolvers.Query.getMdPartyTypeById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(MdPartyType.findById).toHaveBeenCalledWith('1');
                expect(setCache).toHaveBeenCalledWith('1', mdPartyType);
                expect(result).toEqual(createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType fetched successfully', { result: { mdPartyType } }));
            });

            it('should return error if MdPartyType not found', async () => {
                getByIdCache.mockResolvedValue(null);
                MdPartyType.findById.mockResolvedValue(null);

                const result = await mdPartyTypeResolvers.Query.getMdPartyTypeById(null, { id: '1' });

                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType not found', { errors: [{ field: 'getMdPartyTypeById', message: 'MdPartyType not found' }] }));
            });

            it('should return error on exception', async () => {
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);

                const result = await mdPartyTypeResolvers.Query.getMdPartyTypeById(null, { id: '1' });

                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error getting MdPartyType', { errors: [{ field: 'getMdPartyTypeById', message: error.message }] }));
            });
        });

        describe('getMdPartyTypes', () => {
            it('should use default pagination when pagination fields are missing', async () => {
                const mdPartyTypes = [{ id: '1', name: 'Party Type Name' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdPartyType.find.mockResolvedValue(mdPartyTypes);
            
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { filter: {} });
            
                expect(result).toEqual(createResponse('MdPartyTypesResponse', 'SUCCESS', 'MdPartyTypes fetched successfully', { result: { mdPartyTypes }, pagination: paginationInfo }));
                expect(MdPartyType.find).toHaveBeenCalledWith(expect.anything());
            });

            it('should use default filter when filter is not provided', async () => {
                const pagination = { limit: 10, skip: 0 };
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                const mdPartyTypes = [{ id: '1', name: 'Party Type Name' }];
                
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdPartyType.find.mockResolvedValue(mdPartyTypes);
            
                await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { pagination });
            
                expect(buildMdPartyTypeQuery).toHaveBeenCalledWith({});
            });

            it('should return an error when invalid filter values are provided', async () => {
                const error = new Error('Invalid filter values');
                buildMdPartyTypeQuery.mockImplementation(() => {
                    throw error;
                });
            
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { filter: { invalidField: 'invalid' }, pagination: { limit: 10, skip: 0 } });
            
                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error getting MdPartyTypes', { errors: [{ field: 'getMdPartyTypes', message: error.message }] }));
            });

            it('should handle missing or invalid filter by defaulting to an empty filter', async () => {
                const mdPartyTypes = [{ id: '1', name: 'Party Type Name' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdPartyType.find.mockResolvedValue(mdPartyTypes);
            
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { pagination: { limit: 10, skip: 0 } });
            
                expect(result).toEqual(createResponse('MdPartyTypesResponse', 'SUCCESS', 'MdPartyTypes fetched successfully', { result: { mdPartyTypes }, pagination: paginationInfo }));
                expect(buildMdPartyTypeQuery).toHaveBeenCalledWith({});
            });

            it('should return MdPartyTypes if found', async () => {
                const mdPartyTypes = [{ id: '1', name: 'Party Type Name' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdPartyType.find.mockResolvedValue(mdPartyTypes);
        
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
        
                expect(result).toEqual(createResponse('MdPartyTypesResponse', 'SUCCESS', 'MdPartyTypes fetched successfully', { result: { mdPartyTypes }, pagination: paginationInfo }));
            });
            
            it('should return error if no MdPartyTypes found', async () => {
                const mdPartyTypes = [];
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdPartyType.find.mockResolvedValue(mdPartyTypes);
        
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
        
                expect(result).toEqual(createResponse('MdPartyTypesResponse', 'FAILURE', 'No MdPartyTypes found', { result: { mdPartyTypes }, pagination: paginationInfo }));
            });

            it('should return error on exception', async () => {
                const error = new Error('Database error');
                buildMdPartyTypeQuery.mockReturnValue({});
                getPaginationInfo.mockRejectedValue(error);
            
                const result = await mdPartyTypeResolvers.Query.getMdPartyTypes(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
            
                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error getting MdPartyTypes', { errors: [{ field: 'getMdPartyTypes', message: error.message }] }));
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdPartyType', () => {
            it('should create and return MdPartyType', async () => {
                const mdPartyType = { id: '1', name: 'Party Type Name' };
                MdPartyType.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(mdPartyType)
                }));

                const result = await mdPartyTypeResolvers.Mutation.createMdPartyType(null, { input: mdPartyType });

                expect(result).toEqual(createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType created successfully', { result: { mdPartyType } }));
            });

            it('should validate input before creating MdPartyType', async () => {
                const invalidInput = { name: '' };
                
                const result = await mdPartyTypeResolvers.Mutation.createMdPartyType(null, { input: invalidInput });
                
                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Invalid input', { errors: [{ field: 'name', message: 'Name is required' }] }));
                expect(MdPartyType.prototype.save).not.toHaveBeenCalled();
            });

            it('should return an error when required fields are missing in createMdPartyType', async () => {
                const result = await mdPartyTypeResolvers.Mutation.createMdPartyType(null, { input: { name: '' } });
            
                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Invalid input', { errors: [{ field: 'name', message: 'Name is required' }] }));
            });

            it('should return error on exception', async () => {
                const error = new Error('Database error');
                MdPartyType.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                const result = await mdPartyTypeResolvers.Mutation.createMdPartyType(null, { input: {} });

                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error creating MdPartyType', { errors: [{ field: 'createMdPartyType', message: error.message }] }));
            });
        });

        describe('updateMdPartyType', () => {
            it('should update and return MdPartyType', async () => {
                const mdPartyType = { id: '1', name: 'Updated Party Type Name' };
                MdPartyType.findByIdAndUpdate.mockResolvedValue(mdPartyType);
                clearCacheById.mockResolvedValue();

                const result = await mdPartyTypeResolvers.Mutation.updateMdPartyType(null, { id: '1', input: { name: 'Updated Party Type Name' } });

                expect(result).toEqual(createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType updated successfully', { result: { mdPartyType } }));
            });

            it('should return error if MdPartyType not found', async () => {
                MdPartyType.findByIdAndUpdate.mockResolvedValue(null);

                const result = await mdPartyTypeResolvers.Mutation.updateMdPartyType(null, { id: '1', input: { name: 'Updated Party Type Name' } });

                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType not found', { errors: [{ field: 'updateMdPartyType', message: 'MdPartyType not found' }] }));
            });

            it('should return error on exception', async () => {
                const error = new Error('Database error');
                MdPartyType.findByIdAndUpdate.mockRejectedValue(error);

                const result = await mdPartyTypeResolvers.Mutation.updateMdPartyType(null, { id: '1', input: { name: 'Updated Party Type Name' } });

                expect(result).toEqual(createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error updating MdPartyType', { errors: [{ field: 'updateMdPartyType', message: error.message }] }));
            });
        });

        describe('deleteMdPartyType', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should check for references before deletion', async () => {
                const id = 'partyTypeId';
                const references = ['Party', 'Event'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartyTypeResolvers.Mutation.deleteMdPartyType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartyType');
                expect(MdPartyType.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartyTypeErrorResponse',
                    'FAILURE',
                    'MdPartyType cannot be deleted',
                    {
                        errors: [{ 
                            field: 'deleteMdPartyType', 
                            message: 'MdPartyType cannot be deleted as it is being used in: Party, Event' 
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should delete MdPartyType when no references exist', async () => {
                const id = 'partyTypeId';
                const mockPartyType = { _id: id, name: 'Test Party Type' };

                findReferences.mockResolvedValue([]);
                MdPartyType.findByIdAndDelete.mockResolvedValue(mockPartyType);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await mdPartyTypeResolvers.Mutation.deleteMdPartyType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartyType');
                expect(MdPartyType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartyTypeResponse',
                    'SUCCESS',
                    'MdPartyType deleted successfully',
                    { result: { mdPartyType: mockPartyType } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if MdPartyType not found', async () => {
                const id = 'nonexistentId';

                findReferences.mockResolvedValue([]);
                MdPartyType.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartyTypeResolvers.Mutation.deleteMdPartyType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartyType');
                expect(MdPartyType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartyTypeErrorResponse',
                    'FAILURE',
                    'MdPartyType not found',
                    { errors: [{ field: 'deleteMdPartyType', message: 'MdPartyType not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors during deletion', async () => {
                const id = 'partyTypeId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdPartyType.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartyTypeResolvers.Mutation.deleteMdPartyType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPartyType');
                expect(MdPartyType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartyTypeErrorResponse',
                    'FAILURE',
                    'Error deleting party type',
                    { errors: [{ field: 'deleteMdPartyType', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});
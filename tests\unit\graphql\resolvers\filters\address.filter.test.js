const buildAddressQuery = require('../../../../../src/graphql/resolvers/filters/address.filter');

describe('buildAddressQuery', () => {
    it('should return empty query object when no filters provided', () => {
        const result = buildAddressQuery();
        expect(result).toEqual({});
    });

    it('should build query with addressType filter', () => {
        const filters = { addressType: 'HOME' };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            addressType: 'HOME'
        });
    });

    it('should build query with city filter', () => {
        const filters = { city: 'New York' };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            city: { $regex: 'New York', $options: 'i' }
        });
    });

    it('should build query with state filter', () => {
        const filters = { state: 'California' };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            state: { $regex: 'California', $options: 'i' }
        });
    });

    it('should build query with country filter', () => {
        const filters = { country: 'USA' };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            country: { $regex: 'USA', $options: 'i' }
        });
    });

    it('should build query with isPrimary filter', () => {
        const filters = { isPrimary: true };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            isPrimary: true
        });
    });

    it('should build query with multiple filters', () => {
        const filters = {
            addressType: 'WORK',
            city: 'San Francisco',
            state: 'California',
            country: 'USA',
            isPrimary: false
        };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            addressType: 'WORK',
            city: { $regex: 'San Francisco', $options: 'i' },
            state: { $regex: 'California', $options: 'i' },
            country: { $regex: 'USA', $options: 'i' },
            isPrimary: false
        });
    });

    it('should ignore undefined filters', () => {
        const filters = {
            addressType: undefined,
            city: 'Seattle',
            state: undefined,
            country: 'USA',
            isPrimary: undefined
        };
        const result = buildAddressQuery(filters);
        expect(result).toEqual({
            city: { $regex: 'Seattle', $options: 'i' },
            country: { $regex: 'USA', $options: 'i' }
        });
    });
});
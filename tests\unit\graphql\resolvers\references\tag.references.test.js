const findReferences = require('../../../../../src/graphql/resolvers/references/tag.references');
const Media = require('../../../../../src/models/Media');

jest.mock('../../../../../src/models/Media');

describe('findReferences', () => {
    it('should return "Media" when tag references are found', async () => {
        const id = 'someTagId';
        const mockTagReferences = [{ _id: '1', tags: [id] }, { _id: '2', tags: [id] }];
        
        Media.find.mockResolvedValue(mockTagReferences);

        const result = await findReferences(id);

        expect(result).toEqual(['Media']);
        expect(Media.find).toHaveBeenCalledWith({ tags: { $in: [id] } });
    });

    it('should return an empty array when no tag references are found', async () => {
        const id = 'someTagId';
        Media.find.mockResolvedValue([]);

        const result = await findReferences(id);

        expect(result).toEqual([]);
        expect(Media.find).toHaveBeenCalledWith({ tags: { $in: [id] } });
    });
});
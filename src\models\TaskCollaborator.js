const { model, Schema } = require('mongoose');

const taskCollaboratorSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    task: { type: Schema.Types.ObjectId, ref: 'Task', required: true }
}, { 
    timestamps: true,
    collection: 'task_collaborators',
    toJSON: { 
        virtuals: true,
        transform: function(doc, ret) {
            ret.assignedOn = ret.createdAt;
            return ret;
        }
    }
});

taskCollaboratorSchema.index({ user: 1, task: 1 }, { unique: true });

taskCollaboratorSchema.virtual('assignedOn').get(function() {
    return this.createdAt;
});

const TaskCollaborator = model('TaskCollaborator', taskCollaboratorSchema);

module.exports = TaskCollaborator; 
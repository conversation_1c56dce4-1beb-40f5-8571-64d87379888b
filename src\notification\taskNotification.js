const notificationProducer = require("../utils/notificationProducer");
const TaskCollaborator = require("../models/TaskCollaborator");
const Party = require("../models/Party");

async function notifyTaskUpdated(task, party, contextUserId) {
    try {
        const collaborators = await TaskCollaborator.find({ task: task._id }).populate('user');
        
        const specificRecipients = collaborators.map(collab => ({
            userId: collab.user._id,
            email: collab.user.email,
            phone: collab.user.phone,
            role: 'Collaborator'
        }));

        await notificationProducer.sendNotificationToSpecificRecipients('Task Updated', {
            TaskName: task.title,
            EventName: party.name,
            senderId: contextUserId,
            entityType: 'task',
            entityId: task._id
        }, specificRecipients);
    } catch (error) {
        console.error('Error sending task update notification:', error);
    }
}

async function notifyTaskCancelled(task, party, contextUserId) {
    try {
        const collaborators = await TaskCollaborator.find({ task: task._id }).populate('user');
        
        const specificRecipients = collaborators.map(collab => ({
            userId: collab.user._id,
            email: collab.user.email,
            phone: collab.user.phone,
            role: 'Collaborator'
        }));

        await notificationProducer.sendNotificationToSpecificRecipients('Task Cancelled', {
            TaskName: task.title,
            EventName: party.name,
            senderId: contextUserId,
            entityType: 'task',
            entityId: task._id
        }, specificRecipients);
    } catch (error) {
        console.error('Error sending task cancellation notification:', error);
    }
};

async function notifyTaskCollaboratorAdded(task, party, newCollaborators, contextUserId) {
    try {
        const specificRecipients = newCollaborators.map(collab => ({
            userId: collab.user._id,
            email: collab.user.email,
            phone: collab.user.phone,
            role: 'Collaborator'
        }));

        await notificationProducer.sendNotificationToSpecificRecipients('Added as Task Collaborator', {
            TaskName: task.title,
            EventName: party.name,
            senderId: contextUserId,
            entityType: 'task',
            entityId: task._id
        }, specificRecipients);
    } catch (error) {
        console.error('Error sending task collaborator added notification:', error);
    }
}

async function notifyTaskAttachmentAdded(task, party, contextUserId) {
    try {
        const collaborators = await TaskCollaborator.find({ task: task._id }).populate('user');
        
        const specificRecipients = collaborators.map(collab => ({
            userId: collab.user._id,
            email: collab.user.email,
            phone: collab.user.phone,
            role: 'Collaborator'
        }));

        await notificationProducer.sendNotificationToSpecificRecipients('Task Attachment Added', {
            TaskName: task.title,
            EventName: party.name,
            senderId: contextUserId,
            entityType: 'task',
            entityId: task._id
        }, specificRecipients);
    } catch (error) {
        console.error('Error sending task attachment added notification:', error);
    }
}

module.exports = {
    notifyTaskUpdated,
    notifyTaskCancelled,
    notifyTaskCollaboratorAdded,
    notifyTaskAttachmentAdded
};

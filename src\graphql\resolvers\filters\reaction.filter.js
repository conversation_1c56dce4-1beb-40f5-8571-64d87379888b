const { isValidObjectId } = require("../../../utils/validation.util");
const buildUserQuery = require("./user.filter");
const User = require("../../../models/User");

const buildReactionQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.user) {
            if (typeof filter.user === 'string') {
                if (isValidObjectId(filter.user)) {
                    query.user = filter.user;
                } else {
                    throw new Error('Invalid user ID provided');
                }
            } else if (typeof filter.user === 'object') {
                const userQuery = await buildUserQuery(filter.user);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.user = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.user = { $in: [] };
                    }
                }
            }
        }

        if (filter.createdAt) {
            query.createdAt = {};
            if (filter.createdAt.start) {
                query.createdAt.$gte = filter.createdAt.start;
            }
            if (filter.createdAt.end) {
                query.createdAt.$lte = filter.createdAt.end;
            }
        }

        if (filter.updatedAt) {
            query.updatedAt = {};
            if (filter.updatedAt.start) {
                query.updatedAt.$gte = filter.updatedAt.start;
            }
            if (filter.updatedAt.end) {
                query.updatedAt.$lte = filter.updatedAt.end;
            }
        }

        if (filter.reactionUnicode) {
            query.reactionUnicode = filter.reactionUnicode;
        }
    }

    return query;
};

module.exports = buildReactionQuery; 
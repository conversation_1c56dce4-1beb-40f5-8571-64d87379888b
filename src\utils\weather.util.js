require('dotenv').config();

const getWeatherForecast = async (location, days = 1) => {
    const API_KEY = process.env.WEATHER_API_KEY;
    const BASE_URL = process.env.WEATHER_API_BASE_URL;

    if (!API_KEY) {
        throw new Error('Weather API key is not configured');
    }

    if (!BASE_URL) {
        throw new Error('Weather API base URL is not configured');
    }

    if (!location) {
        throw new Error('Location parameter is required');
    }

    try {
        const url = new URL(`${BASE_URL}/forecast.json`);
        url.searchParams.append('key', API_KEY);
        url.searchParams.append('q', location);
        url.searchParams.append('days', days.toString());

        const response = await fetch(url);
        
        if (!response.ok) {
            const errorData = await response.text();
            console.error('Weather API Error:', {
                status: response.status,
                statusText: response.statusText,
                errorData
            });
            throw new Error(`Weather API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data?.forecast?.forecastday?.[0]?.day) {
            throw new Error('Invalid weather data format received');
        }

        return data;
    } catch (error) {
        console.error('Error fetching weather data:', error);
        if (error.message === 'Network error') {
            throw new Error('Failed to fetch weather data');
        }
        throw error;
    }
};

module.exports = {
    getWeatherForecast
};

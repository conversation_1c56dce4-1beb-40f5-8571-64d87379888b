const mongoose = require('mongoose');
const PartyService = require('../../../../../src/models/PartyService');
const findReferences = require('../../../../../src/graphql/resolvers/references/transaction.references');

jest.mock('../../../../../src/models/PartyService');

describe('Transaction References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        PartyService.find.mockResolvedValue([]);

        const result = await findReferences('transactionId');

        expect(result).toEqual([]);
        expect(PartyService.find).toHaveBeenCalledWith({ transactions: 'transactionId' });
    });

    it('should return PartyService when references exist', async () => {
        const mockPartyServices = [
            { _id: new mongoose.Types.ObjectId() },
            { _id: new mongoose.Types.ObjectId() }
        ];
        
        PartyService.find.mockResolvedValue(mockPartyServices);

        const result = await findReferences('transactionId');

        expect(result).toEqual(['PartyService']);
        expect(PartyService.find).toHaveBeenCalledWith({ transactions: 'transactionId' });
    });

    it('should handle errors during reference search', async () => {
        PartyService.find.mockRejectedValue(new Error('Database error'));

        await expect(findReferences('transactionId')).rejects.toThrow('Database error');
        expect(PartyService.find).toHaveBeenCalledWith({ transactions: 'transactionId' });
    });
}); 
const mongoose = require('mongoose');
const buildMdReactionQuery = require('../../../../../src/graphql/resolvers/filters/mdReaction.filter');
const buildMdIconQuery = require('../../../../../src/graphql/resolvers/filters/mdIcon.filter');
const MdIcon = require('../../../../../src/models/MdIcon');

jest.mock('../../../../../src/utils/validation.util', () => ({
    isValidObjectId: jest.fn()
}));
jest.mock('../../../../../src/graphql/resolvers/filters/mdIcon.filter');
jest.mock('../../../../../src/models/MdIcon');

const { isValidObjectId } = require('../../../../../src/utils/validation.util');

describe('buildMdReactionQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter provided', async () => {
        const query = await buildMdReactionQuery();
        expect(query).toEqual({});
    });

    describe('id filter', () => {
        it('should add valid id to query', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { id: 'validId123' };
            
            const query = await buildMdReactionQuery(filter);
            
            expect(query).toEqual({ _id: 'validId123' });
            expect(isValidObjectId).toHaveBeenCalledWith('validId123');
        });

        it('should throw error for invalid id', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { id: 'invalidId' };
            
            await expect(buildMdReactionQuery(filter))
                .rejects
                .toThrow('Invalid id provided');
        });
    });

    describe('name filter', () => {
        it('should add name regex to query', async () => {
            const filter = { name: 'test' };
            
            const query = await buildMdReactionQuery(filter);
            
            expect(query).toEqual({
                name: { $regex: 'test', $options: 'i' }
            });
        });
    });

    describe('active filter', () => {
        it('should add active status to query', async () => {
            const filter = { active: true };
            
            const query = await buildMdReactionQuery(filter);
            
            expect(query).toEqual({ active: true });
        });
    });

    describe('icon filter', () => {
        it('should handle valid icon ID', async () => {
            isValidObjectId.mockReturnValue(true);
            const filter = { icon: 'validIconId' };
            
            const query = await buildMdReactionQuery(filter);
            
            expect(query).toEqual({ icon: 'validIconId' });
        });

        it('should throw error for invalid icon ID', async () => {
            isValidObjectId.mockReturnValue(false);
            const filter = { icon: 'invalidIconId' };
            
            await expect(buildMdReactionQuery(filter))
                .rejects
                .toThrow('Invalid icon ID provided');
        });

        it('should handle icon object filter with matching icons', async () => {
            const iconFilter = { name: 'testIcon' };
            const iconQuery = { name: { $regex: 'testIcon', $options: 'i' } };
            const matchingIcons = [{ _id: 'icon1' }, { _id: 'icon2' }];

            buildMdIconQuery.mockResolvedValue(iconQuery);
            MdIcon.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingIcons)
            });

            const filter = { icon: iconFilter };
            const query = await buildMdReactionQuery(filter);

            expect(buildMdIconQuery).toHaveBeenCalledWith(iconFilter);
            expect(MdIcon.find).toHaveBeenCalledWith(iconQuery);
            expect(query).toEqual({
                icon: { $in: ['icon1', 'icon2'] }
            });
        });

        it('should handle icon object filter with no matching icons', async () => {
            const iconFilter = { name: 'nonexistent' };
            const iconQuery = { name: { $regex: 'nonexistent', $options: 'i' } };

            buildMdIconQuery.mockResolvedValue(iconQuery);
            MdIcon.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });

            const filter = { icon: iconFilter };
            const query = await buildMdReactionQuery(filter);

            expect(buildMdIconQuery).toHaveBeenCalledWith(iconFilter);
            expect(MdIcon.find).toHaveBeenCalledWith(iconQuery);
            expect(query).toEqual({
                icon: { $in: [] }
            });
        });

        it('should handle empty icon query', async () => {
            buildMdIconQuery.mockResolvedValue({});
            
            const filter = { icon: {} };
            const query = await buildMdReactionQuery(filter);
            
            expect(query).toEqual({});
        });
    });

    it('should combine multiple filters', async () => {
        isValidObjectId.mockReturnValue(true);
        const filter = {
            name: 'test',
            active: true,
            icon: 'validIconId'
        };
        
        const query = await buildMdReactionQuery(filter);
        
        expect(query).toEqual({
            name: { $regex: 'test', $options: 'i' },
            active: true,
            icon: 'validIconId'
        });
    });
}); 
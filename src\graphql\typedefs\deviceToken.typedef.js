const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

const deviceTokenTypeDefs = gql`
  ${sharedTypeDef}

  enum DevicePlatform {
    IOS
    ANDROID
  }

  type DeviceToken {
    id: ID!
    user: User!
    deviceId: String!
    token: String!
    platform: DevicePlatform!
  }

  type DeviceTokenWrapper {
    deviceToken: DeviceToken!
  }

  type DeviceTokenResponse implements Response{
    status: ResponseStatus!
    message: String!
    result: DeviceTokenWrapper!
  }

  type DeviceTokenErrorResponse implements Response{
    status: ResponseStatus!
    message: String!
    errors: [Error!]!
  }

  union DeviceTokenResult = DeviceTokenResponse | DeviceTokenErrorResponse

  input DeviceTokenInput {  
    token: String!
    deviceId: String!
    platform: DevicePlatform!
  }

  type Mutation {
    updateDeviceToken(input: DeviceTokenInput!): DeviceTokenResult!
    deleteDeviceToken(token: String!): DeviceTokenResult!
  }
`;

module.exports = deviceTokenTypeDefs;
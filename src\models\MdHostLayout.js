const { model, Schema } = require('mongoose');

const navBarSchema = new Schema({
    home: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    planning: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    guests: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    apps: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    messages: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
}, { _id: false });

const mdHostLayoutSchema = new Schema({
    navBar: { type: navBarSchema, required: true },
}, { timestamps: true, collection: 'md_host_layouts' });

const MdHostLayout = model('MdHostLayout', mdHostLayoutSchema);

module.exports = MdHostLayout;
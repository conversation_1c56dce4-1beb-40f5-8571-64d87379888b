const { gql } = require('graphql-tag');
const vendorServiceTypeDef = require('../../../../src/graphql/typedefs/vendorService.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('vendorServiceTypeDef', () => {
    it('should contain the VendorService type definitions', () => {
        const expectedTypeDefs = gql`
        ${sharedTypeDef}

        type VendorService {
            id: ID!
            vendorType: MdVendorType!
            title: String!
            description: String
            features: [MdFeature!]
            priceRange: String!
            specialities: String
            media: [Media!]
        }

        input VendorServiceFilterInput {
            id: String
            title: String
            priceRange: String
        }

        type VendorServiceWrapper {
            vendorService: VendorService!
        }

        type VendorServicesWrapper {
            vendorServices: [VendorService]!
        }

        type VendorServiceResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: VendorServiceWrapper!
        }

        type VendorServicesResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: VendorServicesWrapper!
            pagination: PaginationInfo!
        }

        type VendorServiceErrorResponse implements Response {
            status: ResponseStatus!
            message: String!
            errors: [Error!]!
        }

        union VendorServiceResult = VendorServiceResponse | VendorServiceErrorResponse
        union VendorServicesResult = VendorServicesResponse | VendorServiceErrorResponse

        type Query {
            getVendorServiceById(id: ID!): VendorServiceResult!
            getVendorServices(filter: VendorServiceFilterInput, pagination: PaginationInput): VendorServicesResult!
        }

        input VendorServiceInput {
            vendorType: ID!
            title: String!
            description: String
            features: [ID!]
            priceRange: String!
            specialities: String
            media: [ID!]
        }

        input VendorServiceUpdateInput {
            vendorType: ID
            title: String
            description: String
            features: [ID!]
            priceRange: String
            specialities: String
            media: [ID!]
        }

        type Mutation {
            createVendorService(input: VendorServiceInput!): VendorServiceResult!
            updateVendorService(id: ID!, input: VendorServiceUpdateInput!): VendorServiceResult!
            deleteVendorService(id: ID!): VendorServiceResult!
        }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(vendorServiceTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});
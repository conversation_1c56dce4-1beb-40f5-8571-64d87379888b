const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Media = require('../../../src/models/Media');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Media Model Test', () => {
    it('should create and save a Media successfully', async () => {
        const validMedia = new Media({
            url: 'http://example.com/media.mp4',
            title: 'Sample Media',
            description: 'This is a sample media description',
            tags: []
        });

        const savedMedia = await validMedia.save();

        expect(savedMedia._id).toBeDefined();
        expect(savedMedia.url).toBe('http://example.com/media.mp4');
        expect(savedMedia.title).toBe('Sample Media');
        expect(savedMedia.description).toBe('This is a sample media description');
        expect(savedMedia.tags).toEqual([]);
        expect(savedMedia.uploadedAt).toBeDefined();
        expect(savedMedia.createdAt).toBeDefined();
        expect(savedMedia.updatedAt).toBeDefined();
    });

    it('should fail to create a Media without required fields', async () => {
        const mediaWithoutRequiredFields = new Media();

        let err;
        try {
            await mediaWithoutRequiredFields.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.url).toBeDefined();
        expect(err.errors.title).toBeDefined();
    });
});
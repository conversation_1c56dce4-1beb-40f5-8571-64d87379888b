const { BlobServiceClient } = require("@azure/storage-blob");
const { v4: uuidv4 } = require("uuid");

const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);

const getContainerClient = (containerName = 'documents') => {
    return blobServiceClient.getContainerClient(containerName);
};

const uploadFileToBlob = async (file, containerName = 'documents') => {
    try {
        console.log('Received file:', file);

        if (file === null || file === undefined) {
            throw new Error('No file provided');
        }

        if (typeof file !== 'object' || Array.isArray(file)) {
            throw new Error('Invalid file format - file must be an object');
        }

        const containerClient = getContainerClient(containerName);

        let fileData;
        if (file instanceof Promise) {
            try {
                fileData = await file;
                console.log('Resolved Promise data:', fileData);
            } catch (error) {
                console.error('Error resolving file promise:', error);
                throw new Error('Failed to process uploaded file');
            }
        } else if (file.promise) {
            try {
                fileData = await file.promise;
                console.log('Resolved file.promise data:', fileData);
            } catch (error) {
                console.error('Error resolving file.promise:', error);
                throw new Error('Failed to process uploaded file');
            }
        } else {
            fileData = file;
        }

        if (!fileData || typeof fileData !== 'object') {
            console.error('Invalid file data:', fileData);
            throw new Error('Invalid file format - file must be an object');
        }

        const { createReadStream, filename, mimetype } = fileData;

        const validationErrors = [];
        if (!createReadStream) {
            validationErrors.push('createReadStream is missing');
        } else if (typeof createReadStream !== 'function') {
            validationErrors.push('createReadStream must be a function');
        }
        
        if (!filename) {
            validationErrors.push('filename is missing');
        } else if (typeof filename !== 'string') {
            validationErrors.push('filename must be a string');
        }
        
        if (!mimetype) {
            validationErrors.push('mimetype is missing');
        } else if (typeof mimetype !== 'string') {
            validationErrors.push('mimetype must be a string');
        }

        if (validationErrors.length > 0) {
            console.error('Validation errors:', validationErrors);
            throw new Error(`Invalid file format: ${validationErrors.join(', ')}`);
        }

        console.log('File validation passed:', { filename, mimetype });

        const stream = createReadStream();
        const extension = filename.split('.').pop();
        const newFilename = `${uuidv4()}.${extension}`;

        const blockBlobClient = containerClient.getBlockBlobClient(newFilename);

        await blockBlobClient.uploadStream(stream);
        await blockBlobClient.setHTTPHeaders({
            blobContentType: mimetype
        });

        return {
            url: blockBlobClient.url,
            key: newFilename
        };
    } catch (error) {
        console.error('Error details:', error);
        throw error;
    }
};

const deleteFileFromBlob = async (key, containerName = 'documents') => {
    try {
        const containerClient = getContainerClient(containerName);
        const blockBlobClient = containerClient.getBlockBlobClient(key);
        await blockBlobClient.delete();
        return true;
    } catch (error) {
        console.error('Error deleting file:', error);
        throw new Error('Error deleting file');
    }
};

const uploadFilesToBlob = async (files, containerName = 'documents') => {
    try {
        if (!files) {
            throw new Error('No files provided');
        }

        const containerClient = getContainerClient(containerName);
        const fileArray = Array.isArray(files) ? files : [files];
        
        const uploadResults = await Promise.all(
            fileArray.map(async (file) => {
                let fileData;
                try {
                    if (file instanceof Promise) {
                        fileData = await file;
                    } else if (file.promise) {
                        fileData = await file.promise;
                    } else {
                        fileData = file;
                    }

                    if (!fileData || typeof fileData !== 'object') {
                        throw new Error('Invalid file format - file must be an object');
                    }

                    const { createReadStream, filename, mimetype } = fileData;

                    if (!createReadStream || typeof createReadStream !== 'function') {
                        throw new Error('createReadStream is missing or invalid');
                    }
                    if (!filename || typeof filename !== 'string') {
                        throw new Error('filename is missing or invalid');
                    }
                    if (!mimetype || typeof mimetype !== 'string') {
                        throw new Error('mimetype is missing or invalid');
                    }

                    const stream = createReadStream();
                    const extension = filename.split('.').pop();
                    const newFilename = `${uuidv4()}.${extension}`;
                    const blockBlobClient = containerClient.getBlockBlobClient(newFilename);

                    await blockBlobClient.uploadStream(stream);
                    await blockBlobClient.setHTTPHeaders({
                        blobContentType: mimetype
                    });

                    return {
                        url: blockBlobClient.url,
                        key: newFilename,
                        originalName: filename
                    };
                } catch (error) {
                    return {
                        error: true,
                        filename: (fileData && fileData.filename) ? fileData.filename : 'unknown',
                        message: error.message
                    };
                }
            })
        );

        const successful = uploadResults.filter(result => !result.error);
        const failures = uploadResults.filter(result => result.error);

        return {
            successful,
            failures,
            totalProcessed: uploadResults.length,
            successCount: successful.length,
            failureCount: failures.length
        };
    } catch (error) {
        console.error('Error in bulk upload:', error);
        throw new Error(`Error processing files: ${error.message}`);
    }
};

module.exports = { 
    uploadFileToBlob, 
    deleteFileFromBlob,
    uploadFilesToBlob,
    getContainerClient
};

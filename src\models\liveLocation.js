const mongoose = require('mongoose');
const { Schema, model } = mongoose;
const { Client } = require('@googlemaps/google-maps-services-js');

const liveLocationSchema = new Schema({
    locationId: { type: String, required: false, default: null }, // Google Place ID for destination
    coordinates: {
        latitude: {
            type: Number,
            required: false,
            default: null
        },
        longitude: {
            type: Number,
            required: false,
            default: null
        }
    },
    allowLiveTracking: { type: String, enum: ['ALLOW', 'DISALLOW', 'PENDING'], required: true, default: 'PENDING' },
    ETA: { type: Date, required: false, default: null },
    guestId: { type: Schema.Types.ObjectId, ref: 'Guest', required: true },
}, { timestamps: true, collection: 'live_locations' });

// Initialize Google Maps client
const googleMapsClient = new Client({});

liveLocationSchema.methods.calculateETA = async function(destPlaceId) {
    if (!destPlaceId) {
        throw new Error('Venue location not set - ETA cannot be calculated');
    }

    try {
        let origins = [];
        
        if (this.coordinates && this.coordinates.latitude && this.coordinates.longitude) {
            origins = [`${this.coordinates.latitude},${this.coordinates.longitude}`];
        } else if (this.locationId) {
            origins = [`place_id:${this.locationId}`];
        } else {
            throw new Error('No location data available for guest (neither coordinates nor placeId)');
        }

        const response = await googleMapsClient.distancematrix({
            params: {
                origins: origins,
                destinations: [`place_id:${destPlaceId}`],
                mode: 'driving',
                departure_time: 'now',
                traffic_model: 'best_guess',
                key: process.env.GOOGLE_MAPS_API_KEY
            }
        });

        if (response.data.rows[0].elements[0].status === 'OK') {
            const durationInSeconds = response.data.rows[0].elements[0].duration.value;
            this.ETA = new Date(Date.now() + (durationInSeconds * 1000));
            await this.save();
            return this.ETA;
        } else {
            throw new Error('Unable to calculate ETA');
        }
    } catch (error) {
        throw new Error(`Error calculating ETA: ${error.message}`);
    }
};

// Method to get coordinates from place ID and update the document
liveLocationSchema.methods.updateCoordinatesFromPlaceId = async function() {
    try {
        const response = await googleMapsClient
            .placeDetails({
                params: {
                    place_id: this.locationId,
                    key: process.env.GOOGLE_MAPS_API_KEY
                }
            });

        if (response.data.result) {
            this.coordinates = {
                latitude: response.data.result.geometry.location.lat,
                longitude: response.data.result.geometry.location.lng
            };
            await this.save();
            return this.coordinates;
        } else {
            throw new Error('Unable to fetch coordinates for the given place ID');
        }
    } catch (error) {
        throw new Error(`Error fetching coordinates: ${error.message}`);
    }
};

const LiveLocation = model('LiveLocation', liveLocationSchema);

module.exports = LiveLocation;

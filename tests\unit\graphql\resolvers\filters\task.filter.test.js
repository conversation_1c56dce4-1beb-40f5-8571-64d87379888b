const buildTaskQuery = require('../../../../../src/graphql/resolvers/filters/task.filter');

describe('buildTaskQuery', () => {
    it('should return an empty query when no filters are provided', () => {
        const filters = {};
        const query = buildTaskQuery(filters);
        expect(query).toEqual({});
    });

    it('should return an empty query when filters are null', () => {
        const filters = null;
        const query = buildTaskQuery(filters);
        expect(query).toEqual({});
    });

    it('should return an empty query when filters are undefined', () => {
        const filters = undefined;
        const query = buildTaskQuery(filters);
        expect(query).toEqual({});
    });

    it('should build a query with title filter', () => {
        const filters = { title: 'test' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ title: { $regex: 'test', $options: 'i' } });
    });

    it('should build a query with type filter', () => {
        const filters = { type: 'PRE_PARTY' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ type: 'PRE_PARTY' });
    });

    it('should build a query with status filter', () => {
        const filters = { status: 'PENDING' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ status: 'PENDING' });
    });

    it('should build a query with priority filter', () => {
        const filters = { priority: 'HIGH' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ priority: 'HIGH' });
    });

    it('should build a query with createdBy filter', () => {
        const filters = { createdBy: 'userId' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ createdBy: 'userId' });
    });

    it('should build a query with assignedTo filter', () => {
        const filters = { assignedTo: 'userId' };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ assignedTo: 'userId' });
    });

    it('should build a query with dueDate filter with start and end dates', () => {
        const filters = { dueDate: { start: '2023-01-01', end: '2023-12-31' } };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ dueDate: { $gte: '2023-01-01', $lte: '2023-12-31' } });
    });

    it('should build a query with dueDate filter with only start date', () => {
        const filters = { dueDate: { start: '2023-01-01' } };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ dueDate: { $gte: '2023-01-01' } });
    });

    it('should build a query with dueDate filter with only end date', () => {
        const filters = { dueDate: { end: '2023-12-31' } };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({ dueDate: { $lte: '2023-12-31' } });
    });

    it('should build a query with multiple filters', () => {
        const filters = {
            title: 'test',
            type: 'PRE_PARTY',
            status: 'PENDING',
            priority: 'HIGH',
            createdBy: 'userId',
            assignedTo: 'userId',
            dueDate: { start: '2023-01-01', end: '2023-12-31' }
        };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({
            title: { $regex: 'test', $options: 'i' },
            type: 'PRE_PARTY',
            status: 'PENDING',
            priority: 'HIGH',
            createdBy: 'userId',
            assignedTo: 'userId',
            dueDate: { $gte: '2023-01-01', $lte: '2023-12-31' }
        });
    });

    it('should build a query with empty dueDate filter', () => {
        const filters = { dueDate: {} };
        const query = buildTaskQuery(filters);
        expect(query).toEqual({});
    });
});
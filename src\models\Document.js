const { model, Schema } = require('mongoose');

const documentSchema = new Schema({
    name: { type: String, required: true },
    documentType: { 
        type: String, 
        enum: ['RECEIPT', 'INVOICE', 'CONTRACT', 'OTHER'],
        required: true 
    },
    documentUrl: { type: String, required: true },
    description: { type: String },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
}, { timestamps: true });

const Document = model('Document', documentSchema);

module.exports = Document;
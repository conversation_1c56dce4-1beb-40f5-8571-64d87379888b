const { Webhook } = require('svix');

const verifyWebhookSignature = (req) => {
  const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
  if (!WEBHOOK_SECRET) {
    throw new Error('You need a WEBHOOK_SECRET in your .env');
  }

  const headers = req.headers;
  const payload = req.body;

  const svix_id = headers['svix-id'];
  const svix_timestamp = headers['svix-timestamp'];
  const svix_signature = headers['svix-signature'];

  if (!svix_id || !svix_timestamp || !svix_signature) {
    throw new Error('Error occurred -- no svix headers');
  }

  const wh = new Webhook(WEBHOOK_SECRET);

  const json = JSON.stringify(payload);

  try {
    const evt = wh.verify(json, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    });
    return evt;
  } catch (err) {
    throw new Error(`Error verifying webhook: ${err.message}`);
  }
};

module.exports = verifyWebhookSignature;
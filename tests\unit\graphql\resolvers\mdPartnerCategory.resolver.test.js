const MdIcon = require('../../../../src/models/MdIcon');
const MdPartnerCategory = require('../../../../src/models/MdPartnerCategory');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildMdPartnerCategoryQuery = require('../../../../src/graphql/resolvers/filters/mdPatnerCategory.filter');
const mdPartnerCategoryResolvers = require('../../../../src/graphql/resolvers/mdPartnerCategory.resolver');

jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/models/MdPartnerCategory');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdPatnerCategory.filter');

describe('mdPartnerCategoryResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdPartnerCategory', () => {
        it('should resolve icon field', async () => {
            const parent = { icon: 'iconId' };
            const icon = { _id: 'iconId', name: 'Icon Name' };
            MdIcon.findById.mockResolvedValue(icon);

            const result = await mdPartnerCategoryResolvers.MdPartnerCategory.icon(parent);

            expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
            expect(result).toEqual(icon);
        });

        it('should throw an error if icon retrieval fails', async () => {
            const parent = { icon: 'iconId' };
            MdIcon.findById.mockRejectedValue(new Error('Error getting icon'));

            await expect(mdPartnerCategoryResolvers.MdPartnerCategory.icon(parent)).rejects.toThrow('Error getting icon');
        });
    });

    describe('Query', () => {
        describe('getMdPartnerCategoryById', () => {
            it('should return MdPartnerCategory if found in cache', async () => {
                const id = 'categoryId';
                const mdPartnerCategory = { _id: id, name: 'Category Name' };
                getByIdCache.mockResolvedValue(mdPartnerCategory);
                createResponse.mockReturnValue('response');

                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategoryById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('MdPartnerCategoryResponse', 'SUCCESS', 'MdPartnerCategory fetched successfully', { result: { mdPartnerCategory } });
                expect(result).toBe('response');
            });

            it('should return MdPartnerCategory if found in database', async () => {
                const id = 'categoryId';
                const populatedMdPartnerCategory = { _id: id, name: 'Category Name', icon: 'iconData' };
                const mdPartnerCategory = {
                    _id: id,
                    name: 'Category Name',
                    populate: jest.fn().mockResolvedValue(populatedMdPartnerCategory)
                };
                getByIdCache.mockResolvedValue(null);
                MdPartnerCategory.findById.mockReturnValue(mdPartnerCategory);
                createResponse.mockReturnValue('response');
                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategoryById(null, { id });
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPartnerCategory.findById).toHaveBeenCalledWith(id);
                expect(mdPartnerCategory.populate).toHaveBeenCalledWith('icon');
                expect(setCache).toHaveBeenCalledWith(id, populatedMdPartnerCategory);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryResponse',
                    'SUCCESS',
                    'MdPartnerCategory fetched successfully',
                    { result: { mdPartnerCategory: populatedMdPartnerCategory } }
                );
                expect(result).toBe('response');
            });

            it('should return error response if MdPartnerCategory not found', async () => {
                const id = 'categoryId';
                getByIdCache.mockResolvedValue(null);
                MdPartnerCategory.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');
                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategoryById(null, { id });
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPartnerCategory.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryErrorResponse', 
                    'FAILURE',                       
                    'MdPartnerCategory not found', 
                    { 
                        errors: [
                            { 
                                field: 'getMdPartnerCategoryById', 
                                message: 'MdPartnerCategory not found' 
                            }
                        ] 
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if an error occurs', async () => {
                const id = 'categoryId';
                getByIdCache.mockRejectedValue(new Error('Error retrieving MdPartnerCategory'));
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategoryById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error retrieving MdPartnerCategory', { errors: [{ field: 'getMdPartnerCategoryById', message: 'Error retrieving MdPartnerCategory' }] });
                expect(result).toBe('errorResponse');
            });
        });

        describe('getMdPartnerCategories', () => {
            it('should return mdPartnerCategories successfully', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const mockMdPartnerCategories = [{ _id: '1', name: 'Category 1' }, { _id: '2', name: 'Category 2' }];
                const mockPaginationInfo = { total: 2, limit: 10, skip: 0 };

                buildMdPartnerCategoryQuery.mockReturnValue(filter);
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                MdPartnerCategory.find.mockReturnValue({
                    populate: jest.fn().mockReturnValue({
                        limit: jest.fn().mockReturnValue({
                            skip: jest.fn().mockResolvedValue(mockMdPartnerCategories),
                        }),
                    }),
                });

                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategories(null, { filter, pagination });

                expect(buildMdPartnerCategoryQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdPartnerCategory, filter, pagination.limit, pagination.skip);
                expect(result).toEqual(createResponse('MdPartnerCategoriesResponse', 'SUCCESS', 'MdPartnerCategories fetched successfully', { result: { mdPartnerCategories: mockMdPartnerCategories }, pagination: mockPaginationInfo }));
            });

            it('should return failure response when no mdPartnerCategories found', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const mockPaginationInfo = { total: 0, limit: 10, skip: 0 };

                buildMdPartnerCategoryQuery.mockReturnValue(filter);
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                MdPartnerCategory.find.mockReturnValue({
                    populate: jest.fn().mockReturnValue({
                        limit: jest.fn().mockReturnValue({
                            skip: jest.fn().mockResolvedValue([]),
                        }),
                    }),
                });

                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategories(null, { filter, pagination });

                expect(result).toEqual(createResponse('MdPartnerCategoriesResponse', 'FAILURE', 'No mdPartnerCategories found', { result: { mdPartnerCategories: [] }, pagination: mockPaginationInfo }));
            });

            it('should return error response on exception', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const errorMessage = 'Database error';

                buildMdPartnerCategoryQuery.mockReturnValue(filter);
                getPaginationInfo.mockRejectedValue(new Error(errorMessage));

                const result = await mdPartnerCategoryResolvers.Query.getMdPartnerCategories(null, { filter, pagination });

                expect(result).toEqual(createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error retrieving MdPartnerCategories', { errors: [{ field: 'getMdPartnerCategories', message: errorMessage }] }));
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdPartnerCategory', () => {
            it('should create and return the MdPartnerCategory', async () => {
                const input = { name: 'Category Name', icon: 'iconId', description: 'Category Description' };
                const mdPartnerCategory = { _id: 'categoryId', ...input };
        
                MdPartnerCategory.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(mdPartnerCategory),
                }));
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdPartnerCategory created successfully', result: { mdPartnerCategory } });
        
                const result = await mdPartnerCategoryResolvers.Mutation.createMdPartnerCategory(null, { input });
        
                expect(MdPartnerCategory).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdPartnerCategory created successfully', result: { mdPartnerCategory } });
            });
        
            it('should return an error response if creation fails', async () => {
                const input = { name: 'Category Name', icon: 'iconId', description: 'Category Description' };
                const error = new Error('Error creating mdPartnerCategory');
        
                MdPartnerCategory.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdPartnerCategory', errors: [{ field: 'createMdPartnerCategory', message: error.message }] });
        
                const result = await mdPartnerCategoryResolvers.Mutation.createMdPartnerCategory(null, { input });
        
                expect(MdPartnerCategory).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdPartnerCategory', errors: [{ field: 'createMdPartnerCategory', message: error.message }] });
            });
        
            it('should return error response if an unexpected error occurs', async () => {
                const input = { name: 'Category Name', icon: 'iconId', description: 'Category Description' };
                const error = new Error('Unexpected error');
        
                MdPartnerCategory.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdPartnerCategory', errors: [{ field: 'createMdPartnerCategory', message: error.message }] });
        
                const result = await mdPartnerCategoryResolvers.Mutation.createMdPartnerCategory(null, { input });
        
                expect(MdPartnerCategory).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdPartnerCategory', errors: [{ field: 'createMdPartnerCategory', message: error.message }] });
            });
        });

        describe('updateMdPartnerCategory', () => {
            it('should update MdPartnerCategory successfully', async () => {
                const id = 'categoryId';
                const input = { name: 'Updated Category' };
                const mdPartnerCategory = { _id: id, name: 'Updated Category', icon: 'iconId' };
                const mockUpdateResponse = {
                    populate: jest.fn().mockResolvedValue(mdPartnerCategory)
                };
                
                MdPartnerCategory.findByIdAndUpdate = jest.fn().mockReturnValue(mockUpdateResponse);
                createResponse.mockReturnValue('successResponse');
                clearCacheById.mockResolvedValue();
                const result = await mdPartnerCategoryResolvers.Mutation.updateMdPartnerCategory(null, { id, input });
                expect(MdPartnerCategory.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(mockUpdateResponse.populate).toHaveBeenCalledWith('icon');
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryResponse',
                    'SUCCESS',
                    'MdPartnerCategory updated successfully',
                    { result: { mdPartnerCategory } }
                );
                expect(result).toBe('successResponse');
            });
            
        
            it('should return an error response if MdPartnerCategory not found', async () => {
                const id = 'categoryId'; 
                const input = { name: 'Updated Category Name', icon: 'updatedIconId', description: 'Updated Category Description' };
            
                MdPartnerCategory.findByIdAndUpdate.mockResolvedValue(null); 
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdPartnerCategory not found', errors: [{ field: 'updateMdPartnerCategory', message: 'MdPartnerCategory not found' }] });
            
                const result = await mdPartnerCategoryResolvers.Mutation.updateMdPartnerCategory(null, { id, input });
            
                expect(MdPartnerCategory.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(result).toEqual({ status: 'FAILURE', message: 'MdPartnerCategory not found', errors: [{ field: 'updateMdPartnerCategory', message: 'MdPartnerCategory not found' }] });
            });
            
        
            it('should return an error response if an error occurs during update', async () => {
                const id = 'categoryId';
                const input = { name: 'Updated Category' };
                MdPartnerCategory.findByIdAndUpdate.mockImplementation(() => {
                    throw new Error('Database error');
                });
                createResponse.mockReturnValue('errorResponse');
                const result = await mdPartnerCategoryResolvers.Mutation.updateMdPartnerCategory(null, { id, input });
        
                expect(MdPartnerCategory.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
        
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryErrorResponse',
                    'FAILURE',
                    'Error updating MdPartnerCategory',
                    { errors: [{ field: 'updateMdPartnerCategory', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdPartnerCategory', () => {        
            it('should delete MdPartnerCategory successfully', async () => {
                const id = 'categoryId';
                const mdPartnerCategory = { _id: id, name: 'Category Name' };
                MdPartnerCategory.findByIdAndDelete.mockResolvedValue(mdPartnerCategory);
                createResponse.mockReturnValue('successResponse');
                clearCacheById.mockResolvedValue();
                const result = await mdPartnerCategoryResolvers.Mutation.deleteMdPartnerCategory(null, { id });
                expect(MdPartnerCategory.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
        
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryResponse',
                    'SUCCESS',
                    'MdPartnerCategory deleted successfully',
                    { result: { mdPartnerCategory } }
                );
                expect(result).toBe('successResponse');
            });
        
            it('should return an error response if MdPartnerCategory is not found', async () => {
                const id = 'categoryId'; 
                MdPartnerCategory.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');
                const result = await mdPartnerCategoryResolvers.Mutation.deleteMdPartnerCategory(null, { id });
                expect(MdPartnerCategory.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryErrorResponse',
                    'FAILURE',
                    'MdPartnerCategory not found',
                    { errors: [{ field: 'deleteMdPartnerCategory', message: 'MdPartnerCategory not found' }] }
                );
                expect(result).toBe('errorResponse');
            });
        
            it('should return an error response if an error occurs during deletion', async () => {
                const id = 'categoryId';
                MdPartnerCategory.findByIdAndDelete.mockImplementation(() => {
                    throw new Error('Database error');
                });
                createResponse.mockReturnValue('errorResponse');
                const result = await mdPartnerCategoryResolvers.Mutation.deleteMdPartnerCategory(null, { id });
                expect(MdPartnerCategory.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPartnerCategoryErrorResponse',
                    'FAILURE',
                    'Error deleting MdPartnerCategory',
                    { errors: [{ field: 'deleteMdPartnerCategory', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});
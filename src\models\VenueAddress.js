const { model, Schema } = require('mongoose');
const { Client } = require('@googlemaps/google-maps-services-js');

const googleMapsClient = new Client({});

const venueAddressSchema = new Schema({
    name: { type: String, required: true },
    placeId: { type: String, required: true },
    coordinates: {
        latitude: {
            type: Number,
            required: false
        },
        longitude: {
            type: Number,
            required: false
        }
    },
    address: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true }
}, { timestamps: true, collection: 'venue_addresses' });

venueAddressSchema.statics.extractAddressDetails = async function(placeId) {
    const response = await googleMapsClient.placeDetails({
        params: {
            place_id: placeId,
            fields: ['formatted_address', 'address_components'],
            key: process.env.GOOGLE_MAPS_API_KEY
        }
    });

    if (!response.data.result?.formatted_address || !response.data.result?.address_components) {
        throw new Error('Could not fetch address details');
    }

    const addressComponents = response.data.result.address_components;
    const city = addressComponents.find(component => 
        component.types.includes('locality'))?.long_name;
    const state = addressComponents.find(component => 
        component.types.includes('administrative_area_level_1'))?.long_name;

    if (!city || !state) {
        throw new Error('Address must include city and state');
    }

    return {
        address: response.data.result.formatted_address,
        city,
        state
    };
};

venueAddressSchema.methods.updateCoordinatesFromPlaceId = async function() {
    try {
        const response = await googleMapsClient
            .placeDetails({
                params: {
                    place_id: this.placeId,
                    key: process.env.GOOGLE_MAPS_API_KEY
                }
            });

        if (response.data.result) {
            this.coordinates = {
                latitude: response.data.result.geometry.location.lat,
                longitude: response.data.result.geometry.location.lng
            };
            await this.save();
            return this.coordinates;
        } else {
            throw new Error('Unable to fetch coordinates for the given place ID');
        }
    } catch (error) {
        throw new Error(`Error fetching coordinates: ${error.message}`);
    }
};

const VenueAddress = model('VenueAddress', venueAddressSchema);

module.exports = VenueAddress;
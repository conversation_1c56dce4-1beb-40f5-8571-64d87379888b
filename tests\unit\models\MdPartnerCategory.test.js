const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdPartnerCategory = require('../../../src/models/MdPartnerCategory');
const MdIcon = require('../../../src/models/MdIcon');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdPartnerCategory Model Test', () => {
    it('should create and save a MdPartnerCategory successfully', async () => {
        const validMdIcon = new MdIcon({
            name: 'Icon Name',
            iconSrc: '<iconSrc></iconSrc>',
        });
        const savedMdIcon = await validMdIcon.save();

        const validMdPartnerCategory = new MdPartnerCategory({
            name: 'Category Name',
            icon: savedMdIcon._id,
            description: 'Category Description',
        });

        const savedMdPartnerCategory = await validMdPartnerCategory.save();

        expect(savedMdPartnerCategory._id).toBeDefined();
        expect(savedMdPartnerCategory.name).toBe('Category Name');
        expect(savedMdPartnerCategory.icon).toEqual(savedMdIcon._id);
        expect(savedMdPartnerCategory.description).toBe('Category Description');
    });

    it('should fail to create a MdPartnerCategory without required fields', async () => {
        const mdPartnerCategory = new MdPartnerCategory();

        let err;
        try {
            await mdPartnerCategory.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.icon).toBeDefined();
    });
});
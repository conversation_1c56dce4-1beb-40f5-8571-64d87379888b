const Document = require('../../models/Document');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const buildDocumentQuery = require('./filters/document.filter');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const findReferences = require('./references/document.references');
const { Types } = require('mongoose');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');

module.exports = {
    Document: {
        createdBy: async (parent) => {
            try {
                let userId;
                
                if (typeof parent.createdBy === 'object') {
                    if (parent.createdBy._id) {
                        userId = parent.createdBy._id;
                    } else if (parent.createdBy.id) {
                        userId = parent.createdBy.id;
                    } else if (Buffer.isBuffer(parent.createdBy)) {
                        userId = parent.createdBy.toString('hex');
                    }
                } else if (typeof parent.createdBy === 'string') {
                    userId = parent.createdBy;
                }

                if (!userId || !Types.ObjectId.isValid(userId)) {
                    throw new Error('Invalid createdBy field');
                }

                const user = await User.findById(userId);
                if (!user) {
                    throw new Error('User not found');
                }

                return user;
            } catch (error) {
                console.error('Error in createdBy resolver:', error);
                if (error.message === 'Invalid createdBy field' || error.message === 'User not found') {
                    throw error;
                }
                throw new Error('Error getting created by');
            }
        }
    },
    Query: {
        getDocumentById: async (_, { id }) => {
            try {
                let document = await getByIdCache(id) || await Document.findById(id).populate('createdBy');
                if (document) {
                    await setCache(id, document);
                } else {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Document not found', { errors: [{ field: 'id', message: 'Document not found' }] });
                }
                return createResponse('DocumentResponse', 'SUCCESS', 'Document fetched successfully', { result: { document } });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error fetching document', { errors: [{ field: 'getDocumentById', message: error.message }] });
            }
        },
        getDocuments: async (_, { filter, pagination }) => {
            try {
                const pipeline = buildDocumentQuery(filter);
        
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
        
                const countPipeline = [...pipeline, { $count: 'total' }];
                const countResult = await Document.aggregate(countPipeline);
                const totalCount = countResult[0]?.total || 0;
        
                pipeline.push({ $skip: skip });
                pipeline.push({ $limit: limit });
        
                pipeline.push({
                    $lookup: {
                        from: 'users',
                        localField: 'createdBy',
                        foreignField: '_id',
                        as: 'createdByUser'
                    }
                });
                pipeline.push({ $unwind: { path: '$createdByUser', preserveNullAndEmptyArrays: true } });
        
                const documents = await Document.aggregate(pipeline);
        
                const paginationInfo = {
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    currentPage: Math.floor(skip / limit) + 1,
                    pageSize: limit,
                    hasNextPage: skip + limit < totalCount,
                    hasPreviousPage: skip > 0
                };
        
                const mappedDocuments = documents.map(doc => ({
                    ...doc,
                    id: doc._id.toString(),
                    createdBy: doc.createdByUser ? {
                        ...doc.createdByUser,
                        id: doc.createdByUser._id.toString()
                    } : null
                }));
        
                if (mappedDocuments.length === 0) {
                    return createResponse('DocumentsResponse', 'FAILURE', 'No documents found', { result: { documents: mappedDocuments }, pagination: paginationInfo });
                }
                return createResponse('DocumentsResponse', 'SUCCESS', 'Documents fetched successfully', { result: { documents: mappedDocuments }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error fetching documents', { errors: [{ field: 'getDocuments', message: error.message }] });
            }
        }
    },
    Mutation: {
        createDocument: async (_, { input }, context) => {
            try {
                const document = new Document({ ...input, createdBy: context.user._id });
                await document.save();
                return createResponse('DocumentResponse', 'SUCCESS', 'Document created successfully', { result: { document } });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error creating document', { errors: [{ field: 'createDocument', message: error.message }] });
            }
        },
        updateDocument: async (_, { id, input }) => {
            try {
                const document = await Document.findByIdAndUpdate(id, input, { new: true }).populate('createdBy');
                if (!document) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Document not found', { errors: [{ field: 'id', message: 'Document not found' }] });
                }
                await setCache(id, document);
                return createResponse('DocumentResponse', 'SUCCESS', 'Document updated successfully', { result: { document } });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error updating document', { errors: [{ field: 'updateDocument', message: error.message }] });
            }
        },
        deleteDocument: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Document cannot be deleted', {
                        errors: [{ field: 'deleteDocument', message: `Document cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }

                const document = await Document.findById(id);
                if (!document) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Document not found', { 
                        errors: [{ field: 'id', message: 'Document not found' }] 
                    });
                }

                const { success, error } = await cascadeDelete('Document', id);
                if (!success) {
                    return createResponse('DocumentErrorResponse', 'FAILURE', 'Error in cascade deletion', {
                        errors: [{ field: 'deleteDocument', message: error.message }]
                    });
                }

                await clearCacheById(id);
                return createResponse('DocumentResponse', 'SUCCESS', 'Document deleted successfully', { 
                    result: { document } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('DocumentErrorResponse', 'FAILURE', 'Error deleting document', { 
                    errors: [{ field: 'deleteDocument', message: error.message }] 
                });
            }
        }
    }
};
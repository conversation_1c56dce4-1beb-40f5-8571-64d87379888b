const MdTheme = require('../../models/MdTheme');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMdThemeQuery = require('./filters/mdTheme.filter');
const { findReferences } = require('../../utils/referenceCheck.util');

const mdThemeResolvers = {
    Query: {
        getMdThemeById: async (_, { id }) => {
            try {
                let mdTheme = await getByIdCache(id);
                if (!mdTheme) {
                    mdTheme = await MdTheme.findById(id);
                    if (!mdTheme) {
                        return createResponse('MdThemeErrorResponse', 'FAILURE', 'Theme not found', {
                            errors: [{ field: 'id', message: 'Theme not found' }]
                        });
                    }
                    await setCache(id, mdTheme);
                }
                return createResponse('MdThemeResponse', 'SUCCESS', 'Theme retrieved successfully', {
                    result: { mdTheme }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdThemeErrorResponse', 'FAILURE', 'Error retrieving theme', {
                    errors: [{ field: 'getMdThemeById', message: error.message }]
                });
            }
        },

        getMdThemes: async (_, { filter, pagination }) => {
            try {
                const query = buildMdThemeQuery(filter);
                
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdTheme, query, limit, skip);

                const mdThemes = await MdTheme.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (mdThemes.length === 0) {
                    return createResponse('MdThemesResponse', 'FAILURE', 'No themes found', {
                        result: { mdThemes },
                        pagination: paginationInfo
                    });
                }

                return createResponse('MdThemesResponse', 'SUCCESS', 'Themes retrieved successfully', {
                    result: { mdThemes },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdThemeErrorResponse', 'FAILURE', 'Error retrieving themes', {
                    errors: [{ field: 'getMdThemes', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdTheme: async (_, { input }) => {
            try {
                const mdTheme = new MdTheme(input);
                await mdTheme.save();
                
                return createResponse('MdThemeResponse', 'SUCCESS', 'Theme created successfully', {
                    result: { mdTheme }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdThemeErrorResponse', 'FAILURE', 'Error creating theme', {
                    errors: [{ field: 'createMdTheme', message: error.message }]
                });
            }
        },

        updateMdTheme: async (_, { id, input }) => {
            try {
                const mdTheme = await MdTheme.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!mdTheme) {
                    return createResponse('MdThemeErrorResponse', 'FAILURE', 'Theme not found', {
                        errors: [{ field: 'id', message: 'Theme not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdThemeResponse', 'SUCCESS', 'Theme updated successfully', {
                    result: { mdTheme }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdThemeErrorResponse', 'FAILURE', 'Error updating theme', {
                    errors: [{ field: 'updateMdTheme', message: error.message }]
                });
            }
        },

        deleteMdTheme: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdTheme');
                if (references.length > 0) {
                    return createResponse('MdThemeErrorResponse', 'FAILURE', 'Theme cannot be deleted', {
                        errors: [{ 
                            field: 'id', 
                            message: `Theme cannot be deleted as it is being used in: ${references.join(', ')}` 
                        }]
                    });
                }

                const mdTheme = await MdTheme.findByIdAndDelete(id);
                
                if (!mdTheme) {
                    return createResponse('MdThemeErrorResponse', 'FAILURE', 'Theme not found', {
                        errors: [{ field: 'id', message: 'Theme not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdThemeResponse', 'SUCCESS', 'Theme deleted successfully', {
                    result: { mdTheme }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdThemeErrorResponse', 'FAILURE', 'Error deleting theme', {
                    errors: [{ field: 'deleteMdTheme', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdThemeResolvers; 
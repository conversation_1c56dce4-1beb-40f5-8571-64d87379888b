const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { User, createUserFromJson } = require('../../../src/models/User');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

beforeEach(async () => {
    await User.deleteMany({});
});

describe('User Model Test', () => {
    it('should create and save a user successfully', async () => {
        const validUser = new User({
            role: ['user'],
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '************',
            isRegistered: true,
            emailVerified: true,
            externalId: 'ext123',
            isActive: true
        });

        const savedUser = await validUser.save();

        expect(savedUser._id).toBeDefined();
        expect(savedUser.role).toEqual(['user']);
        expect(savedUser.firstName).toBe('John');
        expect(savedUser.lastName).toBe('Doe');
        expect(savedUser.email).toBe('<EMAIL>');
        expect(savedUser.phone).toBe('************');
        expect(savedUser.isRegistered).toBe(true);
        expect(savedUser.emailVerified).toBe(true);
        expect(savedUser.externalId).toBe('ext123');
        expect(savedUser.isActive).toBe(true);
        expect(savedUser.createdAt).toBeDefined();
        expect(savedUser.updatedAt).toBeDefined();
    });

    it('should create a user with only required fields and default values', async () => {
        const minimalUser = new User({
            role: ['user'],
            firstName: 'John',
            phone: '************'
        });

        const savedUser = await minimalUser.save();

        expect(savedUser._id).toBeDefined();
        expect(savedUser.role).toEqual(['user']);
        expect(savedUser.firstName).toBe('John');
        expect(savedUser.phone).toBe('************');
        expect(savedUser.isRegistered).toBe(false);
        expect(savedUser.emailVerified).toBe(false);
        expect(savedUser.isActive).toBe(true);
        expect(savedUser.lastName).toBeUndefined();
        expect(savedUser.email).toBeUndefined();
        expect(savedUser.externalId).toBeUndefined();
    });

    it('should enforce unique email constraint for non-null values', async () => {
        await User.createIndexes();
        
        const user1 = new User({
            role: ['user'],
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            phone: '************',
            password: 'password123'
        });
        await user1.save();

        const user2 = new User({
            role: ['user'],
            firstName: 'Test2',
            lastName: 'User2',
            email: '<EMAIL>',
            phone: '************',
            password: 'password456'
        });

        try {
            await user2.save();
            fail('Should have thrown duplicate key error');
        } catch (err) {
            expect(err.name).toBe('MongoServerError');
            expect(err.code).toBe(11000);
            expect(err.message).toContain('duplicate key error');
        }
    });

    it('should allow multiple users with null/undefined email', async () => {
        const firstUser = new User({
            role: ['user'],
            firstName: 'John',
            phone: '************'
        });
        await firstUser.save();

        const secondUser = new User({
            role: ['user'],
            firstName: 'Jane',
            phone: '************'
        });

        await expect(secondUser.save()).resolves.toBeDefined();
        
        const users = await User.find({ email: { $exists: false } });
        expect(users).toHaveLength(2);
    });

    describe('createUserFromJson', () => {
        it('should create a user from valid JSON data', async () => {
            const jsonData = {
                data: {
                    id: 'ext123',
                    first_name: 'John',
                    last_name: 'Doe',
                    email_addresses: [{ email_address: '<EMAIL>' }],
                    phone_numbers: [{ phone_number: '************' }]
                }
            };

            const user = await createUserFromJson(jsonData);

            expect(user._id).toBeDefined();
            expect(user.role).toEqual(['user']);
            expect(user.firstName).toBe('John');
            expect(user.lastName).toBe('Doe');
            expect(user.email).toBe('<EMAIL>');
            expect(user.phone).toBe('************');
            expect(user.isRegistered).toBe(true);
            expect(user.emailVerified).toBe(true);
            expect(user.externalId).toBe('ext123');
            expect(user.isActive).toBe(true);
        });

        it('should handle missing optional fields in JSON data', async () => {
            const jsonData = {
                data: {
                    id: 'ext123',
                    first_name: '',
                    last_name: '',
                    email_addresses: [{ email_address: '<EMAIL>' }],
                    phone_numbers: []
                }
            };

            const user = await createUserFromJson(jsonData);

            expect(user.firstName).toBe('unknown');
            expect(user.lastName).toBe('unknown');
            expect(user.phone).toBe('************');
        });

        it('should throw error for invalid JSON data', async () => {
            const invalidJsonData = {};

            let err;
            try {
                await createUserFromJson(invalidJsonData);
            } catch (error) {
                err = error;
            }

            expect(err).toBeDefined();
        });
    });
});

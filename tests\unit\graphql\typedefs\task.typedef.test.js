const { gql } = require('graphql-tag');
const taskTypeDef = require('../../../../src/graphql/typedefs/task.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('taskTypeDef', () => {
  it('should contain the Task type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type Task {
        id: ID!
        title: String!
        description: String
        type: TaskType!
        status: TaskStatus!
        dueDate: Date
        priority: TaskPriority!
        createdBy: User!
        assignedTo: [User!]
      }

      enum TaskType {
        PRE_PARTY
        DURING_PARTY
        POST_PARTY
      }

      enum TaskStatus {
        PENDING
        IN_PROGRESS
        COMPLETED
        ARCHIVED
      }

      enum TaskPriority {
        LOW
        MEDIUM
        HIGH
      }

      input TaskInput {
        title: String!
        description: String
        type: TaskType!
        status: TaskStatus
        dueDate: Date
        priority: TaskPriority
        createdBy: ID!
        assignedTo: [ID!]
      }

      input TaskUpdateInput {
        title: String
        description: String
        type: TaskType
        status: TaskStatus
        dueDate: Date
        priority: TaskPriority
        assignedTo: [ID!]
      }

      input TaskFilter {
        title: String
        type: TaskType
        status: TaskStatus
        priority: TaskPriority
        createdBy: ID
        assignedTo: ID
        dueDate: DateRangeInput
      }

      type TaskWrapper {
        task: Task!
      }

      type TasksWrapper {
        tasks: [Task]!
      }

      type TaskResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TaskWrapper!
      }

      type TasksResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TasksWrapper!
        pagination: PaginationInfo!
      }

      type TaskErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union TaskResult = TaskResponse | TaskErrorResponse
      union TasksResult = TasksResponse | TaskErrorResponse

      type Query {
        getTaskById(id: ID!): TaskResult!
        getTasks(pagination: PaginationInput, filter: TaskFilter): TasksResult!
      }

      type Mutation {
        createTask(input: TaskInput!): TaskResult!
        updateTask(id: ID!, input: TaskUpdateInput!): TaskResult!
        deleteTask(id: ID!): TaskResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(taskTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
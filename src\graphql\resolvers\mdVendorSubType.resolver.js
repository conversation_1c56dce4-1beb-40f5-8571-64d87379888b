const MdVendorSubType = require('../../models/MdVendorSubType');
const MdIcon = require('../../models/MdIcon');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMdVendorSubTypeQuery = require('./filters/mdVendorSubType.filter');
const { validateReferences } = require('../../utils/validation.util');
const { findReferences } = require('../../utils/referenceCheck.util');

const baseVendorSubTypeIdSchema = {
    icon: { type: 'single', model: MdIcon }
};

const vendorSubTypeIdSchema = { ...baseVendorSubTypeIdSchema };

const createVendorSubTypeIdSchema = {
    ...baseVendorSubTypeIdSchema
};

const mdVendorSubTypeResolvers = {
    MdVendorSubType: {
        icon: async (parent) => {
            try {
                return await MdIcon.findById(parent.icon);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting icon');
            }
        }
    },

    Query: {
        getMdVendorSubTypeById: async (_, { id }) => {
            try {
                const cachedMdVendorSubType = await getByIdCache(id);
                if (cachedMdVendorSubType) {
                    return createResponse('MdVendorSubTypeResponse', 'SUCCESS', 'MdVendorSubType retrieved successfully from cache', {
                        result: { mdVendorSubType: cachedMdVendorSubType }
                    });
                }

                const mdVendorSubType = await MdVendorSubType.findById(id);
                if (!mdVendorSubType) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'MdVendorSubType not found', {
                        errors: [{ field: 'id', message: 'MdVendorSubType not found' }]
                    });
                }

                await setCache(id, mdVendorSubType);
                return createResponse('MdVendorSubTypeResponse', 'SUCCESS', 'MdVendorSubType retrieved successfully', {
                    result: { mdVendorSubType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Error retrieving MdVendorSubType', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        },

        getMdVendorSubTypes: async (_, { filter, pagination }) => {
            try {
                const query = buildMdVendorSubTypeQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdVendorSubType, query, limit, skip);

                const mdVendorSubTypes = await MdVendorSubType.find(query)
                    .skip(skip)
                    .limit(limit);

                if (mdVendorSubTypes.length === 0) {
                    return createResponse('MdVendorSubTypesResponse', 'FAILURE', 'No MdVendorSubTypes found', {
                        result: { mdVendorSubTypes },
                        pagination: paginationInfo
                    });
                }

                return createResponse('MdVendorSubTypesResponse', 'SUCCESS', 'MdVendorSubTypes fetched successfully', {
                    result: { mdVendorSubTypes },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Error getting MdVendorSubTypes', {
                    errors: [{ field: 'getMdVendorSubTypes', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdVendorSubType: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createVendorSubTypeIdSchema, 'MdVendorSubType');
                if (validationError) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const mdVendorSubType = new MdVendorSubType(input);
                await mdVendorSubType.save();

                return createResponse('MdVendorSubTypeResponse', 'SUCCESS', 'MdVendorSubType created successfully', {
                    result: { mdVendorSubType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Error creating MdVendorSubType', {
                    errors: [{ field: 'createMdVendorSubType', message: error.message }]
                });
            }
        },

        updateMdVendorSubType: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, vendorSubTypeIdSchema, 'MdVendorSubType');
                if (validationError) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Validation errors', {
                        errors: validationError.errors
                    });
                }

                const mdVendorSubType = await MdVendorSubType.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!mdVendorSubType) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'MdVendorSubType not found', {
                        errors: [{ field: 'id', message: 'MdVendorSubType not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdVendorSubTypeResponse', 'SUCCESS', 'MdVendorSubType updated successfully', {
                    result: { mdVendorSubType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Error updating MdVendorSubType', {
                    errors: [{ field: 'updateMdVendorSubType', message: error.message }]
                });
            }
        },

        deleteMdVendorSubType: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdVendorSubType');
                if (references.length > 0) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Cannot delete MdVendorSubType with existing references', {
                        errors: references.map(ref => ({
                            field: 'id',
                            message: `Referenced in ${ref.model} with id ${ref.id}`
                        }))
                    });
                }

                const mdVendorSubType = await MdVendorSubType.findByIdAndDelete(id);
                if (!mdVendorSubType) {
                    return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'MdVendorSubType not found', {
                        errors: [{ field: 'id', message: 'MdVendorSubType not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdVendorSubTypeResponse', 'SUCCESS', 'MdVendorSubType deleted successfully', {
                    result: { mdVendorSubType }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdVendorSubTypeErrorResponse', 'FAILURE', 'Error deleting MdVendorSubType', {
                    errors: [{ field: 'deleteMdVendorSubType', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdVendorSubTypeResolvers; 
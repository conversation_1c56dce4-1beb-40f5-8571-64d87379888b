const transactionResolvers = require('../../../../src/graphql/resolvers/transaction.resolver');
const Transaction = require('../../../../src/models/Transaction');
const Document = require('../../../../src/models/Document');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildTransactionQuery = require('../../../../src/graphql/resolvers/filters/transaction.filter');
const findReferences = require('../../../../src/graphql/resolvers/references/transaction.references');

jest.mock('../../../../src/models/Transaction');
jest.mock('../../../../src/models/Document');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/transaction.filter');
jest.mock('../../../../src/graphql/resolvers/references/transaction.references');

describe('Transaction Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Transaction', () => {
        it('should resolve documents field', async () => {
            const parent = { documents: ['doc1', 'doc2'] };
            const mockDocuments = [{ _id: 'doc1' }, { _id: 'doc2' }];
            Document.find.mockResolvedValue(mockDocuments);

            const result = await transactionResolvers.Transaction.documents(parent);

            expect(Document.find).toHaveBeenCalledWith({ _id: { $in: parent.documents } });
            expect(result).toEqual(mockDocuments);
        });

        it('should throw an error if documents resolution fails', async () => {
            const parent = { documents: ['doc1', 'doc2'] };
            Document.find.mockRejectedValue(new Error('Database error'));

            await expect(transactionResolvers.Transaction.documents(parent)).rejects.toThrow('Error getting documents');
        });
    });

    describe('Query', () => {
        describe('getTransactionById', () => {
            it('should return a transaction from cache if available', async () => {
                const id = 'transaction1';
                const cachedTransaction = { _id: id, amount: 100 };
                getByIdCache.mockResolvedValue(cachedTransaction);
                createResponse.mockReturnValue('success response');

                const result = await transactionResolvers.Query.getTransactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('TransactionResponse', 'SUCCESS', 'Transaction fetched successfully', { result: { transaction: cachedTransaction } });
                expect(result).toBe('success response');
            });

            it('should fetch and cache a transaction if not in cache', async () => {
                const id = 'transaction1';
                const fetchedTransaction = { _id: id, amount: 100 };
                getByIdCache.mockResolvedValue(null);
                Transaction.findById.mockResolvedValue(fetchedTransaction);
                createResponse.mockReturnValue('success response');

                const result = await transactionResolvers.Query.getTransactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Transaction.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, fetchedTransaction);
                expect(createResponse).toHaveBeenCalledWith('TransactionResponse', 'SUCCESS', 'Transaction fetched successfully', { result: { transaction: fetchedTransaction } });
                expect(result).toBe('success response');
            });

            it('should return an error response if transaction is not found', async () => {
                const id = 'nonexistent';
                getByIdCache.mockResolvedValue(null);
                Transaction.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await transactionResolvers.Query.getTransactionById(null, { id });

                expect(createResponse).toHaveBeenCalledWith('TransactionErrorResponse', 'FAILURE', 'Transaction not found', { errors: [{ field: 'id', message: 'Transaction not found' }] });
                expect(result).toBe('error response');
            });
        });

        describe('getTransactions', () => {
            it('should return transactions successfully', async () => {
                const filter = { type: 'CREDIT' };
                const pagination = { limit: 10, skip: 0 };
                const query = { type: 'CREDIT' };
                const paginationInfo = { totalPages: 1, totalItems: 5 };
                const transactions = [{ _id: 'transaction1' }, { _id: 'transaction2' }];

                buildTransactionQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                Transaction.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(transactions)
                });
                createResponse.mockReturnValue('success response');

                const result = await transactionResolvers.Query.getTransactions(null, { filter, pagination });

                expect(buildTransactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(Transaction, query, 10, 0);
                expect(Transaction.find).toHaveBeenCalledWith(query);
                expect(createResponse).toHaveBeenCalledWith('TransactionsResponse', 'SUCCESS', 'Transactions fetched successfully', { result: { transactions }, pagination: paginationInfo });
                expect(result).toBe('success response');
            });

            it('should return a failure response if no transactions are found', async () => {
                buildTransactionQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue({});
                Transaction.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([])
                });
                createResponse.mockReturnValue('failure response');

                const result = await transactionResolvers.Query.getTransactions(null, {});

                expect(createResponse).toHaveBeenCalledWith('TransactionsResponse', 'FAILURE', 'No transactions found', { result: { transactions: [] }, pagination: {} });
                expect(result).toBe('failure response');
            });
        });
    });

    describe('Mutation', () => {
        describe('createTransaction', () => {
            it('should create a transaction successfully', async () => {
                const mockInput = { amount: 100, type: 'CREDIT' };
                const transactionMock = {
                    _id: 'newTransaction',
                    ...mockInput,
                    save: jest.fn().mockResolvedValue(true)
                };
                Transaction.mockImplementation(() => transactionMock);
                const createResponseMock = {
                    status: 'SUCCESS',
                    message: 'Transaction created successfully',
                    result: { transaction: transactionMock }
                };
                createResponse.mockReturnValue(createResponseMock);
            
                const result = await transactionResolvers.Mutation.createTransaction(null, { input: mockInput });
            
                expect(Transaction).toHaveBeenCalledWith(mockInput);
                expect(transactionMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('TransactionResponse', 'SUCCESS', 'Transaction created successfully', { result: { transaction: transactionMock } });
                expect(result).toBe(createResponseMock);
            });

            it('should return an error response if creation fails', async () => {
                const input = { amount: 100, type: 'CREDIT' };
                const error = new Error('Creation failed');
                Transaction.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue('error response');

                const result = await transactionResolvers.Mutation.createTransaction(null, { input });

                expect(createResponse).toHaveBeenCalledWith('TransactionErrorResponse', 'FAILURE', 'Error creating transaction', { errors: [{ field: 'createTransaction', message: error.message }] });
                expect(result).toBe('error response');
            });
        });

        describe('deleteTransaction', () => {
            it('should check for references before deletion', async () => {
                const id = 'transactionId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Transaction cannot be deleted',
                    errors: [{
                        field: 'deleteTransaction',
                        message: 'Transaction cannot be deleted as it has references in the following collections: Collection1, Collection2'
                    }]
                });

                const result = await transactionResolvers.Mutation.deleteTransaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Transaction.findByIdAndDelete).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Transaction cannot be deleted',
                    errors: [{
                        field: 'deleteTransaction',
                        message: 'Transaction cannot be deleted as it has references in the following collections: Collection1, Collection2'
                    }]
                });
            });

            it('should delete transaction successfully when no references exist', async () => {
                const id = 'transactionId';
                const mockTransaction = { _id: id, amount: 100 };
                
                findReferences.mockResolvedValue([]);
                Transaction.findByIdAndDelete.mockResolvedValue(mockTransaction);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Transaction deleted successfully',
                    result: { transaction: mockTransaction }
                });

                const result = await transactionResolvers.Mutation.deleteTransaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Transaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TransactionResponse',
                    'SUCCESS',
                    'Transaction deleted successfully',
                    { result: { transaction: mockTransaction } }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Transaction deleted successfully',
                    result: { transaction: mockTransaction }
                });
            });

            it('should return error if transaction not found', async () => {
                const id = 'nonexistentId';
                
                findReferences.mockResolvedValue([]);
                Transaction.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Transaction not found',
                    errors: [{ field: 'deleteTransaction', message: 'Transaction not found' }]
                });

                const result = await transactionResolvers.Mutation.deleteTransaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Transaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TransactionErrorResponse',
                    'FAILURE',
                    'Transaction not found',
                    { errors: [{ field: 'deleteTransaction', message: 'Transaction not found' }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Transaction not found',
                    errors: [{ field: 'deleteTransaction', message: 'Transaction not found' }]
                });
            });

            it('should handle errors during deletion', async () => {
                const id = 'transactionId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Transaction.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting transaction',
                    errors: [{ field: 'deleteTransaction', message: error.message }]
                });

                const result = await transactionResolvers.Mutation.deleteTransaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(Transaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'TransactionErrorResponse',
                    'FAILURE',
                    'Error deleting transaction',
                    { errors: [{ field: 'deleteTransaction', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting transaction',
                    errors: [{ field: 'deleteTransaction', message: error.message }]
                });
            });
        });
    });
});
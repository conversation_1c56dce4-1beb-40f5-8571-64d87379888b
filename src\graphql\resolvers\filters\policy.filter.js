const { isValidObjectId } = require("../../../utils/validation.util");
const buildVendorQuery = require("./vendor.filter");
const buildTagQuery = require("./tag.filter");
const Vendor = require("../../../models/Vendor");
const Tag = require("../../../models/Tag");

const buildPolicyQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.active !== undefined) {
            query.active = filter.active;
        }

        if (filter.vendor) {
            if (typeof filter.vendor === 'string') {
                if (isValidObjectId(filter.vendor)) {
                    query.vendor = filter.vendor;
                } else {
                    throw new Error('Invalid vendor ID provided');
                }
            } else if (typeof filter.vendor === 'object') {
                const vendorPipeline = buildVendorQuery(filter.vendor);
                if (vendorPipeline.length > 0) {
                    const matchingVendors = await Vendor.aggregate(vendorPipeline);
                    if (matchingVendors.length > 0) {
                        query.vendor = { $in: matchingVendors.map(vendor => vendor._id) };
                    } else {
                        query.vendor = { $in: [] };
                    }
                }
            }
        }

        if (filter.tag) {
            if (typeof filter.tag === 'string') {
                if (isValidObjectId(filter.tag)) {
                    query.tags = filter.tag;
                } else {
                    throw new Error('Invalid tag ID provided');
                }
            } else if (typeof filter.tag === 'object') {
                const tagQuery = await buildTagQuery(filter.tag);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.tags = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.tags = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildPolicyQuery; 
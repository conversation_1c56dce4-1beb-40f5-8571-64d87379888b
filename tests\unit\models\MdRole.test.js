const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdRole = require('../../../src/models/MdRole');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdRole Model Test', () => {
    it('should create and save a MdRole successfully', async () => {
        const validMdRole = new MdRole({
            name: 'Admin',
            description: 'Admin Role',
        });

        const savedMdRole = await validMdRole.save();

        expect(savedMdRole._id).toBeDefined();
        expect(savedMdRole.name).toBe('Admin');
        expect(savedMdRole.description).toBe('Admin Role');
    });

    it('should fail to create a MdRole without required fields', async () => {
        const mdRole = new MdRole();

        let err;
        try {
            await mdRole.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });
});
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const PartyService = require('../../../src/models/PartyService');
const MdVendorType = require('../../../src/models/MdVendorType');
const Vendor = require('../../../src/models/Vendor');
const Task = require('../../../src/models/Task');
const Transaction = require('../../../src/models/Transaction');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('PartyService Model Test', () => {
    it('should create and save a PartyService successfully', async () => {
        const mockVendorType = new MdVendorType({
            name: 'Test Vendor Type',
            description: 'Test Description'
        });
        await mockVendorType.save();

        const mockVendor = new Vendor({
            name: 'Test Vendor',
            vendorType: mockVendorType._id,
            primaryContact: new mongoose.Types.ObjectId(),
            businessAddress: new mongoose.Types.ObjectId(),
            contacts: [new mongoose.Types.ObjectId()],
            serviceLocations: [new mongoose.Types.ObjectId()],
            servicesProvided: [new mongoose.Types.ObjectId()]
        });
        await mockVendor.save();

        const mockTask = new Task({
            title: 'Test Task',
            type: 'PRE_PARTY',
            createdBy: new mongoose.Types.ObjectId()
        });
        await mockTask.save();

        const mockTransaction = new Transaction({
            description: 'Test Transaction',
            amount: 100,
            type: 'DEBIT',
            method: 'CASH'
        });
        await mockTransaction.save();

        const validPartyService = new PartyService({
            vendorType: mockVendorType._id,
            vendor: mockVendor._id,
            time: new Date(),
            city: 'Test City',
            address: 'Test Address',
            tasks: [mockTask._id],
            budget: 1000.0,
            expenditure: 500.0,
            transactions: [mockTransaction._id]
        });

        const savedPartyService = await validPartyService.save();

        expect(savedPartyService._id).toBeDefined();
        expect(savedPartyService.vendorType).toEqual(mockVendorType._id);
        expect(savedPartyService.vendor).toEqual(mockVendor._id);
        expect(savedPartyService.time).toBeInstanceOf(Date);
        expect(savedPartyService.city).toBe('Test City');
        expect(savedPartyService.address).toBe('Test Address');
        expect(savedPartyService.tasks).toContainEqual(mockTask._id);
        expect(savedPartyService.budget).toBe(1000.0);
        expect(savedPartyService.expenditure).toBe(500.0);
        expect(savedPartyService.transactions).toContainEqual(mockTransaction._id);
    });

    it('should fail to create a PartyService without required fields', async () => {
        const partyService = new PartyService();

        let err;
        try {
            await partyService.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.vendorType).toBeDefined();
        expect(err.errors.vendor).toBeDefined();
        expect(err.errors.time).toBeDefined();
        expect(err.errors.city).toBeDefined();
        expect(err.errors.address).toBeDefined();
    });

    it('should create a PartyService with default values', async () => {
        const mockVendorType = new MdVendorType({
            name: 'Test Vendor Type',
            description: 'Test Description'
        });
        await mockVendorType.save();

        const mockVendor = new Vendor({
            name: 'Test Vendor',
            vendorType: mockVendorType._id,
            primaryContact: new mongoose.Types.ObjectId(),
            businessAddress: new mongoose.Types.ObjectId(),
            contacts: [new mongoose.Types.ObjectId()],
            serviceLocations: [new mongoose.Types.ObjectId()],
            servicesProvided: [new mongoose.Types.ObjectId()]
        });
        await mockVendor.save();

        const minimalPartyService = new PartyService({
            vendorType: mockVendorType._id,
            vendor: mockVendor._id,
            time: new Date(),
            city: 'Test City',
            address: 'Test Address'
        });

        const savedPartyService = await minimalPartyService.save();

        expect(savedPartyService._id).toBeDefined();
        expect(savedPartyService.budget).toBe(0.0);
        expect(savedPartyService.expenditure).toBe(0.0);
        expect(savedPartyService.tasks).toEqual([]);
        expect(savedPartyService.transactions).toEqual([]);
    });
});
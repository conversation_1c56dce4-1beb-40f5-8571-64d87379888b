const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    input BudgetRangeInput {
        min: Float
        max: Float
    }

    enum UserRole {
        MAIN_HOST
        CO_HOST
        GUEST
    }

    enum EventStatus {
        PLANNED
        CONFIRMED
    }

    type Event {
        id: ID!
        eventType: MdEventType
        name: String!
        description: String!
        startDate: DateTime
        endDate: DateTime
        mainHost: Host!
        coHosts: [Host!]
        guests: [Guest!]
        budget: Float!
        location: MdServiceLocation
        parties: [Party!]
        status: [UserRole]
        tasks: [Task!]
        eventGroup: EventGroup
        eventStatus: EventStatus!
    }

    enum UserTypeForEvent {
        HOST
        GUEST
    }

    input EventFilterInput {
        eventType: MdEventTypeFilterInput
        name: String
        description: String
        theme: MdThemeFilterInput
        budget: BudgetRangeInput
        location: MdServiceLocationFilterInput
        parties: PartyFilterInput
        dateRange: DateTimeRangeInput
        presentAndUpcoming: Boolean
        userType: UserTypeForEvent
        eventStatus: EventStatus
        eventGroupId: ID
    }

    input EventTasksFilterInput {
        partyId: ID,
        party: PartyFilterInput
        task: TaskFilter
    }

    type PartyTasks {
        party: Party!
        tasks: [Task!]!
        tasksCount: Int!
    }

    type PartyTasksWrapper {
        totalTasksCount: Int!
        totalFilteredTasksCount: Int!
        partyTasks: [PartyTasks!]!
    }

    type EventWrapper {
        event: Event!
    }

    type EventsWrapper {
        events: [Event]!
    }

    type EventCollaboratorsWrapper {
        collaborators: [User!]!
    }

    type EventAssigneesWrapper {
        assignees: [User!]!
    }

    type PartyTasksResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartyTasksWrapper!
    }

    type EventResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventWrapper!
    }

    type EventsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventsWrapper!
        pagination: PaginationInfo!
    }

    type EventCollaboratorsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventCollaboratorsWrapper!
    }

    type EventAssigneesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventAssigneesWrapper!
    }

    type EventErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union EventResult = EventResponse | EventErrorResponse
    union EventsResult = EventsResponse | EventErrorResponse
    union EventTasksResult = PartyTasksResponse | EventErrorResponse
    union EventCollaboratorsResult = EventCollaboratorsResponse | EventErrorResponse
    union EventAssigneesResult = EventAssigneesResponse | EventErrorResponse
    
    input CoHostInput {
        firstName: String!
        lastName: String
        phone: String!
        email: String
    }

    input EventInput {
        eventType: ID
        name: String!
        description: String!
        coHosts: [CoHostInput!]
        location: ID
        startDate: DateTime
        endDate: DateTime
        eventGroupId: ID
        eventStatus: EventStatus
    }

    input EventUpdateInput {
        eventType: ID
        name: String
        description: String
        coHosts: [CoHostInput!]
        location: ID
        startDate: DateTime
        endDate: DateTime
        eventGroupId: ID
        eventStatus: EventStatus
    }

    type Query {
        getEventById(id: ID!): EventResult!
        getEvents(filter: EventFilterInput, pagination: PaginationInput): EventsResult!
        getUserEvents(filter: EventFilterInput, pagination: PaginationInput): EventsResult!
        getEventTasks(filter: EventTasksFilterInput, eventId: ID!): EventTasksResult!
        getEventCollaborators(eventId: ID!): EventCollaboratorsResult!
        getEventAssignees(eventId: ID!): EventAssigneesResult!
    }

    type Mutation {
        createEvent(input: EventInput!): EventResult!
        updateEvent(id: ID!, input: EventUpdateInput!): EventResult!
        deleteEvent(id: ID!): EventResult!
    }
`;
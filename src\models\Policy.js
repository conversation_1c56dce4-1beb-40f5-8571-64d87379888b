const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const policySchema = new Schema({
    vendor: { type: Schema.Types.ObjectId, ref: 'Vendor', required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    order: { type: Number, required: true },
    tags: [{ type: Schema.Types.ObjectId, ref: 'Tag' }],
    active: { type: Boolean, required: true, default: false }
}, { timestamps: true, collection: 'policies' });

const Policy = model('Policy', policySchema);

module.exports = Policy; 
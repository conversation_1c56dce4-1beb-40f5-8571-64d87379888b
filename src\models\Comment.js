const { model, Schema } = require('mongoose');

const commentSchema = new Schema({
    content: { type: String, required: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    mentions: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    reactions: [{ type: Schema.Types.ObjectId, ref: 'Reaction' }]
}, { timestamps: true, collection: 'comments' });

const Comment = model('Comment', commentSchema);

module.exports = Comment;
const { gql } = require('graphql-tag');
const vendorRatingTypeDef = require('../../../../src/graphql/typedefs/vendorRating.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('vendorRatingTypeDef', () => {
    it('should contain the VendorRating type definitions', () => {
        const expectedTypeDefs = gql`
        ${sharedTypeDef}

        type VendorRating {
            id: ID!
            user: User!
            vendor: Vendor!
            rating: Float!
            comment: String
        }

        input VendorRatingFilterInput {
            userFirstName: String
            userLastName: String
            vendorName: String
            vendorId: ID
            minRating: Float
            maxRating: Float
        }

        type VendorRatingWrapper {
            vendorRating: VendorRating!
        }

        type VendorRatingsWrapper {
            vendorRatings: [VendorRating]!
        }

        type VendorRatingResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: VendorRatingWrapper!
        }

        type VendorRatingsResponse implements Response {
            status: ResponseStatus!
            message: String!
            result: VendorRatingsWrapper!
            pagination: PaginationInfo!
        }

        type VendorRatingErrorResponse implements Response {
            status: ResponseStatus!
            message: String!
            errors: [Error!]!
        }

        union VendorRatingResult = VendorRatingResponse | VendorRatingErrorResponse
        union VendorRatingsResult = VendorRatingsResponse | VendorRatingErrorResponse

        type Query {
            getVendorRatingById(id: ID!): VendorRatingResult!
            getVendorRatings(filter: VendorRatingFilterInput, pagination: PaginationInput): VendorRatingsResult!
        }

        input VendorRatingInput {
            user: ID!
            vendor: ID!
            rating: Float!
            comment: String
        }

        input VendorRatingUpdateInput {
            rating: Float
            comment: String
        }

        type Mutation {
            createVendorRating(input: VendorRatingInput!): VendorRatingResult!
            updateVendorRating(id: ID!, input: VendorRatingUpdateInput!): VendorRatingResult!
            deleteVendorRating(id: ID!): VendorRatingResult!
        }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(vendorRatingTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});


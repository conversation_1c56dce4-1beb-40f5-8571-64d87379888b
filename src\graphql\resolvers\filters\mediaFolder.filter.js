const { isValidObjectId } = require("../../../utils/validation.util");
const buildEventQuery = require("./event.filter");
const buildPartyQuery = require("./party.filter");
const buildUserQuery = require("./user.filter");
const buildMediaQuery = require("./media.filter");
const buildTagQuery = require("./tag.filter");
const Event = require("../../../models/Event");
const Party = require("../../../models/Party");
const User = require("../../../models/User");
const Media = require("../../../models/Media");
const Tag = require("../../../models/Tag");

const buildMediaFolderQuery = async (filter, userId) => {
    const query = {};

    if (filter) {
        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.event) {
            if (typeof filter.event === 'string') {
                if (isValidObjectId(filter.event)) {
                    query.event = filter.event;
                } else {
                    throw new Error('Invalid event ID provided');
                }
            } else if (typeof filter.event === 'object') {
                const { query: eventQuery, applyPostQueryFilters } = await buildEventQuery(filter.event);
                if (Object.keys(eventQuery).length > 0) {
                    const matchingEvents = await Event.find(eventQuery);
                    const filteredEvents = await applyPostQueryFilters(matchingEvents);
                    
                    if (filteredEvents.length > 0) {
                        query.event = { $in: filteredEvents.map(event => event._id) };
                    } else {
                        query.event = { $in: [] };
                    }
                }
            }
        }

        if (filter.eventId) {
            if (isValidObjectId(filter.eventId)) {
                query.event = filter.eventId;
            } else {
                throw new Error('Invalid event ID provided');
            }
        }

        if (filter.partyId) {
            if (isValidObjectId(filter.partyId)) {
                query.party = filter.partyId;
            } else {
                throw new Error('Invalid party ID provided');
            }
        }

        if (filter.partyMediaId) {
            if (isValidObjectId(filter.partyMediaId)) {
                query.media = filter.partyMediaId;
            } else {
                throw new Error('Invalid party media ID provided');
            }
        }

        if (filter.party && !filter.partyId) {
            if (typeof filter.party === 'string') {
                if (isValidObjectId(filter.party)) {
                    query.party = filter.party;
                } else {
                    throw new Error('Invalid party ID provided');
                }
            } else if (typeof filter.party === 'object') {
                const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filter.party);
                if (Object.keys(partyQuery).length > 0) {
                    const matchingParties = await Party.find(partyQuery);
                    const filteredParties = await applyPostQueryFilters(matchingParties);
                    
                    if (filteredParties.length > 0) {
                        query.party = { $in: filteredParties.map(party => party._id) };
                    } else {
                        query.party = { $in: [] };
                    }
                }
            }
        }

        if (filter.ownerId) {
            if (isValidObjectId(filter.ownerId)) {
                query.owner = { $in: [filter.ownerId] };
            } else {
                throw new Error('Invalid owner ID provided');
            }
        }

        if (filter.owner && !filter.ownerId) {
            if (typeof filter.owner === 'string') {
                if (isValidObjectId(filter.owner)) {
                    query.owner = { $in: [filter.owner] };
                } else {
                    throw new Error('Invalid owner ID provided');
                }
            } else if (typeof filter.owner === 'object') {
                const userQuery = await buildUserQuery(filter.owner);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.owner = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.owner = { $in: [] };
                    }
                }
            }
        }

        if (filter.media) {
            if (typeof filter.media === 'string') {
                if (isValidObjectId(filter.media)) {
                    query.media = filter.media;
                } else {
                    throw new Error('Invalid media ID provided');
                }
            } else if (typeof filter.media === 'object') {
                const mediaQuery = await buildMediaQuery(filter.media);
                if (Object.keys(mediaQuery).length > 0) {
                    const matchingMedia = await Media.find(mediaQuery).select('_id');
                    if (matchingMedia.length > 0) {
                        query.media = { $in: matchingMedia.map(media => media._id) };
                    } else {
                        query.media = { $in: [] };
                    }
                }
            }
        }

        if (filter.sharedWithId) {
            if (isValidObjectId(filter.sharedWithId)) {
                query.sharedWith = filter.sharedWithId;
            } else {
                throw new Error('Invalid sharedWith ID provided');
            }
        }

        if (filter.sharedWith && !filter.sharedWithId) {
            if (typeof filter.sharedWith === 'string') {
                if (isValidObjectId(filter.sharedWith)) {
                    query.sharedWith = filter.sharedWith;
                } else {
                    throw new Error('Invalid sharedWith ID provided');
                }
            } else if (typeof filter.sharedWith === 'object') {
                const userQuery = await buildUserQuery(filter.sharedWith);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.sharedWith = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.sharedWith = { $in: [] };
                    }
                }
            }
        }

        if (filter.contributorsId) {
            if (isValidObjectId(filter.contributorsId)) {
                query.contributors = filter.contributorsId;
            } else {
                throw new Error('Invalid contributors ID provided');
            }
        }

        if (filter.contributors && !filter.contributorsId) {
            if (typeof filter.contributors === 'string') {
                if (isValidObjectId(filter.contributors)) {
                    query.contributors = filter.contributors;
                } else {
                    throw new Error('Invalid contributors ID provided');
                }
            } else if (typeof filter.contributors === 'object') {
                const userQuery = await buildUserQuery(filter.contributors);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.contributors = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.contributors = { $in: [] };
                    }
                }
            }
        }

        if (filter.publish !== undefined) {
            query.publish = filter.publish;
        }

        if (filter.category) {
            query.category = filter.category;
        }

        if (filter.tags) {
            if (typeof filter.tags === 'string') {
                if (isValidObjectId(filter.tags)) {
                    query.tags = filter.tags;
                } else {
                    throw new Error('Invalid tags ID provided');
                }
            } else if (typeof filter.tags === 'object') {
                const tagQuery = await buildTagQuery(filter.tags);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.tags = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.tags = { $in: [] };
                    }
                }
            }
        }

        if (userId) {
            if (!isValidObjectId(userId)) {
                throw new Error('Invalid user ID provided');
            }

            query.$or = [
                { owner: userId },
                { contributors: userId },
                { sharedWith: userId }
            ];

            if (filter.userType) {
                switch (filter.userType) {
                    case 'HOST':
                        delete query.$or;
                        query.owner = userId;
                        break;
                    case 'GUEST':
                        delete query.$or;
                        query.$or = [
                            { contributors: userId },
                            { sharedWith: userId }
                        ];
                        break;
                    default:
                        throw new Error('Invalid userType provided');
                }
            }
        }
    }

    return query;
};

module.exports = buildMediaFolderQuery; 
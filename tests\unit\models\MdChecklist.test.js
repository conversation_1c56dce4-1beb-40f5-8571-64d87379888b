const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdChecklist = require('../../../src/models/MdChecklist');
const MdPartyType = require('../../../src/models/MdPartyType');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdChecklist Model Test', () => {
    it('should create and save a MdChecklist successfully', async () => {
        const mockPartyType = new MdPartyType({
            name: 'Birthday Party',
            description: 'A birthday celebration'
        });
        await mockPartyType.save();

        const validMdChecklist = new MdChecklist({
            partyType: mockPartyType._id,
            title: 'Pre-party Checklist',
            description: 'Things to do before the party'
        });

        const savedMdChecklist = await validMdChecklist.save();

        expect(savedMdChecklist._id).toBeDefined();
        expect(savedMdChecklist.partyType).toEqual(mockPartyType._id);
        expect(savedMdChecklist.title).toBe('Pre-party Checklist');
        expect(savedMdChecklist.description).toBe('Things to do before the party');
        expect(savedMdChecklist.createdAt).toBeDefined();
        expect(savedMdChecklist.updatedAt).toBeDefined();
    });

    it('should fail to create a MdChecklist without required fields', async () => {
        const mdChecklist = new MdChecklist();

        let err;
        try {
            await mdChecklist.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.partyType).toBeDefined();
        expect(err.errors.title).toBeDefined();
        expect(err.errors.description).toBeDefined();
    });

    it('should fail to create a MdChecklist with invalid partyType reference', async () => {
        const invalidMdChecklist = new MdChecklist({
            partyType: 'invalid_id',
            title: 'Invalid Checklist',
            description: 'This should fail'
        });

        let err;
        try {
            await invalidMdChecklist.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.partyType).toBeDefined();
    });

    it('should create and save a MdChecklist with items successfully', async () => {
        const mockPartyType = new MdPartyType({
            name: 'Birthday Party',
            description: 'A birthday celebration'
        });
        await mockPartyType.save();

        const validMdChecklist = new MdChecklist({
            partyType: mockPartyType._id,
            title: 'Pre-party Checklist',
            description: 'Things to do before the party',
            items: [
                {
                    order: 1,
                    title: 'Book Venue',
                    description: 'Find and reserve the party venue'
                },
                {
                    order: 2,
                    title: 'Send Invitations',
                    description: 'Create and send party invitations'
                }
            ]
        });

        const savedMdChecklist = await validMdChecklist.save();

        expect(savedMdChecklist._id).toBeDefined();
        expect(savedMdChecklist.partyType).toEqual(mockPartyType._id);
        expect(savedMdChecklist.title).toBe('Pre-party Checklist');
        expect(savedMdChecklist.description).toBe('Things to do before the party');
        expect(savedMdChecklist.items).toHaveLength(2);
        expect(savedMdChecklist.items[0].order).toBe(1);
        expect(savedMdChecklist.items[0].title).toBe('Book Venue');
        expect(savedMdChecklist.items[1].order).toBe(2);
        expect(savedMdChecklist.items[1].title).toBe('Send Invitations');
    });
});
const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdPromotion {
        id: ID!
        partner: MdPartner!
        name: String!
        thumbnail: String
        mediaUrl: String!
        description: String!
        ctaLink: String!
        tags: [Tag!]
        startDate: DateTime!
        endDate: DateTime!
        active: Boolean!
    }

    input MdPromotionFilterInput {
        id: String
        name: String
        partner: MdPartnerFilterInput
        description: String
        mediaUrl: String
        ctaLink: String
        active: Boolean
        dateRange: DateTimeRangeInput
        tag: TagFilterInput
    }

    type MdPromotionWrapper {
        mdPromotion: MdPromotion!
    }

    type MdPromotionsWrapper {
        mdPromotions: [MdPromotion]!
    }

    type MdPromotionResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPromotionWrapper!
    }

    type MdPromotionsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPromotionsWrapper!
        pagination: PaginationInfo!
    }

    type MdPromotionErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdPromotionResult = MdPromotionResponse | MdPromotionErrorResponse
    union MdPromotionsResult = MdPromotionsResponse | MdPromotionErrorResponse

    type Query {
        getMdPromotionById(id: ID!): MdPromotionResult!
        getMdPromotions(filter: MdPromotionFilterInput, pagination: PaginationInput): MdPromotionsResult!
    }

    input MdPromotionInput {
        partner: ID!
        name: String!
        thumbnail: String
        mediaUrl: String!
        description: String!
        ctaLink: String!
        tags: [ID!]
        startDate: DateTime!
        endDate: DateTime!
        active: Boolean!
    }

    input MdPromotionUpdateInput {
        partner: ID
        name: String
        thumbnail: String
        mediaUrl: String
        description: String
        ctaLink: String
        tags: [ID!]
        startDate: DateTime
        endDate: DateTime
        active: Boolean
    }

    type Mutation {
        createMdPromotion(input: MdPromotionInput!): MdPromotionResult!
        updateMdPromotion(id: ID!, input: MdPromotionUpdateInput!): MdPromotionResult!
        deleteMdPromotion(id: ID!): MdPromotionResult!
    }
`;

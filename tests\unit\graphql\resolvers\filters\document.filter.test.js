const mongoose = require('mongoose');
const buildDocumentQuery = require('../../../../../src/graphql/resolvers/filters/document.filter');

jest.mock('../../../../../src/models/Document', () => ({
    schema: {
        paths: {
        additionalField1: {},
        additionalField2: {}
        }
    }
}));

describe('buildDocumentQuery', () => {
    it('should return an empty pipeline when no filters are provided', () => {
        const result = buildDocumentQuery();
        expect(result).toHaveLength(1);
        expect(result[0]).toHaveProperty('$project');
    });

    it('should build a pipeline with name filter', () => {
        const filters = { name: 'test' };
        const result = buildDocumentQuery(filters);
        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
        $match: { name: { $regex: /test/i } }
        });
    });

    it('should build a pipeline with documentType filter', () => {
        const filters = { documentType: 'RECEIPT' };
        const result = buildDocumentQuery(filters);
        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
        $match: { documentType: 'RECEIPT' }
        });
    });

    it('should build a pipeline with valid createdBy filter', () => {
        const validId = new mongoose.Types.ObjectId();
        const filters = { createdBy: validId.toString() };
        const result = buildDocumentQuery(filters);
        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
        $match: { createdBy: validId }
        });
    });

    it('should throw an error with invalid createdBy filter', () => {
        const filters = { createdBy: 'invalid-id' };
        expect(() => buildDocumentQuery(filters)).toThrow('Invalid createdBy ID provided');
    });

    it('should build a pipeline with creator name filters', () => {
        const filters = { creatorFirstName: 'John', creatorLastName: 'Doe' };
        const result = buildDocumentQuery(filters);
        expect(result).toHaveLength(4);
        expect(result[0]).toEqual({
        $lookup: {
            from: 'users',
            localField: 'createdBy',
            foreignField: '_id',
            as: 'creator'
        }
        });
        expect(result[1]).toEqual({ $unwind: '$creator' });
        expect(result[2]).toEqual({
        $match: {
            'creator.firstName': { $regex: /John/i },
            'creator.lastName': { $regex: /Doe/i }
        }
        });
    });

    it('should include all fields in the project stage', () => {
        const result = buildDocumentQuery();
        expect(result[result.length - 1].$project).toEqual({
        _id: 1,
        name: 1,
        documentType: 1,
        documentUrl: 1,
        description: 1,
        createdBy: 1,
        createdAt: 1,
        updatedAt: 1,
        creator: 1,
        additionalField1: 1,
        additionalField2: 1
        });
    });

    it('should combine multiple filters', () => {
        const filters = {
        name: 'test',
        documentType: 'RECEIPT',
        creatorFirstName: 'John'
        };
        const result = buildDocumentQuery(filters);
        expect(result).toHaveLength(4);
        expect(result[2]).toEqual({
        $match: {
            name: { $regex: /test/i },
            documentType: 'RECEIPT',
            'creator.firstName': { $regex: /John/i }
        }
        });
    });
});
const mongoose = require('mongoose');
const findReferences = require('../../../../../src/graphql/resolvers/references/document.references');
const Transaction = require('../../../../../src/models/Transaction');

jest.mock('../../../../../src/models/Transaction');

describe('findReferences', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return an empty array when no references are found', async () => {
    Transaction.find.mockResolvedValue([]);

    const result = await findReferences('nonexistentId');

    expect(result).toEqual([]);
    expect(Transaction.find).toHaveBeenCalledWith({ 'documents': 'nonexistentId' });
  });

  it('should return ["Transaction"] when transaction references are found', async () => {
    Transaction.find.mockResolvedValue([{ _id: 'transactionId' }]);

    const result = await findReferences('existingDocumentId');

    expect(result).toEqual(['Transaction']);
    expect(Transaction.find).toHaveBeenCalledWith({ 'documents': 'existingDocumentId' });
  });
});
const { gql } = require('graphql-tag');

const mdPolicyTypeDef = gql`
    type MdPolicy {
        id: ID!
        name: String!
        description: String!
        tags: [Tag!]
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdPolicyFilterInput {
        id: String
        name: String
        description: String
        tag: TagFilterInput
    }

    type MdPolicyWrapper {
        mdPolicy: MdPolicy!
    }

    type MdPoliciesWrapper {
        mdPolicies: [MdPolicy]!
    }

    type MdPolicyResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPolicyWrapper!
    }

    type MdPoliciesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPoliciesWrapper!
        pagination: PaginationInfo!
    }

    type MdPolicyErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdPolicyResult = MdPolicyResponse | MdPolicyErrorResponse
    union MdPoliciesResult = MdPoliciesResponse | MdPolicyErrorResponse

    type Query {
        getMdPolicyById(id: ID!): MdPolicyResult!
        getMdPolicies(filter: MdPolicyFilterInput, pagination: PaginationInput): MdPoliciesResult!
    }

    input MdPolicyInput {
        name: String!
        description: String!
        tags: [ID!]
    }

    input MdPolicyUpdateInput {
        name: String
        description: String
        tags: [ID!]
    }

    type Mutation {
        createMdPolicy(input: MdPolicyInput!): MdPolicyResult!
        updateMdPolicy(id: ID!, input: MdPolicyUpdateInput!): MdPolicyResult!
        deleteMdPolicy(id: ID!): MdPolicyResult!
    }
`;

module.exports = mdPolicyTypeDef; 
const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const notificationTypeDefs = gql`
  ${sharedTypeDef}

  type Notification {
    id: ID!
    userId: ID!
    title: String!
    body: String!
    data: JSON
    createdAt: String!
  }

  input NotificationInput {
    userId: ID
    title: String!
    body: String!
    data: JSON
    sound: String
    badge: Int
    channelId: String
  }

  input TokenNotificationInput {
    token: String!
    title: String!
    body: String!
    data: JSON
    sound: String
    badge: Int
    channelId: String
  }

  type NotificationResult {
    message: String!
    notification: Notification
    receipts: [String!]
    deviceTokens: [String!]
  }

  type NotificationError {
    message: String!
    details: JSON
  }

  type ReceiptCheckResult {
    message: String!
    receipts: [ReceiptStatus!]!
    invalidTokensCount: Int!
  }

  type ReceiptStatus {
    id: String!
    status: String!
    message: String
    details: JSON
  }

  union NotificationResponseUnion = NotificationResult | NotificationError

  extend type Mutation {
    sendNotification(input: NotificationInput!): NotificationResponseUnion!
  }
`;

module.exports = notificationTypeDefs; 
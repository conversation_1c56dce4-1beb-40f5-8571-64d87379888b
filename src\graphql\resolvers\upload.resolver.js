const { createResponse } = require('../../utils/response.util');
const { uploadFilesToBlob, deleteFileFromBlob, getContainerClient } = require('../../utils/azureStorage.util');
const { GraphQLUpload } = require('graphql-upload');
const { Readable } = require('stream');

const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '20000000', 10);
const MAX_FILES = parseInt(process.env.MAX_FILES || '10', 10);

const uploadResolvers = {
    Upload: GraphQLUpload,

    Mutation: {
        uploadFiles: async (_, { files, containerName }) => {
            try {
                const fileArray = Array.isArray(files) ? files : [files];

                if (fileArray.length > MAX_FILES) {
                    return createResponse('FileUploadResponse', 'FAILURE', 'Too many files', {
                        errors: [{
                            field: 'uploadFiles',
                            message: `Maximum number of files allowed is ${MAX_FILES}. Received ${fileArray.length} files.`
                        }]
                    });
                }

                const validFiles = [];
                const failures = [];
                
                for (const file of fileArray) {
                    const resolvedFile = await Promise.resolve(file);
                    if (resolvedFile.createReadStream) {
                        const stream = resolvedFile.createReadStream();
                        const chunks = [];
                        let totalSize = 0;

                        try {
                            for await (const chunk of stream) {
                                totalSize += chunk.length;
                                if (totalSize > MAX_FILE_SIZE) {
                                    failures.push({
                                        filename: resolvedFile.filename,
                                        message: `File exceeds maximum size of ${MAX_FILE_SIZE / 1000000}MB`
                                    });
                                    break;
                                }
                                chunks.push(chunk);
                            }

                            if (totalSize <= MAX_FILE_SIZE) {
                                resolvedFile.createReadStream = () => Readable.from(Buffer.concat(chunks));
                                validFiles.push(resolvedFile);
                            }
                        } catch (error) {
                            failures.push({
                                filename: resolvedFile.filename,
                                message: `Error processing file: ${error.message}`
                            });
                        }
                    }
                }

                let uploadResult = {
                    successful: [],
                    failures,
                    totalProcessed: fileArray.length,
                    successCount: 0,
                    failureCount: failures.length
                };

                if (validFiles.length > 0) {
                    const result = await uploadFilesToBlob(validFiles, containerName);
                    uploadResult = {
                        ...result,
                        failures: [...failures, ...result.failures],
                        totalProcessed: fileArray.length,
                        failureCount: failures.length + result.failureCount
                    };
                }

                const message = uploadResult.successCount === 0 
                    ? 'No files were uploaded successfully'
                    : uploadResult.failureCount === 0
                        ? 'All files uploaded successfully'
                        : `${uploadResult.successCount} files uploaded, ${uploadResult.failureCount} failed`;

                return createResponse('FileUploadResponse', 
                    uploadResult.successCount > 0 ? 'SUCCESS' : 'FAILURE',
                    message,
                    {
                        result: { bulkResult: uploadResult }
                    }
                );
            } catch (error) {
                console.error(error);
                return createResponse('FileUploadResponse', 'FAILURE', 'Error uploading files', {
                    errors: [{ field: 'uploadFiles', message: error.message }]
                });
            }
        },

        deleteFile: async (_, { key, containerName = 'documents' }) => {
            try {
                const containerClient = getContainerClient(containerName);
                const blockBlobClient = containerClient.getBlockBlobClient(key);
                const url = blockBlobClient.url;
                
                await deleteFileFromBlob(key, containerName);
                return createResponse('FileDeleteResponse', 'SUCCESS', 'File deleted successfully', {
                    result: { file: { key, url } }
                });
            } catch (error) {
                console.error(error);
                return createResponse('FileErrorResponse', 'FAILURE', 'Error deleting file', {
                    errors: [{ field: 'deleteFile', message: error.message }]
                });
            }
        }
    }
};

module.exports = uploadResolvers; 
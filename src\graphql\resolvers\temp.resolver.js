module.exports = {
    Query: {
        tempQuery: (_, __, context) => {
            return {
                message: 'Hello World'
            };
        },
        
        tempAuthQuery: (_, __, context) => {
            console.log(context);
            const user = context.user;
            if (!user) {
                throw new Error('Not authenticated');
            }
            return {
                message: `Hello ${user.firstName || 'authenticated user'}!`
            };
        }
    }
};


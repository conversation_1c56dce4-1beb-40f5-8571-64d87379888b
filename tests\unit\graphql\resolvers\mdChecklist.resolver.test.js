const mongoose = require('mongoose');
const MdChecklist = require('../../../../src/models/MdChecklist');
const MdPartyType = require('../../../../src/models/MdPartyType');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const mdChecklistResolvers = require('../../../../src/graphql/resolvers/mdChecklist.resolver');

jest.mock('../../../../src/models/MdChecklist');
jest.mock('../../../../src/models/MdPartyType');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/validation.util');

describe('mdChecklistResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdChecklist Field Resolvers', () => {
        describe('partyType', () => {
            it('should return party type successfully', async () => {
                const mockPartyType = { _id: 'partyTypeId', name: 'Test Party Type' };
                const parent = { partyType: 'partyTypeId' };

                MdPartyType.findById.mockResolvedValue(mockPartyType);

                const result = await mdChecklistResolvers.MdChecklist.partyType(parent);

                expect(MdPartyType.findById).toHaveBeenCalledWith('partyTypeId');
                expect(result).toEqual(mockPartyType);
            });

            it('should throw error when getting party type fails', async () => {
                const parent = { partyType: 'partyTypeId' };
                const error = new Error('Database error');

                MdPartyType.findById.mockRejectedValue(error);

                await expect(mdChecklistResolvers.MdChecklist.partyType(parent))
                    .rejects
                    .toThrow('Error getting party type');
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getMdChecklistById', () => {
            it('should return checklist from cache if available', async () => {
                const mockChecklist = { _id: 'checklistId' };
                getByIdCache.mockResolvedValue(mockChecklist);
                createResponse.mockReturnValue('success response');

                const result = await mdChecklistResolvers.Query.getMdChecklistById(null, { id: 'checklistId' });

                expect(getByIdCache).toHaveBeenCalledWith('checklistId');
                expect(setCache).toHaveBeenCalledWith('checklistId', mockChecklist);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistResponse',
                    'SUCCESS',
                    'MdChecklist fetched successfully',
                    { result: { mdChecklist: mockChecklist } }
                );
                expect(result).toBe('success response');
            });

            it('should return checklist from database if not in cache', async () => {
                const mockChecklist = { _id: 'checklistId' };
                getByIdCache.mockResolvedValue(null);
                MdChecklist.findById.mockResolvedValue(mockChecklist);
                createResponse.mockReturnValue('success response');

                const result = await mdChecklistResolvers.Query.getMdChecklistById(null, { id: 'checklistId' });

                expect(MdChecklist.findById).toHaveBeenCalledWith('checklistId');
                expect(setCache).toHaveBeenCalledWith('checklistId', mockChecklist);
                expect(result).toBe('success response');
            });

            it('should return error response when checklist not found', async () => {
                getByIdCache.mockResolvedValue(null);
                MdChecklist.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdChecklistResolvers.Query.getMdChecklistById(null, { id: 'checklistId' });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'MdChecklist not found',
                    { errors: [{ field: 'getMdChecklistById', message: 'MdChecklist not found' }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('getMdChecklists', () => {
            it('should return checklists successfully', async () => {
                const mockChecklists = [{ _id: 'checklistId1' }, { _id: 'checklistId2' }];
                const mockPaginationInfo = { total: 2, hasMore: false };

                const mockQuery = { status: 'ACTIVE' };
                const mockPopulate = jest.fn().mockReturnThis();
                const mockLimit = jest.fn().mockReturnThis();
                const mockSkip = jest.fn().mockReturnThis();
                const mockSort = jest.fn().mockResolvedValue(mockChecklists);

                MdChecklist.find.mockReturnValue({
                    populate: mockPopulate,
                    limit: mockLimit,
                    skip: mockSkip,
                    sort: mockSort
                });

                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                createResponse.mockReturnValue('success response');

                const result = await mdChecklistResolvers.Query.getMdChecklists(null, {
                    filter: { status: 'ACTIVE' },
                    pagination: { limit: 10, skip: 0 }
                });

                expect(getPaginationInfo).toHaveBeenCalled();
                expect(mockPopulate).toHaveBeenCalledWith('partyType');
                expect(mockLimit).toHaveBeenCalledWith(10);
                expect(mockSkip).toHaveBeenCalledWith(0);
                expect(mockSort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(result).toBe('success response');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createMdChecklist', () => {
            it('should return validation error response', async () => {
                const input = { title: 'Test Checklist' };
                const validationError = {
                    errors: [{ field: 'partyType', message: 'Party type is required' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('error response');

                const result = await mdChecklistResolvers.Mutation.createMdChecklist(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'Invalid reference',
                    { errors: validationError.errors }
                );
                expect(result).toBe('error response');
            });
        });

        describe('deleteMdChecklist', () => {
            it('should delete MdChecklist successfully when no references exist', async () => {
                const id = 'testId';
                const mockMdChecklist = { _id: id, name: 'Test Checklist' };

                findReferences.mockResolvedValue([]);
                MdChecklist.findByIdAndDelete.mockResolvedValue(mockMdChecklist);
                clearCacheById.mockResolvedValue();
                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await mdChecklistResolvers.Mutation.deleteMdChecklist(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdChecklist');
                expect(MdChecklist.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistResponse',
                    'SUCCESS',
                    'MdChecklist deleted successfully',
                    { result: { mdChecklist: mockMdChecklist } }
                );
                expect(result.status).toBe('SUCCESS');
            });

            it('should return error response when MdChecklist has references', async () => {
                const id = 'testId';
                const references = ['Party', 'Event'];

                findReferences.mockResolvedValue(references);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await mdChecklistResolvers.Mutation.deleteMdChecklist(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdChecklist');
                expect(MdChecklist.findByIdAndDelete).not.toHaveBeenCalled();
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'MdChecklist cannot be deleted',
                    { errors: [{ field: 'id', message: 'MdChecklist cannot be deleted as it is being used in: Party, Event' }] }
                );
                expect(result.status).toBe('FAILURE');
            });

            it('should return error response when MdChecklist not found', async () => {
                const id = 'nonExistentId';

                findReferences.mockResolvedValue([]);
                MdChecklist.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await mdChecklistResolvers.Mutation.deleteMdChecklist(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdChecklist');
                expect(MdChecklist.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'MdChecklist not found',
                    { errors: [{ field: 'deleteMdChecklist', message: 'MdChecklist not found' }] }
                );
                expect(result.status).toBe('FAILURE');
            });

            it('should handle unexpected errors during deletion', async () => {
                const id = 'testId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdChecklist.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await mdChecklistResolvers.Mutation.deleteMdChecklist(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdChecklist');
                expect(MdChecklist.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'Error deleting MdChecklist',
                    { errors: [{ field: 'deleteMdChecklist', message: error.message }] }
                );
                expect(result.status).toBe('FAILURE');
            });
        });

        describe('updateMdChecklist', () => {
            it('should update and return the checklist successfully', async () => {
                const id = 'checklistId';
                const input = { 
                    title: 'Updated Checklist',
                    partyType: 'partyTypeId'
                };
                const updatedChecklist = { 
                    _id: id,
                    ...input
                };

                validateReferences.mockResolvedValue(null);
                MdChecklist.findByIdAndUpdate.mockResolvedValue(updatedChecklist);
                createResponse.mockReturnValue('successResponse');

                const result = await mdChecklistResolvers.Mutation.updateMdChecklist(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdChecklist');
                expect(MdChecklist.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistResponse',
                    'SUCCESS',
                    'MdChecklist updated successfully',
                    { result: { mdChecklist: updatedChecklist } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when checklist is not found', async () => {
                const id = 'nonexistentId';
                const input = { title: 'Updated Checklist' };

                validateReferences.mockResolvedValue(null);
                MdChecklist.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdChecklistResolvers.Mutation.updateMdChecklist(null, { id, input });

                expect(MdChecklist.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'MdChecklist not found',
                    { errors: [{ field: 'updateMdChecklist', message: 'MdChecklist not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when validation fails', async () => {
                const id = 'checklistId';
                const input = { partyType: 'invalidId' };
                const validationError = {
                    errors: [{ field: 'partyType', message: 'Invalid party type reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('validationErrorResponse');

                const result = await mdChecklistResolvers.Mutation.updateMdChecklist(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdChecklist');
                expect(MdChecklist.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'Invalid reference',
                    { errors: validationError.errors }
                );
                expect(result).toBe('validationErrorResponse');
            });

            it('should return error response when update fails', async () => {
                const id = 'checklistId';
                const input = { title: 'Updated Checklist' };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                MdChecklist.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdChecklistResolvers.Mutation.updateMdChecklist(null, { id, input });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdChecklistErrorResponse',
                    'FAILURE',
                    'Error updating MdChecklist',
                    { errors: [{ field: 'updateMdChecklist', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 

const { model, Schema } = require('mongoose');

const invitationSettingsSchema = new Schema({
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: true },
    is_guest_list_public: { type: Boolean, default: false },
    additional_guest_allowed: { type: Boolean, default: false },
    additional_guest_limit: { type: Number, default: 0 },
    allow_guest_to_add_photo: { type: Boolean, default: false },
    send_auto_reminder_to_all_guests: { type: Boolean, default: false },
}, { timestamps: true});

const InvitationSettings = model('InvitationSettings', invitationSettingsSchema);

module.exports = InvitationSettings;

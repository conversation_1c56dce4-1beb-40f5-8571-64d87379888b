const Tag = require('../../models/Tag');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const buildTagQuery = require('./filters/tag.filter');
const findReferences = require('./references/tag.references');
const MdIcon = require('../../models/MdIcon');
const { validateReferences } = require('../../utils/validation.util');

const tagIdSchema = {
    icon: { type: 'single', model: MdIcon }
};

const tagResolvers = {
    Tag: {
        icon: async (parent) => {
            try {
                return await MdIcon.findById(parent.icon);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting icon');
            }
        }
    },
    Query: {
        getTagById: async (_, { id }) => {
            try {
                let tag = await getByIdCache(id);
                if (!tag) {
                    tag = await Tag.findById(id);
                    if (!tag) {
                        return createResponse('TagErrorResponse', 'FAILURE', 'Tag not found', { 
                            errors: [{ field: 'id', message: 'Tag not found' }] 
                        });
                    }
                    await setCache(id, tag);
                }
                return createResponse('TagResponse', 'SUCCESS', 'Tag fetched successfully', { 
                    result: { tag } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('TagErrorResponse', 'FAILURE', 'Error retrieving tag', { 
                    errors: [{ field: 'getTagById', message: error.message }] 
                });
            }
        },
        getTags: async (_, { filter, pagination }) => {
            try {
                const query = await buildTagQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Tag, query, limit, skip);

                const tags = await Tag.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (tags.length === 0) {
                    return createResponse('TagsResponse', 'FAILURE', 'No tags found', { result: { tags }, pagination: paginationInfo });
                }
                return createResponse('TagsResponse', 'SUCCESS', 'Tags fetched successfully', { result: { tags }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('TagErrorResponse', 'FAILURE', 'Error retrieving tags', { errors: [{ field: 'getTags', message: error.message }] });
            }
        }
    },
    Mutation: {
        createTag: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, tagIdSchema, 'Tag');
                if (validationError) {
                    return createResponse('TagErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const tag = new Tag(input);
                await tag.save();
                return createResponse('TagResponse', 'SUCCESS', 'Tag created successfully', { result: { tag } });
            } catch (error) {
                console.error(error);
                return createResponse('TagErrorResponse', 'FAILURE', 'Error creating tag', { errors: [{ field: 'createTag', message: error.message }] });
            }
        },
        deleteTag: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('TagErrorResponse', 'FAILURE', 'Tag cannot be deleted', {
                        errors: [{ field: 'deleteTag', message: `Tag cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }
                const tag = await Tag.findByIdAndDelete(id);
                if (!tag) {
                    return createResponse('TagErrorResponse', 'FAILURE', 'Tag not found', { errors: [{ field: 'deleteTag', message: 'Tag not found' }] });
                }
                await clearCacheById(id);
                return createResponse('TagResponse', 'SUCCESS', 'Tag deleted successfully', { result: { tag } });
            } catch (error) {
                console.error(error);
                return createResponse('TagErrorResponse', 'FAILURE', 'Error deleting tag', { errors: [{ field: 'deleteTag', message: error.message }] });
            }
        },
        updateTag: async (_, { id, input }) => {
            try {
                const tag = await Tag.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                if (!tag) {
                    return createResponse('TagErrorResponse', 'FAILURE', 'Tag not found', {
                        errors: [{ field: 'id', message: 'Tag not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('TagResponse', 'SUCCESS', 'Tag updated successfully', {
                    result: { tag }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TagErrorResponse', 'FAILURE', 'Error updating tag', {
                    errors: [{ field: 'updateTag', message: error.message }]
                });
            }
        }
    }
};

module.exports = tagResolvers;
const mongoose = require('mongoose');
const MdVendorSubType = require('../../../../src/models/MdVendorSubType');
const MdIcon = require('../../../../src/models/MdIcon');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const mdVendorSubTypeResolvers = require('../../../../src/graphql/resolvers/mdVendorSubType.resolver');
const buildMdVendorSubTypeQuery = require('../../../../src/graphql/resolvers/filters/mdVendorSubType.filter');

const baseVendorSubTypeIdSchema = {
    icon: { type: 'single', model: MdIcon }
};

const vendorSubTypeIdSchema = { ...baseVendorSubTypeIdSchema };

const createVendorSubTypeIdSchema = {
    ...baseVendorSubTypeIdSchema
};


jest.mock('../../../../src/models/MdVendorSubType');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdVendorSubType.filter');

describe('mdVendorSubTypeResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdVendorSubType Field Resolvers', () => {
        describe('icon', () => {
            it('should return icon when found', async () => {
                const mockIcon = { _id: 'iconId', name: 'Test Icon' };
                const parent = { icon: 'iconId' };

                MdIcon.findById.mockResolvedValue(mockIcon);

                const result = await mdVendorSubTypeResolvers.MdVendorSubType.icon(parent);

                expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
                expect(result).toEqual(mockIcon);
            });

            it('should throw error when icon retrieval fails', async () => {
                const parent = { icon: 'iconId' };
                const error = new Error('Database error');

                MdIcon.findById.mockRejectedValue(error);

                await expect(mdVendorSubTypeResolvers.MdVendorSubType.icon(parent))
                    .rejects
                    .toThrow('Error getting icon');
            });
        });
    });

    describe('Query', () => {
        describe('getMdVendorSubTypeById', () => {
            it('should return cached MdVendorSubType if available', async () => {
                const id = 'testId';
                const cachedMdVendorSubType = {
                    _id: id,
                    name: 'Cached VendorSubType'
                };

                getByIdCache.mockResolvedValue(cachedMdVendorSubType);
                createResponse.mockReturnValue('successResponse');

                const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdVendorSubType.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeResponse',
                    'SUCCESS',
                    'MdVendorSubType retrieved successfully from cache',
                    { result: { mdVendorSubType: cachedMdVendorSubType } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache MdVendorSubType if not in cache', async () => {
                const id = 'testId';
                const mdVendorSubType = {
                    _id: id,
                    name: 'Test VendorSubType'
                };

                getByIdCache.mockResolvedValue(null);
                MdVendorSubType.findById.mockResolvedValue(mdVendorSubType);
                setCache.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdVendorSubType.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, mdVendorSubType);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeResponse',
                    'SUCCESS',
                    'MdVendorSubType retrieved successfully',
                    { result: { mdVendorSubType } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if MdVendorSubType not found', async () => {
                const id = 'nonexistentId';

                getByIdCache.mockResolvedValue(null);
                MdVendorSubType.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdVendorSubType.findById).toHaveBeenCalledWith(id);
                expect(setCache).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'MdVendorSubType not found',
                    { errors: [{ field: 'id', message: 'MdVendorSubType not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors and return error response', async () => {
                const id = 'testId';
                const error = new Error('Database error');

                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Error retrieving MdVendorSubType',
                    { errors: [{ field: 'id', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});

describe('mdVendorSubTypeResolvers Query', () => {
    describe('getMdVendorSubTypes', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should return vendor sub types successfully', async () => {
            const filter = { name: 'test' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: 'test', $options: 'i' } };
            const mdVendorSubTypes = [
                { _id: '1', name: 'Test SubType 1' },
                { _id: '2', name: 'Test SubType 2' }
            ];
            const paginationInfo = {
                total: 2,
                limit: 10,
                skip: 0
            };

            buildMdVendorSubTypeQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);

            const mockSkip = jest.fn().mockReturnThis();
            const mockLimit = jest.fn().mockResolvedValue(mdVendorSubTypes);

            MdVendorSubType.find.mockReturnValue({
                skip: mockSkip,
                limit: mockLimit
            });

            const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypes(null, { filter, pagination });

            expect(buildMdVendorSubTypeQuery).toHaveBeenCalledWith(filter);
            expect(getPaginationInfo).toHaveBeenCalledWith(MdVendorSubType, query, 10, 0);
            expect(MdVendorSubType.find).toHaveBeenCalledWith(query);
            expect(mockSkip).toHaveBeenCalledWith(0);
            expect(mockLimit).toHaveBeenCalledWith(10);
            expect(createResponse).toHaveBeenCalledWith(
                'MdVendorSubTypesResponse',
                'SUCCESS',
                'MdVendorSubTypes fetched successfully',
                {
                    result: { mdVendorSubTypes },
                    pagination: paginationInfo
                }
            );
        });

        it('should return empty response when no vendor sub types found', async () => {
            const filter = { name: 'nonexistent' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: 'nonexistent', $options: 'i' } };
            const mdVendorSubTypes = [];
            const paginationInfo = {
                total: 0,
                limit: 10,
                skip: 0
            };

            buildMdVendorSubTypeQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);

            const mockSkip = jest.fn().mockReturnThis();
            const mockLimit = jest.fn().mockResolvedValue(mdVendorSubTypes);

            MdVendorSubType.find.mockReturnValue({
                skip: mockSkip,
                limit: mockLimit
            });

            const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypes(null, { filter, pagination });

            expect(createResponse).toHaveBeenCalledWith(
                'MdVendorSubTypesResponse',
                'FAILURE',
                'No MdVendorSubTypes found',
                {
                    result: { mdVendorSubTypes },
                    pagination: paginationInfo
                }
            );
        });

        it('should handle errors properly', async () => {
            const error = new Error('Database error');
            buildMdVendorSubTypeQuery.mockImplementation(() => {
                throw error;
            });

            const result = await mdVendorSubTypeResolvers.Query.getMdVendorSubTypes(null, { filter: {}, pagination: {} });

            expect(createResponse).toHaveBeenCalledWith(
                'MdVendorSubTypeErrorResponse',
                'FAILURE',
                'Error getting MdVendorSubTypes',
                {
                    errors: [{ field: 'getMdVendorSubTypes', message: error.message }]
                }
            );
        });

        it('should use default pagination values when not provided', async () => {
            const filter = {};
            const query = {};
            const mdVendorSubTypes = [{ _id: '1', name: 'Test SubType' }];
            const paginationInfo = {
                total: 1,
                limit: 10,
                skip: 0
            };

            buildMdVendorSubTypeQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);

            const mockSkip = jest.fn().mockReturnThis();
            const mockLimit = jest.fn().mockResolvedValue(mdVendorSubTypes);

            MdVendorSubType.find.mockReturnValue({
                skip: mockSkip,
                limit: mockLimit
            });

            await mdVendorSubTypeResolvers.Query.getMdVendorSubTypes(null, { filter });

            expect(getPaginationInfo).toHaveBeenCalledWith(MdVendorSubType, query, 10, 0);
            expect(mockSkip).toHaveBeenCalledWith(0);
            expect(mockLimit).toHaveBeenCalledWith(10);
        });
    });
});

describe('mdVendorSubTypeResolvers', () => {
    describe('Mutation', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        describe('createMdVendorSubType', () => {
            it('should create and return MdVendorSubType successfully', async () => {
                const input = { name: 'Test SubType', icon: 'iconId' };
                const mockMdVendorSubType = {
                    _id: 'id1',
                    ...input,
                    save: jest.fn().mockResolvedValue(true)
                };

                validateReferences.mockResolvedValue(null);
                MdVendorSubType.mockImplementation(() => mockMdVendorSubType);
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdVendorSubType created successfully',
                    result: { mdVendorSubType: mockMdVendorSubType }
                });

                const result = await mdVendorSubTypeResolvers.Mutation.createMdVendorSubType(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, createVendorSubTypeIdSchema, 'MdVendorSubType');
                expect(MdVendorSubType).toHaveBeenCalledWith(input);
                expect(mockMdVendorSubType.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeResponse',
                    'SUCCESS',
                    'MdVendorSubType created successfully',
                    { result: { mdVendorSubType: mockMdVendorSubType } }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'MdVendorSubType created successfully',
                    result: { mdVendorSubType: mockMdVendorSubType }
                });
            });

            it('should return error response when validation fails', async () => {
                const input = { name: 'Test SubType', icon: 'invalidIconId' };
                const validationError = {
                    message: 'Validation errors',
                    errors: [{ field: 'icon', message: 'Invalid icon reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });

                MdVendorSubType.mockClear();

                const result = await mdVendorSubTypeResolvers.Mutation.createMdVendorSubType(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, createVendorSubTypeIdSchema, 'MdVendorSubType');
                expect(MdVendorSubType).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });
            });

            it('should handle error when creating MdVendorSubType fails', async () => {
                const input = {
                    name: 'Test Vendor SubType',
                    icon: 'iconId'
                };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                MdVendorSubType.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating MdVendorSubType',
                    errors: [{ field: 'createMdVendorSubType', message: error.message }]
                });

                const result = await mdVendorSubTypeResolvers.Mutation.createMdVendorSubType(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdVendorSubType');
                expect(MdVendorSubType).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Error creating MdVendorSubType',
                    {
                        errors: [{ field: 'createMdVendorSubType', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating MdVendorSubType',
                    errors: [{ field: 'createMdVendorSubType', message: error.message }]
                });
            });
        });

        describe('updateMdVendorSubType', () => {
            it('should update MdVendorSubType successfully', async () => {
                const id = 'vendorSubTypeId';
                const input = { name: 'Updated SubType' };
                const updatedSubType = { _id: id, ...input };

                validateReferences.mockResolvedValue(null);
                MdVendorSubType.findByIdAndUpdate.mockResolvedValue(updatedSubType);
                createResponse.mockReturnValue('success response');

                const result = await mdVendorSubTypeResolvers.Mutation.updateMdVendorSubType(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, vendorSubTypeIdSchema, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeResponse',
                    'SUCCESS',
                    'MdVendorSubType updated successfully',
                    { result: { mdVendorSubType: updatedSubType } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response when validation fails', async () => {
                const id = 'vendorSubTypeId';
                const input = { name: 'Updated SubType' };
                const validationError = {
                    message: 'Validation errors',
                    errors: [{ field: 'icon', message: 'Invalid icon reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('error response');

                const result = await mdVendorSubTypeResolvers.Mutation.updateMdVendorSubType(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, vendorSubTypeIdSchema, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Validation errors',
                    { errors: validationError.errors }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if an error occurs during update', async () => {
                const id = 'vendorSubTypeId';
                const input = { name: 'Updated SubType' };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                MdVendorSubType.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdVendorSubTypeResolvers.Mutation.updateMdVendorSubType(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, vendorSubTypeIdSchema, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Error updating MdVendorSubType',
                    { errors: [{ field: 'updateMdVendorSubType', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdVendorSubType', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should check for references before deletion', async () => {
                const id = 'vendorSubTypeId';
                const references = [
                    { model: 'Collection1', id: 'ref1' },
                    { model: 'Collection2', id: 'ref2' }
                ];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdVendorSubTypeResolvers.Mutation.deleteMdVendorSubType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Cannot delete MdVendorSubType with existing references',
                    {
                        errors: [
                            { field: 'id', message: 'Referenced in Collection1 with id ref1' },
                            { field: 'id', message: 'Referenced in Collection2 with id ref2' }
                        ]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should successfully delete MdVendorSubType when no references exist', async () => {
                const id = 'vendorSubTypeId';
                const deletedVendorSubType = { _id: id, name: 'Test VendorSubType' };

                findReferences.mockResolvedValue([]);
                MdVendorSubType.findByIdAndDelete.mockResolvedValue(deletedVendorSubType);
                createResponse.mockReturnValue('successResponse');

                const result = await mdVendorSubTypeResolvers.Mutation.deleteMdVendorSubType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeResponse',
                    'SUCCESS',
                    'MdVendorSubType deleted successfully',
                    { result: { mdVendorSubType: deletedVendorSubType } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if an error occurs during deletion', async () => {
                const id = 'vendorSubTypeId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdVendorSubType.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdVendorSubTypeResolvers.Mutation.deleteMdVendorSubType(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdVendorSubType');
                expect(MdVendorSubType.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdVendorSubTypeErrorResponse',
                    'FAILURE',
                    'Error deleting MdVendorSubType',
                    { errors: [{ field: 'deleteMdVendorSubType', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
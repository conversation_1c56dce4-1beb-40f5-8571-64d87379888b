const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdAppSettings = require('../../../src/models/MdAppSettings');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();

    await mongoose.connect(uri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdAppSettings Model Test', () => {
    it('should create and save MdAppSettings successfully', async () => {
        const validAppSettings = new MdAppSettings({
        version: '1.0.0',
        themes: {
            light: {
            backgroundColor: '#ffffff',
            primaryColor: '#000000',
            secondaryColor: '#cccccc',
            textColor: '#333333',
            },
            dark: {
            backgroundColor: '#000000',
            primaryColor: '#ffffff',
            secondaryColor: '#999999',
            textColor: '#cccccc',
            },
        },
        navBars: {
            userDashboard: {
            tabs: {
                home: new mongoose.Types.ObjectId(),
                myEvents: new mongoose.Types.ObjectId(),
                publicEvents: new mongoose.Types.ObjectId(),
            },
            },
            hostEventDashboard: {
            tabs: {
                eventsDb: new mongoose.Types.ObjectId(),
                tasks: new mongoose.Types.ObjectId(),
                guests: new mongoose.Types.ObjectId(),
                apps: new mongoose.Types.ObjectId(),
                messages: new mongoose.Types.ObjectId(),
            },
            },
        },
        });

        const savedAppSettings = await validAppSettings.save();
        expect(savedAppSettings._id).toBeDefined();
        expect(savedAppSettings.version).toBe('1.0.0');
    });

    it('should fail validation if required fields are missing', async () => {
        const invalidAppSettings = new MdAppSettings({
        themes: {
            light: {
            backgroundColor: '#ffffff',
            primaryColor: '#000000',
            secondaryColor: '#cccccc',
            textColor: '#333333',
            },
        },
        navBars: {
            userDashboard: {
            tabs: {
                home: new mongoose.Types.ObjectId(),
                myEvents: new mongoose.Types.ObjectId(),
                publicEvents: new mongoose.Types.ObjectId(),
            },
            },
        },
        });

        let err;
        try {
        await invalidAppSettings.save();
        } catch (error) {
        err = error;
        }
        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.version).toBeDefined();
        expect(err.errors['themes.dark.backgroundColor']).toBeDefined();
    });

    it('should reference MdIcon correctly in navBars', async () => {
        const appSettings = new MdAppSettings({
        version: '1.0.1',
        themes: {
            light: {
            backgroundColor: '#f0f0f0',
            primaryColor: '#333333',
            secondaryColor: '#444444',
            textColor: '#555555',
            },
            dark: {
            backgroundColor: '#222222',
            primaryColor: '#dddddd',
            secondaryColor: '#bbbbbb',
            textColor: '#cccccc',
            },
        },
        navBars: {
            userDashboard: {
            tabs: {
                home: new mongoose.Types.ObjectId(),
                myEvents: new mongoose.Types.ObjectId(),
                publicEvents: new mongoose.Types.ObjectId(),
            },
            },
            hostEventDashboard: {
            tabs: {
                eventsDb: new mongoose.Types.ObjectId(),
                tasks: new mongoose.Types.ObjectId(),
                guests: new mongoose.Types.ObjectId(),
                apps: new mongoose.Types.ObjectId(),
                messages: new mongoose.Types.ObjectId(),
            },
            },
        },
        });

        const savedAppSettings = await appSettings.save();
        expect(savedAppSettings.navBars.userDashboard.tabs.home).toBeDefined();
        expect(mongoose.Types.ObjectId.isValid(savedAppSettings.navBars.userDashboard.tabs.home)).toBe(true);
    });
});
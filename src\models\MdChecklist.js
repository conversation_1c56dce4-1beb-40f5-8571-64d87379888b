const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const checklistItemSchema = new Schema({
    order: { type: Number, required: true },
    title: { type: String, required: true },
    description: { type: String, required: true }
});

const mdChecklistSchema = new Schema({
    partyType: { type: Schema.Types.ObjectId, ref: 'MdPartyType', required: true },
    title: { type: String, required: true },
    description: { type: String, required: true },
    items: { type: [checklistItemSchema], required: true, default: [] }
}, { timestamps: true, collection: 'md_checklists' });

const MdChecklist = model('MdChecklist', mdChecklistSchema);

module.exports = MdChecklist; 
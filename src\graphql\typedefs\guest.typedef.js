const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Guest {
        id: ID
        user: User!
        party: Party!
        additionalGuestsCount: Int
    }

    input GuestFilterInput{
        userId: ID
        partyId: ID
        user: UserFilterInput
        party: PartyFilterInput
    }

    type GuestWrapper {
        guest: Guest!
    }

    type GuestsWrapper {
        guests: [Guest]!
    }

    type GuestR<PERSON>ponse implements Response {
        status: ResponseStatus!
        message: String!
        result: GuestWrapper!
    }

    type GuestsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: GuestsWrapper!
        pagination: PaginationInfo!
    }

    type PartyGuestsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: GuestsWrapper!
    }

    type GuestErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union GuestResult = GuestResponse | GuestErrorResponse
    union GuestsResult = GuestsResponse | GuestErrorResponse
    union PartyGuestsResult = PartyGuestsResponse | GuestErrorResponse

    type Query {
        getGuestById(id: ID!): GuestResult!
        getGuestsByPartyId(partyId: ID!, filter: GuestFilterInput): PartyGuestsResult!
        getGuests(filter: GuestFilterInput, pagination: PaginationInput): GuestsResult!
    }

    input ContactInput {
        firstName: String!
        lastName: String
        email: String
        phone: PhoneNumber!
    }

    type Mutation {
        createGuestFromContact(contact: ContactInput!, partyId: ID!): GuestResult!
        createGuestsFromContacts(contacts: [ContactInput!]!, partyId: ID!): GuestsResult!
        createGuest(userId: ID!, partyId: ID!): GuestResult!
        deleteGuest(id: ID!): GuestResult!
    }
`;

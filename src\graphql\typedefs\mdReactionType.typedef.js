const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdReactionType {
        id: ID!
        name: String!
        icon: MdIcon!
        active: Boolean!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdReactionTypeFilterInput {
        id: String
        name: String
        icon: MdIconFilterInput
        active: Boolean
    }

    type MdReactionTypeWrapper {
        mdReactionType: MdReactionType!
    }

    type MdReactionTypesWrapper {
        mdReactionTypes: [MdReactionType]!
    }

    type MdReactionTypesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdReactionTypesWrapper!
        pagination: PaginationInfo!
    }

    type MdReactionTypeResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdReactionTypeWrapper!
    }

    type MdReactionTypeErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdReactionTypeResult = MdReactionTypeResponse | MdReactionTypeErrorResponse
    union MdReactionTypesResult = MdReactionTypesResponse | MdReactionTypeErrorResponse

    type Query {
        getMdReactionTypeById(id: ID!): MdReactionTypeResult!
        getMdReactionTypes(filter: MdReactionTypeFilterInput, pagination: PaginationInput): MdReactionTypesResult!
    }

    input MdReactionTypeInput {
        name: String!
        icon: ID!
        active: Boolean!
    }

    input MdReactionTypeUpdateInput {
        name: String
        icon: ID
        active: Boolean
    }

    type Mutation {
        createMdReactionType(input: MdReactionTypeInput!): MdReactionTypeResult!
        updateMdReactionType(id: ID!, input: MdReactionTypeUpdateInput!): MdReactionTypeResult!
        deleteMdReactionType(id: ID!): MdReactionTypeResult!
    }
`; 
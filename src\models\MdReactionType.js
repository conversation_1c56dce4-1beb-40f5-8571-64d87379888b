const { model, Schema } = require('mongoose');

const mdReactionTypeSchema = new Schema({
    name: { type: String, required: true },
    icon: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    active: { type: Boolean, required: true }
}, { timestamps: true, collection: 'md_reaction_types' });

const MdReactionType = model('MdReactionType', mdReactionTypeSchema);

module.exports = MdReactionType; 
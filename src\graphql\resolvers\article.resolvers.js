const axios = require('axios');
const { createResponse } = require('../../utils/response.util');
const buildArticleFilter = require('./filters/article.filter');
const UserArticleActivity = require('../../models/UserArticleActivity');

const articleResolvers = {
  Article: {
    userArticleActivity: async (parent, _, context) => {
      if (!context.user) return null;
      
      try {
        const activity = await UserArticleActivity.findOne({
          userId: context.user._id,
          $or: [
            { articleId: parent.id },
            { 'articleId.id': parent.id }
          ]
        });
        return activity;
      } catch (error) {
        console.error('Error fetching user article activity:', error);
        return null;
      }
    }
  },
  Query: {
    getArticles: async (_, { filter, pagination }, context) => {
      try {
        // this link could be changed later
        const response = await axios.post(
          'https://945mpwty29.ap-south-1.awsapprunner.com/feeds/search-by-tags?tags=all',
          '',
          {
            headers: {
              'accept': 'application/json'
            }
          }
        );

        if (!response.data || response.data.length === 0) {
          return createResponse('ArticlesResponse', 'SUCCESS', 'No articles found', {
            result: { articles: [] },
            pagination: {
              totalItems: 0,
              totalPages: 0,
              currentPage: 1,
              pageSize: pagination?.limit || 10
            }
          });
        }

        // Apply filtering
        const filterFn = buildArticleFilter(filter);
        let articles = response.data.filter(filterFn);

        // Apply pagination
        const limit = pagination?.limit || 10;
        const skip = pagination?.skip || 0;
        const currentPage = Math.floor(skip / limit) + 1;
        
        const paginatedArticles = articles.slice(skip, skip + limit);
        const totalCount = articles.length;

        return createResponse('ArticlesResponse', 'SUCCESS', 'Articles fetched successfully', {
          result: { 
            articles: paginatedArticles
          },
          pagination: {
            totalItems: totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: currentPage,
            pageSize: limit
          }
        });
      } catch (error) {
        console.error('Error fetching articles:', error);
        if (error.response) {
          return createResponse('ArticleErrorResponse', 'FAILURE', error.response.data.message || 'Error fetching articles', {
            errors: [{ field: 'getArticles', message: error.response.data.message || error.message }]
          });
        } else if (error.request) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Network error occurred', {
            errors: [{ field: 'getArticles', message: 'Network error occurred' }]
          });
        } else {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error fetching articles', {
            errors: [{ field: 'getArticles', message: error.message }]
          });
        }
      }
    },

    getAllArticles: async (_, { filter = {}, pagination = {} }, context) => {
      try {
        // Construct query parameters
        const queryParams = new URLSearchParams();
        
        // Add pagination parameters
        queryParams.append('skip', pagination.skip || 0);
        queryParams.append('limit', pagination.limit || 10);
        
        // Add filter parameters
        if (filter.approved !== undefined) queryParams.append('approved', filter.approved);
        if (filter.search) queryParams.append('search', filter.search);
        if (filter.sort_by) queryParams.append('sort_by', filter.sort_by);
        if (filter.sort_order) queryParams.append('sort_order', filter.sort_order);
        if (filter.id) queryParams.append('id', filter.id);
        if (filter.headline) queryParams.append('headline', filter.headline);
        
        // Format dates to YYYY-MM-DD format
        if (filter.start_date) {
          const startDate = new Date(filter.start_date);
          queryParams.append('start_date', startDate.toISOString().split('T')[0]);
        }
        if (filter.end_date) {
          const endDate = new Date(filter.end_date);
          queryParams.append('end_date', endDate.toISOString().split('T')[0]);
        }

        // Make the request to the external API
        const response = await axios.get(
          `https://945mpwty29.ap-south-1.awsapprunner.com/feeds/get-articles?${queryParams.toString()}`,
          {
            headers: {
              'accept': 'application/json'
            }
          }
        );

        if (!response.data || !response.data.articles) {
          return createResponse('ArticlesResponse', 'SUCCESS', 'No articles found', {
            result: { articles: [] },
            pagination: {
              totalItems: 0,
              totalPages: 0,
              currentPage: Math.floor((pagination.skip || 0) / (pagination.limit || 10)) + 1,
              pageSize: pagination.limit || 10
            }
          });
        }

        // Map the API response to match our GraphQL schema
        return createResponse('ArticlesResponse', 'SUCCESS', 'Articles fetched successfully', {
          result: { 
            articles: response.data.articles.map(article => ({
              id: article.id,
              headline: article.headline,
              description: article.description,
              image: article.image,
              source: article.source,
              date: article.date,
              approved: article.approved
            }))
          },
          pagination: {
            totalItems: response.data.total,
            totalPages: Math.ceil(response.data.total / (pagination.limit || 10)),
            currentPage: Math.floor(response.data.skip / (pagination.limit || 10)) + 1,
            pageSize: response.data.limit
          }
        });
      } catch (error) {
        console.error('Error fetching articles:', error);
        if (error.response) {
          return createResponse('ArticleErrorResponse', 'FAILURE', error.response.data.message || 'Error fetching articles', {
            errors: [{ field: 'getAllArticles', message: error.response.data.message || error.message }]
          });
        } else if (error.request) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Network error occurred', {
            errors: [{ field: 'getAllArticles', message: 'Network error occurred' }]
          });
        } else {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error fetching articles', {
            errors: [{ field: 'getAllArticles', message: error.message }]
          });
        }
      }
    }
  },

  Mutation: {
    createArticle: async (_, { input }, context) => {
      try {
        const response = await axios.post(
          'https://945mpwty29.ap-south-1.awsapprunner.com/feeds/create-article',
          input,
          {
            headers: {
              'accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.data) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error creating article', {
            errors: [{ field: 'createArticle', message: 'No response from server' }]
          });
        }

        return createResponse('ArticleResponse', 'SUCCESS', 'Article created successfully', {
          result: { 
            article: response.data
          }
        });
      } catch (error) {
        console.error('Error creating article:', error);
        if (error.response) {
          return createResponse('ArticleErrorResponse', 'FAILURE', error.response.data.message || 'Error creating article', {
            errors: [{ field: 'createArticle', message: error.response.data.message || error.message }]
          });
        } else if (error.request) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Network error occurred', {
            errors: [{ field: 'createArticle', message: 'Network error occurred' }]
          });
        } else {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error creating article', {
            errors: [{ field: 'createArticle', message: error.message }]
          });
        }
      }
    },
    
    updateArticle: async (_, { id, input }, context) => {
      try {
        const response = await axios.patch(
          `https://945mpwty29.ap-south-1.awsapprunner.com/feeds/update-article/${id}`,
          input,
          {
            headers: {
              'accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.data) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error updating article', {
            errors: [{ field: 'updateArticle', message: 'No response from server' }]
          });
        }

        return createResponse('ArticleResponse', 'SUCCESS', 'Article updated successfully', {
          result: { 
            article: response.data.article
          }
        });
      } catch (error) {
        console.error('Error updating article:', error);
        if (error.response) {
          return createResponse('ArticleErrorResponse', 'FAILURE', error.response.data.message || 'Error updating article', {
            errors: [{ field: 'updateArticle', message: error.response.data.message || error.message }]
          });
        } else if (error.request) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Network error occurred', {
            errors: [{ field: 'updateArticle', message: 'Network error occurred' }]
          });
        } else {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error updating article', {
            errors: [{ field: 'updateArticle', message: error.message }]
          });
        }
      }
    },

    deleteArticle: async (_, { id }, context) => {
      try {
        const response = await axios.delete(
          `https://945mpwty29.ap-south-1.awsapprunner.com/feeds/delete-article/${id}`,
          {
            headers: {
              'accept': 'application/json'
            }
          }
        );

        if (!response.data) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error deleting article', {
            errors: [{ field: 'deleteArticle', message: 'No response from server' }]
          });
        }

        return createResponse('ArticleResponse', 'SUCCESS', 'Article deleted successfully', {
          result: { 
            article: response.data.article
          }
        });
      } catch (error) {
        console.error('Error deleting article:', error);
        if (error.response) {
          return createResponse('ArticleErrorResponse', 'FAILURE', error.response.data.message || 'Error deleting article', {
            errors: [{ field: 'deleteArticle', message: error.response.data.message || error.message }]
          });
        } else if (error.request) {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Network error occurred', {
            errors: [{ field: 'deleteArticle', message: 'Network error occurred' }]
          });
        } else {
          return createResponse('ArticleErrorResponse', 'FAILURE', 'Error deleting article', {
            errors: [{ field: 'deleteArticle', message: error.message }]
          });
        }
      }
    }
  }
};

module.exports = articleResolvers; 

const Invitation = require('../../models/Invitation');
const Media = require('../../models/Media');
const { createResponse } = require('../../utils/response.util');
const { validateReferences } = require('../../utils/validation.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { deleteMediaAndRelatedData } = require('../../utils/media.util');
const Guest = require('../../models/Guest');
const Party = require('../../models/Party');
const InvitationRSVP = require('../../models/InvitationRSVP');
const LiveLocation = require('../../models/liveLocation');
const { notifyInvitationSent } = require('../../notification/partyNotification');

const updateInvitationSchema = {
    media: { type: 'array', required: false, model: Media },
    addNewGuests: { type: 'array', required: false, model: Guest }
}

const createRsvp = async (invitationId, guestIds) => {
    const rsvps = [];
    for (const guestId of guestIds) {
        const guest = await Guest.findById(guestId);
        if (!guest) {
            throw new Error(`Guest with ID ${guestId} not found`);
        }

        const existingRsvp = await InvitationRSVP.findOne({
            invitation: invitationId,
            guest: guestId
        });
        
        if (!existingRsvp) {
            const rsvp = new InvitationRSVP({
                invitation: invitationId,
                guest: guestId,
                status: 'PENDING'
            });
            await rsvp.save();
            rsvps.push(rsvp);

            const liveLocation = new LiveLocation({
                guestId: guestId
            });
            await liveLocation.save();
        }
    }

    if (rsvps.length > 0) {
        const invitation = await Invitation.findById(invitationId);
        const party = await Party.findById(invitation.party);
        party.rsvps = [...party.rsvps, ...rsvps.map(rsvp => rsvp._id)];
        await party.save();
    }

    return guestIds;
}

const invitationResolvers = {
    Invitation: {
        media: async (parent) => {
            return await Media.find({ _id: { $in: parent.media } });
        },
        savedGuests: async (parent) => {
            return await Guest.find({ _id: { $in: parent.savedGuests } });
        },
        sentToGuests: async (parent) => {
            return await Guest.find({ _id: { $in: parent.sentToGuests } });
        }
    },
    Mutation: {
        addMediaToInvitation: async (_, { invitationId, media }, context) => {
            try {
                const invitation = await Invitation.findById(invitationId);
                if (!invitation) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitationId', message: 'Invitation not found' }]
                    });
                }

                if (media) {
                    media.forEach(async (mediaItem) => {
                        const db_media = await Media.findById(mediaItem);
                        if (!db_media) {
                            return createResponse('InvitationErrorResponse', 'FAILURE', 'Media item not found', {
                                errors: [{ field: 'mediaItem', message: 'Media item not found' }]
                            });
                        }
                    });
                }

                if (invitation.media) {
                    const existingMedia = new Set(invitation.media.map(m => m.toString()));
                    const newMedia = media.filter(m => !existingMedia.has(m.toString()));
                    invitation.media = [...invitation.media, ...newMedia];
                }

                await invitation.save();
                return createResponse('InvitationResponse', 'SUCCESS', 'Media added to invitation successfully', {
                    result: { invitation }
                });

            } catch (error) {
                return createResponse('InvitationErrorResponse', 'FAILURE', 'Error adding media to invitation', {
                    errors: [{ field: 'addMediaToInvitation', message: error.message }]
                });
            }
        },

        updateInvitation: async (_, { invitationId, invitation }, context) => {
            try {
                const db_invitation = await Invitation.findById(invitationId);
                if (!db_invitation) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitationId', message: 'Invitation not found' }]
                    });
                }

                const validationResult = await validateReferences(invitation, updateInvitationSchema, 'InvitationErrorResponse');
                if (validationResult) {
                    const errors = Array.isArray(validationResult) 
                        ? validationResult 
                        : [{ 
                            field: validationResult.field || 'updateInvitation',
                            message: validationResult.message || 'Invalid reference'
                        }];

                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors
                    });
                }

                if (invitation.media) {
                    invitation.media.forEach(async (mediaItem) => {
                        const db_media = await Media.findById(mediaItem);
                        if (!db_media) {
                            return createResponse('InvitationErrorResponse', 'FAILURE', 'Media item not found', {
                                errors: [{ field: 'mediaItem', message: 'Media item not found' }]
                            });
                        }
                    });
                }

                if (invitation.media) {
                    const newMediaItem = invitation.media[0];
                    db_invitation.media = [newMediaItem];
                }

                if (invitation.message) {
                    db_invitation.message = invitation.message;
                }

                await db_invitation.save();
                return createResponse('InvitationResponse', 'SUCCESS', 'Invitation updated successfully', {
                    result: { invitation: db_invitation }
                });

            } catch (error) {
                return createResponse('InvitationErrorResponse', 'FAILURE', 'Error updating invitation', {
                    errors: [{ field: 'updateInvitation', message: error.message }]
                });
            }
        },

        removeMediaFromInvitation: async (_, { invitationId, media }, context) => {
            try {
                const invitation = await Invitation.findById(invitationId);
                if (!invitation) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitationId', message: 'Invitation not found' }]
                    });
                }

                const validationResult = await validateReferences({ media }, updateInvitationSchema, 'InvitationErrorResponse');
                if (validationResult) {
                    const errors = Array.isArray(validationResult) 
                        ? validationResult 
                        : [{ 
                            field: validationResult.field || 'removeMediaFromInvitation',
                            message: validationResult.message || 'Invalid reference'
                        }];

                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors
                    });
                }

                media.forEach(async (mediaItem) => {
                    const references = await findReferences(mediaItem, 'Invitation');
                    if (references.length > 0) {
                        return createResponse('InvitationErrorResponse', 'FAILURE', 'Media item is referenced by other entities', {
                            errors: [{ field: 'mediaItem', message: 'Media item is referenced by other entities' }]
                        });
                    }
                });

                const existingMedia = new Set(invitation.media.map(m => m.toString()));
                const mediaToRemove = media.filter(m => existingMedia.has(m.toString()));
                
                if (mediaToRemove.length === 0) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'No matching media found in invitation', {
                        errors: [{ field: 'media', message: 'Specified media not found in invitation' }]
                    });
                }

                mediaToRemove.forEach(async (mediaItem) => {
                    await deleteMediaAndRelatedData(mediaItem.toString());
                });

                invitation.media = invitation.media.filter(mediaItem => !mediaToRemove.includes(mediaItem.toString()));
                
                await invitation.save();

                return createResponse('InvitationResponse', 'SUCCESS', 'Media removed from invitation successfully', {
                    result: { invitation }
                });
            } catch (error) {
                return createResponse('InvitationErrorResponse', 'FAILURE', 'Error removing media from invitation', {
                    errors: [{ field: 'removeMediaFromInvitation', message: error.message }]
                });
            }
        },

        sendInvitationTo: async (_, { sendInvitationToInput }, context) => {
            try {
                const { invitationId, addGuests, save, message, media } = sendInvitationToInput;
                
                const invitation = await Invitation.findById(invitationId);
                if (!invitation) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ field: 'invitationId', message: 'Invitation not found' }]
                    });
                }

                const party = await Party.findById(invitation.party);
                if (!party) {
                    return createResponse('InvitationErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'party', message: 'Party not found' }]
                    });
                }

                if (addGuests) {
                    for (const guestId of addGuests) {
                        const guest = await Guest.findById(guestId);
                        if (!guest) {
                            return createResponse('InvitationErrorResponse', 'FAILURE', 'Guest not found', {
                                errors: [{ field: 'addGuests', message: `Guest with ID ${guestId} not found` }]
                            });
                        }
                    }

                    const validationResult = await validateReferences({ addGuests }, updateInvitationSchema, 'InvitationErrorResponse');
                    if (validationResult) {
                        return createResponse('InvitationErrorResponse', 'FAILURE', 'Invalid reference', {
                            errors: validationResult
                        });
                    }
                }

                if (media) {
                    for (const mediaItem of media) {
                        const db_media = await Media.findById(mediaItem);
                        if (!db_media) {
                            return createResponse('InvitationErrorResponse', 'FAILURE', 'Media item not found', {
                                errors: [{ field: 'media', message: `Media with ID ${mediaItem} not found` }]
                            });
                        }
                    }
                }

                if (!save) {
                    if (addGuests) {
                        const allGuestsToSend = new Set([
                            ...addGuests.map(id => id.toString()),
                            ...invitation.savedGuests.map(id => id.toString())
                        ]);

                        for (const guestId of allGuestsToSend) {
                            const guest = await Guest.findById(guestId).populate('user');
                            if (guest) {
                                await notifyInvitationSent(guest, party, context.user._id);
                            }
                        }
                        
                        const uniqueSentToGuests = new Set([
                            ...invitation.sentToGuests.map(id => id.toString()),
                            ...allGuestsToSend
                        ]);
                        
                        invitation.sentToGuests = Array.from(uniqueSentToGuests);
                        
                        invitation.savedGuests = [];
                        
                        await createRsvp(invitationId, uniqueSentToGuests);
                    }
                } else {
                    if (save && addGuests) {
                        const currentSavedGuests = new Set(invitation.savedGuests.map(id => id.toString()));
                        const newGuests = new Set(addGuests.map(id => id.toString()));
                        const guestsToRemove = Array.from(currentSavedGuests).filter(id => !newGuests.has(id));

                        if (guestsToRemove.length > 0) {
                            party.guests = party.guests.filter(guest => !guestsToRemove.includes(guest._id.toString()));
                            await party.save();
                            
                            invitation.savedGuests = addGuests;
                            await invitation.save();

                            const deleteResult = await Guest.deleteMany({ _id: { $in: guestsToRemove } });
                            console.log(`Deleted ${deleteResult.deletedCount} guests from collection`);
                        }

                        invitation.savedGuests = addGuests;
                    }
                }

                if (message !== undefined) {
                    invitation.message = message;
                }

                if (media !== undefined) {
                    invitation.media = media;
                }

                await invitation.save();

                return createResponse('InvitationResponse', 'SUCCESS', 'Invitation updated successfully', {
                    result: { invitation }
                });
            
            } catch (error) {
                return createResponse('InvitationErrorResponse', 'FAILURE', 'Error sending invitation to guest', {
                    errors: [{ field: 'sendInvitationTo', message: error.message }]
                });
            }
        },
    }
}

module.exports = invitationResolvers;

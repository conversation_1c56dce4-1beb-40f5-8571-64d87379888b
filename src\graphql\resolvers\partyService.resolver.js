const PartyService = require('../../models/PartyService');
const MdVendorType = require('../../models/MdVendorType');
const Vendor = require('../../models/Vendor');
const Task = require('../../models/Task');
const Transaction = require('../../models/Transaction');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildPartyServiceQuery = require('./filters/partyService.filter');
const { validateIds } = require('../../utils/validation.util');
const { partyServiceSchema } = require('../../models/PartyService');
const { findReferences } = require('../../utils/referenceCheck.util');

const partyServiceIdSchema = {
    vendorType: { type: 'single', required: true },
    vendor: { type: 'single', required: true },
    transactions: { type: 'array', required: false }
};

const partyServiceResolvers = {
    PartyService: {
        vendorType: async (parent) => {
            try {
                return await MdVendorType.findById(parent.vendorType);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor type');
            }
        },
        vendor: async (parent) => {
            try {
                return await Vendor.findById(parent.vendor);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor');
            }
        },
        transactions: async (parent) => {
            try {
                return await Transaction.find({ _id: { $in: parent.transactions } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting transactions');
            }
        }
    },

    Query: {
        getPartyServiceById: async (_, { id }) => {
            try {
                let partyService = await getByIdCache(id);
                if (!partyService) {
                    partyService = await PartyService.findById(id)
                        .populate('vendorType')
                        .populate('vendor')
                        .populate('transactions');

                    if (!partyService) {
                        return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Party service not found', {
                            errors: [{ field: 'id', message: 'Party service not found' }]
                        });
                    }
                    await setCache(id, partyService);
                }
                return createResponse('PartyServiceResponse', 'SUCCESS', 'Party service retrieved successfully', {
                    result: { partyService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Error retrieving party service', {
                    errors: [{ field: 'getPartyServiceById', message: error.message }]
                });
            }
        },

        getPartyServices: async (_, { filter, pagination }) => {
            try {
                const query = buildPartyServiceQuery(filter);
                
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(PartyService, query, limit, skip);

                const partyServices = await PartyService.find(query)
                    .populate('vendorType')
                    .populate('vendor')
                    .populate('transactions')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (partyServices.length === 0) {
                    return createResponse('PartyServicesResponse', 'FAILURE', 'No party services found', {
                        result: { partyServices },
                        pagination: paginationInfo
                    });
                }

                return createResponse('PartyServicesResponse', 'SUCCESS', 'Party services fetched successfully', {
                    result: { partyServices },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Error retrieving party services', {
                    errors: [{ field: 'getPartyServices', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createPartyService: async (_, { input }) => {
            try {
                const validationErrors = validateIds(input, partyServiceIdSchema);
                if (validationErrors.length > 0) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Invalid input', {
                        errors: validationErrors
                    });
                }

                const partyService = new PartyService(input);
                await partyService.save();
                
                const populatedPartyService = await PartyService.findById(partyService._id)
                    .populate('vendorType')
                    .populate('vendor')
                    .populate('transactions');

                return createResponse('PartyServiceResponse', 'SUCCESS', 'Party service created successfully', {
                    result: { partyService: populatedPartyService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Error creating party service', {
                    errors: [{ field: 'createPartyService', message: error.message }]
                });
            }
        },

        updatePartyService: async (_, { id, input }) => {
            try {
                if (!isValidObjectId(id)) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Invalid ID', {
                        errors: [{ field: 'id', message: 'Invalid party service ID format' }]
                    });
                }

                const validationErrors = validateIds(input, partyServiceIdSchema);
                if (validationErrors.length > 0) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Invalid input', {
                        errors: validationErrors
                    });
                }

                const partyService = await PartyService.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                )
                .populate('vendorType')
                .populate('vendor')
                .populate('transactions');

                if (!partyService) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Party service not found', {
                        errors: [{ field: 'id', message: 'Party service not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('PartyServiceResponse', 'SUCCESS', 'Party service updated successfully', {
                    result: { partyService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Error updating party service', {
                    errors: [{ field: 'updatePartyService', message: error.message }]
                });
            }
        },

        deletePartyService: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'PartyService');
                if (references.length > 0) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Party service cannot be deleted', {
                        errors: [{ field: 'id', message: `Party service cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }
                const partyService = await PartyService.findByIdAndDelete(id);
                
                if (!partyService) {
                    return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Party service not found', {
                        errors: [{ field: 'id', message: 'Party service not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('PartyServiceResponse', 'SUCCESS', 'Party service deleted successfully', {
                    result: { partyService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyServiceErrorResponse', 'FAILURE', 'Error deleting party service', {
                    errors: [{ field: 'deletePartyService', message: error.message }]
                });
            }
        }
    }
};

module.exports = partyServiceResolvers;

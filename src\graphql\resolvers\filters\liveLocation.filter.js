const { isValidObjectId } = require("../../../utils/validation.util");

const buildLiveLocationQuery = async (filter, guests) => {
    const query = {};

    if (filter?.allowLiveTracking) {
        query.allowLiveTracking = filter.allowLiveTracking;
    }

    if (guests) {
        query.guestId = { $in: guests.map(guest => guest._id) };
    }

    return query;
}

module.exports = buildLiveLocationQuery;

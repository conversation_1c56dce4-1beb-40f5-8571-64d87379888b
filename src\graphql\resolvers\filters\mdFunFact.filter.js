const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdEventTypeQuery = require("./mdEventType.filter");
const MdEventType = require("../../../models/MdEventType");

const buildMdFunFactQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.funFact) {
            query.funFact = { $regex: filter.funFact, $options: 'i' };
        }

        if (filter.eventTypes) {
            if (typeof filter.eventTypes === 'string') {
                if (isValidObjectId(filter.eventTypes)) {
                    query.eventTypes = filter.eventTypes;
                } else {
                    throw new Error('Invalid eventTypes ID provided');
                }
            } else if (typeof filter.eventTypes === 'object') {
                const eventTypeQuery = await buildMdEventTypeQuery(filter.eventTypes);
                if (Object.keys(eventTypeQuery).length > 0) {
                    const matchingEventTypes = await MdEventType.find(eventTypeQuery).select('_id');
                    if (matchingEventTypes.length > 0) {
                        query.eventTypes = { $in: matchingEventTypes.map(et => et._id) };
                    } else {
                        query.eventTypes = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdFunFactQuery;


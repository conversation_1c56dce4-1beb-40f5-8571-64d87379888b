const express = require('express');
const verifyWebhookSignature = require('../utils/verifyWebhookSignature.util');
const router = express.Router();
const {createUserFromJson} = require('../models/User');

// Webhook endpoint to handle user added notifications
router.post('/user-added', async (req, res) => {
  try {
     // Verify the webhook signature using Svix
     const payload = verifyWebhookSignature(req);

     await createUserFromJson(payload);

    // Process the user data
    const userId = payload.data.id;
    
    // Example: Log the user data
    console.log('New user added:', userId);

    // Send a response back to Clerk
    res.status(200).json({ message: 'Webhook received successfully' });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(400).json({ message: 'Invalid webhook' });
  }
});

module.exports = router;
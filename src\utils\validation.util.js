const { mongoose } = require("mongoose");
const { createResponse } = require("./response.util");

const isValidObjectId = (id) => {
    return typeof id === 'string' && mongoose.Types.ObjectId.isValid(id);
}

const validateIds = (input, schema) => {
    const errors = [];
    
    Object.entries(schema).forEach(([field, config]) => {
        const value = input[field];
        if (!value && !config.required) return;
        
        if (config.type === 'single') {
            if (!isValidObjectId(value)) {
                errors.push({
                    field,
                    message: `Invalid ${field} ID format`
                });
            }
        } else if (config.type === 'array' && Array.isArray(value)) {
            const invalidId = value.find(id => !isValidObjectId(id));
            if (invalidId) {
                errors.push({
                    field,
                    message: `One or more ${field} IDs are invalid`
                });
            }
        }
    });

    return errors;
};

const validateSingleReference = async (model, id, fieldName) => {
    const exists = await model.findById(id);
    if (!exists) {
        return createResponse('ErrorResponse', 'FAILURE', `${fieldName} not found`, {
            errors: [{ field: fieldName.toLowerCase(), message: `${fieldName} not found` }]
        });
    }
    return null;
};

const validateArrayReferences = async (model, ids, fieldName) => {
    if (!ids || ids.length === 0) return null;
    
    const items = await model.find({ _id: { $in: ids } });
    if (items.length !== ids.length) {
        return createResponse('ErrorResponse', 'FAILURE', `${fieldName} not found`, {
            errors: [{ field: fieldName.toLowerCase(), message: `${fieldName} not found` }]
        });
    }
    return null;
};

const getNestedValue = (obj, path) => {
    if (!path.includes('.')) {
        return obj[path];
    }
    return path.split('.').reduce((current, key) => {
        return current ? current[key] : undefined;
    }, obj);
};

const validateReferences = async (input, schema, responseType) => {
    for (const [fieldPath, config] of Object.entries(schema)) {
        const fieldValue = getNestedValue(input, fieldPath);
        
        if (!fieldValue && config.required) {
            return createResponse(
                `${responseType}ErrorResponse`,
                'FAILURE',
                `${fieldPath} is required`,
                {
                    errors: [{ field: fieldPath, message: `${fieldPath} is required` }]
                }
            );
        }

        if (fieldValue) {
            const validationResult = config.type === 'array' 
                ? await validateArrayReferences(config.model, fieldValue, fieldPath)
                : await validateSingleReference(config.model, fieldValue, fieldPath);

            if (validationResult) return validationResult;
        }
    }
    return null;
};

module.exports = { isValidObjectId, validateIds, validateReferences };
const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mdEventTypeSchema = new Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    duration: { 
        type: String, 
        enum: ['ONE_DAY', 'MULTI_DAY'], 
        required: true 
    },
    partyTypes: [{ type: Schema.Types.ObjectId, ref: 'MdPartyType' }],
    bannerImage: { type: String }
}, { timestamps: true, collection: 'md_event_types' });

const MdEventType = model('MdEventType', mdEventTypeSchema);

module.exports = MdEventType;
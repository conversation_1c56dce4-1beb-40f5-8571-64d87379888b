const Document = require('../../models/Document');
const Transaction = require('../../models/Transaction');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { createResponse } = require('../../utils/response.util');
const buildTransactionQuery = require('./filters/transaction.filter');
const findReferences = require('./references/transaction.references');
const dateTimeScalar = require('./scalars/dateTime.scalar');

const transactionResolvers = {
    DateTime: dateTimeScalar,
    Transaction: {
        documents: async (parent) => {
            try {
                return await Document.find({ _id: { $in: parent.documents } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting documents');
            }
        }
    },

    Query: {
        getTransactionById: async (_, { id }) => {
            try {
                let transaction = await getByIdCache(id);
                if (!transaction) {
                    transaction = await Transaction.findById(id);
                    if (!transaction) {
                        return createResponse('TransactionErrorResponse', 'FAILURE', 'Transaction not found', { 
                            errors: [{ field: 'id', message: 'Transaction not found' }] 
                        });
                    }
                    await setCache(id, transaction);
                }
                return createResponse('TransactionResponse', 'SUCCESS', 'Transaction fetched successfully', { 
                    result: { transaction } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('TransactionErrorResponse', 'FAILURE', 'Error retrieving transaction', { 
                    errors: [{ field: 'getTransactionById', message: error.message }] 
                });
            }
        },

        getTransactions: async (_, { filter, pagination }) => {
            try {
                const query = buildTransactionQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Transaction, query, limit, skip);

                const transactions = await Transaction.find(query).limit(limit).skip(skip).sort({ timeStamp: -1 });

                if (transactions.length === 0) {
                    return createResponse('TransactionsResponse', 'FAILURE', 'No transactions found', { result: { transactions }, pagination: paginationInfo });
                }
                return createResponse('TransactionsResponse', 'SUCCESS', 'Transactions fetched successfully', { result: { transactions }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('TransactionErrorResponse', 'FAILURE', 'Error retrieving transactions', { errors: [{ field: 'getTransactions', message: error.message }] });
            }
        }
    },

    Mutation: {
        createTransaction: async (_, { input }) => {
            try {
                const transaction = new Transaction(input);
                await transaction.save();
                return createResponse('TransactionResponse', 'SUCCESS', 'Transaction created successfully', { result: { transaction } });
            } catch (error) {
                console.error(error);
                return createResponse('TransactionErrorResponse', 'FAILURE', 'Error creating transaction', { errors: [{ field: 'createTransaction', message: error.message }] });
            }
        },

        deleteTransaction: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('TransactionErrorResponse', 'FAILURE', 'Transaction cannot be deleted', {
                        errors: [{ field: 'deleteTransaction', message: `Transaction cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }

                const transaction = await Transaction.findByIdAndDelete(id);
                if (!transaction) {
                    return createResponse('TransactionErrorResponse', 'FAILURE', 'Transaction not found', { errors: [{ field: 'deleteTransaction', message: 'Transaction not found' }] });
                }
                await clearCacheById(id);
                return createResponse('TransactionResponse', 'SUCCESS', 'Transaction deleted successfully', { result: { transaction } });
            } catch (error) {
                console.error(error);
                return createResponse('TransactionErrorResponse', 'FAILURE', 'Error deleting transaction', { errors: [{ field: 'deleteTransaction', message: error.message }] });
            }
        }
    }
};

module.exports = transactionResolvers;
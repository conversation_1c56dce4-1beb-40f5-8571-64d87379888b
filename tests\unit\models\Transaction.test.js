const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Transaction = require('../../../src/models/Transaction');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Transaction Model Test', () => {
    it('should create and save a transaction successfully', async () => {
        const validTransaction = new Transaction({
            description: 'Test Transaction',
            amount: 100.50,
            type: 'CREDIT',
            method: 'CASH',
            documents: [new mongoose.Types.ObjectId()]
        });

        const savedTransaction = await validTransaction.save();

        expect(savedTransaction._id).toBeDefined();
        expect(savedTransaction.description).toBe('Test Transaction');
        expect(savedTransaction.amount).toBe(100.50);
        expect(savedTransaction.type).toBe('CREDIT');
        expect(savedTransaction.method).toBe('CASH');
        expect(savedTransaction.timeStamp).toBeDefined();
        expect(savedTransaction.documents).toHaveLength(1);
        expect(savedTransaction.createdAt).toBeDefined();
        expect(savedTransaction.updatedAt).toBeDefined();
    });

    it('should fail to create a transaction without required fields', async () => {
        const transaction = new Transaction();
    
        let err;
        try {
            await transaction.validate();
        } catch (error) {
            err = error;
        }
    
        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.description).toBeDefined();
        expect(err.errors.type).toBeDefined();
        expect(err.errors.method).toBeDefined();
    });

    it('should fail to create a transaction with invalid type', async () => {
        const invalidTransaction = new Transaction({
            description: 'Invalid Transaction',
            amount: 50,
            type: 'INVALID_TYPE',
            method: 'CASH'
        });

        let err;
        try {
            await invalidTransaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.type).toBeDefined();
    });

    it('should fail to create a transaction with invalid method', async () => {
        const invalidTransaction = new Transaction({
            description: 'Invalid Transaction',
            amount: 50,
            type: 'DEBIT',
            method: 'INVALID_METHOD'
        });

        let err;
        try {
            await invalidTransaction.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.method).toBeDefined();
    });
});
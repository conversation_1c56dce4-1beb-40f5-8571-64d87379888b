const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Task = require('../../../src/models/Task');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Task Model Test', () => {
    it('should create and save a Task successfully', async () => {
        const validTask = new Task({
            title: 'Test Task',
            description: 'This is a test task',
            type: 'PRE_PARTY',
            status: 'PENDING',
            dueDate: new Date(),
            priority: 'MEDIUM',
            createdBy: new mongoose.Types.ObjectId(),
            assignedTo: [new mongoose.Types.ObjectId()]
        });

        const savedTask = await validTask.save();

        expect(savedTask._id).toBeDefined();
        expect(savedTask.title).toBe('Test Task');
        expect(savedTask.description).toBe('This is a test task');
        expect(savedTask.type).toBe('PRE_PARTY');
        expect(savedTask.status).toBe('PENDING');
        expect(savedTask.dueDate).toBeInstanceOf(Date);
        expect(savedTask.priority).toBe('MEDIUM');
        expect(savedTask.createdBy).toBeDefined();
        expect(savedTask.assignedTo).toHaveLength(1);
    });

    it('should fail to create a Task without required fields', async () => {
        const task = new Task();
    
        let err;
        try {
            await task.save();
        } catch (error) {
            err = error;
        }
    
        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.title).toBeDefined();
        expect(err.errors.type).toBeDefined();
        expect(err.errors.createdBy).toBeDefined();
    });
    it('should fail to create a Task with invalid enum values', async () => {
        const invalidTask = new Task({
            title: 'Test Task',
            type: 'INVALID_TYPE',
            status: 'INVALID_STATUS',
            priority: 'INVALID_PRIORITY',
            createdBy: new mongoose.Types.ObjectId()
        });

        let err;
        try {
            await invalidTask.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.type).toBeDefined();
        expect(err.errors.status).toBeDefined();
        expect(err.errors.priority).toBeDefined();
    });
});
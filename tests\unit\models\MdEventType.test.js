const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const MdEventType = require('../../../src/models/MdEventType');
const MdPartyType = require('../../../src/models/MdPartyType');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('MdEventType Model Test', () => {
    it('should create and save a MdEventType successfully', async () => {
        const mockPartyType = new MdPartyType({ name: 'Birthday Party', description: 'A fun birthday celebration' });
        await mockPartyType.save();

        const validMdEventType = new MdEventType({
            name: 'Summer Festival',
            description: 'A multi-day summer music festival',
            duration: 'MULTI_DAY',
            partyTypes: [mockPartyType._id],
            bannerImage: 'https://example.com/summer-festival-banner.jpg'
        });

        const savedMdEventType = await validMdEventType.save();

        expect(savedMdEventType._id).toBeDefined();
        expect(savedMdEventType.name).toBe('Summer Festival');
        expect(savedMdEventType.description).toBe('A multi-day summer music festival');
        expect(savedMdEventType.duration).toBe('MULTI_DAY');
        expect(savedMdEventType.partyTypes[0]).toEqual(mockPartyType._id);
        expect(savedMdEventType.bannerImage).toBe('https://example.com/summer-festival-banner.jpg');
    });

    it('should fail to create a MdEventType without required fields', async () => {
        const invalidMdEventType = new MdEventType();

        let err;
        try {
            await invalidMdEventType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.name).toBeDefined();
        expect(err.errors.description).toBeDefined();
        expect(err.errors.duration).toBeDefined();
    });

    it('should fail to create a MdEventType with invalid duration', async () => {
        const invalidMdEventType = new MdEventType({
            name: 'Invalid Event',
            description: 'An event with invalid duration',
            duration: 'INVALID_DURATION'
        });

        let err;
        try {
            await invalidMdEventType.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.duration).toBeDefined();
    });

    it('should create a MdEventType with minimal required fields', async () => {
        const minimalMdEventType = new MdEventType({
            name: 'Minimal Event',
            description: 'An event with minimal details',
            duration: 'ONE_DAY'
        });

        const savedMinimalMdEventType = await minimalMdEventType.save();

        expect(savedMinimalMdEventType._id).toBeDefined();
        expect(savedMinimalMdEventType.name).toBe('Minimal Event');
        expect(savedMinimalMdEventType.description).toBe('An event with minimal details');
        expect(savedMinimalMdEventType.duration).toBe('ONE_DAY');
        expect(savedMinimalMdEventType.partyTypes).toHaveLength(0);
        expect(savedMinimalMdEventType.bannerImage).toBeUndefined();
    });
});
const mongoose = require('mongoose');
const buildMdPromotionQuery = require('../../../../../src/graphql/resolvers/filters/mdPromotion.filter');
const buildMdPartnerQuery = require('../../../../../src/graphql/resolvers/filters/mdPartner.filter');
const buildTagQuery = require('../../../../../src/graphql/resolvers/filters/tag.filter');
const MdPartner = require('../../../../../src/models/MdPartner');
const Tag = require('../../../../../src/models/Tag');

jest.mock('../../../../../src/utils/validation.util', () => ({
    isValidObjectId: jest.fn()
}));
jest.mock('../../../../../src/graphql/resolvers/filters/mdPartner.filter');
jest.mock('../../../../../src/graphql/resolvers/filters/tag.filter');
jest.mock('../../../../../src/models/MdPartner');
jest.mock('../../../../../src/models/Tag');

describe('buildMdPromotionQuery', () => {
    const validObjectId = new mongoose.Types.ObjectId().toString();

    beforeEach(() => {
        jest.clearAllMocks();
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(true);
        
        MdPartner.find.mockReturnValue({
            select: jest.fn()
        });
        
        Tag.find.mockReturnValue({
            select: jest.fn()
        });
    });

    it('should return empty query object when no filter is provided', async () => {
        const query = await buildMdPromotionQuery();
        expect(query).toEqual({});
    });

    it('should build query with valid id filter', async () => {
        const filter = { id: validObjectId };
        const query = await buildMdPromotionQuery(filter);
        expect(query).toEqual({ _id: validObjectId });
    });

    it('should throw error for invalid id', async () => {
        require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);
        const filter = { id: 'invalid-id' };
        await expect(buildMdPromotionQuery(filter)).rejects.toThrow('Invalid id provided');
    });

    it('should build query with text search filters', async () => {
        const filter = {
            name: 'test',
            description: 'description',
            mediaUrl: 'video',
            ctaLink: 'example.com'
        };
        const query = await buildMdPromotionQuery(filter);
        expect(query).toEqual({
            name: { $regex: 'test', $options: 'i' },
            description: { $regex: 'description', $options: 'i' },
            mediaUrl: { $regex: 'video', $options: 'i' },
            ctaLink: { $regex: 'example.com', $options: 'i' }
        });
    });

    it('should build query with active status', async () => {
        const filter = { active: true };
        const query = await buildMdPromotionQuery(filter);
        expect(query).toEqual({ active: true });
    });

    it('should build query with date range', async () => {
        const filter = {
            dateRange: {
                start: new Date('2024-01-01'),
                end: new Date('2024-12-31')
            }
        };
        const query = await buildMdPromotionQuery(filter);
        expect(query).toEqual({
            endDate: { $gte: filter.dateRange.start },
            startDate: { $lte: filter.dateRange.end }
        });
    });

    describe('partner filter', () => {
        it('should handle partner filter with valid ObjectId', async () => {
            const filter = { partner: validObjectId };
            const query = await buildMdPromotionQuery(filter);
            expect(query).toEqual({ partner: validObjectId });
        });

        it('should throw error for invalid partner ID', async () => {
            require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);
            const filter = { partner: 'invalid-id' };
            await expect(buildMdPromotionQuery(filter)).rejects.toThrow('Invalid partner ID provided');
        });

        it('should handle partner filter with object criteria', async () => {
            const partnerQuery = { name: 'test partner' };
            const matchingPartners = [{ _id: validObjectId }];
            
            buildMdPartnerQuery.mockResolvedValue(partnerQuery);
            MdPartner.find().select.mockResolvedValue(matchingPartners);
            
            const filter = { partner: partnerQuery };
            const query = await buildMdPromotionQuery(filter);
            
            expect(query).toEqual({
                partner: { $in: matchingPartners.map(partner => partner._id) }
            });
        });

        it('should handle empty partner results', async () => {
            buildMdPartnerQuery.mockResolvedValue({ name: 'nonexistent' });
            MdPartner.find().select.mockResolvedValue([]);
            
            const filter = { partner: { name: 'nonexistent' } };
            const query = await buildMdPromotionQuery(filter);
            
            expect(query).toEqual({
                partner: { $in: [] }
            });
        });
    });

    describe('tag filter', () => {
        it('should handle tag filter with valid ObjectId', async () => {
            const filter = { tag: validObjectId };
            const query = await buildMdPromotionQuery(filter);
            expect(query).toEqual({ tags: validObjectId });
        });

        it('should throw error for invalid tag ID', async () => {
            require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);
            const filter = { tag: 'invalid-id' };
            await expect(buildMdPromotionQuery(filter)).rejects.toThrow('Invalid tag ID provided');
        });

        it('should handle tag filter with object criteria', async () => {
            const tagQuery = { name: 'test tag' };
            const matchingTags = [{ _id: validObjectId }];
            
            buildTagQuery.mockResolvedValue(tagQuery);
            Tag.find().select.mockResolvedValue(matchingTags);
            
            const filter = { tag: tagQuery };
            const query = await buildMdPromotionQuery(filter);
            
            expect(query).toEqual({
                tags: { $in: matchingTags.map(tag => tag._id) }
            });
        });

        it('should handle empty tag results', async () => {
            buildTagQuery.mockResolvedValue({ name: 'nonexistent' });
            Tag.find().select.mockResolvedValue([]);
            
            const filter = { tag: { name: 'nonexistent' } };
            const query = await buildMdPromotionQuery(filter);
            
            expect(query).toEqual({
                tags: { $in: [] }
            });
        });
    });

    it('should build complex query with multiple filters', async () => {
        const startDate = new Date('2024-01-01');
        const endDate = new Date('2024-12-31');
        
        const filter = {
            name: 'test',
            active: true,
            dateRange: {
                start: startDate,
                end: endDate
            },
            partner: validObjectId
        };

        const query = await buildMdPromotionQuery(filter);
        
        expect(query).toEqual({
            name: { $regex: 'test', $options: 'i' },
            active: true,
            endDate: { $gte: startDate },
            startDate: { $lte: endDate },
            partner: validObjectId
        });
    });
}); 
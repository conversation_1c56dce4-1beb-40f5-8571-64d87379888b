const MdFeature = require('../../models/MdFeature');
const MdIcon = require('../../models/MdIcon');
const { getByIdCache, clearCacheById, setCache } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { createResponse } = require('../../utils/response.util');
const buildMdFeatureQuery = require('./filters/mdFeature.filter');
const findReferences = require('./references/mdFeature.references');

const mdFeatureResolvers = {
    MdFeature: {
        icon: async (parent) => {
            try {
                return await MdIcon.findById(parent.icon);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting icon');
            }
        }
    },

    Query: {
        getMdFeatureById: async (_, { id }) => {
            try {
                let mdFeature = await getByIdCache(id) || await MdFeature.findById(id);
                if (mdFeature) {
                    await setCache(id, mdFeature);
                } else {
                    return createResponse('MdFeatureErrorResponse', 'FAILURE', 'MdFeature not found', { errors: [{ field: 'getMdFeatureById', message: 'MdFeature not found' }] });
                }
                return createResponse('MdFeatureResponse', 'SUCCESS', 'MdFeature fetched successfully', { result: { mdFeature } });
            } catch (error) {
                console.error(error);
                return createResponse('MdFeatureErrorResponse', 'FAILURE', 'Error getting mdFeature', { errors: [{ field: 'getMdFeatureById', message: error.message }] });
            }
        },
        getMdFeatures: async (_, { filter, pagination }) => {
            try {
                const query = buildMdFeatureQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdFeature, query, limit, skip);

                const mdFeatures = await MdFeature.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdFeatures.length === 0) {
                    return createResponse('MdFeaturesResponse', 'FAILURE', 'No mdFeatures found', { result: { mdFeatures }, pagination: paginationInfo });
                }
                return createResponse('MdFeaturesResponse', 'SUCCESS', 'MdFeatures fetched successfully', { result: { mdFeatures }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdFeatureErrorResponse', 'FAILURE', 'Error getting mdFeatures', { errors: [{ field: 'getMdFeatures', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdFeature: async (_, { input }) => {
            try {
                const mdFeature = new MdFeature(input);
                await mdFeature.save();
                return createResponse('MdFeatureResponse', 'SUCCESS', 'MdFeature created successfully', { result: { mdFeature } });
            } catch (error) {
                console.error(error);
                return createResponse('MdFeatureErrorResponse', 'FAILURE', 'Error creating mdFeature', { errors: [{ field: 'createMdFeature', message: error.message }] });
            }
        },
        updateMdFeature: async (_, { id, input }) => {
            try {
                const mdFeature = await MdFeature.findByIdAndUpdate(id, input, { new: true });
                if (!mdFeature) {
                    return createResponse('MdFeatureErrorResponse', 'FAILURE', 'MdFeature not found', { errors: [{ field: 'updateMdFeature', message: 'MdFeature not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdFeatureResponse', 'SUCCESS', 'MdFeature updated successfully', { result: { mdFeature } });
            } catch (error) {
                console.error(error);
                return createResponse('MdFeatureErrorResponse', 'FAILURE', 'Error updating mdFeature', { errors: [{ field: 'updateMdFeature', message: error.message }] });
            }
        },
        deleteMdFeature: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('MdFeatureErrorResponse', 'FAILURE', 'MdFeature cannot be deleted', {
                        errors: [{ field: 'deleteMdFeature', message: `MdFeature cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                    });
                }
                const mdFeature = await MdFeature.findByIdAndDelete(id);
                if (!mdFeature) {
                    return createResponse('MdFeatureErrorResponse', 'FAILURE', 'MdFeature not found', { errors: [{ field: 'deleteMdFeature', message: 'MdFeature not found' }] });
                }

                await clearCacheById(id);
                return createResponse('MdFeatureResponse', 'SUCCESS', 'MdFeature deleted successfully', { result: { mdFeature } });
            } catch (error) {
                console.error(error);
                return createResponse('MdFeatureErrorResponse', 'FAILURE', 'Error deleting mdFeature', { errors: [{ field: 'deleteMdFeature', message: error.message }] });
            }
        }
    }
}

module.exports = mdFeatureResolvers;
const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdPartner {
        id: ID!
        name: String!
        categories: [Tag!]!
        description: String
        icon: MdIcon!
        banner_image_url: String
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdPartnerFilterInput {
        id: String
        name: String
        categories: TagFilterInput
        description: String
        icon: MdIconFilterInput
        banner_image_url: String
    }

    type MdPartnerWrapper {
        mdPartner: MdPartner!
    }

    type MdPartnersWrapper {
        mdPartners: [MdPartner]!
    }

    type MdPartnersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartnersWrapper!
        pagination: PaginationInfo!
    }

    type MdPartnerResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartnerWrapper!
    }

    type MdPartnerErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdPartnerResult = MdPartnerResponse | MdPartnerErrorResponse
    union MdPartnersResult = MdPartnersResponse | MdPartnerErrorResponse

    type Query {
        getMdPartnerById(id: ID!): MdPartnerResult!
        getMdPartners(filter: MdPartnerFilterInput, pagination: PaginationInput): MdPartnersResult!
    }

    input MdPartnerInput {
        name: String!
        categories: [ID!]!
        description: String
        icon: ID!
        banner_image_url: String
    }

    input MdPartnerUpdateInput {
        name: String
        categories: [ID!]
        description: String
        icon: ID
        banner_image_url: String
    }

    type Mutation {
        createMdPartner(input: MdPartnerInput!): MdPartnerResult!
        updateMdPartner(id: ID!, input: MdPartnerUpdateInput!): MdPartnerResult!
        deleteMdPartner(id: ID!): MdPartnerResult!
    }
`;
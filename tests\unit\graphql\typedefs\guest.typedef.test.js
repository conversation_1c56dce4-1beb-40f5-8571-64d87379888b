const { gql } = require('graphql-tag');
const hostTypeDef = require('../../../../src/graphql/typedefs/host.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('hostTypeDef', () => {
  it('should contain the Host type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type Host {
        id: ID
        userId: User!
      }

      type HostWrapper {
        host: Host!
      }

      type HostsWrapper {
        hosts: [Host]!
      }

      type HostResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostWrapper!
      }

      type HostsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: HostsWrapper!
        pagination: PaginationInfo!
      }

      type HostErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union HostResult = HostResponse | HostErrorResponse
      union HostsResult = HostsResponse | HostErrorResponse

      type Query {
        getHostById(id: ID!): HostResult!
        getHosts(pagination: PaginationInput): HostsResult!
      }

      type Mutation {
        createHost(userId: ID!): HostResult!
        deleteHost(id: ID!): HostResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(hostTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});

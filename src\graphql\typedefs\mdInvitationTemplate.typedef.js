const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdInvitationTemplate {
        id: ID!
        name: String!
        imageUrl: String!
        tags: [Tag]
    }

    input MdInvitationTemplateFilterInput {
        id: ID
        name: String
        tags: [TagFilterInput]
    }

    type MdInvitationTemplateWrapper {
        mdInvitationTemplate: MdInvitationTemplate!
    }

    type MdInvitationTemplatesWrapper {
        mdInvitationTemplates: [MdInvitationTemplate]!
    }

    type MdInvitationTemplateResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInvitationTemplateWrapper!
    }

    type MdInvitationTemplatesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInvitationTemplatesWrapper!
        pagination: PaginationInfo!
    }

    type MdInvitationTemplateErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!    
    }

    type MdInvitationTemplateTagsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TagsWrapper!
    }

    union MdInvitationTemplateResult = MdInvitationTemplateResponse | MdInvitationTemplateErrorResponse
    union MdInvitationTemplatesResult = MdInvitationTemplatesResponse | MdInvitationTemplateErrorResponse
    union MdInvitationTemplateTagsResult = MdInvitationTemplateTagsResponse | MdInvitationTemplateErrorResponse

    type Query {
        getMdInvitationTemplateById(id: ID!): MdInvitationTemplateResult!
        getMdInvitationTemplates(filter: MdInvitationTemplateFilterInput, pagination: PaginationInput): MdInvitationTemplatesResult!
        getAllMdInvitationTemplateTags: MdInvitationTemplateTagsResult!
    }

    input MdInvitationTemplateInput {
        name: String!
        imageUrl: String!
        tags: [ID!]
    }

    input MdInvitationTemplateUpdateInput {
        name: String
        imageUrl: String
        tags: [ID!]
    }

    type Mutation {
        createMdInvitationTemplate(input: MdInvitationTemplateInput!): MdInvitationTemplateResult!
        updateMdInvitationTemplate(id: ID!, input: MdInvitationTemplateUpdateInput!): MdInvitationTemplateResult!
        deleteMdInvitationTemplate(id: ID!): MdInvitationTemplateResult!
    }
`;
const { isValidObjectId } = require("../../../utils/validation.util");
const buildTagQuery = require("./tag.filter");
const buildMdIconQuery = require("./mdIcon.filter");
const Tag = require("../../../models/Tag");
const MdIcon = require("../../../models/MdIcon");

const buildMdPartnerQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.banner_image_url) {
            query.banner_image_url = { $regex: filter.banner_image_url, $options: 'i' };
        }

        if (filter.categories) {
            if (typeof filter.categories === 'string') {
                if (isValidObjectId(filter.categories)) {
                    query.categories = filter.categories;
                } else {
                    throw new Error('Invalid categories ID provided');
                }
            } else if (typeof filter.categories === 'object') {
                const tagQuery = await buildTagQuery(filter.categories);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.categories = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.categories = { $in: [] };
                    }
                }
            }
        }

        if (filter.icon) {
            if (typeof filter.icon === 'string') {
                if (isValidObjectId(filter.icon)) {
                    query.icon = filter.icon;
                } else {
                    throw new Error('Invalid icon ID provided');
                }
            } else if (typeof filter.icon === 'object') {
                const iconQuery = await buildMdIconQuery(filter.icon);
                if (Object.keys(iconQuery).length > 0) {
                    const matchingIcons = await MdIcon.find(iconQuery).select('_id');
                    if (matchingIcons.length > 0) {
                        query.icon = { $in: matchingIcons.map(icon => icon._id) };
                    } else {
                        query.icon = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdPartnerQuery;
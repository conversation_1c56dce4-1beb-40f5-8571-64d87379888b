const VendorService = require('../../../../src/models/VendorService');
const buildVendorServiceQuery = require('../../../../src/graphql/resolvers/filters/vendorService.filter');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const MdVendorType = require('../../../../src/models/MdVendorType');
const MdFeature = require('../../../../src/models/MdFeature');
const Media = require('../../../../src/models/Media');
const vendorServiceResolvers = require('../../../../src/graphql/resolvers/vendorService.resolver');
const findReferences = require('../../../../src/graphql/resolvers/references/vendorService.references');

jest.mock('../../../../src/models/VendorService');
jest.mock('../../../../src/graphql/resolvers/filters/vendorService.filter');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/models/MdVendorType');
jest.mock('../../../../src/models/MdFeature');
jest.mock('../../../../src/models/Media');
jest.mock('../../../../src/graphql/resolvers/references/vendorService.references');

describe('VendorService Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('VendorService: vendorType', () => {
        it('should return vendor type if found', async () => {
            const vendorService = { vendorType: 'vendorTypeId' };
            const mockVendorType = { _id: 'vendorTypeId', name: 'Vendor Type' };
            MdVendorType.findById.mockResolvedValue(mockVendorType);

            const result = await vendorServiceResolvers.VendorService.vendorType(vendorService);

            expect(MdVendorType.findById).toHaveBeenCalledWith('vendorTypeId');
            expect(result).toEqual(mockVendorType);
        });

        it('should throw an error if vendor type retrieval fails', async () => {
            const vendorService = { vendorType: 'vendorTypeId' };
            MdVendorType.findById.mockRejectedValue(new Error('Database error'));

            await expect(vendorServiceResolvers.VendorService.vendorType(vendorService)).rejects.toThrow('Error getting vendor type');
        });
    });

    describe('VendorService: features', () => {
        it('should return features if found', async () => {
            const vendorService = { features: ['featureId1', 'featureId2'] };
            const mockFeatures = [{ _id: 'featureId1', name: 'Feature 1' }, { _id: 'featureId2', name: 'Feature 2' }];
            MdFeature.find.mockResolvedValue(mockFeatures);

            const result = await vendorServiceResolvers.VendorService.features(vendorService);

            expect(MdFeature.find).toHaveBeenCalledWith({ _id: { $in: ['featureId1', 'featureId2'] } });
            expect(result).toEqual(mockFeatures);
        });

        it('should throw an error if features retrieval fails', async () => {
            const vendorService = { features: ['featureId1', 'featureId2'] };
            MdFeature.find.mockRejectedValue(new Error('Database error'));

            await expect(vendorServiceResolvers.VendorService.features(vendorService)).rejects.toThrow('Error getting features');
        });
    });

    describe('VendorService: media', () => {
        it('should return media if found', async () => {
            const vendorService = { media: ['mediaId1', 'mediaId2'] };
            const mockMedia = [{ _id: 'mediaId1', url: 'http://example.com/media1.jpg' }, { _id: 'mediaId2', url: 'http://example.com/media2.jpg' }];
            Media.find.mockResolvedValue(mockMedia);

            const result = await vendorServiceResolvers.VendorService.media(vendorService);

            expect(Media.find).toHaveBeenCalledWith({ _id: { $in: ['mediaId1', 'mediaId2'] } });
            expect(result).toEqual(mockMedia);
        });

        it('should throw an error if media retrieval fails', async () => {
            const vendorService = { media: ['mediaId1', 'mediaId2'] };
            Media.find.mockRejectedValue(new Error('Database error'));

            await expect(vendorServiceResolvers.VendorService.media(vendorService)).rejects.toThrow('Error getting media');
        });
    });

    describe('Query: getVendorServiceById', () => {
        
        it('should fetch vendor service by id from cache', async () => {
            const vendorService = { id: '123', vendorType: 'type1', features: ['feature1'], media: ['media1'] };
            getByIdCache.mockResolvedValue(vendorService);
            setCache.mockResolvedValue();

            const result = await vendorServiceResolvers.Query.getVendorServiceById(null, { id: '123' });

            expect(getByIdCache).toHaveBeenCalledWith('123');
            expect(setCache).toHaveBeenCalledWith('123', vendorService);
            expect(result).toEqual(createResponse('VendorServiceResponse', 'SUCCESS', 'VendorService fetched successfully', { result: { vendorService } }));
        });

        it('should return error if vendor service not found', async () => {
            getByIdCache.mockResolvedValue(null);
            VendorService.findById.mockResolvedValue(null);

            const result = await vendorServiceResolvers.Query.getVendorServiceById(null, { id: '123' });

            expect(result).toEqual(createResponse('VendorServiceErrorResponse', 'FAILURE', 'VendorService not found', {
                errors: [{ field: 'getVendorServiceById', message: 'VendorService not found' }]
            }));
        });

        it('should return error when fetching vendor service fails', async () => {
            getByIdCache.mockRejectedValue(new Error('Cache Error'));

            const result = await vendorServiceResolvers.Query.getVendorServiceById(null, { id: '123' });

            expect(result).toEqual(createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error getting VendorService', {
                errors: [{ field: 'getVendorServiceById', message: 'Cache Error' }]
            }));
        });

        it('should return error response if VendorService not found', async () => {
            const id = 'nonexistentId';
            getByIdCache.mockResolvedValue(null);
            VendorService.findById.mockReturnValue({
              populate: jest.fn().mockReturnValue({
                populate: jest.fn().mockReturnValue({
                  populate: jest.fn().mockResolvedValue(null)
                })
              })
            });
            createResponse.mockReturnValue({
              status: 'FAILURE',
              message: 'VendorService not found',
              errors: [{ field: 'getVendorServiceById', message: 'VendorService not found' }]
            });
    
            const result = await vendorServiceResolvers.Query.getVendorServiceById(null, { id });
    
            expect(getByIdCache).toHaveBeenCalledWith(id);
            expect(VendorService.findById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
              'VendorServiceErrorResponse',
              'FAILURE',
              'VendorService not found',
              {
                errors: [{ field: 'getVendorServiceById', message: 'VendorService not found' }]
              }
            );
            expect(result).toEqual({
              status: 'FAILURE',
              message: 'VendorService not found',
              errors: [{ field: 'getVendorServiceById', message: 'VendorService not found' }]
            });
        });
    });

    describe('Query: getVendorServices', () => {
        it('should fetch vendor services with pagination', async () => {
            const vendorServices = [{ id: '123' }];
            const paginationInfo = { total: 1, limit: 10, skip: 0 };
            buildVendorServiceQuery.mockReturnValue({});
            getPaginationInfo.mockResolvedValue(paginationInfo);
            VendorService.find.mockResolvedValue(vendorServices);

            const result = await vendorServiceResolvers.Query.getVendorServices(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

            expect(buildVendorServiceQuery).toHaveBeenCalled();
            expect(getPaginationInfo).toHaveBeenCalledWith(VendorService, {}, 10, 0);
            expect(result).toEqual(createResponse('VendorServicesResponse', 'SUCCESS', 'Vendor services fetched successfully', {
                result: { vendorServices },
                pagination: paginationInfo
            }));
        });

        it('should return error when fetching vendor services fails', async () => {
            buildVendorServiceQuery.mockReturnValue({});
            getPaginationInfo.mockRejectedValue(new Error('Query Error'));

            const result = await vendorServiceResolvers.Query.getVendorServices(null, { filter: {}, pagination: {} });

            expect(result).toEqual(createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error getting vendor services', {
                errors: [{ field: 'getVendorServices', message: 'Query Error' }]
            }));
        });

        it('should return success response when vendor services are found', async () => {
            const filter = { name: 'Test Service' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: 'Test Service', $options: 'i' } };
            const vendorServices = [
              { _id: '1', name: 'Test Service 1' },
              { _id: '2', name: 'Test Service 2' }
            ];
            const paginationInfo = { total: 2, limit: 10, skip: 0 };
    
            buildVendorServiceQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);
    
            const mockFind = {
              populate: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              sort: jest.fn().mockResolvedValue(vendorServices)
            };
    
            VendorService.find.mockReturnValue(mockFind);
            createResponse.mockReturnValue({
              status: 'SUCCESS',
              message: 'Vendor services fetched successfully',
              result: { vendorServices },
              pagination: paginationInfo
            });
    
            const result = await vendorServiceResolvers.Query.getVendorServices(null, { filter, pagination });
    
            expect(buildVendorServiceQuery).toHaveBeenCalledWith(filter);
            expect(getPaginationInfo).toHaveBeenCalledWith(VendorService, query, pagination.limit, pagination.skip);
            expect(VendorService.find).toHaveBeenCalledWith(query);
            expect(mockFind.populate).toHaveBeenCalledWith('vendorType');
            expect(mockFind.populate).toHaveBeenCalledWith('features');
            expect(mockFind.populate).toHaveBeenCalledWith('media');
            expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
            expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
            expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
            expect(createResponse).toHaveBeenCalledWith(
              'VendorServicesResponse',
              'SUCCESS',
              'Vendor services fetched successfully',
              { result: { vendorServices }, pagination: paginationInfo }
            );
            expect(result).toEqual({
              status: 'SUCCESS',
              message: 'Vendor services fetched successfully',
              result: { vendorServices },
              pagination: paginationInfo
            });
        });

        it('should return failure response when no vendor services are found', async () => {
            const filter = { name: 'Nonexistent Service' };
            const pagination = { limit: 10, skip: 0 };
            const query = { name: { $regex: 'Nonexistent Service', $options: 'i' } };
            const vendorServices = [];
            const paginationInfo = { total: 0, limit: 10, skip: 0 };
    
            buildVendorServiceQuery.mockReturnValue(query);
            getPaginationInfo.mockResolvedValue(paginationInfo);
    
            const mockFind = {
              populate: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              sort: jest.fn().mockResolvedValue(vendorServices)
            };
    
            VendorService.find.mockReturnValue(mockFind);
            createResponse.mockReturnValue({
              status: 'FAILURE',
              message: 'No vendor services found',
              result: { vendorServices },
              pagination: paginationInfo
            });
    
            const result = await vendorServiceResolvers.Query.getVendorServices(null, { filter, pagination });
    
            expect(buildVendorServiceQuery).toHaveBeenCalledWith(filter);
            expect(getPaginationInfo).toHaveBeenCalledWith(VendorService, query, pagination.limit, pagination.skip);
            expect(VendorService.find).toHaveBeenCalledWith(query);
            expect(createResponse).toHaveBeenCalledWith(
              'VendorServicesResponse',
              'FAILURE',
              'No vendor services found',
              { result: { vendorServices }, pagination: paginationInfo }
            );
            expect(result).toEqual({
              status: 'FAILURE',
              message: 'No vendor services found',
              result: { vendorServices },
              pagination: paginationInfo
            });
        });
    });

    describe('Mutation: createVendorService', () => {
        it('should create a vendor service successfully', async () => {
            const input = { name: 'Test Vendor' };
            const vendorService = { id: '123', ...input };
            VendorService.mockImplementation(() => ({
                save: jest.fn().mockResolvedValue(vendorService)
            }));

            const result = await vendorServiceResolvers.Mutation.createVendorService(null, { input });

            expect(result).toEqual(createResponse('VendorServiceResponse', 'SUCCESS', 'Vendor service created successfully', { result: { vendorService } }));
        });

        it('should return error when creation fails', async () => {
            VendorService.mockImplementation(() => ({
                save: jest.fn().mockRejectedValue(new Error('Create Error'))
            }));

            const result = await vendorServiceResolvers.Mutation.createVendorService(null, { input: { name: 'Test Vendor' } });

            expect(result).toEqual(createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error creating vendor service', {
                errors: [{ field: 'createVendorService', message: 'Create Error' }]
            }));
        });
    });

    describe('Mutation: updateVendorService', () => {
        it('should update VendorService successfully', async () => {
            const id = 'serviceId';
            const input = { title: 'Updated Service' };
            const vendorService = { 
                _id: id, 
                title: 'Updated Service',
                vendorType: 'vendorTypeId',
                features: ['featureId'],
                media: ['mediaId']
            };
            
            const mockUpdateResponse = {
                populate: jest.fn().mockReturnThis(),
            };
            mockUpdateResponse.populate
                .mockReturnValueOnce(mockUpdateResponse)
                .mockReturnValueOnce(mockUpdateResponse)
                .mockReturnValueOnce(vendorService);
        
            VendorService.findByIdAndUpdate = jest.fn().mockReturnValue(mockUpdateResponse);
            createResponse.mockReturnValue('successResponse');
            clearCacheById.mockResolvedValue();
        
            const result = await vendorServiceResolvers.Mutation.updateVendorService(null, { id, input });
        
            expect(VendorService.findByIdAndUpdate).toHaveBeenCalledWith(
                id,
                { $set: input },
                { new: true }
            );
            expect(mockUpdateResponse.populate.mock.calls).toEqual([
                ['vendorType'],
                ['features'],
                ['media']
            ]);
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(createResponse).toHaveBeenCalledWith(
                'VendorServiceResponse',
                'SUCCESS',
                'Vendor service updated successfully',
                { result: { vendorService } }
            );
            expect(result).toBe('successResponse');
        });

        it('should return error response if VendorService not found', async () => {
            const id = 'nonexistentId';
            const input = { name: 'Updated Service' };
            
            const mockPopulateChain = {
                populate: jest.fn().mockReturnThis(),
            };
            
            VendorService.findByIdAndUpdate.mockReturnValue(mockPopulateChain);
            mockPopulateChain.populate.mockReturnValueOnce(mockPopulateChain)
                .mockReturnValueOnce(mockPopulateChain)
                .mockReturnValueOnce(null);
        
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'VendorService not found',
                errors: [{ field: 'updateVendorService', message: 'VendorService not found' }]
            });
        
            const result = await vendorServiceResolvers.Mutation.updateVendorService(null, { id, input });
        
            expect(VendorService.findByIdAndUpdate).toHaveBeenCalledWith(
                id,
                { $set: input },
                { new: true }
            );
            expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(1, 'vendorType');
            expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(2, 'features');
            expect(mockPopulateChain.populate).toHaveBeenNthCalledWith(3, 'media');
            expect(createResponse).toHaveBeenCalledWith(
                'VendorServiceErrorResponse',
                'FAILURE',
                'VendorService not found',
                {
                    errors: [{ field: 'updateVendorService', message: 'VendorService not found' }]
                }
            );
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'VendorService not found',
                errors: [{ field: 'updateVendorService', message: 'VendorService not found' }]
            });
        });
    });

    describe('Mutation: deleteVendorService', () => {
        it('should check for references before deletion', async () => {
            const id = 'serviceId';
            const references = ['Collection1', 'Collection2'];
            
            findReferences.mockResolvedValue(references);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'VendorService cannot be deleted',
                errors: [{
                    field: 'deleteVendorService',
                    message: 'VendorService cannot be deleted as it has references in the following collections: Collection1, Collection2'
                }]
            });

            const result = await vendorServiceResolvers.Mutation.deleteVendorService(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorService.findByIdAndDelete).not.toHaveBeenCalled();
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'VendorService cannot be deleted',
                errors: [{
                    field: 'deleteVendorService',
                    message: 'VendorService cannot be deleted as it has references in the following collections: Collection1, Collection2'
                }]
            });
        });

        it('should delete VendorService successfully when no references exist', async () => {
            const id = 'serviceId';
            const vendorService = { _id: id, title: 'Test Service' };
            
            findReferences.mockResolvedValue([]);
            VendorService.findByIdAndDelete.mockResolvedValue(vendorService);
            clearCacheById.mockResolvedValue();
            createResponse.mockReturnValue({
                status: 'SUCCESS',
                message: 'Vendor service deleted successfully',
                result: { vendorService }
            });

            const result = await vendorServiceResolvers.Mutation.deleteVendorService(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorService.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(clearCacheById).toHaveBeenCalledWith(id);
            expect(result).toEqual({
                status: 'SUCCESS',
                message: 'Vendor service deleted successfully',
                result: { vendorService }
            });
        });

        it('should return error response if VendorService not found', async () => {
            const id = 'nonexistentId';
            
            findReferences.mockResolvedValue([]);
            VendorService.findByIdAndDelete.mockResolvedValue(null);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'VendorService not found',
                errors: [{ field: 'deleteVendorService', message: 'VendorService not found' }]
            });

            const result = await vendorServiceResolvers.Mutation.deleteVendorService(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorService.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'VendorService not found',
                errors: [{ field: 'deleteVendorService', message: 'VendorService not found' }]
            });
        });

        it('should handle errors during deletion', async () => {
            const id = 'serviceId';
            const error = new Error('Database error');
            
            findReferences.mockResolvedValue([]);
            VendorService.findByIdAndDelete.mockRejectedValue(error);
            createResponse.mockReturnValue({
                status: 'FAILURE',
                message: 'Error deleting vendor service',
                errors: [{ field: 'deleteVendorService', message: error.message }]
            });

            const result = await vendorServiceResolvers.Mutation.deleteVendorService(null, { id });

            expect(findReferences).toHaveBeenCalledWith(id);
            expect(VendorService.findByIdAndDelete).toHaveBeenCalledWith(id);
            expect(result).toEqual({
                status: 'FAILURE',
                message: 'Error deleting vendor service',
                errors: [{ field: 'deleteVendorService', message: error.message }]
            });
        });
    });
});

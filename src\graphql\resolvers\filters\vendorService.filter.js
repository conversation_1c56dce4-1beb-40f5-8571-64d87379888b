const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdVendorTypeQuery = require("./mdVendorType.filter");
const buildVendorQuery = require("./vendor.filter");
const buildMdFeatureQuery = require("./mdFeature.filter");
const buildMediaQuery = require("./media.filter");
const MdVendorType = require("../../../models/MdVendorType");
const Vendor = require("../../../models/Vendor");
const MdFeature = require("../../../models/MdFeature");
const Media = require("../../../models/Media");

const buildVendorServiceQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.title) {
            query.title = { $regex: filter.title, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.priceRange) {
            query.priceRange = { $regex: filter.priceRange, $options: 'i' };
        }

        if (filter.specialities) {
            query.specialities = { $regex: filter.specialities, $options: 'i' };
        }

        if (filter.vendorType) {
            if (typeof filter.vendorType === 'string') {
                if (isValidObjectId(filter.vendorType)) {
                    query.vendorType = filter.vendorType;
                } else {
                    throw new Error('Invalid vendorType ID provided');
                }
            } else if (typeof filter.vendorType === 'object') {
                const vendorTypeQuery = await buildMdVendorTypeQuery(filter.vendorType);
                if (Object.keys(vendorTypeQuery).length > 0) {
                    const matchingVendorTypes = await MdVendorType.find(vendorTypeQuery).select('_id');
                    if (matchingVendorTypes.length > 0) {
                        query.vendorType = { $in: matchingVendorTypes.map(vt => vt._id) };
                    } else {
                        query.vendorType = { $in: [] };
                    }
                }
            }
        }

        if (filter.vendor) {
            if (typeof filter.vendor === 'string') {
                if (isValidObjectId(filter.vendor)) {
                    query.vendor = filter.vendor;
                } else {
                    throw new Error('Invalid vendor ID provided');
                }
            } else if (typeof filter.vendor === 'object') {
                const vendorPipeline = buildVendorQuery(filter.vendor);
                if (vendorPipeline.length > 0) {
                    const matchingVendors = await Vendor.aggregate(vendorPipeline);
                    if (matchingVendors.length > 0) {
                        query.vendor = { $in: matchingVendors.map(vendor => vendor._id) };
                    } else {
                        query.vendor = { $in: [] };
                    }
                }
            }
        }

        if (filter.features && filter.features.length > 0) {
            const featureQueries = await Promise.all(
                filter.features.map(async (feature) => {
                    if (typeof feature === 'string') {
                        if (isValidObjectId(feature)) {
                            return feature;
                        } else {
                            throw new Error('Invalid feature ID provided');
                        }
                    } else if (typeof feature === 'object') {
                        const featureQuery = await buildMdFeatureQuery(feature);
                        if (Object.keys(featureQuery).length > 0) {
                            const matchingFeatures = await MdFeature.find(featureQuery).select('_id');
                            return matchingFeatures.map(feature => feature._id);
                        }
                    }
                    return [];
                })
            );

            const flattenedFeatures = featureQueries.flat().filter(Boolean);
            if (flattenedFeatures.length > 0) {
                query.features = { $all: flattenedFeatures };
            } else {
                query.features = { $in: [] };
            }
        }

        if (filter.media && filter.media.length > 0) {
            const mediaQueries = await Promise.all(
                filter.media.map(async (media) => {
                    if (typeof media === 'string') {
                        if (isValidObjectId(media)) {
                            return media;
                        } else {
                            throw new Error('Invalid media ID provided');
                        }
                    } else if (typeof media === 'object') {
                        const mediaQuery = await buildMediaQuery(media);
                        if (Object.keys(mediaQuery).length > 0) {
                            const matchingMedia = await Media.find(mediaQuery).select('_id');
                            return matchingMedia.map(media => media._id);
                        }
                    }
                    return [];
                })
            );

            const flattenedMedia = mediaQueries.flat().filter(Boolean);
            if (flattenedMedia.length > 0) {
                query.media = { $all: flattenedMedia };
            } else {
                query.media = { $in: [] };
            }
        }
    }

    return query;
};

module.exports = buildVendorServiceQuery;
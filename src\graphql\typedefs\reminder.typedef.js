const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const reminderTypeDef = gql`
    type Reminder {
        id: ID!
        message: String!
        party: Party!
        datetime: DateTime!
    }

    type ReminderWrapper {
        reminder: Reminder!
    }

    type RemindersWrapper {
        reminders: [Reminder!]!
    }

    type ReminderResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: ReminderWrapper!
    }

    type RemindersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: RemindersWrapper!
        pagination: PaginationInfo!
    }

    type ReminderErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union ReminderResult = ReminderResponse | ReminderErrorResponse
    union RemindersResult = RemindersResponse | ReminderErrorResponse

    input ReminderFilterInput {
        id: ID
        party: ID!
        message: String
        datetime: DateTime
    }

    type Query {
        getReminderById(id: ID!): ReminderResult!
        getReminders(filter: ReminderFilterInput, pagination: PaginationInput): RemindersResult!
    }

    input ReminderInput {
        message: String!
        party: ID!
        datetime: DateTime!
    }

    input ReminderUpdateInput {
        message: String
        datetime: DateTime
    }

    type Mutation {
        createReminder(input: ReminderInput!): ReminderResult!
        updateReminder(id: ID!, reminder: ReminderUpdateInput!): ReminderResult!
        deleteReminder(id: ID!): ReminderResult!
    }    
`;

module.exports = reminderTypeDef;

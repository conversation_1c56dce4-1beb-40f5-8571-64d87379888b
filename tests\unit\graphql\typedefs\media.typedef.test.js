const { gql } = require('graphql-tag');
const mediaTypeDef = require('../../../../src/graphql/typedefs/media.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mediaTypeDef', () => {
    it('should contain the Media type definitions', () => {
        const expectedTypeDefs = gql`
            ${sharedTypeDef}

            type Media {
                id: ID!
                url: String!
                title: String!
                description: String
                uploadedAt: DateTime!
                tags: [Tag!]
            }

            input MediaFilterInput {
                title: String
                uploadedAt: DateTimeRangeInput
                tags: [String!]
            }

            type MediaWrapper {
                media: Media!
            }

            type MediasWrapper {
                medias: [Media]!
            }

            type MediaResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: MediaWrapper!
            }

            type MediasResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: MediasWrapper!
                pagination: PaginationInfo!
            }

            type MediaErrorResponse implements Response {
                status: ResponseStatus!
                message: String!
                errors: [Error!]!
            }

            union MediaResult = MediaResponse | MediaErrorResponse
            union MediasResult = MediasResponse | MediaErrorResponse

            type Query {
                getMediaById(id: ID!): MediaResult!
                getMedias(filter: MediaFilterInput, pagination: PaginationInput): MediasResult!
            }

            input MediaInput {
                url: String!
                title: String!
                description: String
                tagIds: [ID!]
            }

            input MediaUpdateInput {
                url: String
                title: String
                description: String
                tagIds: [ID!]
            }

            type Mutation {
                createMedia(input: MediaInput!): MediaResult!
                updateMedia(id: ID!, input: MediaUpdateInput!): MediaResult!
                deleteMedia(id: ID!): MediaResult!
            }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(mediaTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});
const { Server: SocketServer } = require('socket.io');
const Event = require('../models/Event');
const Host = require('../models/Host');
const Party = require('../models/Party');
const Guest = require('../models/Guest');
const LiveLocation = require('../models/liveLocation');
const { User } = require('../models/User');
const { verifyTokenLocally, verifyClerkUser } = require('./clerk.util');
const { notifyLocationTrackingRequest } = require('../notification/activityNotification');

let io = null;

async function getPersonData({ id, eventId }) {
    const event = await Event.findById(eventId);
    const mainHost = await Host.findOne({
        _id: event.mainHost,
        userId: id
    });

    if (mainHost) {
        return { role: 'mainhost', id: mainHost._id };
    }

    const parties = await Party.find({ eventId: event._id });
    const allCoHostIds = [
        ...(event.coHosts || []),
        ...parties.reduce((ids, party) => [...ids, ...(party.coHosts || [])], [])
    ];

    const coHost = await Host.findOne({
        _id: { $in: allCoHostIds },
        userId: id
    });

    if (coHost) {
        return { role: 'cohost', id: coHost._id };
    }

    const partyids = parties.map(p => p._id)

    const guest = await Guest.findOne({
        party: { $in: partyids },
        user: id
    });

    if (guest) {
        return { role: 'guest', id: guest._id };
    }

    return null;
}

function setupSocketIO(httpServer) {
    // Create Socket.IO instance with proper configuration
    io = new SocketServer(httpServer, {
        cors: {
            origin: "*", // In production, replace with your specific origins
            methods: ["GET", "POST"]
        },
        pingTimeout: 60000, // 60 seconds
        pingInterval: 25000, // 25 seconds
    });

    // Create a namespace for events with authentication middleware
    const eventNamespace = io.of('/event');

    // Add authentication middleware
    eventNamespace.use(async (socket, next) => {
        try {
            let token = socket.handshake.auth.token || socket.handshake.headers['authorization'];

            if (!token) {
                const error = new Error('Authentication token missing');
                error.data = { code: 'BAD_REQUEST', status: 400 };
                return next(error);
            }

            // Clean up the token if it includes 'Bearer '
            if (token.startsWith('Bearer ')) {
                token = token.slice(7);
            }

            // Basic token format validation
            if (!/^[\w-]+\.[\w-]+\.[\w-]+$/.test(token)) {
                const error = new Error('Invalid token format');
                error.data = { code: 'BAD_REQUEST', status: 400 };
                return next(error);
            }

            try {
                const verifiedToken = await verifyTokenLocally(token);
                await verifyClerkUser(verifiedToken.sub);

                const user = await User.findOne({ externalId: verifiedToken.sub });
                socket.data.userId = user._id;
                next();
            } catch (error) {
                console.error('Token verification failed:', error);
                const authError = new Error('Invalid authentication token');
                authError.data = { code: 'UNAUTHORIZED', status: 401 };
                return next(authError);
            }
        } catch (error) {
            console.error('Authentication error:', error);
            const serverError = new Error('Authentication failed');
            serverError.data = { code: 'SERVER_ERROR', status: 500 };
            return next(serverError);
        }
    });

    let room = null;

    eventNamespace.on('connection', async (socket) => {
        console.log('Authenticated client connected to event namespace:', socket.id);
        // Join specific event's party room when client requests
        socket.on('joinEventParty', async ({ eventId, partyId }) => {
            try {
                // Use the verified userId from the socket
                const id = socket.data.userId.toString();

                room = `event_${eventId}_party_${partyId}`;

                // Validate parameters
                if (!eventId || !partyId || !id) {
                    socket.emit('error', { message: 'Invalid parameters' });
                    return;
                }

                const personData = await getPersonData({ id, eventId });
                if (personData === null) {
                    socket.emit('error', { message: 'You are not authorized to join this event' });
                    return;
                }

                socket.data.personData = personData;

                room = `event_${eventId}_party_${partyId}`;
                await socket.join(room);

                console.log(`Socket ${socket.id} joined party room: ${room}`);
                socket.emit('roomJoined', { room, eventId, partyId, socketId: socket.id });

            } catch (error) {
                console.error('Error joining room:', error);
                socket.emit('error', { message: 'Failed to join room' });
            }
        });

        socket.on('sendLiveLocation', async ({ location }) => {
            const liveLocationData = await LiveLocation.findOne({ guestId: location.guestId });
            if (liveLocationData) {
                liveLocationData.allowLiveTracking = location.allowLiveTracking;
                liveLocationData.locationId = location.locationId;
                await liveLocationData.save();
                socket.emit('liveLocationSent', { message: 'Live location sent successfully' });
                io.of('/event').to(room).emit('liveLocationUpdated', { liveLocationData });
            }
        });

        // Leave room handler
        socket.on('leaveEventParty', async ({ eventId, partyId }) => {
            try {
                await socket.leave(eventRoom);
                console.log(`Socket ${socket.id} left party room: ${eventRoom}`);
                socket.emit('roomLeft', { room, eventId, partyId });

            } catch (error) {
                console.error('Error leaving room:', error);
                socket.emit('error', { message: 'Failed to leave room' });
            }
        });

        socket.on('disconnect', () => {
            console.log('Client disconnected from event namespace:', socket.id);
        });

        // Error handling
        socket.on('error', (error) => {
            console.error('Socket error:', error);
            socket.emit('error', { message: 'Internal socket error' });
        });
    });

    // Global error handling for Socket.IO
    io.on('error', (error) => {
        console.error('Socket.IO error:', error);
    });

    return io;
}

function emitPartyActivityUpdate({ eventId, partyId, message, sub_type }) {
    if (!io) {
        console.error('Socket.IO not initialized');
        return;
    }

    try {
        // Validate parameters
        if (!eventId || !partyId || !message) {
            console.error('Invalid parameters for emitUpdateParty');
            return;
        }

        const room = `event_${eventId}_party_${partyId}`;
        const eventNamespace = io.of('/event');

        // Emit update with timestamp and relevant data
        eventNamespace.to(room).emit('partyActivityUpdate', {
            type: 'PARTY_ACTIVITY_UPDATE',
            sub_type,
            timestamp: new Date().toISOString(),
            data: {
                partyId,
                eventId,
                message
            }
        });

        console.log(`Party activity update emitted to room: ${room}`);
    } catch (error) {
        console.error('Error emitting party activity update:', error);
    }
}

async function requestLiveLocation({ eventId, partyId }) {
    try {
        const party = await Party.findById(partyId);
        const requester = await User.findById(socket.data.userId);
        const requesterName = `${requester.firstName || ''} ${requester.lastName || ''}`.trim();
        
        const guestData = await Guest.find({ party: partyId }).populate('user');
        const liveLocationData = await LiveLocation.find({ 
            guestId: { $in: guestData.map(guest => guest._id) } 
        });
        
        const room = `event_${eventId}_party_${partyId}`;
        const sockets = await io.of('/event').in(room).fetchSockets();

        // Send notifications to each guest who will receive the location request
        for (const guest of guestData) {
            const locationData = liveLocationData.find(
                location => location.guestId.toString() === guest._id.toString()
            );
            
            if (locationData && 
                (locationData.allowLiveTracking === 'PENDING' || 
                 locationData.allowLiveTracking === 'ALLOW')) {
                // Send notification to this specific guest
                await notifyLocationTrackingRequest(
                    party.name,
                    requesterName,
                    partyId,
                    guest.user._id
                );
            }
        }

        // Emit socket events to connected clients
        sockets.forEach(async socket => {
            const locationData = liveLocationData.find(
                location => location.guestId.toString() === socket.data.personData.id.toString()
            );
            if (locationData && 
                (locationData.allowLiveTracking === 'PENDING' || 
                 locationData.allowLiveTracking === 'ALLOW')) {
                socket.emit('getLiveLocation', locationData);
            }
        });
    } catch (error) {
        console.error('Error in requestLiveLocation:', error);
    }
}

module.exports = {
    setupSocketIO,
    emitPartyActivityUpdate,
    requestLiveLocation
}; 
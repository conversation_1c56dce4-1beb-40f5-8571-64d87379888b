const buildTransactionQuery = require('../../../../../src/graphql/resolvers/filters/transaction.filter');

describe('buildTransactionQuery', () => {
    it('should return an empty query object when no filters are provided', () => {
        const result = buildTransactionQuery();
        expect(result).toEqual({});
    });

    it('should build a query with description filter', () => {
        const filters = { description: 'test' };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        description: { $regex: 'test', $options: 'i' }
        });
    });

    it('should build a query with type filter', () => {
        const filters = { type: 'INCOME' };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({ type: 'INCOME' });
    });

    it('should build a query with method filter', () => {
        const filters = { method: 'CREDIT_CARD' };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({ method: 'CREDIT_CARD' });
    });

    it('should build a query with timeStamp range filter', () => {
        const filters = {
        timeStamp: { start: '2023-01-01', end: '2023-12-31' }
        };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        timeStamp: { $gte: '2023-01-01', $lte: '2023-12-31' }
        });
    });

    it('should build a query with only timeStamp start filter', () => {
        const filters = {
        timeStamp: { start: '2023-01-01' }
        };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        timeStamp: { $gte: '2023-01-01' }
        });
    });

    it('should build a query with only timeStamp end filter', () => {
        const filters = {
        timeStamp: { end: '2023-12-31' }
        };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        timeStamp: { $lte: '2023-12-31' }
        });
    });

    it('should build a query with minAmount filter', () => {
        const filters = { minAmount: 100 };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        amount: { $gte: 100 }
        });
    });

    it('should build a query with maxAmount filter', () => {
        const filters = { maxAmount: 1000 };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        amount: { $lte: 1000 }
        });
    });

    it('should build a query with both minAmount and maxAmount filters', () => {
        const filters = { minAmount: 100, maxAmount: 1000 };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        amount: { $gte: 100, $lte: 1000 }
        });
    });

    it('should build a query with multiple filters', () => {
        const filters = {
        description: 'test',
        type: 'EXPENSE',
        method: 'CASH',
        timeStamp: { start: '2023-01-01', end: '2023-12-31' },
        minAmount: 50,
        maxAmount: 500
        };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        description: { $regex: 'test', $options: 'i' },
        type: 'EXPENSE',
        method: 'CASH',
        timeStamp: { $gte: '2023-01-01', $lte: '2023-12-31' },
        amount: { $gte: 50, $lte: 500 }
        });
    });

    it('should build a query with only timeStamp end filter', () => {
        const filters = {
        timeStamp: { end: '2023-12-31' }
        };
        const result = buildTransactionQuery(filters);
        expect(result).toEqual({
        timeStamp: { $lte: '2023-12-31' }
        });
    });
});
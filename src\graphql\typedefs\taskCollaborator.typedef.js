const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type TaskCollaborator {
        id: ID!
        user: User!
        task: Task!
        assignedOn: DateTime!
    }

    input TaskCollaboratorFilterInput {
        taskId: ID
        userId: ID
        assignedOn: DateTimeRangeInput
    }

    type TaskCollaboratorWrapper {
        taskCollaborator: TaskCollaborator!
    }

    type TaskCollaboratorsWrapper {
        taskCollaborators: [TaskCollaborator]!
    }

    type TaskCollaboratorResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TaskCollaboratorWrapper!
    }

    type TaskCollaboratorsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TaskCollaboratorsWrapper!
        pagination: PaginationInfo!
    }

    type TaskCollaboratorErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union TaskCollaboratorResult = TaskCollaboratorResponse | TaskCollaboratorErrorResponse
    union TaskCollaboratorsResult = TaskCollaboratorsResponse | TaskCollaboratorErrorResponse

    type Query {
        getTaskCollaboratorById(id: ID!): TaskCollaboratorResult!
        getTaskCollaborators(filter: TaskCollaboratorFilterInput, pagination: PaginationInput): TaskCollaboratorsResult!
    }

    type Mutation {
        createTaskCollaborator(taskId: ID!, userIds: [ID!]!): TaskCollaboratorResult!
        deleteTaskCollaborator(id: ID!): TaskCollaboratorResult!
    }
`; 
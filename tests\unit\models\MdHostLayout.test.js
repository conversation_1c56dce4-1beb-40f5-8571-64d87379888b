const mongoose = require('mongoose');
const MdHostLayout = require('../../../src/models/MdHostLayout');
const { MongoMemoryServer } = require('mongodb-memory-server');



describe('MdHostLayout Model', () => {
    let mongoServer;

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        const uri = mongoServer.getUri();
        await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    });
    
    afterAll(async () => {
        await mongoose.disconnect();
        await mongoServer.stop();
    });

    it('should create and save a host layout successfully', async () => {
        const validHostLayout = new MdHostLayout({
            navBar: {
                home: new mongoose.Types.ObjectId(),
                planning: new mongoose.Types.ObjectId(),
                guests: new mongoose.Types.ObjectId(),
                apps: new mongoose.Types.ObjectId(),
                messages: new mongoose.Types.ObjectId(),
            }
        });

        const savedHostLayout = await validHostLayout.save();
        
        expect(savedHostLayout._id).toBeDefined();
        expect(savedHostLayout.navBar.home).toEqual(validHostLayout.navBar.home);
        expect(savedHostLayout.navBar.planning).toEqual(validHostLayout.navBar.planning);
        expect(savedHostLayout.navBar.guests).toEqual(validHostLayout.navBar.guests);
        expect(savedHostLayout.navBar.apps).toEqual(validHostLayout.navBar.apps);
        expect(savedHostLayout.navBar.messages).toEqual(validHostLayout.navBar.messages);
    });

    it('should fail to create a host layout without required fields', async () => {
        const invalidHostLayout = new MdHostLayout({});
        let err;
        try {
            await invalidHostLayout.save();
        } catch (error) {
            err = error;
        }
        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.navBar).toBeDefined();
    });

    it('should fail to create a host layout with invalid navBar fields', async () => {
        const invalidHostLayout = new MdHostLayout({
            navBar: {
                home: 'invalid',
                planning: 'invalid',
                guests: 'invalid',
                apps: 'invalid',
                messages: 'invalid',
            }
        });
        let err;
        try {
            await invalidHostLayout.save();
        } catch (error) {
            err = error;
        }
        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors['navBar.home']).toBeDefined();
        expect(err.errors['navBar.planning']).toBeDefined();
        expect(err.errors['navBar.guests']).toBeDefined();
        expect(err.errors['navBar.apps']).toBeDefined();
        expect(err.errors['navBar.messages']).toBeDefined();
    });

    it('should successfully update an existing host layout', async () => {
        const hostLayout = new MdHostLayout({
            navBar: {
                home: new mongoose.Types.ObjectId(),
                planning: new mongoose.Types.ObjectId(),
                guests: new mongoose.Types.ObjectId(),
                apps: new mongoose.Types.ObjectId(),
                messages: new mongoose.Types.ObjectId(),
            }
        });

        await hostLayout.save();

        const newHomeId = new mongoose.Types.ObjectId();
        hostLayout.navBar.home = newHomeId;
        const updatedHostLayout = await hostLayout.save();

        expect(updatedHostLayout.navBar.home).toEqual(newHomeId);
    });
});

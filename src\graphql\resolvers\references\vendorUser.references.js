const Vendor = require('../../../models/Vendor');

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];

    const vendorReferences = await Vendor.find({
        $or: [
            { 'primaryContact': id },
            { 'contacts': id }
        ]
    });
    if (hasReferences(vendorReferences)) {
        references.push('Vendor');
    }

    return references;
}

module.exports = findReferences;
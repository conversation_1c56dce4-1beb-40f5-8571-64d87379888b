const mongoose = require('mongoose');

const userArticleActivitySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  articleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Article',
    required: true
  },
  liked: {
    type: Boolean,
    default: null
  },
  saved: {
    type: Boolean,
    default: false
  },
  shared: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  collection: 'user_article_activities'
});

// Create compound index for userId and articleId to ensure uniqueness
userArticleActivitySchema.index({ userId: 1, articleId: 1 }, { unique: true });

module.exports = mongoose.model('UserArticleActivity', userArticleActivitySchema); 
const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Reaction {
        id: ID!
        reactionUnicode: String!
        user: User!
        createdAt: DateTime!
        updatedAt: DateTime!
    }

    input ReactionFilterInput {
        reactionUnicode: String
        user: UserFilterInput
        createdAt: DateTimeRangeInput
        updatedAt: DateTimeRangeInput
    }

    type ReactionWrapper {
        reaction: Reaction!
    }

    type ReactionsWrapper {
        reactions: [Reaction]!
    }

    type ReactionResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: ReactionWrapper!
    }

    type ReactionsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: ReactionsWrapper!
        pagination: PaginationInfo!
    }

    type ReactionErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union ReactionResult = ReactionResponse | ReactionErrorResponse
    union ReactionsResult = ReactionsResponse | ReactionErrorResponse

    type Query {
        getReactionById(id: ID!): ReactionResult!
        getReactions(filter: ReactionFilterInput, pagination: PaginationInput): ReactionsResult!
    }

    input ReactionInput {
        reactionUnicode: String!
    }

    type Mutation {
        createReaction(input: ReactionInput!): ReactionResult!
        deleteReaction(id: ID!): ReactionResult!
    }
`; 
const mongoose = require('mongoose');
const { isValidObjectId } = mongoose;
const buildMdIconQuery = require('./mdIcon.filter');

async function buildMdInvitationTemplateQuery(filter = {}) {
    const query = {};

    if (filter.id) {
        if (Array.isArray(filter.id)) {
            query._id = { $in: filter.id };
        } else {
            query._id = filter.id;
        }
    }

    // Create an $or array for name and tag searches
    const orConditions = [];

    // Partial name matching using case-insensitive regex
    if (filter.name) {
        orConditions.push({ name: { $regex: filter.name, $options: 'i' } });
    }

    // Handle tag filtering
    if (filter.tags && filter.tags.length > 0) {
        const tagQueries = await Promise.all(filter.tags.map(async tagFilter => {
            // If we have a direct ID match
            if (tagFilter.id) {
                if (Array.isArray(tagFilter.id)) {
                    return { tags: { $in: tagFilter.id } };
                }
                return { tags: tagFilter.id };
            }
            
            // If we need to find tag IDs based on other criteria (name, slug, icon)
            const tagQuery = {};
            if (tagFilter.name) {
                tagQuery.name = { $regex: tagFilter.name, $options: 'i' };
            }
            if (tagFilter.slug) {
                tagQuery.slug = { $regex: tagFilter.slug, $options: 'i' };
            }
            if (tagFilter.icon) {
                if (typeof tagFilter.icon === 'string') {
                    if (isValidObjectId(tagFilter.icon)) {
                        tagQuery.icon = tagFilter.icon;
                    } else {
                        throw new Error('Invalid icon ID provided');
                    }
                } else if (typeof tagFilter.icon === 'object') {
                    const iconQuery = await buildMdIconQuery(tagFilter.icon);
                    if (Object.keys(iconQuery).length > 0) {
                        tagQuery.icon = iconQuery._id;
                    }
                }
            }

            // Find matching tag IDs
            if (Object.keys(tagQuery).length > 0) {
                const matchingTags = await mongoose.model('Tag').find(tagQuery).select('_id');
                const tagIds = matchingTags.map(tag => tag._id);
                return { tags: { $in: tagIds } };
            }

            return null;
        }));

        const validQueries = tagQueries.filter(query => query !== null);
        if (validQueries.length > 0) {
            orConditions.push(...validQueries);
        }
    }

    // Add the $or conditions to the query if there are any
    if (orConditions.length > 0) {
        query.$or = orConditions;
    }

    return query;
}

module.exports = buildMdInvitationTemplateQuery;


const { isValidObjectId } = require("../../../utils/validation.util");

const buildInAppNotificationQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.userId) {
            const userId = filter.userId.toString();
            if (isValidObjectId(userId)) {
                query.userId = userId;
            } else {
                throw new Error('Invalid userId provided');
            }
        }

        if (filter.read !== undefined) {
            query.read = filter.read;
        }

        if (filter.message) {
            query.message = { $regex: filter.message, $options: 'i' };
        }

        if (filter.createdAt) {
            query.createdAt = {};
            if (filter.createdAt.start) {
                query.createdAt.$gte = filter.createdAt.start;
            }
            if (filter.createdAt.end) {
                query.createdAt.$lte = filter.createdAt.end;
            }
        }
    }

    return query;
};

module.exports = buildInAppNotificationQuery; 
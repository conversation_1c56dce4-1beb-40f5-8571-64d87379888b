const buildMdPartnerCategoryQuery = require('../../../../../src/graphql/resolvers/filters/mdPatnerCategory.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdPartnerCategoryQuery', () => {
    it('should return an empty query object when no filters are provided', () => {
        const result = buildMdPartnerCategoryQuery();
        expect(result).toEqual({});
    });

    it('should build a query with a valid id filter', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const filters = { id: validId };
        const result = buildMdPartnerCategoryQuery(filters);
        expect(result).toEqual({ _id: validId });
    });

    it('should throw an error for an invalid id', () => {
        const invalidId = 'invalid_id';
        isValidObjectId.mockReturnValue(false);
        const filters = { id: invalidId };
        expect(() => buildMdPartnerCategoryQuery(filters)).toThrow('Invalid id provided');
    });

    it('should build a query with a name filter', () => {
        const filters = { name: 'Test Category' };
        const result = buildMdPartnerCategoryQuery(filters);
        expect(result).toEqual({ name: { $regex: 'Test Category', $options: 'i' } });
    });

    it('should build a query with both id and name filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);
        const filters = { id: validId, name: 'Test Category' };
        const result = buildMdPartnerCategoryQuery(filters);
        expect(result).toEqual({
            _id: validId,
            name: { $regex: 'Test Category', $options: 'i' }
        });
    });
});
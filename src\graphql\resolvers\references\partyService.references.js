const Transaction = require('../../../models/Transaction');
const Task = require('../../../models/Task');

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

const findReferences = async (id) => {
    const references = [];
    
    const transactionReferences = await Transaction.find({ 'partyService': id });
    if (hasReferences(transactionReferences)) {
        references.push('Transaction');
    }

    const taskReferences = await Task.find({ 'partyService': id });
    if (hasReferences(taskReferences)) {
        references.push('Task');
    }

    return references;
}

module.exports = findReferences;

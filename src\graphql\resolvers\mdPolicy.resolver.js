const MdPolicy = require('../../models/MdPolicy');
const Tag = require('../../models/Tag');
const { getByIdCache, clearCacheById, setCache } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { createResponse } = require('../../utils/response.util');
const { validateReferences } = require('../../utils/validation.util');
const buildMdPolicyQuery = require('./filters/mdPolicy.filter');

const baseMdPolicyIdSchema = {
    tags: { type: 'multiple', model: Tag }
};

const mdPolicyIdSchema = { ...baseMdPolicyIdSchema };

const mdPolicyResolvers = {
    MdPolicy: {
        tags: async (parent) => {
            try {
                return await Tag.find({ _id: { $in: parent.tags } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting tags');
            }
        }
    },

    Query: {
        getMdPolicyById: async (_, { id }) => {
            try {
                let mdPolicy = await getByIdCache(id);
                if (!mdPolicy) {
                    mdPolicy = await MdPolicy.findById(id);
                    if (!mdPolicy) {
                        return createResponse('MdPolicyErrorResponse', 'FAILURE', 'MdPolicy not found', {
                            errors: [{ field: 'getMdPolicyById', message: 'MdPolicy not found' }]
                        });
                    }
                    await setCache(id, mdPolicy);
                }
                return createResponse('MdPolicyResponse', 'SUCCESS', 'MdPolicy fetched successfully', {
                    result: { mdPolicy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Error getting MdPolicy', {
                    errors: [{ field: 'getMdPolicyById', message: error.message }]
                });
            }
        },

        getMdPolicies: async (_, { filter, pagination }) => {
            try {
                const query = await buildMdPolicyQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdPolicy, query, limit, skip);

                const mdPolicies = await MdPolicy.find(query)
                    .populate('tags')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (mdPolicies.length === 0) {
                    return createResponse('MdPoliciesResponse', 'FAILURE', 'No MdPolicies found', {
                        result: { mdPolicies },
                        pagination: paginationInfo
                    });
                }
                return createResponse('MdPoliciesResponse', 'SUCCESS', 'MdPolicies fetched successfully', {
                    result: { mdPolicies },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Error getting MdPolicies', {
                    errors: [{ field: 'getMdPolicies', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdPolicy: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, mdPolicyIdSchema, 'MdPolicy');
                if (validationError) {
                    return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors: validationError.errors
                    });
                }
                const mdPolicy = new MdPolicy(input);
                await mdPolicy.save();
                
                const populatedMdPolicy = await MdPolicy.findById(mdPolicy._id).populate('tags');
                
                return createResponse('MdPolicyResponse', 'SUCCESS', 'MdPolicy created successfully', {
                    result: { mdPolicy: populatedMdPolicy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Error creating MdPolicy', {
                    errors: [{ field: 'createMdPolicy', message: error.message }]
                });
            }
        },

        updateMdPolicy: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdPolicyIdSchema, 'MdPolicy');
                if (validationError) {
                    return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Invalid reference', {
                        errors: validationError.errors
                    });
                }
                const mdPolicy = await MdPolicy.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                ).populate('tags');

                if (!mdPolicy) {
                    return createResponse('MdPolicyErrorResponse', 'FAILURE', 'MdPolicy not found', {
                        errors: [{ field: 'updateMdPolicy', message: 'MdPolicy not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdPolicyResponse', 'SUCCESS', 'MdPolicy updated successfully', {
                    result: { mdPolicy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Error updating MdPolicy', {
                    errors: [{ field: 'updateMdPolicy', message: error.message }]
                });
            }
        },

        deleteMdPolicy: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdPolicy');
                if (references.length > 0) {
                    return createResponse('MdPolicyErrorResponse', 'FAILURE', 'MdPolicy cannot be deleted', {
                        errors: [{ field: 'id', message: `MdPolicy cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const mdPolicy = await MdPolicy.findByIdAndDelete(id);
                
                if (!mdPolicy) {
                    return createResponse('MdPolicyErrorResponse', 'FAILURE', 'MdPolicy not found', {
                        errors: [{ field: 'deleteMdPolicy', message: 'MdPolicy not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdPolicyResponse', 'SUCCESS', 'MdPolicy deleted successfully', {
                    result: { mdPolicy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPolicyErrorResponse', 'FAILURE', 'Error deleting MdPolicy', {
                    errors: [{ field: 'deleteMdPolicy', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdPolicyResolvers; 
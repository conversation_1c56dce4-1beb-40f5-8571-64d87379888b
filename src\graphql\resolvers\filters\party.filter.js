const Guest = require('../../../models/Guest');
const PartyService = require('../../../models/PartyService');
const MdServiceLocation = require('../../../models/MdServiceLocation');
const buildServiceLocationQuery = require('../filters/mdServiceLocation.filter');
const { isValidObjectId } = require('mongoose');
const buildMdVendorTypeQuery = require('../filters/mdVendorType.filter');
const MdVendorType = require('../../../models/MdVendorType');
const buildEventQuery = require('./event.filter');
const Event = require('../../../models/Event');
const VenueAddress = require('../../../models/VenueAddress');
const buildVenueAddressQuery = require('../filters/venueAddress.filter');

const buildPartyQuery = async (filters) => {
    const query = {};
    let postQueryFilters = {};

    if (filters) {
        if (filters.timeRange) {
            query.time = {};
            if (filters.timeRange.start) {
                query.time.$gte = filters.timeRange.start;
            }
            if (filters.timeRange.end) {
                query.time.$lte = filters.timeRange.end;
            }
        }

        if (filters.name) {
            query.name = { $regex: new RegExp(filters.name, 'i') };
        }

        if (filters.minExpectedGuests !== undefined || filters.maxExpectedGuests !== undefined) {
            postQueryFilters.expectedGuests = {
                min: filters.minExpectedGuests,
                max: filters.maxExpectedGuests
            };
        }

        if (filters.minBudget !== undefined || filters.maxBudget !== undefined) {
            postQueryFilters.budget = {
                min: filters.minBudget,
                max: filters.maxBudget
            };
        }

        if (filters.minExpenditure !== undefined || filters.maxExpenditure !== undefined) {
            postQueryFilters.expenditure = {
                min: filters.minExpenditure,
                max: filters.maxExpenditure
            };
        }

        if (filters.vendorTypes) {
            if (typeof filters.vendorTypes === 'string') {
                if (isValidObjectId(filters.vendorTypes)) {
                    query.vendorTypes = filters.vendorTypes;
                } else {
                    throw new Error('Invalid vendorTypes ID provided');
                }
            } else if (typeof filters.vendorTypes === 'object') {
                const vendorTypeQuery = await buildMdVendorTypeQuery(filters.vendorTypes);
                if (Object.keys(vendorTypeQuery).length > 0) {
                    const matchingVendorTypes = await MdVendorType.find(vendorTypeQuery).select('_id');
                    if (matchingVendorTypes.length > 0) {
                        query.vendorTypes = { $in: matchingVendorTypes.map(vt => vt._id) };
                    } else {
                        query.vendorTypes = { $in: [] };
                    }
                }
            }
        }

        

        if (filters.serviceLocation) {
            if (typeof filters.serviceLocation === 'string') {
                if (isValidObjectId(filters.serviceLocation)) {
                    query.serviceLocation = filters.serviceLocation;
                } else {
                    throw new Error('Invalid serviceLocation ID provided');
                }
            } else if (typeof filters.serviceLocation === 'object') {
                const serviceLocationQuery = await buildServiceLocationQuery(filters.serviceLocation);
                if (Object.keys(serviceLocationQuery).length > 0) {
                    const matchingLocations = await MdServiceLocation.find(serviceLocationQuery).select('_id');
                    if (matchingLocations.length > 0) {
                        query.serviceLocation = { $in: matchingLocations.map(loc => loc._id) };
                    } else {
                        query.serviceLocation = { $in: [] };
                    }
                }
            }
        }

        if (filters.eventId) {
            if (isValidObjectId(filters.eventId)) {
                query.eventId = filters.eventId;
            } else {
                throw new Error('Invalid eventId provided');
            }
        }

        if (filters.venueAddress) {
            if (typeof filters.venueAddress === 'string') {
                if (isValidObjectId(filters.venueAddress)) {
                    query.venueAddress = filters.venueAddress;
                } else {
                    throw new Error('Invalid venueAddress ID provided');
                }
            } else if (typeof filters.venueAddress === 'object') {
                const venueAddressQuery = buildVenueAddressQuery(filters.venueAddress);
                if (Object.keys(venueAddressQuery).length > 0) {
                    const matchingAddresses = await VenueAddress.find(venueAddressQuery).select('_id');
                    if (matchingAddresses.length > 0) {
                        query.venueAddress = { $in: matchingAddresses.map(addr => addr._id) };
                    } else {
                        query.venueAddress = { $in: [] };
                    }
                }
            }
        }
    }

    return {
        query,
        postQueryFilters,
        applyPostQueryFilters: async (parties) => {
            if (Object.keys(postQueryFilters).length === 0) {
                return parties;
            }

            const partiesWithCalculatedFields = await Promise.all(parties.map(async party => {
                const partyServices = await PartyService.find({ _id: { $in: party.services } });
                const totalBudget = partyServices.reduce((total, service) => total + (service.budget || 0), 0);
                const totalExpenditure = partyServices.reduce((total, service) => total + (service.expenditure || 0), 0);

                party.expectedGuestCount = expectedGuestCount;
                party.totalBudget = totalBudget;
                party.totalExpenditure = totalExpenditure;
                return party;
            }));

            return partiesWithCalculatedFields.filter(party => {
                let keep = true;

                if (postQueryFilters.expectedGuests) {
                    if (postQueryFilters.expectedGuests.min !== undefined) {
                        keep = keep && party.expectedGuestCount >= postQueryFilters.expectedGuests.min;
                    }
                    if (postQueryFilters.expectedGuests.max !== undefined) {
                        keep = keep && party.expectedGuestCount <= postQueryFilters.expectedGuests.max;
                    }
                }

                if (postQueryFilters.budget) {
                    if (postQueryFilters.budget.min !== undefined) {
                        keep = keep && party.totalBudget >= postQueryFilters.budget.min;
                    }
                    if (postQueryFilters.budget.max !== undefined) {
                        keep = keep && party.totalBudget <= postQueryFilters.budget.max;
                    }
                }

                if (postQueryFilters.expenditure) {
                    if (postQueryFilters.expenditure.min !== undefined) {
                        keep = keep && party.totalExpenditure >= postQueryFilters.expenditure.min;
                    }
                    if (postQueryFilters.expenditure.max !== undefined) {
                        keep = keep && party.totalExpenditure <= postQueryFilters.expenditure.max;
                    }
                }

                return keep;
            });
        }
    };
};

module.exports = buildPartyQuery; 
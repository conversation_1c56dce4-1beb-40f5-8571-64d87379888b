const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const tagSchema = new Schema({
    name: { type: String, required: true, unique: true, maxlength: 50 },   
    icon: { type: mongoose.Schema.Types.ObjectId, ref: 'MdIcon' },
    slug: { type: String, required: true, unique: true, maxlength: 50 }
}, { timestamps: true });

const Tag = model('Tag', tagSchema);

module.exports = Tag;
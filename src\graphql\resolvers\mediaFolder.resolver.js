const MediaFolder = require('../../models/MediaFolder');
const Event = require('../../models/Event');
const Party = require('../../models/Party');
const { User } = require('../../models/User');
const Tag = require('../../models/Tag');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMediaFolderQuery = require('./filters/mediaFolder.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');
const { deleteMediaAndRelatedData, getArchivesAlbum } = require('../../utils/media.util');
const { validateFolderName, validateEventCollaborators, validateImageUrl, validateAlbumCover } = require('../../utils/media.util');
const Media = require('../../models/Media');
const buildMediaFilterForMediaFolderQuery = require('./filters/mediaFilterForMediaFolder.filter');
const { hasMediaFolderLevelAccess } = require('../../utils/auth/accessLevels.util');
const { notifyPhotoAddedToAlbum, notifyPhotoRemovedFromAlbum } = require('../../notification/partyNotification');


const baseMediaFolderIdSchema = {
    event: { type: 'single', model: Event },
    party: { type: 'single', model: Party },
    media: { type: 'array', model: Media },
    tags: { type: 'array', model: Tag },
    albumCover: { type: 'single', model: Media }
};

const mediaFolderIdSchema = { ...baseMediaFolderIdSchema };

const createMediaFolderIdSchema = {
    ...baseMediaFolderIdSchema,
};

const mediaFolderResolvers = {
    MediaFolder: {
        event: async (parent) => {
            try {
                return await Event.findById(parent.event);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event');
            }
        },
        party: async (parent) => {
            try {
                return await Party.findById(parent.party);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party');
            }
        },
        media: async (parent, { filter }, context) => {
            try {
                let query = { _id: { $in: parent.media || [] } };

                if (filter) {
                    const filterQuery = await buildMediaFilterForMediaFolderQuery(filter, parent.id);
                    query = {
                        $and: [
                            { _id: { $in: parent.media || [] } },
                            filterQuery
                        ]
                    };
                }

                return await Media.find(query);
            } catch (error) {
                console.error('Error in MediaFolder.media resolver:', error);
                throw error;
            }
        },
        owner: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.owner } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting owners');
            }
        },
        sharedWith: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.sharedWith } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting shared users');
            }
        },
        contributors: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.contributors } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting contributors');
            }
        },
        tags: async (parent) => {
            try {
                return await Tag.find({ _id: { $in: parent.tags } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting tags');
            }
        },
        albumCover: async (parent) => {
            try {
                if (!parent.media || parent.media.length === 0) {
                    return null;
                }

                if (parent.albumCover && parent.albumCover.length > 0) {
                    return parent.albumCover;
                }

                const mediaItems = await Media.find({ 
                    _id: { $in: parent.media },
                    url: { $regex: /\.(jpg|jpeg|png|gif|webp)$/i }
                });

                if (mediaItems.length === 0) {
                    return null;
                }

                const shuffled = mediaItems.map(m => m.url).sort(() => 0.5 - Math.random());
                const selectedUrls = shuffled.slice(0, Math.min(2, shuffled.length));

                await MediaFolder.findByIdAndUpdate(parent._id, { albumCover: selectedUrls });

                return selectedUrls;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting album cover');
            }
        }
    },

    Query: {
        getMediaFolderById: async (_, { id }, context) => {
            try {
                if (!await hasMediaFolderLevelAccess(context.user._id, id)) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this media folder' }]
                    });
                }

                let mediaFolder = await getByIdCache(id);
                if (!mediaFolder) {
                    mediaFolder = await MediaFolder.findById(id);
                    if (!mediaFolder) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Media folder not found', {
                            errors: [{ field: 'id', message: 'Media folder not found' }]
                        });
                    }
                }
                
                return createResponse('MediaFolderResponse', 'SUCCESS', 'Media folder retrieved successfully', {
                    result: { mediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error retrieving media folder', {
                    errors: [{ field: 'getMediaFolderById', message: error.message }]
                });
            }
        },

        getFavoritesMediaFolder: async (_, __, context) => {
            try {
                const userId = context.user?._id;
                const favoritesAlbum = await MediaFolder.findOne({ owner: userId, name: 'MY FAVORITES ALBUM' });
                return favoritesAlbum ? createResponse('MediaFolderResponse', 'SUCCESS', 'Favorites album retrieved successfully', { result: { mediaFolder: favoritesAlbum } }) : createResponse('MediaFolderErrorResponse', 'FAILURE', 'Favorites album not found', { errors: [{ field: 'getUserFavoriteMediaFolders', message: 'Favorites album not found' }] });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error retrieving favorites album', { errors: [{ field: 'getUserFavoriteMediaFolders', message: error.message }] });
            }
        },

        getArchivesMediaFolder: async (_, __, context) => {
            try {
                const userId = context.user?._id;
                const archivesFolder = await MediaFolder.findOne({ owner: userId, name: 'MY ARCHIVES ALBUM', category: 'ALBUM' });
                return archivesFolder ? createResponse('MediaFolderResponse', 'SUCCESS', 'Archives album retrieved successfully', { result: { mediaFolder: archivesFolder } }) : createResponse('MediaFolderErrorResponse', 'FAILURE', 'Archives album not found', { errors: [{ field: 'getArchivesMediaFolder', message: 'Archives album not found' }] });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error retrieving archives album', { errors: [{ field: 'getArchivesMediaFolder', message: error.message }] });
            }
        },

        getMediaFolders: async (_, { filter, pagination }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const userId = context.user._id.toString();
                const baseQuery = await buildMediaFolderQuery(filter, userId);

                // Add condition to exclude special folders
                baseQuery.name = { 
                    $nin: ['MY FAVORITES ALBUM', 'MY ARCHIVES ALBUM'] 
                };

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MediaFolder, baseQuery, limit, skip);

                const mediaFolders = await MediaFolder.find(baseQuery)
                    .sort({ createdAt: -1 })
                    .limit(limit)
                    .skip(skip);

                if (mediaFolders.length === 0) {
                    return createResponse('MediaFoldersResponse', 'FAILURE', 'No media folders found', {
                        result: { mediaFolders },
                        pagination: paginationInfo
                    });
                }

                return createResponse('MediaFoldersResponse', 'SUCCESS', 'Media folders fetched successfully', {
                    result: { mediaFolders },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error retrieving media folders', {
                    errors: [{ field: 'getMediaFolders', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMediaFolder: async (_, { input }, context) => {
            try {
                const validationErrors = validateFolderName(input.name);
                if (validationErrors.length > 0) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid folder name', {
                        errors: validationErrors
                    });
                }

                const sanitizedInput = {
                    ...input,
                    sharedWith: input.sharedWith || [],
                    contributors: input.contributors || [],
                    tags: input.tags || [],
                    publish: input.publish ?? false
                };

                if (sanitizedInput.event) {
                    const event = await Event.findById(sanitizedInput.event);
                    if (!event) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Event not found', {
                            errors: [{ field: 'event', message: 'Event not found' }]
                        });
                    }

                    const collaboratorErrors = await validateEventCollaborators(event, sanitizedInput);
                    if (collaboratorErrors.length > 0) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized', {
                            errors: collaboratorErrors
                        });
                    }
                }

                if (input.albumCover) {
                    if (!Array.isArray(input.albumCover)) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid album cover format', {
                            errors: [{ field: 'albumCover', message: 'Album cover must be an array' }]
                        });
                    }

                    if (input.albumCover.length > 2) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Too many album cover images', {
                            errors: [{ field: 'albumCover', message: 'Maximum of 2 album cover images allowed' }]
                        });
                    }

                    const imageValidations = await Promise.all(
                        input.albumCover.map(url => validateImageUrl(url))
                    );

                    if (imageValidations.some(isValid => !isValid)) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid image URL', {
                            errors: [{ field: 'albumCover', message: 'All album cover URLs must be valid image files' }]
                        });
                    }
                }

                const referenceValidationErrors = await validateReferences(sanitizedInput, createMediaFolderIdSchema);
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors)
                        ? referenceValidationErrors.map(error => ({
                            field: error.field || 'reference',
                            message: error.message
                        }))
                        : [{
                            field: referenceValidationErrors.field || 'reference',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid media folder information', { errors });
                }

                const mediaFolder = new MediaFolder({ ...sanitizedInput, owner: [context.user._id] });
                await mediaFolder.save();
                await notifyPhotoAddedToAlbum(mediaFolder, party, context.user._id);

                return createResponse('MediaFolderResponse', 'SUCCESS', 'Media folder created successfully', {
                    result: { mediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error creating media folder', {
                    errors: [{ field: 'createMediaFolder', message: error.message }]
                });
            }
        },

        updateMediaFolder: async (_, { id, input }) => {
            try {
                if (input.name) {
                    const validationErrors = validateFolderName(input.name);
                    if (validationErrors.length > 0) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid folder name', {
                            errors: validationErrors
                        });
                    }
                }

                const existingFolder = await MediaFolder.findById(id);
                if (!existingFolder) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Media folder not found', {
                        errors: [{ field: 'id', message: 'Media folder not found' }]
                    });
                }

                const eventId = input.event || existingFolder.event;
                if (eventId) {
                    const event = await Event.findById(eventId);
                    if (!event) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Event not found', {
                            errors: [{ field: 'event', message: 'Event not found' }]
                        });
                    }

                    const collaboratorErrors = await validateEventCollaborators(event, input);
                    if (collaboratorErrors.length > 0) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized', {
                            errors: collaboratorErrors
                        });
                    }
                }

                if (input.albumCover) {
                    if (!Array.isArray(input.albumCover)) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid album cover format', {
                            errors: [{ field: 'albumCover', message: 'Album cover must be an array' }]
                        });
                    }

                    if (input.albumCover.length > 2) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Too many album cover images', {
                            errors: [{ field: 'albumCover', message: 'Maximum of 2 album cover images allowed' }]
                        });
                    }

                    const imageValidations = await Promise.all(
                        input.albumCover.map(url => validateImageUrl(url))
                    );

                    if (imageValidations.some(isValid => !isValid)) {
                        return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid image URL', {
                            errors: [{ field: 'albumCover', message: 'All album cover URLs must be valid image files' }]
                        });
                    }
                }

                const referenceValidationErrors = await validateReferences(input, mediaFolderIdSchema);
                if (referenceValidationErrors) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Invalid media folder information', {
                        errors: referenceValidationErrors
                    });
                }

                const mediaFolder = await MediaFolder.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                await clearCacheById(id);
                return createResponse('MediaFolderResponse', 'SUCCESS', 'Media folder updated successfully', {
                    result: { mediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error updating media folder', {
                    errors: [{ field: 'updateMediaFolder', message: error.message }]
                });
            }
        },

        deleteMediaFolder: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MediaFolder');
                if (references.length > 0) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Media folder cannot be deleted', {
                        errors: [{ field: 'deleteMediaFolder', message: `Media folder cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const mediaFolder = await MediaFolder.findById(id);
                if (!mediaFolder) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Media folder not found', {
                        errors: [{ field: 'id', message: 'Media folder not found' }]
                    });
                }

                if (mediaFolder.media && mediaFolder.media.length > 0) {
                    if (mediaFolder.name.toLowerCase() === "my archives album") {
                        // For archives folder, permanently delete media
                        for (const mediaId of mediaFolder.media) {
                            await deleteMediaAndRelatedData(mediaId);
                        }
                    } else {
                        // For other folders, move media to archives if not already there
                        const archivesFolder = await getArchivesAlbum(mediaFolder.owner);

                        // Add media to archives folder
                        await MediaFolder.findByIdAndUpdate(
                            archivesFolder._id,
                            { $addToSet: { media: { $each: mediaFolder.media } } }
                        );
                    }
                }

                await MediaFolder.findByIdAndDelete(id);
                await clearCacheById(id);

                const successMessage = mediaFolder.name.toLowerCase() === "my archives album"
                    ? 'Archives folder and all media permanently deleted'
                    : 'Media folder deleted and media moved to archives';

                return createResponse('MediaFolderResponse', 'SUCCESS', successMessage, {
                    result: { mediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error deleting media folder', {
                    errors: [{ field: 'deleteMediaFolder', message: error.message }]
                });
            }
        },

        addMediaToMediaFolder: async (_, { mediaFolderId, mediaId }, context) => {
            try {
                if (!await hasMediaFolderLevelAccess(context.user._id, mediaFolderId)) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this media folder' }]
                    });
                }

                const mediaFolder = await MediaFolder.findById(mediaFolderId);
                if (!mediaFolder) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'MediaFolder not found', {
                        errors: [{ field: 'mediaFolderId', message: 'MediaFolder not found' }]
                    });
                }

                const isOwner = mediaFolder.owner.some(ownerId => 
                    ownerId.toString() === context.user._id.toString()
                );
                const isContributor = mediaFolder.contributors?.some(
                    contributorId => contributorId.toString() === context.user._id.toString()
                );

                if (!isOwner && !isContributor) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized', {
                        errors: [{ 
                            field: 'authorization', 
                            message: 'Only the folder owner or contributors can add media to this folder' 
                        }]
                    });
                }

                const updatedMediaFolder = await MediaFolder.findByIdAndUpdate(
                    mediaFolderId,
                    { $addToSet: { media: mediaId } },
                    { new: true }
                );

                if (mediaFolder.event) {
                    const party = await Party.findOne({ eventId: mediaFolder.event });
                    if (party) {
                        await notifyPhotoAddedToAlbum(mediaFolder, party, context.user._id);
                    }
                }

                await clearCacheById(mediaFolderId);
                return createResponse('MediaFolderResponse', 'SUCCESS', 'Media added to MediaFolder successfully', {
                    result: { mediaFolder: updatedMediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error adding Media to MediaFolder', {
                    errors: [{ field: 'addMediaToMediaFolder', message: error.message }]
                });
            }
        },

        removeMediaFromMediaFolder: async (_, { mediaFolderId, mediaId }, context) => {
            try {
                if (!await hasMediaFolderLevelAccess(context.user._id, mediaFolderId)) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this media folder' }]
                    });
                }

                const mediaFolder = await MediaFolder.findById(mediaFolderId);
                const media = await Media.findById(mediaId);

                if (!mediaFolder || !media) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Resource not found', {
                        errors: [{ 
                            field: !mediaFolder ? 'mediaFolderId' : 'mediaId', 
                            message: !mediaFolder ? 'MediaFolder not found' : 'Media not found' 
                        }]
                    });
                }

                const isMediaOwner = media.owner.toString() === context.user._id.toString();
                const isFolderOwner = mediaFolder.owner.toString() === context.user._id.toString();

                if (!isMediaOwner && !isFolderOwner) {
                    return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Unauthorized', {
                        errors: [{ 
                            field: 'authorization', 
                            message: 'Only the media owner or folder owner can remove media from this folder' 
                        }]
                    });
                }

                if (mediaFolder.event) {
                    const party = await Party.findOne({ eventId: mediaFolder.event });
                    if (party) {
                        await notifyPhotoRemovedFromAlbum(mediaFolder, party, context.user._id);
                    }
                }

                if (mediaFolder.name.toLowerCase() === "my archives album") {
                    // Remove from archives and permanently delete
                    await mediaFolder.updateOne({ $pull: { media: mediaId } });
                    await deleteMediaAndRelatedData(mediaId);
                } else {
                    // Move to archives
                    const archivesAlbum = await getArchivesAlbum(context.user._id.toString());
                    
                    await MediaFolder.updateMany(
                        { media: mediaId },
                        { $pull: { media: mediaId } }
                    );
                    const updatedArchivesAlbum = await MediaFolder.findByIdAndUpdate(
                        archivesAlbum._id,
                        { $addToSet: { media: mediaId } },
                        { new: true }
                    );

                    await updatedArchivesAlbum.save();
                }

                await clearCacheById(mediaFolderId);
                const updatedMediaFolder = await MediaFolder.findById(mediaFolderId);

                return createResponse('MediaFolderResponse', 'SUCCESS', 'Media removed and deleted successfully', {
                    result: { mediaFolder: updatedMediaFolder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaFolderErrorResponse', 'FAILURE', 'Error removing and deleting media', {
                    errors: [{ field: 'removeMediaFromMediaFolder', message: error.message }]
                });
            }
        },
    }
};

module.exports = mediaFolderResolvers; 
const mongoose = require('mongoose');
const MdEventType = require('../../../../src/models/MdEventType');
const MdPartyType = require('../../../../src/models/MdPartyType');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const buildMdEventTypeQuery = require('../../../../src/graphql/resolvers/filters/mdEventType.filter');
const mdEventTypeResolvers = require('../../../../src/graphql/resolvers/mdEventType.resolver');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { validateReferences } = require('../../../../src/utils/validation.util');

const mdEventTypeIdSchema = {
    partyTypes: { type: 'array', required: true, model: MdPartyType }
};

jest.mock('../../../../src/models/MdEventType');
jest.mock('../../../../src/models/MdPartyType');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdEventType.filter');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util');

describe('MdEventType Resolvers', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('MdEventType.partyTypes', () => {
    it('should return party types successfully', async () => {
      const mockPartyTypes = [
          { _id: 'type1', name: 'Party Type 1' },
          { _id: 'type2', name: 'Party Type 2' }
      ];

      const mockMdEventType = {
          partyTypes: ['type1', 'type2']
      };

      MdPartyType.find.mockResolvedValue(mockPartyTypes);

      const result = await mdEventTypeResolvers.MdEventType.partyTypes(mockMdEventType);

      expect(MdPartyType.find).toHaveBeenCalledWith({
          _id: { $in: mockMdEventType.partyTypes }
      });
      expect(result).toEqual(mockPartyTypes);
    });

    it('should throw error when party types fetch fails', async () => {
      const mockMdEventType = {
          partyTypes: ['type1', 'type2']
      };

      const error = new Error('Database error');
      MdPartyType.find.mockRejectedValue(error);

      await expect(mdEventTypeResolvers.MdEventType.partyTypes(mockMdEventType))
          .rejects
          .toThrow('Error getting party types');

      expect(MdPartyType.find).toHaveBeenCalledWith({
        _id: { $in: mockMdEventType.partyTypes }
      });
    });
  });

  describe('Query.getMdEventTypeById', () => {
    it('should return MdEventType from cache if available', async () => {
      const id = 'eventType1';
      const cachedEventType = { _id: id, name: 'Cached Event Type' };

      getByIdCache.mockResolvedValue(cachedEventType);
      createResponse.mockReturnValue('success response');

      const result = await mdEventTypeResolvers.Query.getMdEventTypeById(null, { id });

      expect(getByIdCache).toHaveBeenCalledWith(id);
      expect(setCache).toHaveBeenCalledWith(id, cachedEventType);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeResponse',
        'SUCCESS',
        'MdEventType fetched successfully',
        { result: { mdEventType: cachedEventType } }
      );
      expect(result).toBe('success response');
    });

    it('should fetch MdEventType from database if not in cache', async () => {
      const id = 'eventType1';
      const dbEventType = { _id: id, name: 'DB Event Type' };

      getByIdCache.mockResolvedValue(null);
      MdEventType.findById.mockResolvedValue(dbEventType);
      createResponse.mockReturnValue('success response');

      const result = await mdEventTypeResolvers.Query.getMdEventTypeById(null, { id });

      expect(getByIdCache).toHaveBeenCalledWith(id);
      expect(MdEventType.findById).toHaveBeenCalledWith(id);
      expect(setCache).toHaveBeenCalledWith(id, dbEventType);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeResponse',
        'SUCCESS',
        'MdEventType fetched successfully',
        { result: { mdEventType: dbEventType } }
      );
      expect(result).toBe('success response');
    });

    it('should return error response if MdEventType not found', async () => {
      const id = 'nonexistent';

      getByIdCache.mockResolvedValue(null);
      MdEventType.findById.mockResolvedValue(null);
      createResponse.mockReturnValue('error response');

      const result = await mdEventTypeResolvers.Query.getMdEventTypeById(null, { id });

      expect(getByIdCache).toHaveBeenCalledWith(id);
      expect(MdEventType.findById).toHaveBeenCalledWith(id);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeErrorResponse',
        'FAILURE',
        'MdEventType not found',
        { errors: [{ field: 'getMdEventTypeById', message: 'MdEventType not found' }] }
      );
      expect(result).toBe('error response');
    });

    it('should return error response when an error occurs', async () => {
      const id = 'eventTypeId';
      const error = new Error('Database error');
      
      getByIdCache.mockRejectedValue(error);
      createResponse.mockReturnValue({
        status: 'FAILURE',
        message: 'Error getting MdEventType',
        errors: [{ field: 'getMdEventTypeById', message: error.message }]
      });

      const result = await mdEventTypeResolvers.Query.getMdEventTypeById(null, { id });

      expect(getByIdCache).toHaveBeenCalledWith(id);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeErrorResponse',
        'FAILURE',
        'Error getting MdEventType',
        { errors: [{ field: 'getMdEventTypeById', message: error.message }] }
      );
      expect(result).toEqual({
        status: 'FAILURE',
        message: 'Error getting MdEventType',
        errors: [{ field: 'getMdEventTypeById', message: error.message }]
      });
    });
  });

  describe('Query.getMdEventTypes', () => {
    it('should return MdEventTypes with pagination', async () => {
      const filter = { name: 'Test' };
      const pagination = { limit: 10, skip: 0 };
      const query = { name: { $regex: 'Test', $options: 'i' } };
      const paginationInfo = { totalCount: 20, totalPages: 2 };
      const mdEventTypes = [{ _id: '1', name: 'Test Event 1' }, { _id: '2', name: 'Test Event 2' }];

      buildMdEventTypeQuery.mockReturnValue(query);
      getPaginationInfo.mockResolvedValue(paginationInfo);
      MdEventType.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            sort: jest.fn().mockResolvedValue(mdEventTypes)
          })
        })
      });
      createResponse.mockReturnValue('success response');

      const result = await mdEventTypeResolvers.Query.getMdEventTypes(null, { filter, pagination });

      expect(buildMdEventTypeQuery).toHaveBeenCalledWith(filter);
      expect(getPaginationInfo).toHaveBeenCalledWith(MdEventType, query, pagination.limit, pagination.skip);
      expect(MdEventType.find).toHaveBeenCalledWith(query);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypesResponse',
        'SUCCESS',
        'MdEventTypes fetched successfully',
        { result: { mdEventTypes }, pagination: paginationInfo }
      );
      expect(result).toBe('success response');
    });

    it('should return error response when fetching MdEventTypes fails', async () => {
      const filter = { name: 'Test' };
      const pagination = { limit: 10, skip: 0 };
      const error = new Error('Database error');
    
      buildMdEventTypeQuery.mockReturnValue({});
      getPaginationInfo.mockRejectedValue(error);
      createResponse.mockReturnValue('error response');
    
      const result = await mdEventTypeResolvers.Query.getMdEventTypes(null, { filter, pagination });
    
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeErrorResponse',
        'FAILURE',
        'Error getting MdEventTypes',
        { errors: [{ field: 'getMdEventTypes', message: error.message }] }
      );
      expect(result).toBe('error response');
    });

    it('should return failure response if no MdEventTypes found', async () => {
      const filter = { name: 'Nonexistent' };
      const pagination = { limit: 10, skip: 0 };
      const query = { name: { $regex: 'Nonexistent', $options: 'i' } };
      const paginationInfo = { totalCount: 0, totalPages: 0 };

      buildMdEventTypeQuery.mockReturnValue(query);
      getPaginationInfo.mockResolvedValue(paginationInfo);
      MdEventType.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            sort: jest.fn().mockResolvedValue([])
          })
        })
      });
      createResponse.mockReturnValue('failure response');

      const result = await mdEventTypeResolvers.Query.getMdEventTypes(null, { filter, pagination });

      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypesResponse',
        'FAILURE',
        'No MdEventTypes found',
        { result: { mdEventTypes: [] }, pagination: paginationInfo }
      );
      expect(result).toBe('failure response');
    });

    it('should fetch and return MdEventTypes with pagination and sorting', async () => {
        const mockFilter = { name: 'Test' };
        const mockPagination = { limit: 10, skip: 0 };
        const mockQuery = { name: { $regex: /Test/i } };
        const mockMdEventTypes = [
            { _id: '1', name: 'Test1', createdAt: new Date() },
            { _id: '2', name: 'Test2', createdAt: new Date() }
        ];
        const mockPaginationInfo = {
            totalCount: 2,
            hasNextPage: false
        };

        buildMdEventTypeQuery.mockReturnValue(mockQuery);
        getPaginationInfo.mockResolvedValue(mockPaginationInfo);
        MdEventType.find.mockReturnValue({
            limit: jest.fn().mockReturnValue({
                skip: jest.fn().mockReturnValue({
                    sort: jest.fn().mockResolvedValue(mockMdEventTypes)
                })
            })
        });
        createResponse.mockReturnValue('successResponse');

        const result = await mdEventTypeResolvers.Query.getMdEventTypes(null, { filter: mockFilter, pagination: mockPagination });

        expect(buildMdEventTypeQuery).toHaveBeenCalledWith(mockFilter);
        expect(getPaginationInfo).toHaveBeenCalledWith(MdEventType, mockQuery, mockPagination.limit, mockPagination.skip);
        expect(MdEventType.find).toHaveBeenCalledWith(mockQuery);
        expect(MdEventType.find().limit).toHaveBeenCalledWith(mockPagination.limit);
        expect(MdEventType.find().limit().skip).toHaveBeenCalledWith(mockPagination.skip);
        expect(MdEventType.find().limit().skip().sort).toHaveBeenCalledWith({ createdAt: -1 });
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypesResponse',
            'SUCCESS',
            'MdEventTypes fetched successfully',
            { result: { mdEventTypes: mockMdEventTypes }, pagination: mockPaginationInfo }
        );
        expect(result).toBe('successResponse');
    });
  });

  describe('Mutation.createMdEventType', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should create and return the MdEventType successfully', async () => {
        const input = {
            name: 'Birthday Party',
            description: 'A birthday celebration',
            duration: 'ONE_DAY',
            partyTypes: ['partyTypeId1', 'partyTypeId2']
        };
        
        const savedMdEventType = {
            _id: 'eventTypeId',
            ...input
        };
        
        validateReferences.mockResolvedValue(null);
        const mockSave = jest.fn().mockResolvedValue(savedMdEventType);
        
        const mockInstance = {
            ...savedMdEventType
        };
        Object.defineProperty(mockInstance, 'save', {
            value: mockSave,
            enumerable: false 
        });
        
        MdEventType.mockImplementation(() => mockInstance);
        
        createResponse.mockReturnValue({
            status: 'SUCCESS',
            message: 'MdEventType created successfully',
            result: { mdEventType: savedMdEventType }
        });

        const result = await mdEventTypeResolvers.Mutation.createMdEventType(null, { input });

        expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
        expect(MdEventType).toHaveBeenCalledWith(input);
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeResponse',
            'SUCCESS',
            'MdEventType created successfully',
            { result: { mdEventType: savedMdEventType } }
        );
        expect(result).toEqual({
            status: 'SUCCESS',
            message: 'MdEventType created successfully',
            result: { mdEventType: savedMdEventType }
        });
    });

    it('should return validation error response when validation fails', async () => {
        const input = {
            name: 'Birthday Party',
            description: 'A birthday celebration',
            duration: 'ONE_DAY',
            partyTypes: ['invalidPartyTypeId']
        };

        const validationError = {
            message: 'Invalid references found',
            errors: [
                {
                    field: 'partyTypes',
                    message: 'Invalid party type reference'
                }
            ]
        };

        validateReferences.mockResolvedValue(validationError);
        createResponse.mockReturnValue({
            status: 'FAILURE',
            message: validationError.message,
            errors: validationError.errors
        });

        const result = await mdEventTypeResolvers.Mutation.createMdEventType(null, { input });

        expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
        expect(MdEventType).not.toHaveBeenCalled();
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeErrorResponse',
            'FAILURE',
            validationError.message,
            { errors: validationError.errors }
        );
        expect(result).toEqual({
            status: 'FAILURE',
            message: validationError.message,
            errors: validationError.errors
        });
    });

    it('should return error response when creation fails', async () => {
        const input = {
            name: 'Birthday Party',
            description: 'A birthday celebration',
            duration: 'ONE_DAY',
            partyTypes: ['partyTypeId1']
        };

        const error = new Error('Database error');

        validateReferences.mockResolvedValue(null);
        MdEventType.mockImplementation(() => ({
            save: jest.fn().mockRejectedValue(error)
        }));
        createResponse.mockReturnValue({
            status: 'FAILURE',
            message: 'Error creating MdEventType',
            errors: [{ field: 'createMdEventType', message: error.message }]
        });

        const result = await mdEventTypeResolvers.Mutation.createMdEventType(null, { input });

        expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
        expect(MdEventType).toHaveBeenCalledWith(input);
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeErrorResponse',
            'FAILURE',
            'Error creating MdEventType',
            { errors: [{ field: 'createMdEventType', message: error.message }] }
        );
        expect(result).toEqual({
            status: 'FAILURE',
            message: 'Error creating MdEventType',
            errors: [{ field: 'createMdEventType', message: error.message }]
        });
    });
  });

  describe('Mutation.updateMdEventType', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should update an existing MdEventType', async () => {
      const id = 'eventType1';
      const input = { name: 'Updated Event Type' };
      const updatedMdEventType = { _id: id, ...input };

      validateReferences.mockResolvedValue(null);
      MdEventType.findByIdAndUpdate.mockResolvedValue(updatedMdEventType);
      clearCacheById.mockResolvedValue();
      createResponse.mockReturnValue('success response');

      const result = await mdEventTypeResolvers.Mutation.updateMdEventType(null, { id, input });

      expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
      expect(MdEventType.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
      expect(clearCacheById).toHaveBeenCalledWith(id);
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeResponse',
        'SUCCESS',
        'MdEventType updated successfully',
        { result: { mdEventType: updatedMdEventType } }
      );
      expect(result).toBe('success response');
    });

    it('should return error response if MdEventType not found', async () => {
      const id = 'nonexistent';
      const input = { name: 'Updated Event Type' };

      validateReferences.mockResolvedValue(null);
      MdEventType.findByIdAndUpdate.mockResolvedValue(null);
      createResponse.mockReturnValue('error response');

      const result = await mdEventTypeResolvers.Mutation.updateMdEventType(null, { id, input });

      expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
      expect(MdEventType.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeErrorResponse',
        'FAILURE',
        'MdEventType not found',
        { errors: [{ field: 'updateMdEventType', message: 'MdEventType not found' }] }
      );
      expect(result).toBe('error response');
    });

    it('should return error response when updating MdEventType fails', async () => {
      const id = 'eventType1';
      const input = { name: 'Updated Event Type' };
      const error = new Error('Database error');

      validateReferences.mockResolvedValue(null);
      MdEventType.findByIdAndUpdate.mockRejectedValue(error);
      createResponse.mockReturnValue('error response');

      const result = await mdEventTypeResolvers.Mutation.updateMdEventType(null, { id, input });

      expect(validateReferences).toHaveBeenCalledWith(input, mdEventTypeIdSchema, 'MdEventType');
      expect(MdEventType.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
      expect(createResponse).toHaveBeenCalledWith(
        'MdEventTypeErrorResponse',
        'FAILURE',
        'Error updating MdEventType',
        { errors: [{ field: 'updateMdEventType', message: error.message }] }
      );
      expect(result).toBe('error response');
    });
  });

  describe('Mutation.deleteMdEventType', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should check for references before deletion', async () => {
        const id = 'eventTypeId';
        const references = ['MdFunFact'];
        
        findReferences.mockResolvedValue(references);
        createResponse.mockReturnValue('errorResponse');

        const result = await mdEventTypeResolvers.Mutation.deleteMdEventType(null, { id });

        expect(findReferences).toHaveBeenCalledWith(id, 'MdEventType');
        expect(MdEventType.findByIdAndDelete).not.toHaveBeenCalled();
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeErrorResponse',
            'FAILURE',
            'MdEventType cannot be deleted',
            {
                errors: [{ 
                    field: 'id', 
                    message: 'MdEventType cannot be deleted as it is being used in: MdFunFact' 
                }]
            }
        );
        expect(result).toBe('errorResponse');
    });

    it('should delete MdEventType when no references exist', async () => {
        const id = 'eventTypeId';
        const mdEventType = { _id: id, name: 'Test Event Type' };
        
        findReferences.mockResolvedValue([]);
        MdEventType.findByIdAndDelete.mockResolvedValue(mdEventType);
        clearCacheById.mockResolvedValue();
        createResponse.mockReturnValue('successResponse');

        const result = await mdEventTypeResolvers.Mutation.deleteMdEventType(null, { id });

        expect(findReferences).toHaveBeenCalledWith(id, 'MdEventType');
        expect(MdEventType.findByIdAndDelete).toHaveBeenCalledWith(id);
        expect(clearCacheById).toHaveBeenCalledWith(id);
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeResponse',
            'SUCCESS',
            'MdEventType deleted successfully',
            { result: { mdEventType } }
        );
        expect(result).toBe('successResponse');
    });

    it('should return error response if MdEventType not found', async () => {
        const id = 'nonexistentId';
        
        findReferences.mockResolvedValue([]);
        MdEventType.findByIdAndDelete.mockResolvedValue(null);
        createResponse.mockReturnValue('errorResponse');

        const result = await mdEventTypeResolvers.Mutation.deleteMdEventType(null, { id });

        expect(findReferences).toHaveBeenCalledWith(id, 'MdEventType');
        expect(MdEventType.findByIdAndDelete).toHaveBeenCalledWith(id);
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeErrorResponse',
            'FAILURE',
            'MdEventType not found',
            { errors: [{ field: 'deleteMdEventType', message: 'MdEventType not found' }] }
        );
        expect(result).toBe('errorResponse');
    });

    it('should handle errors during deletion', async () => {
        const id = 'eventTypeId';
        const error = new Error('Database error');
        
        findReferences.mockResolvedValue([]);
        MdEventType.findByIdAndDelete.mockRejectedValue(error);
        createResponse.mockReturnValue('errorResponse');

        const result = await mdEventTypeResolvers.Mutation.deleteMdEventType(null, { id });

        expect(findReferences).toHaveBeenCalledWith(id, 'MdEventType');
        expect(MdEventType.findByIdAndDelete).toHaveBeenCalledWith(id);
        expect(createResponse).toHaveBeenCalledWith(
            'MdEventTypeErrorResponse',
            'FAILURE',
            'Error deleting MdEventType',
            { errors: [{ field: 'deleteMdEventType', message: error.message }] }
        );
        expect(result).toBe('errorResponse');
    });
  });
});
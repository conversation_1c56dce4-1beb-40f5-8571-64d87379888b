const mongoose = require('mongoose');
const MdServiceLocation = require('../../../../src/models/MdServiceLocation');
const mdServiceLocationResolvers = require('../../../../src/graphql/resolvers/mdServiceLocation.resolver');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildMdServiceLocationQuery = require('../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');

jest.mock('../../../../src/models/MdServiceLocation');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');
jest.mock('../../../../src/utils/referenceCheck.util');

describe('MdServiceLocation Resolvers', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('Query', () => {
        describe('getMdServiceLocationById', () => {
            it('should return MdServiceLocation if found in cache', async () => {
                const mockMdServiceLocation = { id: '1', name: 'Test Location' };
                getByIdCache.mockResolvedValue(mockMdServiceLocation);
                createResponse.mockReturnValue('success response');

                const result = await mdServiceLocationResolvers.Query.getMdServiceLocationById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(setCache).toHaveBeenCalledWith('1', mockMdServiceLocation);
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationResponse',
                'SUCCESS',
                'MdServiceLocation fetched successfully',
                { result: { mdServiceLocation: mockMdServiceLocation } }
                );
                expect(result).toBe('success response');
            });

            it('should return MdServiceLocation if found in database but not in cache', async () => {
                const mockMdServiceLocation = { id: '1', name: 'Test Location' };
                getByIdCache.mockResolvedValue(null);
                MdServiceLocation.findById.mockResolvedValue(mockMdServiceLocation);
                createResponse.mockReturnValue('success response');

                const result = await mdServiceLocationResolvers.Query.getMdServiceLocationById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(MdServiceLocation.findById).toHaveBeenCalledWith('1');
                expect(setCache).toHaveBeenCalledWith('1', mockMdServiceLocation);
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationResponse',
                'SUCCESS',
                'MdServiceLocation fetched successfully',
                { result: { mdServiceLocation: mockMdServiceLocation } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response if MdServiceLocation not found', async () => {
                getByIdCache.mockResolvedValue(null);
                MdServiceLocation.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Query.getMdServiceLocationById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(MdServiceLocation.findById).toHaveBeenCalledWith('1');
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'MdServiceLocation not found',
                { errors: [{ field: 'getMdServiceLocationById', message: 'MdServiceLocation not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response on exception', async () => {
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Query.getMdServiceLocationById(null, { id: '1' });

                expect(getByIdCache).toHaveBeenCalledWith('1');
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'Error getting MdServiceLocation',
                { errors: [{ field: 'getMdServiceLocationById', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('getMdServiceLocations', () => {
            it('should return MdServiceLocations if found', async () => {
                const mockMdServiceLocations = [{ id: '1', name: 'Location 1' }, { id: '2', name: 'Location 2' }];
                const mockPaginationInfo = { total: 2, limit: 10, skip: 0 };
                buildMdServiceLocationQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
            
                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockMdServiceLocations),
                };
            
                MdServiceLocation.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('success response');
            
                const result = await mdServiceLocationResolvers.Query.getMdServiceLocations(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
            
                expect(buildMdServiceLocationQuery).toHaveBeenCalledWith({});
                expect(getPaginationInfo).toHaveBeenCalledWith(MdServiceLocation, {}, 10, 0);
                expect(MdServiceLocation.find).toHaveBeenCalledWith({});
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationsResponse',
                    'SUCCESS',
                    'MdServiceLocations fetched successfully',
                    { result: { mdServiceLocations: mockMdServiceLocations }, pagination: mockPaginationInfo }
                );
                expect(result).toBe('success response');
            });

            it('should return failure response if no MdServiceLocations found', async () => {
                const mockPaginationInfo = { total: 0, limit: 10, skip: 0 };
                buildMdServiceLocationQuery.mockReturnValue({});
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
            
                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([]),
                };
            
                MdServiceLocation.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('failure response');
            
                const result = await mdServiceLocationResolvers.Query.getMdServiceLocations(null, { filter: {}, pagination: { limit: 10, skip: 0 } });
            
                expect(buildMdServiceLocationQuery).toHaveBeenCalledWith({});
                expect(getPaginationInfo).toHaveBeenCalledWith(MdServiceLocation, {}, 10, 0);
                expect(MdServiceLocation.find).toHaveBeenCalledWith({});
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationsResponse',
                    'FAILURE',
                    'No MdServiceLocations found',
                    { result: { mdServiceLocations: [] }, pagination: mockPaginationInfo }
                );
                expect(result).toBe('failure response');
            });

            it('should return error response on exception', async () => {
                const error = new Error('Database error');
                buildMdServiceLocationQuery.mockImplementation(() => {
                throw error;
                });
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Query.getMdServiceLocations(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

                expect(buildMdServiceLocationQuery).toHaveBeenCalledWith({});
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'Error getting MdServiceLocations',
                { errors: [{ field: 'getMdServiceLocations', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });
    });

    describe('Mutation', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            findReferences.mockReset();
        });

        describe('createMdServiceLocation', () => {
            it('should create a new MdServiceLocation successfully', async () => {
                const mockInput = { name: 'New Location', city: 'New City' };
                const mdServiceLocationMock = {
                    _id: '123',
                    ...mockInput,
                    save: jest.fn().mockResolvedValue(true)
                };
                MdServiceLocation.mockImplementation(() => mdServiceLocationMock);
                const createResponseMock = {
                    status: 'SUCCESS',
                    message: 'MdServiceLocation created successfully',
                    result: { mdServiceLocation: mdServiceLocationMock }
                };
                createResponse.mockReturnValue(createResponseMock);
            
                const result = await mdServiceLocationResolvers.Mutation.createMdServiceLocation(null, { input: mockInput });
            
                expect(MdServiceLocation).toHaveBeenCalledWith(mockInput);
                expect(mdServiceLocationMock.save).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith('MdServiceLocationResponse', 'SUCCESS', 'MdServiceLocation created successfully', { result: { mdServiceLocation: mdServiceLocationMock } });
                expect(result).toBe(createResponseMock);
            });

            it('should return error response on exception', async () => {
                const input = { name: 'New Location', city: 'New City' };
                const error = new Error('Database error');
                MdServiceLocation.mockImplementation(() => ({
                save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Mutation.createMdServiceLocation(null, { input });

                expect(MdServiceLocation).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'Error creating MdServiceLocation',
                { errors: [{ field: 'createMdServiceLocation', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('updateMdServiceLocation', () => {
            it('should update and return the MdServiceLocation', async () => {
                const id = '1';
                const input = { name: 'Updated Location' };
                const mockMdServiceLocation = { id, ...input };
                MdServiceLocation.findByIdAndUpdate.mockResolvedValue(mockMdServiceLocation);
                createResponse.mockReturnValue('success response');

                const result = await mdServiceLocationResolvers.Mutation.updateMdServiceLocation(null, { id, input });

                expect(MdServiceLocation.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationResponse',
                'SUCCESS',
                'MdServiceLocation updated successfully',
                { result: { mdServiceLocation: mockMdServiceLocation } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response if MdServiceLocation not found', async () => {
                const id = '1';
                const input = { name: 'Updated Location' };
                MdServiceLocation.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Mutation.updateMdServiceLocation(null, { id, input });

                expect(MdServiceLocation.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'MdServiceLocation not found',
                { errors: [{ field: 'updateMdServiceLocation', message: 'MdServiceLocation not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response on exception', async () => {
                const id = '1';
                const input = { name: 'Updated Location' };
                const error = new Error('Database error');
                MdServiceLocation.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('error response');

                const result = await mdServiceLocationResolvers.Mutation.updateMdServiceLocation(null, { id, input });

                expect(MdServiceLocation.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(createResponse).toHaveBeenCalledWith(
                'MdServiceLocationErrorResponse',
                'FAILURE',
                'Error updating MdServiceLocation',
                { errors: [{ field: 'updateMdServiceLocation', message: error.message }] }
                );
                expect(result).toBe('error response');
            });
        });

        describe('deleteMdServiceLocation', () => {
            it('should delete MdServiceLocation successfully when no references exist', async () => {
                const id = 'testId';
                const mockMdServiceLocation = { _id: id, name: 'Test Location' };

                findReferences.mockResolvedValue([]);
                MdServiceLocation.findByIdAndDelete.mockResolvedValue(mockMdServiceLocation);
                createResponse.mockReturnValue('successResponse');

                const result = await mdServiceLocationResolvers.Mutation.deleteMdServiceLocation(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdServiceLocation');
                expect(MdServiceLocation.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationResponse',
                    'SUCCESS',
                    'MdServiceLocation deleted successfully',
                    { result: { mdServiceLocation: mockMdServiceLocation } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when MdServiceLocation has references', async () => {
                const id = 'testId';
                const references = ['Party', 'Vendor'];

                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdServiceLocationResolvers.Mutation.deleteMdServiceLocation(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdServiceLocation');
                expect(MdServiceLocation.findByIdAndDelete).not.toHaveBeenCalled();
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationErrorResponse',
                    'FAILURE',
                    'MdServiceLocation cannot be deleted',
                    { errors: [{ field: 'id', message: `MdServiceLocation cannot be deleted as it is being used in: ${references.join(', ')}` }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when MdServiceLocation is not found', async () => {
                const id = 'testId';

                findReferences.mockResolvedValue([]);
                MdServiceLocation.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdServiceLocationResolvers.Mutation.deleteMdServiceLocation(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdServiceLocation');
                expect(MdServiceLocation.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationErrorResponse',
                    'FAILURE',
                    'MdServiceLocation not found',
                    { errors: [{ field: 'deleteMdServiceLocation', message: 'MdServiceLocation not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when an error occurs during deletion', async () => {
                const id = 'testId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);
                MdServiceLocation.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdServiceLocationResolvers.Mutation.deleteMdServiceLocation(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdServiceLocation');
                expect(MdServiceLocation.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdServiceLocationErrorResponse',
                    'FAILURE',
                    'Error deleting MdServiceLocation',
                    { errors: [{ field: 'deleteMdServiceLocation', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
});

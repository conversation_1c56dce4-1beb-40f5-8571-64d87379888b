const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { createResponse } = require('../../../../src/utils/response.util');
const buildMdFeatureQuery = require('../../../../src/graphql/resolvers/filters/mdFeature.filter');
const mdFeatureResolvers = require('../../../../src/graphql/resolvers/mdFeature.resolver');
const MdIcon = require('../../../../src/models/MdIcon');
const MdFeature = require('../../../../src/models/MdFeature');
const findReferences = require('../../../../src/graphql/resolvers/references/mdFeature.references');

jest.mock('../../../../src/models/MdFeature');
jest.mock('../../../../src/models/MdIcon');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdFeature.filter');
jest.mock('../../../../src/graphql/resolvers/references/mdFeature.references');

describe('mdFeatureResolvers', () => {
    describe('MdFeature', () => {
        it('should return the icon for a given MdFeature', async () => {
            const parent = { icon: 'iconId' };
            const icon = { _id: 'iconId', name: 'Icon Name' };

            MdIcon.findById.mockResolvedValue(icon);

            const result = await mdFeatureResolvers.MdFeature.icon(parent);

            expect(MdIcon.findById).toHaveBeenCalledWith('iconId');
            expect(result).toEqual(icon);
        });

        it('should throw an error if icon retrieval fails', async () => {
            const parent = { icon: 'iconId' };
            const error = new Error('Error getting icon');

            MdIcon.findById.mockRejectedValue(error);

            await expect(mdFeatureResolvers.MdFeature.icon(parent)).rejects.toThrow('Error getting icon');
        });
    });

    describe('Query', () => {
        describe('getMdFeatureById', () => {
            it('should return the MdFeature if found in cache', async () => {
                const id = 'featureId';
                const cachedMdFeature = { _id: id, name: 'Cached Feature Name' };

                getByIdCache.mockResolvedValue(cachedMdFeature);
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdFeature fetched successfully', result: { mdFeature: cachedMdFeature } });

                const result = await mdFeatureResolvers.Query.getMdFeatureById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith('MdFeatureResponse', 'SUCCESS', 'MdFeature fetched successfully', { result: { mdFeature: cachedMdFeature } });
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdFeature fetched successfully', result: { mdFeature: cachedMdFeature } });
            });

            it('should return the MdFeature if found in database', async () => {
                const id = 'featureId';
                const mdFeature = { _id: id, name: 'Feature Name' };

                getByIdCache.mockResolvedValue(null);
                MdFeature.findById.mockResolvedValue(mdFeature);
                setCache.mockResolvedValue();
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdFeature fetched successfully', result: { mdFeature } });

                const result = await mdFeatureResolvers.Query.getMdFeatureById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdFeature.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, mdFeature);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdFeature fetched successfully', result: { mdFeature } });
            });

            it('should return an error response if MdFeature is not found', async () => {
                const id = 'featureId';

                getByIdCache.mockResolvedValue(null);
                MdFeature.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'getMdFeatureById', message: 'MdFeature not found' }] });

                const result = await mdFeatureResolvers.Query.getMdFeatureById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdFeature.findById).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'getMdFeatureById', message: 'MdFeature not found' }] });
            });

            it('should return an error response if an error occurs', async () => {
                const id = 'featureId';
                const error = new Error('Error getting mdFeature');

                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting mdFeature', errors: [{ field: 'getMdFeatureById', message: error.message }] });

                const result = await mdFeatureResolvers.Query.getMdFeatureById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error getting mdFeature', errors: [{ field: 'getMdFeatureById', message: error.message }] });
            });
        });

        describe('getMdFeatures', () => {
            it('should return the MdFeatures if found', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'test', $options: 'i' } };
                const mdFeatures = [{ _id: 'featureId', name: 'Feature Name' }];
                const paginationInfo = { total: 1, limit: 10, skip: 0 };

                buildMdFeatureQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdFeature.find.mockResolvedValue(mdFeatures);
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdFeatures fetched successfully', result: { mdFeatures }, pagination: paginationInfo });

                const result = await mdFeatureResolvers.Query.getMdFeatures(null, { filter, pagination });

                expect(buildMdFeatureQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdFeature, query, 10, 0);
                expect(MdFeature.find).toHaveBeenCalledWith(query);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdFeatures fetched successfully', result: { mdFeatures }, pagination: paginationInfo });
            });

            it('should return an error response if no MdFeatures are found', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'test', $options: 'i' } };
                const mdFeatures = [];
                const paginationInfo = { total: 0, limit: 10, skip: 0 };

                buildMdFeatureQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdFeature.find.mockResolvedValue(mdFeatures);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'No mdFeatures found', result: { mdFeatures }, pagination: paginationInfo });

                const result = await mdFeatureResolvers.Query.getMdFeatures(null, { filter, pagination });

                expect(buildMdFeatureQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdFeature, query, 10, 0);
                expect(MdFeature.find).toHaveBeenCalledWith(query);
                expect(result).toEqual({ status: 'FAILURE', message: 'No mdFeatures found', result: { mdFeatures }, pagination: paginationInfo });
            });

            it('should return an error response if an error occurs', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'test', $options: 'i' } };
                const error = new Error('Error getting mdFeatures');

                buildMdFeatureQuery.mockReturnValue(query);
                getPaginationInfo.mockRejectedValue(error);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error getting mdFeatures', errors: [{ field: 'getMdFeatures', message: error.message }] });

                const result = await mdFeatureResolvers.Query.getMdFeatures(null, { filter, pagination });

                expect(buildMdFeatureQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdFeature, query, 10, 0);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error getting mdFeatures', errors: [{ field: 'getMdFeatures', message: error.message }] });
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdFeature', () => {
            it('should create and return the MdFeature', async () => {
                const input = { name: 'Feature Name', icon: 'iconId' };
                const mdFeature = { _id: 'featureId', ...input };

                MdFeature.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(mdFeature),
                }));
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdFeature created successfully', result: { mdFeature } });

                const result = await mdFeatureResolvers.Mutation.createMdFeature(null, { input });

                expect(MdFeature).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdFeature created successfully', result: { mdFeature } });
            });

            it('should return an error response if creation fails', async () => {
                const input = { name: 'Feature Name', icon: 'iconId' };
                const error = new Error('Error creating mdFeature');

                MdFeature.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error),
                }));
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error creating mdFeature', errors: [{ field: 'createMdFeature', message: error.message }] });

                const result = await mdFeatureResolvers.Mutation.createMdFeature(null, { input });

                expect(MdFeature).toHaveBeenCalledWith(input);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error creating mdFeature', errors: [{ field: 'createMdFeature', message: error.message }] });
            });
        });

        describe('updateMdFeature', () => {
            it('should update and return the MdFeature', async () => {
                const id = 'featureId';
                const input = { name: 'Updated Feature Name' };
                const mdFeature = { _id: id, ...input };

                MdFeature.findByIdAndUpdate.mockResolvedValue(mdFeature);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({ status: 'SUCCESS', message: 'MdFeature updated successfully', result: { mdFeature } });

                const result = await mdFeatureResolvers.Mutation.updateMdFeature(null, { id, input });

                expect(MdFeature.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'SUCCESS', message: 'MdFeature updated successfully', result: { mdFeature } });
            });

            it('should return an error response if MdFeature is not found', async () => {
                const id = 'featureId';
                const input = { name: 'Updated Feature Name' };

                MdFeature.findByIdAndUpdate.mockResolvedValue(null);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'updateMdFeature', message: 'MdFeature not found' }] });

                const result = await mdFeatureResolvers.Mutation.updateMdFeature(null, { id, input });

                expect(MdFeature.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(result).toEqual({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'updateMdFeature', message: 'MdFeature not found' }] });
            });

            it('should return an error response if an error occurs', async () => {
                const id = 'featureId';
                const input = { name: 'Updated Feature Name' };
                const error = new Error('Error updating mdFeature');

                MdFeature.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error updating mdFeature', errors: [{ field: 'updateMdFeature', message: error.message }] });

                const result = await mdFeatureResolvers.Mutation.updateMdFeature(null, { id, input });

                expect(MdFeature.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(result).toEqual({ status: 'FAILURE', message: 'Error updating mdFeature', errors: [{ field: 'updateMdFeature', message: error.message }] });
            });
        });

        describe('deleteMdFeature', () => {
            it('should check for references before deletion', async () => {
                const id = 'featureId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'MdFeature cannot be deleted',
                    errors: [{
                        field: 'deleteMdFeature',
                        message: `MdFeature cannot be deleted as it has references in the following collections: ${references.join(', ')}`
                    }]
                });

                const result = await mdFeatureResolvers.Mutation.deleteMdFeature(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(MdFeature.findByIdAndDelete).not.toHaveBeenCalled();
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'MdFeature cannot be deleted',
                    errors: [{
                        field: 'deleteMdFeature',
                        message: 'MdFeature cannot be deleted as it has references in the following collections: Collection1, Collection2'
                    }]
                });
            });

            it('should delete and return the MdFeature when no references exist', async () => {
                const id = 'featureId';
                const mdFeature = { _id: id, name: 'Feature Name' };

                findReferences.mockResolvedValue([]);
                MdFeature.findByIdAndDelete.mockResolvedValue(mdFeature);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'MdFeature deleted successfully',
                    result: { mdFeature }
                });

                const result = await mdFeatureResolvers.Mutation.deleteMdFeature(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id);
                expect(MdFeature.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'MdFeature deleted successfully',
                    result: { mdFeature }
                });
            });

            it('should return an error response if MdFeature is not found', async () => {
                const id = 'featureId';

                MdFeature.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'deleteMdFeature', message: 'MdFeature not found' }] });

                const result = await mdFeatureResolvers.Mutation.deleteMdFeature(null, { id });

                expect(MdFeature.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'FAILURE', message: 'MdFeature not found', errors: [{ field: 'deleteMdFeature', message: 'MdFeature not found' }] });
            });

            it('should return an error response if an error occurs', async () => {
                const id = 'featureId';
                const error = new Error('Error deleting mdFeature');

                MdFeature.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({ status: 'FAILURE', message: 'Error deleting mdFeature', errors: [{ field: 'deleteMdFeature', message: error.message }] });

                const result = await mdFeatureResolvers.Mutation.deleteMdFeature(null, { id });

                expect(MdFeature.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(result).toEqual({ status: 'FAILURE', message: 'Error deleting mdFeature', errors: [{ field: 'deleteMdFeature', message: error.message }] });
            });
        });
    });
});

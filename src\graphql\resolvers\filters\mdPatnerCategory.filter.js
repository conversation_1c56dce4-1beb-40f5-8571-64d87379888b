const { isValidObjectId } = require("../../../utils/validation.util");

const buildMdPartnerCategoryQuery = (filters) => {
    const query = {};

    if (filters) {
        if (filters.id) {
            if (isValidObjectId(filters.id)) {
                query._id = filters.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filters.name) {
            query.name = { $regex: filters.name, $options: 'i' };
        }
    }

    return query;
};

module.exports = buildMdPartnerCategoryQuery;
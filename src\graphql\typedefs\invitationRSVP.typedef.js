const { gql } = require('apollo-server-express');
const { sharedTypeDef } = require('./shared.typedef');

const invitationRSVPTypeDef = gql`

    enum InvitationRSVPStatus {
        PENDING
        ACCEPTED
        REJECTED
        MAYBE
    }

    type InvitationRSVP {
        id: ID!
        invitation: Invitation!
        guest: Guest!
        status: InvitationRSVPStatus!
        message: String
    }

    type InvitationRSVPWrapper {
        invitationRSVP: InvitationRSVP!
    }

    type InvitationRSVPResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: InvitationRSVPWrapper!
    }

    type InvitationRSVPErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }
    
    union InvitationRSVPResult = InvitationRSVPResponse | InvitationRSVPErrorResponse

    type Query {
        getInvitationRSVPById(id: ID!): InvitationRSVPResult!
    }

    input InvitationRSVPInput {
        invitation: ID
        guest: ID
        status: InvitationRSVPStatus
        message: String
        additionalGuestsCount: Int
    }
    
    type Mutation {
        updateInvitationRSVP(id: ID!, input: InvitationRSVPInput!): InvitationRSVPResult!
    }
    `

module.exports = invitationRSVPTypeDef;

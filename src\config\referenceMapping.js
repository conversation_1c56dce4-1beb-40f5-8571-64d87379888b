const Party = require('../models/Party');
const PartyService = require('../models/PartyService');
const Transaction = require('../models/Transaction');
const Task = require('../models/Task');
const MdEventType = require('../models/MdEventType');
const MdFunFact = require('../models/MdFunFact');
const Vendor = require('../models/Vendor');
const MdFeature = require('../models/MdFeature');
const MdPartner = require('../models/MdPartner');
const MdPartnerCategory = require('../models/MdPartnerCategory');
const MdAppSettings = require('../models/MdAppSettings');
const MdVendorType = require('../models/MdVendorType');
const VendorService = require('../models/VendorService');
const Event = require('../models/Event');
const MdPartyType = require('../models/MdPartyType');
const { User } = require('../models/User');
const Tag = require('../models/Tag');
const Media = require('../models/Media');
const MdPolicy = require('../models/MdPolicy');
const MdPromotion = require('../models/MdPromotion');
const Reaction = require('../models/Reaction');
const Invitation = require('../models/Invitation');
const MdInterestCategory = require('../models/MdInterestCategory');
const EventGroup = require('../models/EventGroup');
const InAppNotification = require('../models/InAppNotification');

const referenceMapping = {
    MdPartyType: [
        { model: Party,field: 'partyType'}
    ],

    Address: [
        { model: Party, field: 'address'},
        { model: PartyService, field: 'address' },
        { model: Vendor, field: 'businessAddress' },
        { model: Event, field: 'location' }
    ],

    Host: [
        { model: Party, field: 'coHosts', isArray: true },
        { model: Event, field: 'mainHost' },
        { model: Event, field: 'coHosts', isArray: true }
    ],

    Guest: [
        { model: Event, field: 'guests', isArray: true }
    ],

    PartyService: [
        { model: Party, field: 'services', isArray: true },
        { model: Transaction, field: 'partyService' },
        { model: Task, field: 'partyService' }
    ],

    Transaction: [
        { model: PartyService, field: 'transactions', isArray: true }
    ],

    Task: [
        { model: PartyService, field: 'tasks', isArray: true }
    ],

    Vendor: [
        { model: PartyService, field: 'vendor' }
    ],

    VendorService: [],

    MdVendorType: [
        { model: Party, field: 'vendorTypes', isArray: true },
        { model: VendorService, field: 'vendorTypes', isArray: true },
        { model: MdPartyType, field: 'vendorTypes', isArray: true },
        { model: PartyService, field: 'vendorType' }
    ],

    MdEventType: [
        { model: MdFunFact, field: 'eventTypes', isArray: true },
        { model: Event, field: 'eventType' }
    ],

    MdIcon: [
        { model: MdFeature, field: 'icon' },
        { model: MdPartner, field: 'icon' },
        { model: MdPartnerCategory, field: 'icon' },
        { model: MdVendorType, field: 'icon' },
        { model: MdAppSettings, field: 'navBars.userDashboard.tabs.home' },
        { model: MdAppSettings, field: 'navBars.userDashboard.tabs.myEvents' },
        { model: MdAppSettings, field: 'navBars.userDashboard.tabs.publicEvents' },
        { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.eventsDb' },
        { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.tasks' },
        { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.guests' },
        { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.apps' },
        { model: MdAppSettings, field: 'navBars.hostEventDashboard.tabs.messages' },
        { model: Tag, field: 'icon' }
    ],

    MdFunFact: [
        { model: MdEventType, field: 'eventTypes', isArray: true }
    ],

    MdTheme: [],

    MdRole: [
        { model: User, field: 'roles', isArray: true }
    ],

    MdServiceLocation: [
        { model: Vendor, field: 'serviceLocations', isArray: true },
        { model: Party, field: 'serviceLocation' },
        { model: Event, field: 'location' }
    ],

    Party: [
        { model: Event, field: 'parties', isArray: true }
    ],

    MdVendorSubType: [],

    Event: [],

    MdChecklist: [],

    MdPolicy: [],

    Policy: [],

    Tag: [
        { model: MdPolicy, field: 'tags', isArray: true },
        { model: Media, field: 'tags', isArray: true },
        { model: MdPromotion, field: 'tags', isArray: true },
        { model: MdPartner, field: 'category', isArray: true }
    ],

    MdPromotion: [],

    MdPartner: [
        { model: MdPromotion, field: 'partner' },
        { model: MdVendorType, field: 'partners', isArray: true }
    ],

    Reaction: [],

    Comment: [
        { model: Task, field: 'comments', isArray: true }
    ],

    Document: [
        { model: Task, field: 'attachments', isArray: true }
    ],

    Media: [
        { model: VendorService, field: 'media' },
    ],

    TaskCollaborator: [],

    VenueAddress: [
        { model: Party, field: 'venueAddress' }
    ],

    MediaFolder: [],

    Invitation: [
        { model: Media, field: 'media', isArray: true }
    ],

    MdInterestCategory: [
        { model: EventGroup, field: 'interestCategories', isArray: true }
    ],

    MdInterest: [
        { model: MdInterestCategory, field: 'interests', isArray: true },
        { model: EventGroup, field: 'interests', isArray: true }
    ],

    EventGroup: [
        { model: Event, field: 'eventGroups', isArray: true }
    ],

    InAppNotification: []
};

module.exports = referenceMapping; 
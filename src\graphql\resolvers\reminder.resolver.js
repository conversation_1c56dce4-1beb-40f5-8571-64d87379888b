const Reminder = require('../../models/Reminder');
const Party = require('../../models/Party');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const buildReminderQuery = require('./filters/reminder.filter');

const reminderResolvers = {
    Reminder: {
        party: async (parent) => {
            try {
                const party = await Party.findById(parent.party);
                if (!party) {
                    throw new Error('Party not found');
                }
                return party;
            } catch (error) {
                console.error('Error in Reminder.party resolver:', error);
                throw new Error(error.message);
            }
        }
    },

    Query: {
        getReminderById: async (_, { id }, context) => {
            try {
                const reminder = await getByIdCache(id);
                if (!reminder) {
                    reminder = await <PERSON>minder.findById(id);
                    await setCache(id, reminder);
                }
                return createResponse('ReminderResponse', 'SUCCESS', 'Reminder fetched successfully', {
                    result: { reminder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReminderErrorResponse', 'FAILURE', 'Error retrieving reminder', {
                    errors: [{ field: 'getReminderById', message: error.message }]
                });
            }
        },

        getReminders: async (_, { filter, pagination }, context) => {
            try {
                const query = await buildReminderQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
                
                const reminders = await Reminder.find(query).limit(limit).skip(skip).populate('party').sort({ createdAt: -1 });

                const paginationInfo = await getPaginationInfo(Reminder, query, limit, skip);

                return createResponse('RemindersResponse', 'SUCCESS', 'Reminders fetched successfully', {
                    result: { reminders },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReminderErrorResponse', 'FAILURE', 'Error retrieving reminders', {
                    errors: [{ field: 'getReminders', message: error.message }]
                });
            }
        },
    },

    Mutation: {
        createReminder: async (_, { input }, context) => {
            try {
                const party = await Party.findById(input.party)
                if (!party) {
                    return createResponse('ReminderErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'party', message: 'Party not found' }]
                    });
                }
                
                const reminder = new Reminder(input);
                await reminder.save();
                await setCache(reminder._id, reminder);
                return createResponse('ReminderResponse', 'SUCCESS', 'Reminder created successfully', {
                    result: { reminder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReminderErrorResponse', 'FAILURE', 'Error creating reminder', {
                    errors: [{ field: 'createReminder', message: error.message }]
                });
            }
        },

        updateReminder: async (_, { id, reminder: input }, context) => {
            try {
                const existingReminder = await Reminder.findById(id);
                if (!existingReminder) {
                    return createResponse('ReminderErrorResponse', 'FAILURE', 'Reminder not found', {
                        errors: [{ field: 'updateReminder', message: 'Reminder not found' }]
                    });
                }

                const reminder = await Reminder.findByIdAndUpdate(id, input, { new: true });
                await setCache(id, reminder);
                return createResponse('ReminderResponse', 'SUCCESS', 'Reminder updated successfully', {
                    result: { reminder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReminderErrorResponse', 'FAILURE', 'Error updating reminder', {
                    errors: [{ field: 'updateReminder', message: error.message }]
                });
            }
        },

        deleteReminder: async (_, { id }, context) => {
            try {
                const reminder = await Reminder.findByIdAndDelete(id);
                if (!reminder) {
                    return createResponse('ReminderErrorResponse', 'FAILURE', 'Reminder not found', {
                        errors: [{ field: 'deleteReminder', message: 'Reminder not found' }]
                    });
                }
                await clearCacheById(id);
                return createResponse('ReminderResponse', 'SUCCESS', 'Reminder deleted successfully', {
                    result: { reminder }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReminderErrorResponse', 'FAILURE', 'Error deleting reminder', {
                    errors: [{ field: 'deleteReminder', message: error.message }]
                });
            }
        }
    }
}

module.exports = reminderResolvers;

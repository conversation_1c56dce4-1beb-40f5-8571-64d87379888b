const referenceMapping = require('../config/referenceMapping');

const hasReferences = (references) => Array.isArray(references) && references.length > 0;

async function findReferences(id, entityType) {
    if (!referenceMapping[entityType]) {
        throw new Error(`No reference mapping found for entity type: ${entityType}`);
    }

    const references = [];
    const mappings = referenceMapping[entityType];

    for (const mapping of mappings) {
        const { model, field, isArray } = mapping;
        let query = {};

        if (isArray) {
            query[field] = id;
        } else {
            query[field] = id;
        }

        try {
            const results = await model.find(query);
            if (hasReferences(results)) {
                references.push(model.collection.collectionName);
            }
        } catch (error) {
            console.error(`Error checking references in ${model.collection.collectionName}:`, error);
            throw error;
        }
    }

    return references;
}

module.exports = { findReferences }; 
const buildMdVendorTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdVendorType.filter');

describe('buildMdVendorTypeQuery', () => {
    it('should return an empty object when no filters are provided', () => {
        const result = buildMdVendorTypeQuery();
        expect(result).toEqual({});
    });

    it('should return an empty object when an empty filter object is provided', () => {
        const result = buildMdVendorTypeQuery({});
        expect(result).toEqual({});
    });

    it('should build a query with name filter', () => {
        const filters = { name: 'Test Vendor' };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            name: { $regex: 'Test Vendor', $options: 'i' }
        });
    });

    it('should build a query with speciality filter', () => {
        const filters = { speciality: 'Test Speciality' };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            speciality: { $regex: 'Test Speciality', $options: 'i' }
        });
    });

    it('should build a query with both name and speciality filters', () => {
        const filters = { name: 'Test Vendor', speciality: 'Test Speciality' };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            name: { $regex: 'Test Vendor', $options: 'i' },
            speciality: { $regex: 'Test Speciality', $options: 'i' }
        });
    });

    it('should ignore unknown filters', () => {
        const filters = { name: 'Test Vendor', unknownFilter: 'Unknown' };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            name: { $regex: 'Test Vendor', $options: 'i' }
        });
    });

    it('should handle empty string filters', () => {
        const filters = { name: '', speciality: '' };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({});
    });

    it('should handle non-string filter values', () => {
        const filters = { name: 123, speciality: true };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            name: { $regex: '123', $options: 'i' },
            speciality: { $regex: 'true', $options: 'i' }
        });
    });

    it('should build a query with valid id', () => {
        const validId = '507f1f77bcf86cd799439011';
        const filters = { id: validId };
        const result = buildMdVendorTypeQuery(filters);
        expect(result).toEqual({
            _id: validId
        });
    });

    it('should throw an error for invalid id', () => {
        const invalidId = '12345';
        const filters = { id: invalidId };
        expect(() => buildMdVendorTypeQuery(filters)).toThrow('Invalid id provided');
    });
});
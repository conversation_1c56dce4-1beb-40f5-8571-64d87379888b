const AWS = require('aws-sdk');

const eventBridge = new AWS.EventBridge({region: process.env.AWS_REGION});

async function scheduleParty(party) {
    try {
        const goLiveTime = new Date(party.time - 3 * 60 * 60 * 1000);
        const ruleName = `goLive-${party._id}`;

        // Format the date for cron expression
        const minute = goLiveTime.getUTCMinutes();
        const hour = goLiveTime.getUTCHours();
        const day = goLiveTime.getUTCDate();
        const month = goLiveTime.getUTCMonth() + 1; // getMonth() returns 0-11
        const year = goLiveTime.getUTCFullYear();

        // Create cron expression: "minute hour day month ? year"
        const cronExpression = `${minute} ${hour} ${day} ${month} ? ${year}`;

        await eventBridge.putRule({
            Name: ruleName,
            ScheduleExpression: `cron(${cronExpression})`,
            State: 'ENABLED',
            Description: `Go live trigger for party ${party._id}`
        }).promise();

        await eventBridge.putTargets({
            Rule: ruleName,
            Targets: [{
                Id: "1",
                Arn: process.env.GO_LIVE_FUNCTION_ARN,
                Input: JSON.stringify({ partyId: party._id.toString() })
            }]
        }).promise();

        console.log(`Scheduled party ${party._id} to go live at ${goLiveTime.toISOString()}`);
    } catch (error) {
        console.error(`Error scheduling party ${party._id}:`, error);
        // Don't throw to prevent breaking the party creation/update flow
    }
}

async function deleteScheduledParty(party) {
    const ruleName = `goLive-${party._id}`;

    try {
        // First remove the targets
        await eventBridge.removeTargets({
            Rule: ruleName,
            Ids: ["1"]
        }).promise();

        // Then delete the rule
        await eventBridge.deleteRule({
            Name: ruleName,
            Force: true
        }).promise();

        console.log(`Deleted scheduled party ${party._id}`);
    } catch (error) {
        console.error(`Error deleting scheduled party ${party._id}:`, error);
        // Don't throw to prevent breaking the party deletion flow
    }
}

module.exports = { 
    scheduleParty, 
    deleteScheduledParty 
};
const mongoose = require('mongoose');
const buildVendorRatingQuery = require('../../../../../src/graphql/resolvers/filters/vendorRating.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('mongoose', () => {
    const mockObjectId = function(id) {
        return id.toString();
    };
    mockObjectId.isValid = jest.fn();
    return {
        Types: {
            ObjectId: mockObjectId
        }
    };
});

jest.mock('../../../../../src/utils/validation.util');

describe('buildVendorRatingQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return basic pipeline when no filters are provided', () => {
        const result = buildVendorRatingQuery();
        expect(result).toEqual([
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ]);
    });

    it('should build pipeline with user name filters', () => {
        const result = buildVendorRatingQuery({
            userFirstName: 'John',
            userLastName: 'Doe'
        });

        expect(result).toEqual([
            {
                $lookup: {
                    from: 'users',
                    localField: 'user',
                    foreignField: '_id',
                    as: 'userDetails'
                }
            },
            { $unwind: '$userDetails' },
            {
                $match: {
                    'userDetails.firstName': { $regex: /John/i },
                    'userDetails.lastName': { $regex: /Doe/i }
                }
            },
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ]);
    });

    it('should build pipeline with vendor name filter', () => {
        const result = buildVendorRatingQuery({
            vendorName: 'Test Vendor'
        });

        expect(result).toEqual([
            {
                $lookup: {
                    from: 'vendors',
                    localField: 'vendor',
                    foreignField: '_id',
                    as: 'vendorDetails'
                }
            },
            { $unwind: '$vendorDetails' },
            {
                $match: {
                    'vendorDetails.name': { $regex: /Test Vendor/i }
                }
            },
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ]);
    });

    it('should build pipeline with valid vendor ID', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);

        const result = buildVendorRatingQuery({
            vendorId: validId
        });

        expect(result).toEqual([
            {
                $match: {
                    vendor: validId
                }
            },
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ]);
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });

    it('should throw error for invalid vendor ID', () => {
        const invalidId = 'invalid-id';
        isValidObjectId.mockReturnValue(false);

        expect(() => {
            buildVendorRatingQuery({
                vendorId: invalidId
            });
        }).toThrow('Invalid vendor ID provided');
        expect(isValidObjectId).toHaveBeenCalledWith(invalidId);
    });

    it('should build pipeline with rating range filters', () => {
        const result = buildVendorRatingQuery({
            minRating: 3,
            maxRating: 5
        });

        expect(result).toEqual([
            {
                $match: {
                    rating: {
                        $gte: 3,
                        $lte: 5
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ]);
    });

    it('should build pipeline with all filters combined', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);

        const result = buildVendorRatingQuery({
            userFirstName: 'John',
            userLastName: 'Doe',
            vendorName: 'Test Vendor',
            vendorId: validId,
            minRating: 3,
            maxRating: 5
        });

        const expectedPipeline = [
            {
                $lookup: {
                    from: 'users',
                    localField: 'user',
                    foreignField: '_id',
                    as: 'userDetails'
                }
            },
            { $unwind: '$userDetails' },
            {
                $lookup: {
                    from: 'vendors',
                    localField: 'vendor',
                    foreignField: '_id',
                    as: 'vendorDetails'
                }
            },
            { $unwind: '$vendorDetails' },
            {
                $match: {
                    'userDetails.firstName': { $regex: /John/i },
                    'userDetails.lastName': { $regex: /Doe/i },
                    'vendorDetails.name': { $regex: /Test Vendor/i },
                    vendor: validId,
                    rating: {
                        $gte: 3,
                        $lte: 5
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    user: 1,
                    vendor: 1,
                    rating: 1,
                    comment: 1,
                    userDetails: 1,
                    vendorDetails: 1
                }
            }
        ];

        expect(result).toEqual(expectedPipeline);
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });
});

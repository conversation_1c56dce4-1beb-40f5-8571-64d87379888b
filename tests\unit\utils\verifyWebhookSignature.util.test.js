const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
const { Webhook } = require('svix');

jest.mock('svix');

describe('verifyWebhookSignature', () => {
    let req;

    beforeEach(() => {
        req = {
            headers: {
                'svix-id': 'test-id',
                'svix-timestamp': 'test-timestamp',
                'svix-signature': 'test-signature',
            },
            body: { data: 'test' },
        };
        process.env.WEBHOOK_SECRET = 'test-secret';
    });

    afterEach(() => {
        jest.clearAllMocks();

    });

    it('should verify the webhook signature sucessfully', () => {
        const mockVerify = jest.fn().mockReturnValue('verified-event');
        Webhook.mockImplementation(() => ({
            verify: mockVerify,
        }));

        const result = verifyWebhookSignature(req);
        expect(result).toBe('verified-event');
        expect(mockVerify).toHaveBeenCalledWith(JSON.stringify(req.body), {
            'svix-id': req.headers['svix-id'],
            'svix-timestamp': req.headers['svix-timestamp'],
            'svix-signature': req.headers['svix-signature'],
        });
    });

    it('should throw an error if WEBHOOK_SECRET is not set', () => {
        delete process.env.WEBHOOK_SECRET;
        expect(() => verifyWebhookSignature(req)).toThrow('You need a WEBHOOK_SECRET in your .env');
    });

    it('should throw an error if svix headers are missing', () => {
        req.headers = {};
        expect(() => verifyWebhookSignature(req)).toThrow('Error occurred -- no svix headers');
    });

    it('should throw an error if verification fails', () => {
        const mockVerify = jest.fn().mockImplementation(() => {
          throw new Error('Verification failed');
        });
        Webhook.mockImplementation(() => ({
          verify: mockVerify,
        }));
    
        expect(() => verifyWebhookSignature(req)).toThrow('Error verifying webhook: Verification failed');
    });
});

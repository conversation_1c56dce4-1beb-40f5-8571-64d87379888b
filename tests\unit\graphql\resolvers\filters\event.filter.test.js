const mongoose = require('mongoose');
const buildEventQuery = require('../../../../../src/graphql/resolvers/filters/event.filter');
const MdEventType = require('../../../../../src/models/MdEventType');
const Party = require('../../../../../src/models/Party');
const MdServiceLocation = require('../../../../../src/models/MdServiceLocation');
const buildPartyQuery = require('../../../../../src/graphql/resolvers/filters/party.filter');
const buildServiceLocationQuery = require('../../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');

jest.mock('../../../../../src/utils/validation.util', () => ({
    isValidObjectId: jest.fn()
}));

jest.mock('../../../../../src/models/MdEventType');
jest.mock('../../../../../src/models/Party');
jest.mock('../../../../../src/models/MdServiceLocation');
jest.mock('../../../../../src/graphql/resolvers/filters/party.filter');
jest.mock('../../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');

describe('buildEventQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filters provided', async () => {
        const result = await buildEventQuery();
        expect(result.query).toEqual({});
        expect(result.postQueryFilters).toEqual({});
    });

    it('should build name and description filters correctly', async () => {
        const filters = {
            name: 'test event',
            description: 'test description'
        };

        const result = await buildEventQuery(filters);
        expect(result.query).toEqual({
            name: { $regex: 'test event', $options: 'i' },
            description: { $regex: 'test description', $options: 'i' }
        });
    });

    it('should build budget range filters correctly', async () => {
        const filters = {
            budget: {
                min: 1000,
                max: 5000
            }
        };

        const result = await buildEventQuery(filters);
        expect(result.query).toEqual({
            budget: {
                $gte: 1000,
                $lte: 5000
            }
        });
    });

    it('should handle eventType string ID correctly', async () => {
        const { isValidObjectId } = require('../../../../../src/utils/validation.util');
        isValidObjectId.mockReturnValue(true);

        const filters = {
            eventType: 'validEventTypeId'
        };

        const result = await buildEventQuery(filters);
        expect(result.query).toEqual({
            eventType: 'validEventTypeId'
        });
    });

    it('should throw error for invalid eventType ID', async () => {
        const { isValidObjectId } = require('../../../../../src/utils/validation.util');
        isValidObjectId.mockReturnValue(false);

        const filters = {
            eventType: 'invalidId'
        };

        await expect(buildEventQuery(filters)).rejects.toThrow('Invalid eventType ID provided');
    });

    it('should handle eventType object filters correctly', async () => {
        const mockEventTypes = [
            { _id: 'eventType1' },
            { _id: 'eventType2' }
        ];

        MdEventType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue(mockEventTypes)
        });

        const filters = {
            eventType: {
                name: 'test'
            }
        };

        const result = await buildEventQuery(filters);
        expect(result.query.eventType.$in).toEqual(['eventType1', 'eventType2']);
    });

    it('should handle dateRange filters correctly', async () => {
        const mockParties = [
            { eventId: 'event1' },
            { eventId: 'event2' }
        ];

        Party.find.mockResolvedValue(mockParties);

        const filters = {
            dateRange: {
                start: new Date('2024-01-01'),
                end: new Date('2024-12-31')
            }
        };

        const result = await buildEventQuery(filters);
        expect(result.query._id.$in).toEqual(['event1', 'event2']);
    });

    it('should handle presentAndUpcoming filter correctly', async () => {
        const filters = {
            presentAndUpcoming: true
        };

        const result = await buildEventQuery(filters);
        expect(result.postQueryFilters.customSort).toBeDefined();
        expect(result.postQueryFilters.customSort.presentAndUpcoming).toBe(true);
        expect(result.postQueryFilters.customSort.currentDate).toBeInstanceOf(Date);
    });

    it('should apply post query filters correctly', async () => {
        const mockEvents = [
            { 
                _id: 'event1',
                createdAt: new Date('2024-01-01'),
                toObject: () => ({ _id: 'event1', createdAt: new Date('2024-01-01') })
            },
            {
                _id: 'event2',
                createdAt: new Date('2024-02-01'),
                toObject: () => ({ _id: 'event2', createdAt: new Date('2024-02-01') })
            }
        ];

        Party.find.mockImplementation(() => ({
            select: () => ({
                sort: () => ({
                    limit: () => ({
                        lean: () => Promise.resolve([{ time: new Date('2024-03-01') }])
                    })
                })
            })
        }));

        const filters = {
            presentAndUpcoming: true
        };

        const result = await buildEventQuery(filters);
        const filteredEvents = await result.applyPostQueryFilters(mockEvents);
        
        expect(filteredEvents).toBeDefined();
        expect(Array.isArray(filteredEvents)).toBe(true);
    });
});

describe('Event Filter - Sorting Logic', () => {
  describe('applyPostQueryFilters sorting', () => {
    let events;
    let currentDate;

    beforeEach(() => {
      jest.clearAllMocks();
      currentDate = new Date('2024-01-15');
      
      events = [
        {
          _id: '1',
          name: 'Event 1',
          createdAt: new Date('2024-01-01'),
          toObject: function() { return this; }
        },
        {
          _id: '2',
          name: 'Event 2',
          createdAt: new Date('2024-01-02'),
          toObject: function() { return this; }
        },
        {
          _id: '3',
          name: 'Event 3',
          createdAt: new Date('2024-01-03'),
          toObject: function() { return this; }
        }
      ];
    });

    it('should sort events correctly when presentAndUpcoming is false', async () => {
      const { applyPostQueryFilters } = await buildEventQuery({ presentAndUpcoming: false });
      
      Party.find
        .mockReturnValueOnce({
          select: () => ({
            sort: () => ({
              limit: () => ({
                lean: () => Promise.resolve([{ time: new Date('2023-12-01') }])
              })
            })
          })
        })
        .mockReturnValueOnce({
          select: () => ({
            sort: () => ({
              limit: () => ({
                lean: () => Promise.resolve([{ time: new Date('2023-12-15') }])
              })
            })
          })
        })
        .mockReturnValueOnce({
          select: () => ({
            sort: () => ({
              limit: () => ({
                lean: () => Promise.resolve([{ time: new Date('2023-12-10') }])
              })
            })
          })
        });

      const sortedEvents = await applyPostQueryFilters(events);

      expect(sortedEvents.map(e => e._id)).toEqual(['2', '3', '1']);
    });

    it('should sort events with no actualEndDate by createdAt when presentAndUpcoming is true', async () => {
      const { applyPostQueryFilters } = await buildEventQuery({ presentAndUpcoming: true });
      
      Party.find.mockReturnValue({
        select: () => ({
          sort: () => ({
            limit: () => ({
              lean: () => Promise.resolve([])
            })
          })
        })
      });

      const sortedEvents = await applyPostQueryFilters(events);

      expect(sortedEvents.map(e => e._id)).toEqual(['3', '2', '1']);
    });
  });
});

describe('Event Filter - Party Filtering', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should filter events based on matching parties', async () => {
        const mockPartyQuery = { someField: 'someValue' };
        const mockApplyPostQueryFilters = jest.fn().mockImplementation(parties => parties);
        buildPartyQuery.mockResolvedValue({
            query: mockPartyQuery,
            applyPostQueryFilters: mockApplyPostQueryFilters
        });

        const mockParties = [
            { eventId: 'event1' },
            { eventId: 'event2' },
            { eventId: 'event1' } 
        ];
        Party.find.mockResolvedValue(mockParties);

        const filters = {
            parties: {
                somePartyFilter: 'value'
            }
        };

        const result = await buildEventQuery(filters);

        expect(buildPartyQuery).toHaveBeenCalledWith(filters.parties);
        expect(Party.find).toHaveBeenCalledWith(mockPartyQuery);
        expect(mockApplyPostQueryFilters).toHaveBeenCalledWith(mockParties);
        expect(result.query._id).toEqual({
            $in: ['event1', 'event2']
        });
    });

    it('should return empty event ID list when no parties match', async () => {
        const mockPartyQuery = { someField: 'someValue' };
        buildPartyQuery.mockResolvedValue({
            query: mockPartyQuery,
            applyPostQueryFilters: jest.fn().mockReturnValue([])
        });

        Party.find.mockResolvedValue([]);

        const filters = {
            parties: {
                somePartyFilter: 'value'
            }
        };

        const result = await buildEventQuery(filters);

        expect(result.query._id).toEqual({
            $in: []
        });
    });

    it('should not modify query when party filter is empty', async () => {
        buildPartyQuery.mockResolvedValue({
            query: {},
            applyPostQueryFilters: jest.fn()
        });

        const filters = {
            parties: {}
        };

        const result = await buildEventQuery(filters);

        expect(Party.find).not.toHaveBeenCalled();
        expect(result.query).toEqual({});
    });

    it('should handle null/undefined party filters', async () => {
        const filters = {
            name: 'Some Event'
        };

        const result = await buildEventQuery(filters);

        expect(buildPartyQuery).not.toHaveBeenCalled();
        expect(Party.find).not.toHaveBeenCalled();
        expect(result.query).toEqual({
            name: { $regex: 'Some Event', $options: 'i' }
        });
    });
});

describe('Event Filter - Location Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('location filtering', () => {
        it('should handle string location ID correctly', async () => {
            const validLocationId = new mongoose.Types.ObjectId().toString();
            const filters = {
                location: validLocationId
            };

            require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(true);

            const result = await buildEventQuery(filters);

            expect(result.query.location).toBe(validLocationId);
        });

        it('should throw error for invalid location ID string', async () => {
            const filters = {
                location: 'invalid-id'
            };

            require('../../../../../src/utils/validation.util').isValidObjectId.mockReturnValue(false);

            await expect(buildEventQuery(filters)).rejects.toThrow('Invalid location ID provided');
        });

        it('should handle object-based location query with matches', async () => {
            const locationFilters = {
                city: 'New York'
            };
            const filters = {
                location: locationFilters
            };

            const mockLocationQuery = { city: 'New York' };
            const mockLocations = [
                { _id: new mongoose.Types.ObjectId() },
                { _id: new mongoose.Types.ObjectId() }
            ];

            buildServiceLocationQuery.mockResolvedValue(mockLocationQuery);
            MdServiceLocation.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(mockLocations)
            });

            const result = await buildEventQuery(filters);

            expect(buildServiceLocationQuery).toHaveBeenCalledWith(locationFilters);
            expect(MdServiceLocation.find).toHaveBeenCalledWith(mockLocationQuery);
            expect(result.query.location).toEqual({
                $in: mockLocations.map(loc => loc._id)
            });
        });

        it('should handle object-based location query with no matches', async () => {
            const locationFilters = {
                city: 'NonexistentCity'
            };
            const filters = {
                location: locationFilters
            };

            const mockLocationQuery = { city: 'NonexistentCity' };
            buildServiceLocationQuery.mockResolvedValue(mockLocationQuery);
            MdServiceLocation.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });

            const result = await buildEventQuery(filters);

            expect(buildServiceLocationQuery).toHaveBeenCalledWith(locationFilters);
            expect(MdServiceLocation.find).toHaveBeenCalledWith(mockLocationQuery);
            expect(result.query.location).toEqual({ $in: [] });
        });

        it('should handle empty location query object', async () => {
            const filters = {
                location: {}
            };

            buildServiceLocationQuery.mockResolvedValue({});

            const result = await buildEventQuery(filters);

            expect(buildServiceLocationQuery).toHaveBeenCalledWith({});
            expect(result.query.location).toBeUndefined();
        });
    });
});

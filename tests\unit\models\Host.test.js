const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Host = require('../../../src/models/Host');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Host Model Test', () => {
    it('should create and save a Host successfully', async () => {
        const validHost = new Host({
            userId: new mongoose.Types.ObjectId(),
        });

        const savedHost = await validHost.save();

        expect(savedHost._id).toBeDefined();
        expect(savedHost.userId).toBeDefined();
    });

    it('should fail to create a Host without required fields', async () => {
        const host = new Host();

        let err;
        try {
            await host.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.userId).toBeDefined();
    });
});
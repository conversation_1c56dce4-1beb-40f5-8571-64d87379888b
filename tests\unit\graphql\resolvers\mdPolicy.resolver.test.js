const mdPolicyResolvers = require('../../../../src/graphql/resolvers/mdPolicy.resolver');
const MdPolicy = require('../../../../src/models/MdPolicy');
const Tag = require('../../../../src/models/Tag');
const { getByIdCache, clearCacheById, setCache } = require('../../../../src/utils/cache.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { createResponse } = require('../../../../src/utils/response.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const buildMdPolicyQuery = require('../../../../src/graphql/resolvers/filters/mdPolicy.filter');

jest.mock('../../../../src/models/MdPolicy');
jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdPolicy.filter');

describe('mdPolicyResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdPolicy Field Resolvers', () => {
        it('should resolve tags field', async () => {
            const parent = { tags: ['tag1', 'tag2'] };
            const mockTags = [
                { _id: 'tag1', name: 'Tag 1' },
                { _id: 'tag2', name: 'Tag 2' }
            ];

            Tag.find.mockResolvedValue(mockTags);

            const result = await mdPolicyResolvers.MdPolicy.tags(parent);

            expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: parent.tags } });
            expect(result).toEqual(mockTags);
        });

        it('should handle error when resolving tags', async () => {
            const parent = { tags: ['tag1'] };
            const error = new Error('Database error');

            Tag.find.mockRejectedValue(error);

            await expect(mdPolicyResolvers.MdPolicy.tags(parent)).rejects.toThrow('Error getting tags');
            expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: parent.tags } });
        });
    });

    describe('Query Resolvers', () => {
        describe('getMdPolicyById', () => {
            it('should return cached policy if exists', async () => {
                const id = 'policyId';
                const cachedPolicy = { _id: id, name: 'Cached Policy' };
                
                getByIdCache.mockResolvedValue(cachedPolicy);
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Query.getMdPolicyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPolicy.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyResponse',
                    'SUCCESS',
                    'MdPolicy fetched successfully',
                    { result: { mdPolicy: cachedPolicy } }
                );
                expect(result).toBe('success response');
            });

            it('should fetch and cache policy if not in cache', async () => {
                const id = 'policyId';
                const policy = { _id: id, name: 'Test Policy' };
                
                getByIdCache.mockResolvedValue(null);
                MdPolicy.findById.mockResolvedValue(policy);
                setCache.mockResolvedValue();
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Query.getMdPolicyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPolicy.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, policy);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyResponse',
                    'SUCCESS',
                    'MdPolicy fetched successfully',
                    { result: { mdPolicy: policy } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response when policy not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                MdPolicy.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('error response');

                const result = await mdPolicyResolvers.Query.getMdPolicyById(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'MdPolicy not found',
                    { errors: [{ field: 'getMdPolicyById', message: 'MdPolicy not found' }] }
                );
                expect(result).toBe('error response');
            });

            it('should handle errors when getting MdPolicy by ID fails', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                
                getByIdCache.mockResolvedValue(null);
                MdPolicy.findById.mockRejectedValue(error);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error getting MdPolicy',
                    errors: [{ field: 'getMdPolicyById', message: error.message }]
                });

                const result = await mdPolicyResolvers.Query.getMdPolicyById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPolicy.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Error getting MdPolicy',
                    {
                        errors: [{ field: 'getMdPolicyById', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error getting MdPolicy',
                    errors: [{ field: 'getMdPolicyById', message: error.message }]
                });
            });
        });

        describe('getMdPolicies', () => {
            it('should return policies with pagination', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const policies = [{ _id: '1', name: 'Policy 1' }];
                const paginationInfo = { total: 1, hasMore: false };

                const mockQuery = { name: { $regex: 'Test', $options: 'i' } };
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(policies)
                };

                MdPolicy.find.mockReturnValue(mockPopulateChain);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Query.getMdPolicies(null, { filter, pagination });

                expect(MdPolicy.find).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPoliciesResponse',
                    'SUCCESS',
                    'MdPolicies fetched successfully',
                    {
                        result: { mdPolicies: policies },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('success response');
            });

            it('should handle errors when getting MdPolicies fails', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                const mockQuery = { name: { $regex: 'Test', $options: 'i' } };
                buildMdPolicyQuery.mockResolvedValue(mockQuery);
                
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockRejectedValue(error)
                };

                MdPolicy.find.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('error response');

                const result = await mdPolicyResolvers.Query.getMdPolicies(null, { filter, pagination });

                expect(MdPolicy.find).toHaveBeenCalledWith(mockQuery);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Error getting MdPolicies',
                    {
                        errors: [{ field: 'getMdPolicies', message: error.message }]
                    }
                );
                expect(result).toBe('error response');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createMdPolicy', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should create policy successfully', async () => {
                const input = { name: 'New Policy', tags: ['tag1'] };
                const savedPolicy = { _id: 'newId', ...input };
                
                validateReferences.mockResolvedValue(null);
                MdPolicy.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(savedPolicy)
                }));
                MdPolicy.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(savedPolicy)
                });
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Mutation.createMdPolicy(null, { input });

                expect(validateReferences).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyResponse',
                    'SUCCESS',
                    'MdPolicy created successfully',
                    { result: { mdPolicy: savedPolicy } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response when validation fails', async () => {
                const input = { name: 'New Policy', tags: ['invalidTag'] };
                const validationError = {
                    errors: [{ field: 'tags', message: 'Invalid tag reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue('error response');

                const result = await mdPolicyResolvers.Mutation.createMdPolicy(null, { input });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Invalid reference',
                    { errors: validationError.errors }
                );
                expect(result).toBe('error response');
            });

            it('should handle error when creating MdPolicy fails', async () => {
                const input = {
                    name: 'Test Policy',
                    description: 'Test Description',
                    tags: ['tag1', 'tag2']
                };
                const error = new Error('Database error');
                
                validateReferences.mockResolvedValue(null);
                
                MdPolicy.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating MdPolicy',
                    errors: [{ field: 'createMdPolicy', message: error.message }]
                });

                const result = await mdPolicyResolvers.Mutation.createMdPolicy(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdPolicy');
                expect(MdPolicy).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Error creating MdPolicy',
                    {
                        errors: [{ field: 'createMdPolicy', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating MdPolicy',
                    errors: [{ field: 'createMdPolicy', message: error.message }]
                });
            });
        });

        describe('updateMdPolicy', () => {
            it('should update policy successfully', async () => {
                const id = 'policyId';
                const input = { name: 'Updated Policy' };
                const updatedPolicy = { _id: id, ...input };

                validateReferences.mockResolvedValue(null);
                MdPolicy.findByIdAndUpdate.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(updatedPolicy)
                });
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Mutation.updateMdPolicy(null, { id, input });

                expect(validateReferences).toHaveBeenCalled();
                expect(MdPolicy.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyResponse',
                    'SUCCESS',
                    'MdPolicy updated successfully',
                    { result: { mdPolicy: updatedPolicy } }
                );
                expect(result).toBe('success response');
            });

            it('should return error response if update fails', async () => {
                const id = 'policyId';
                const input = { 
                    name: 'Updated Policy',
                    description: 'Updated description',
                    tags: ['tag1', 'tag2']
                };
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockRejectedValue(error);
                MdPolicy.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulate
                });
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPolicyResolvers.Mutation.updateMdPolicy(null, { id, input });

                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'MdPolicy');
                expect(MdPolicy.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(mockPopulate).toHaveBeenCalledWith('tags');
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Error updating MdPolicy',
                    { errors: [{ field: 'updateMdPolicy', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdPolicy', () => {
            it('should delete policy successfully', async () => {
                const id = 'policyId';
                const deletedPolicy = { _id: id, name: 'Deleted Policy' };

                findReferences.mockResolvedValue([]);
                MdPolicy.findByIdAndDelete.mockResolvedValue(deletedPolicy);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('success response');

                const result = await mdPolicyResolvers.Mutation.deleteMdPolicy(null, { id });

                expect(MdPolicy.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyResponse',
                    'SUCCESS',
                    'MdPolicy deleted successfully',
                    { result: { mdPolicy: deletedPolicy } }
                );
                expect(result).toBe('success response');
            });

            it('should return error if policy has references', async () => {
                const id = 'policyId';
                const references = ['Reference1', 'Reference2'];

                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('error response');

                const result = await mdPolicyResolvers.Mutation.deleteMdPolicy(null, { id });

                expect(MdPolicy.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'MdPolicy cannot be deleted',
                    { errors: [{ field: 'id', message: `MdPolicy cannot be deleted as it is being used in: ${references.join(', ')}` }] }
                );
                expect(result).toBe('error response');
            });

            it('should return error response if an error occurs during deletion', async () => {
                const id = 'policyId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                MdPolicy.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPolicyResolvers.Mutation.deleteMdPolicy(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPolicy');
                expect(MdPolicy.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPolicyErrorResponse',
                    'FAILURE',
                    'Error deleting MdPolicy',
                    { errors: [{ field: 'deleteMdPolicy', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
                expect(clearCacheById).not.toHaveBeenCalled();
            });
        });
    });
}); 
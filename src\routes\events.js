const express = require('express');
const router = express.Router();
const cors = require('cors');
const getRedisClient = require('../infrastructure/redisClient');
const { isValidJWT, verifyTokenLocally, verifyClerkUser } = require('../utils/clerk.util');
const { getTokenFromHeader } = require('../utils/serverSetup.util');
const { GraphQLError } = require('graphql');
const Party = require('../models/Party');
const { hasEventPartyGuestLevelAccess } = require('../utils/auth/accessLevels.util');
const { User } = require('../models/User');
const { readActivityMessages } = require('../utils/messageQueue.util');

// Enable CORS for all routes in this router
router.use(cors());

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const token = getTokenFromHeader(req);
    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const verifiedToken = await verifyTokenLocally(token);
    await verifyClerkUser(verifiedToken.sub);
    
    // Find and cache the database user ID during authentication
    const dbUser = await User.findOne({ externalId: verifiedToken.sub });
    if (!dbUser) {
      return res.status(401).json({ error: 'User not found in database' });
    }
    
    req.user = {
      ...verifiedToken,
      _id: dbUser._id // Add database ID to the user object
    };
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Authentication failed' });
  }
};

// Keep track of connected clients
const clients = new Set();

// SSE endpoint
router.get('/', authenticate, async (req, res) => {
  const partyId = req.query.partyId;

  try {
    // Now we can use req.user._id directly without another DB query
    const party = await Party.findById(partyId);
    if (!party) {
      return res.status(404).json({ error: 'Party not found' });
    }

    const { hasAccess } = await hasEventPartyGuestLevelAccess(req.user._id, party.eventId, partyId);
    if (!hasAccess) {
      return res.status(403).json({ error: 'Unauthorized access to this party' });
    }

    // Set headers for SSE and CORS
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',  // Allow all origins
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'  // Added Authorization to allowed headers
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ type: 'connection', message: 'Connected to event stream' })}\n\n`);

    // Add this client to our Set
    clients.add(res);

    // Set up interval to check for new messages
    const intervalId = setInterval(async () => {
      try {
        const messages = await readActivityMessages(partyId);
        
        if (messages.length > 0) {
          messages.forEach(message => {
              res.write(`data: ${JSON.stringify(message)}\n\n`);
          });
        }
      } catch (error) {
        console.error('Error sending messages:', error);
      }
    }, 1000); // Check every second

    // Handle client disconnect
    req.on('close', () => {
      clearInterval(intervalId);
      clients.delete(res);
      res.end();
    });

  } catch (error) {
    console.error('Error setting up SSE:', error);
    res.status(500).end();
  }
});

// Helper function to broadcast messages to all connected clients
const broadcastMessage = (message) => {
  clients.forEach(client => {
    try {
      client.write(`data: ${JSON.stringify(message)}\n\n`);
    } catch (error) {
      console.error('Error broadcasting to client:', error);
      clients.delete(client);
    }
  });
};

module.exports = router;
const { model, Schema } = require('mongoose');

const invitationRSVPSchema = new Schema({
    invitation: { type: Schema.Types.ObjectId, ref: 'Invitation', required: true },
    guest: { type: Schema.Types.ObjectId, ref: 'Guest', required: true },
    status: { type: String, enum: ['PENDING', 'ACCEPTED', 'REJECTED', 'MAYBE'], default: 'PENDING' },
    message: { type: String, required: false },
}, { timestamps: true});


const InvitationRSVP = model('InvitationRSVP', invitationRSVPSchema);

module.exports = InvitationRSVP;


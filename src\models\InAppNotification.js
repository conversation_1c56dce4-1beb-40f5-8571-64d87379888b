const { model, Schema } = require('mongoose');

const inAppNotificationSchema = new Schema({
    userId: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    sender: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    message: {
        type: String,
        required: true
    },
    media: {
        type: String,
        required: false
    },
    link: {
        type: String,
        required: false
    },
    read: {
        type: Boolean,
        required: true,
        default: false
    }
}, { 
    timestamps: true,
    collection: 'in_app_notifications'
});

inAppNotificationSchema.index({ userId: 1, read: 1 });

module.exports = model('InAppNotification', inAppNotificationSchema); 
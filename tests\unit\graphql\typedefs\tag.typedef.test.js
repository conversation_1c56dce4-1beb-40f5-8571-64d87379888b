const { gql } = require('graphql-tag');
const tagTypeDef = require('../../../../src/graphql/typedefs/tag.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('tagTypeDef', () => {
  it('should contain the Tag type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type Tag {
          id: ID!
          name: String!
          icon: MdIcon
          slug: String
      }

      input TagFilterInput {
          id: ID
          name: String
          icon: MdIconFilterInput
          slug: String
      }

      type TagWrapper {
          tag: Tag!
      }

      type TagsWrapper {
          tags: [Tag]!
      }

      type TagResponse implements Response {
          status: ResponseStatus!
          message: String!
          result: TagWrapper!
      }

      type TagsResponse implements Response {
          status: ResponseStatus!
          message: String!
          result: TagsWrapper!
          pagination: PaginationInfo!
      }

      type TagErrorResponse implements Response {
          status: ResponseStatus!
          message: String!
          errors: [Error!]!
      }

      union TagResult = TagResponse | TagErrorResponse
      union TagsResult = TagsResponse | TagErrorResponse

      type Query {
          getTagById(id: ID!): TagResult!
          getTags(filter: TagFilterInput, pagination: PaginationInput): TagsResult!
      }

      input TagInput {
          name: String!
          icon: ID
          slug: String
      }

      input TagUpdateInput {
          name: String
          icon: ID
          slug: String
      }

      type Mutation {
          createTag(input: TagInput!): TagResult!
          updateTag(id: ID!, input: TagUpdateInput!): TagResult!
          deleteTag(id: ID!): TagResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(tagTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
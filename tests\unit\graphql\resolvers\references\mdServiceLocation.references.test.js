const mongoose = require('mongoose');
const findReferences = require('../../../../../src/graphql/resolvers/references/mdServiceLocation.references');
const Vendor = require('../../../../../src/models/Vendor');

jest.mock('../../../../../src/models/Vendor');

describe('mdServiceLocation References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        Vendor.find.mockResolvedValue([]);

        const id = new mongoose.Types.ObjectId().toString();
        const result = await findReferences(id);

        expect(Vendor.find).toHaveBeenCalledWith({ 'serviceLocations': id });
        expect(result).toEqual([]);
    });

    it('should return array with "Vendor" when vendor references exist', async () => {
        const mockVendors = [
            { _id: new mongoose.Types.ObjectId(), name: 'Vendor 1' },
            { _id: new mongoose.Types.ObjectId(), name: 'Vendor 2' }
        ];
        Vendor.find.mockResolvedValue(mockVendors);

        const id = new mongoose.Types.ObjectId().toString();
        const result = await findReferences(id);

        expect(Vendor.find).toHaveBeenCalledWith({ 'serviceLocations': id });
        expect(result).toEqual(['Vendor']);
    });

    it('should handle errors gracefully', async () => {
        const error = new Error('Database error');
        Vendor.find.mockRejectedValue(error);

        const id = new mongoose.Types.ObjectId().toString();
        
        await expect(findReferences(id)).rejects.toThrow('Database error');
        expect(Vendor.find).toHaveBeenCalledWith({ 'serviceLocations': id });
    });
});

const mongoose = require('mongoose');
const MdTheme = require('../../../../src/models/MdTheme');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const mdThemeResolvers = require('../../../../src/graphql/resolvers/mdTheme.resolver');

console.error = jest.fn();

jest.mock('../../../../src/models/MdTheme');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/utils/referenceCheck.util');

describe('mdThemeResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        console.error.mockClear();
    });

    describe('Query', () => {
        describe('getMdThemeById', () => {
            it('should return cached theme if available', async () => {
                const id = 'themeId';
                const cachedTheme = { _id: id, name: 'Cached Theme' };
                
                getByIdCache.mockResolvedValue(cachedTheme);
                createResponse.mockReturnValue('successResponse');

                const result = await mdThemeResolvers.Query.getMdThemeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdTheme.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeResponse',
                    'SUCCESS',
                    'Theme retrieved successfully',
                    { result: { mdTheme: cachedTheme } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache theme if not in cache', async () => {
                const id = 'themeId';
                const theme = { _id: id, name: 'Test Theme' };
                
                getByIdCache.mockResolvedValue(null);
                MdTheme.findById.mockResolvedValue(theme);
                createResponse.mockReturnValue('successResponse');

                const result = await mdThemeResolvers.Query.getMdThemeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdTheme.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, theme);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeResponse',
                    'SUCCESS',
                    'Theme retrieved successfully',
                    { result: { mdTheme: theme } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if theme not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                MdTheme.findById.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdThemeResolvers.Query.getMdThemeById(null, { id });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Theme not found',
                    { errors: [{ field: 'id', message: 'Theme not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors and return error response', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                getByIdCache.mockRejectedValue(error);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving theme',
                    errors: [{ field: 'getMdThemeById', message: error.message }]
                });

                const result = await mdThemeResolvers.Query.getMdThemeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Error retrieving theme',
                    {
                        errors: [{ field: 'getMdThemeById', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving theme',
                    errors: [{ field: 'getMdThemeById', message: error.message }]
                });
            });

            it('should return error response when theme is not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                
                MdTheme.findById.mockResolvedValue(null);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Theme not found',
                    errors: [{ field: 'id', message: 'Theme not found' }]
                });

                const result = await mdThemeResolvers.Query.getMdThemeById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdTheme.findById).toHaveBeenCalledWith(id);
                expect(setCache).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Theme not found',
                    {
                        errors: [{ field: 'id', message: 'Theme not found' }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Theme not found',
                    errors: [{ field: 'id', message: 'Theme not found' }]
                });
            });
        });

        describe('getMdThemes', () => {
            it('should return themes with pagination', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const themes = [{ _id: '1', name: 'Theme 1' }];
                const paginationInfo = { totalPages: 1, totalItems: 1 };

                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdTheme.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(themes)
                });
                createResponse.mockReturnValue('successResponse');

                const result = await mdThemeResolvers.Query.getMdThemes(null, { filter, pagination });

                expect(getPaginationInfo).toHaveBeenCalled();
                expect(MdTheme.find).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemesResponse',
                    'SUCCESS',
                    'Themes retrieved successfully',
                    { 
                        result: { mdThemes: themes },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should return appropriate response when no themes found', async () => {
                const filter = {};
                const pagination = { limit: 10, skip: 0 };
                const paginationInfo = { totalPages: 0, totalItems: 0 };

                getPaginationInfo.mockResolvedValue(paginationInfo);
                MdTheme.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([])
                });
                createResponse.mockReturnValue('emptyResponse');

                const result = await mdThemeResolvers.Query.getMdThemes(null, { filter, pagination });

                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemesResponse',
                    'FAILURE',
                    'No themes found',
                    { 
                        result: { mdThemes: [] },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('emptyResponse');
            });

            it('should handle errors when retrieving themes', async () => {
                const filter = { name: 'test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                MdTheme.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockRejectedValue(error)
                });

                const result = await mdThemeResolvers.Query.getMdThemes(null, { filter, pagination });

                expect(console.error).toHaveBeenCalledWith(error);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Error retrieving themes',
                    {
                        errors: [{ field: 'getMdThemes', message: error.message }]
                    }
                );
                expect(result).toEqual(createResponse.mock.results[0].value);
            });
        });
    });

    describe('Mutation', () => {
        describe('createMdTheme', () => {
            it('should create and return the theme successfully', async () => {
                const input = { 
                    name: 'Test Theme',
                    description: 'Test Description',
                    primaryColor: '#000000'
                };
                const savedTheme = { _id: 'themeId', ...input };
                
                const mockInstance = {
                    ...savedTheme
                };
                Object.defineProperty(mockInstance, 'save', {
                    value: jest.fn().mockResolvedValue(savedTheme),
                    enumerable: false 
                });
                
                MdTheme.mockImplementation(() => mockInstance);
                
                createResponse.mockReturnValue({
                    status: 'SUCCESS',
                    message: 'Theme created successfully',
                    result: { mdTheme: savedTheme }
                });
                
                const result = await mdThemeResolvers.Mutation.createMdTheme(null, { input });

                expect(MdTheme).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeResponse',
                    'SUCCESS',
                    'Theme created successfully',
                    { result: { mdTheme: savedTheme } }
                );
                expect(result).toEqual({
                    status: 'SUCCESS',
                    message: 'Theme created successfully',
                    result: { mdTheme: savedTheme }
                });
            });

            it('should return error response when theme creation fails', async () => {
                const input = { 
                    name: 'Test Theme',
                    description: 'Test Description',
                    primaryColor: '#000000'
                };
                const error = new Error('Database error');

                MdTheme.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating theme',
                    errors: [{ field: 'createMdTheme', message: error.message }]
                });

                const result = await mdThemeResolvers.Mutation.createMdTheme(null, { input });

                expect(MdTheme).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Error creating theme',
                    { errors: [{ field: 'createMdTheme', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating theme',
                    errors: [{ field: 'createMdTheme', message: error.message }]
                });
            });
        });

        describe('updateMdTheme', () => {
            it('should update and return the theme', async () => {
                const id = 'themeId';
                const input = { name: 'Updated Theme' };
                const updatedTheme = { _id: id, ...input };

                MdTheme.findByIdAndUpdate.mockResolvedValue(updatedTheme);
                createResponse.mockReturnValue('successResponse');

                const result = await mdThemeResolvers.Mutation.updateMdTheme(null, { id, input });

                expect(MdTheme.findByIdAndUpdate).toHaveBeenCalledWith(id, input, { new: true });
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeResponse',
                    'SUCCESS',
                    'Theme updated successfully',
                    { result: { mdTheme: updatedTheme } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if update fails', async () => {
                const id = 'themeId';
                const input = { name: 'Updated Theme' };
                const error = new Error('Database error');

                MdTheme.findByIdAndUpdate.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');
            
                const result = await mdThemeResolvers.Mutation.updateMdTheme(null, { id, input });

                expect(MdTheme.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Error updating theme',
                    { errors: [{ field: 'updateMdTheme', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdTheme', () => {
            it('should delete theme if no references exist', async () => {
                const id = 'themeId';
                const deletedTheme = { _id: id, name: 'Deleted Theme' };

                findReferences.mockResolvedValue([]);
                MdTheme.findByIdAndDelete.mockResolvedValue(deletedTheme);
                createResponse.mockReturnValue('successResponse');

                const result = await mdThemeResolvers.Mutation.deleteMdTheme(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdTheme');
                expect(MdTheme.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeResponse',
                    'SUCCESS',
                    'Theme deleted successfully',
                    { result: { mdTheme: deletedTheme } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if theme has references', async () => {
                const id = 'themeId';
                const references = ['Party1', 'Party2'];

                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdThemeResolvers.Mutation.deleteMdTheme(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdTheme');
                expect(MdTheme.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Theme cannot be deleted',
                    { 
                        errors: [{ 
                            field: 'id', 
                            message: `Theme cannot be deleted as it is being used in: ${references.join(', ')}` 
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response if deletion fails', async () => {
                const id = 'themeId';
                const error = new Error('Database error');

                findReferences.mockResolvedValue([]);

                MdTheme.findByIdAndDelete.mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting theme',
                    errors: [{ field: 'deleteMdTheme', message: error.message }]
                });

                const result = await mdThemeResolvers.Mutation.deleteMdTheme(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdTheme');
                expect(MdTheme.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdThemeErrorResponse',
                    'FAILURE',
                    'Error deleting theme',
                    { errors: [{ field: 'deleteMdTheme', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting theme',
                    errors: [{ field: 'deleteMdTheme', message: error.message }]
                });
            });
        });
    });
}); 
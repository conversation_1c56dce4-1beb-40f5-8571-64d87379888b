const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdIconQuery = require("./mdIcon.filter");
const MdIcon = require("../../../models/MdIcon");

const buildMdReactionTypeQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.active !== undefined) {
            query.active = filter.active;
        }

        if (filter.icon) {
            if (typeof filter.icon === 'string') {
                if (isValidObjectId(filter.icon)) {
                    query.icon = filter.icon;
                } else {
                    throw new Error('Invalid icon ID provided');
                }
            } else if (typeof filter.icon === 'object') {
                const iconQuery = await buildMdIconQuery(filter.icon);
                if (Object.keys(iconQuery).length > 0) {
                    const matchingIcons = await MdIcon.find(iconQuery).select('_id');
                    if (matchingIcons.length > 0) {
                        query.icon = { $in: matchingIcons.map(icon => icon._id) };
                    } else {
                        query.icon = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdReactionTypeQuery; 
const MdAppSettings = require('../../models/MdAppSettings');
const { createResponse } = require('../../utils/response.util');
const { getByIdCache, setCache, getBySubstring, clearCacheBySubstring } = require('../../utils/cache.util');
const { transformIcon } = require('../../utils/transformation.util');
const MdIcon = require('../../models/MdIcon');
const { validateReferences } = require('../../utils/validation.util');

const mdAppSettingsIdSchema = {
    'navBars.userDashboard.tabs.home': { model: MdIcon, type: 'single', required: true },
    'navBars.userDashboard.tabs.myEvents': { model: MdIcon, type: 'single', required: true },
    'navBars.userDashboard.tabs.publicEvents': { model: MdIcon, type: 'single', required: true },
    'navBars.userDashboard.tabs.photos': { model: MdIcon, type: 'single', required: true },
    'navBars.hostEventDashboard.tabs.eventsDb': { model: MdIcon, type: 'single', required: true },
    'navBars.hostEventDashboard.tabs.tasks': { model: MdIcon, type: 'single', required: true },
    'navBars.hostEventDashboard.tabs.guests': { model: MdIcon, type: 'single', required: true },
    'navBars.hostEventDashboard.tabs.apps': { model: MdIcon, type: 'single', required: true },
    'navBars.hostEventDashboard.tabs.messages': { model: MdIcon, type: 'single', required: true }
};

const mdAppSettingsResolvers = {       
    Query: {
        getMdAppSettingsByVersion: async (_, { version }) => {
            try {
                let mdAppSettings = await getByIdCache("mdAppSettings"+version);
                if (!mdAppSettings) {
                    mdAppSettings = await MdAppSettings.findOne({ version }).populate({
                        path: 'navBars.userDashboard.tabs.home navBars.userDashboard.tabs.myEvents navBars.userDashboard.tabs.publicEvents navBars.userDashboard.tabs.photos',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    }).populate({
                        path: 'navBars.hostEventDashboard.tabs.eventsDb navBars.hostEventDashboard.tabs.tasks navBars.hostEventDashboard.tabs.guests navBars.hostEventDashboard.tabs.apps navBars.hostEventDashboard.tabs.messages',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    });
                }

                if (mdAppSettings) {
                    if (mdAppSettings?.navBars?.userDashboard?.tabs) {
                        const tabs = mdAppSettings.navBars.userDashboard.tabs;
                        Object.entries(tabs).forEach(([key, value]) => {
                            if (value) {
                                const transformed = transformIcon(value);
                                if (transformed) {
                                    tabs[key] = transformed;
                                }
                            }
                        });
                    }

                    if (mdAppSettings?.navBars?.hostEventDashboard?.tabs) {
                        const tabs = mdAppSettings.navBars.hostEventDashboard.tabs;
                        Object.entries(tabs).forEach(([key, value]) => {
                            if (value) {
                                const transformed = transformIcon(value);
                                if (transformed) {
                                    tabs[key] = transformed;
                                }
                            }
                        });
                    }

                    await setCache("mdAppSettings"+mdAppSettings.version, mdAppSettings);
                } else {
                    return createResponse('MdAppSettingsErrorResponse', 'FAILURE', 'MdAppSettings not found', { errors: [{ field: 'getMdAppSettingsByVersion', message: 'MdAppSettings not found' }] });
                }
                return createResponse('MdAppSettingsResponse', 'SUCCESS', 'MdAppSettings fetched successfully', { result: { mdAppSettings } });
            } catch (error) {
                console.error(error);
                return createResponse('MdAppSettingsErrorResponse', 'FAILURE', 'Error getting MdAppSettings', { errors: [{ field: 'getMdAppSettingsByVersion', message: error.message }] });
            }
        },

        getMdAppSettings: async () => {
            try {
                let mdAppSettings = await getBySubstring("mdAppSettings");
                if (!mdAppSettings) {
                    mdAppSettings = await MdAppSettings.findOne({}).sort({ createdAt: -1 })
                        .populate({
                            path: 'navBars.userDashboard.tabs.home navBars.userDashboard.tabs.myEvents navBars.userDashboard.tabs.publicEvents navBars.userDashboard.tabs.photos',
                            model: 'MdIcon',
                            select: '_id id name iconSrc createdAt updatedAt'
                        })
                        .populate({
                            path: 'navBars.hostEventDashboard.tabs.eventsDb navBars.hostEventDashboard.tabs.tasks navBars.hostEventDashboard.tabs.guests navBars.hostEventDashboard.tabs.apps navBars.hostEventDashboard.tabs.messages',
                            model: 'MdIcon',
                            select: '_id id name iconSrc createdAt updatedAt'
                        });
                }

                if (mdAppSettings) {
                    if (mdAppSettings?.navBars?.userDashboard?.tabs) {
                        const tabs = mdAppSettings.navBars.userDashboard.tabs;
                        Object.entries(tabs).forEach(([key, value]) => {
                            if (value) {
                                const transformed = transformIcon(value);
                                if (transformed) {
                                    tabs[key] = transformed;
                                }
                            }
                        });
                    }

                    if (mdAppSettings?.navBars?.hostEventDashboard?.tabs) {
                        const tabs = mdAppSettings.navBars.hostEventDashboard.tabs;
                        Object.entries(tabs).forEach(([key, value]) => {
                            if (value) {
                                const transformed = transformIcon(value);
                                if (transformed) {
                                    tabs[key] = transformed;
                                }
                            }
                        });
                    }

                    await setCache("mdAppSettings"+mdAppSettings.version, mdAppSettings);
                } else {
                    return createResponse('MdAppSettingsErrorResponse', 'FAILURE', 'MdAppSettings not found', { 
                        errors: [{ field: 'getMdAppSettings', message: 'MdAppSettings not found' }] 
                    });
                }

                return createResponse('MdAppSettingsResponse', 'SUCCESS', 'MdAppSettings fetched successfully', { 
                    result: { mdAppSettings } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdAppSettingsErrorResponse', 'FAILURE', 'Error getting MdAppSettings', { 
                    errors: [{ field: 'getMdAppSettings', message: error.message }] 
                });
            }
        },

        getMdAppSettingsVersions: async () => {
            try {
                const mdAppSettings = await MdAppSettings.find({}, 'version').sort({ createdAt: -1 });
                const versions = [...new Set(mdAppSettings.map(item => item.version))];
                return createResponse('MdAppSettingsVersionsResponse', 'SUCCESS', 'MdAppSettings versions fetched successfully', { result: versions });
            } catch (error) {
                console.error(error);
                return createResponse('MdAppSettingsVersionsErrorResponse', 'FAILURE', 'Error getting MdAppSettings versions', { errors: [{ field: 'getMdAppSettingsVersions', message: error.message }] });
            }
        }
    },
    Mutation: {
        createMdAppSettings: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, mdAppSettingsIdSchema, 'MdAppSettings');
                if (validationError) {
                    return createResponse('MdAppSettingsErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const existingSettings = await MdAppSettings.findOne({ version: input.version });
                
                let mdAppSettings;
                if (existingSettings) {
                    mdAppSettings = await MdAppSettings.findByIdAndUpdate(
                        existingSettings._id,
                        input,
                        { new: true }
                    ).populate({
                        path: 'navBars.userDashboard.tabs.home navBars.userDashboard.tabs.myEvents navBars.userDashboard.tabs.publicEvents navBars.userDashboard.tabs.photos',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    }).populate({
                        path: 'navBars.hostEventDashboard.tabs.eventsDb navBars.hostEventDashboard.tabs.tasks navBars.hostEventDashboard.tabs.guests navBars.hostEventDashboard.tabs.apps navBars.hostEventDashboard.tabs.messages',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    });
                } else {
                    const newMdAppSettings = new MdAppSettings(input);
                    await newMdAppSettings.save();
                    
                    mdAppSettings = await MdAppSettings.findById(newMdAppSettings._id).populate({
                        path: 'navBars.userDashboard.tabs.home navBars.userDashboard.tabs.myEvents navBars.userDashboard.tabs.publicEvents navBars.userDashboard.tabs.photos',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    }).populate({
                        path: 'navBars.hostEventDashboard.tabs.eventsDb navBars.hostEventDashboard.tabs.tasks navBars.hostEventDashboard.tabs.guests navBars.hostEventDashboard.tabs.apps navBars.hostEventDashboard.tabs.messages',
                        model: 'MdIcon',
                        select: '_id id name iconSrc createdAt updatedAt'
                    });
                }

                await clearCacheBySubstring("mdAppSettings");
                return createResponse('MdAppSettingsResponse', 'SUCCESS', 
                    existingSettings ? 'MdAppSettings updated successfully' : 'MdAppSettings created successfully', 
                    { result: { mdAppSettings } }
                );
            } catch (error) {
                console.error(error);
                return createResponse('MdAppSettingsErrorResponse', 'FAILURE', 'Error creating MdAppSettings', 
                    { errors: [{ field: 'createMdAppSettings', message: error.message }] }
                );
            }
        }
    }
};

module.exports = mdAppSettingsResolvers;

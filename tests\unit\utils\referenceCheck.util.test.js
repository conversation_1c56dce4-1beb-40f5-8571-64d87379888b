const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { findReferences } = require('../../../src/utils/referenceCheck.util');

jest.mock('../../../src/config/referenceMapping', () => ({
    TestEntity: [
        {
            model: {
                find: jest.fn(),
                collection: { collectionName: 'testCollection1' }
            },
            field: 'testField1',
            isArray: false
        },
        {
            model: {
                find: jest.fn(),
                collection: { collectionName: 'testCollection2' }
            },
            field: 'testField2',
            isArray: true
        }
    ]
}));

describe('Reference Check Utility Tests', () => {
    describe('findReferences', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should find references successfully', async () => {
            const testId = 'testId123';
            const mappings = require('../../../src/config/referenceMapping').TestEntity;

            mappings[0].model.find.mockResolvedValueOnce([{ _id: 'ref1' }]);
            mappings[1].model.find.mockResolvedValueOnce([{ _id: 'ref2' }]);

            const references = await findReferences(testId, 'TestEntity');

            expect(references).toEqual(['testCollection1', 'testCollection2']);
            expect(mappings[0].model.find).toHaveBeenCalledWith({ testField1: testId });
            expect(mappings[1].model.find).toHaveBeenCalledWith({ testField2: testId });
        });

        it('should return empty array when no references found', async () => {
            const testId = 'testId123';
            const mappings = require('../../../src/config/referenceMapping').TestEntity;

            mappings[0].model.find.mockResolvedValueOnce([]);
            mappings[1].model.find.mockResolvedValueOnce([]);

            const references = await findReferences(testId, 'TestEntity');

            expect(references).toEqual([]);
            expect(mappings[0].model.find).toHaveBeenCalledWith({ testField1: testId });
            expect(mappings[1].model.find).toHaveBeenCalledWith({ testField2: testId });
        });

        it('should throw error for invalid entity type', async () => {
            const testId = 'testId123';

            await expect(findReferences(testId, 'InvalidEntity'))
                .rejects
                .toThrow('No reference mapping found for entity type: InvalidEntity');
        });

        it('should handle database errors', async () => {
            const testId = 'testId123';
            const mappings = require('../../../src/config/referenceMapping').TestEntity;
            const dbError = new Error('Database error');

            mappings[0].model.find.mockRejectedValueOnce(dbError);

            await expect(findReferences(testId, 'TestEntity'))
                .rejects
                .toThrow(dbError);

            expect(mappings[0].model.find).toHaveBeenCalledWith({ testField1: testId });
            expect(mappings[1].model.find).not.toHaveBeenCalled();
        });

        it('should handle partial references', async () => {
            const testId = 'testId123';
            const mappings = require('../../../src/config/referenceMapping').TestEntity;

            mappings[0].model.find.mockResolvedValueOnce([{ _id: 'ref1' }]);
            mappings[1].model.find.mockResolvedValueOnce([]);

            const references = await findReferences(testId, 'TestEntity');

            expect(references).toEqual(['testCollection1']);
            expect(mappings[0].model.find).toHaveBeenCalledWith({ testField1: testId });
            expect(mappings[1].model.find).toHaveBeenCalledWith({ testField2: testId });
        });
    });
}); 
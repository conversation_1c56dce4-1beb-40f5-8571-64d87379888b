const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');
const webhookRouter = require('../../../src/routes/webhook');
const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
const { createUserFromJson } = require('../../../src/models/User');

jest.mock('../../../src/utils/verifyWebhookSignature.util');
jest.mock('../../../src/models/User');

const app = express();
app.use(bodyParser.json());
app.use('/webhook', webhookRouter);

describe('POST /webhook/user-added', () => {
  it('should handle user added notifications successfully', async () => {
    const mockPayload = {
      data: {
        id: 'user123',
      },
    };

    verifyWebhookSignature.mockReturnValue(mockPayload);
    createUserFromJson.mockResolvedValue();

    const response = await request(app)
      .post('/webhook/user-added')
      .send({ data: { id: 'user123' } })
      .set('svix-id', 'test-id')
      .set('svix-timestamp', 'test-timestamp')
      .set('svix-signature', 'test-signature');

    expect(response.status).toBe(200);
    expect(response.body).toEqual({ message: 'Webhook received successfully' });
    expect(createUserFromJson).toHaveBeenCalledWith(mockPayload);
  });

  it('should return 400 if webhook signature verification fails', async () => {
    verifyWebhookSignature.mockImplementation(() => {
      throw new Error('Invalid signature');
    });

    const response = await request(app)
      .post('/webhook/user-added')
      .send({ data: { id: 'user123' } })
      .set('svix-id', 'test-id')
      .set('svix-timestamp', 'test-timestamp')
      .set('svix-signature', 'test-signature');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({ message: 'Invalid webhook' });
  });
});
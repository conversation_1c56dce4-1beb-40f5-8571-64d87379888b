const MdRole = require("../../models/MdRole");
const { getByIdCache, clearCacheById, setCache } = require("../../utils/cache.util");
const { getPaginationInfo } = require("../../utils/paginationInfo.util");
const { createResponse } = require("../../utils/response.util");
const buildMdRoleQuery = require("./filters/mdRole.filter");

const mdRoleResolvers = {
    Query: {
        getMdRoleById: async (_, { id }) => {
            try {
                let mdRole = await getByIdCache(id) || await MdRole.findById(id);
                if (mdRole) {
                    await setCache(id, mdRole);
                } else {
                    return createResponse('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'getMdRoleById', message: 'MdRole not found' }] });
                }
                return createResponse('MdRoleResponse', 'SUCCESS', 'MdRole fetched successfully', { result: { mdRole } });
            } catch (error) {
                console.error(error);
                return createResponse('MdRoleErrorResponse', 'FAILURE', 'Error getting mdRole', { errors: [{ field: 'getMdRoleById', message: error.message }] });
            }
        },
        getMdRoles: async (_, { filter, pagination }) => {
            try {
                const query = buildMdRoleQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdRole, query, limit, skip);

                const mdRoles = await MdRole.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdRoles.length === 0) {
                    return createResponse('MdRolesResponse', 'FAILURE', 'No mdRoles found', { result: { mdRoles }, pagination: paginationInfo });
                }
                return createResponse('MdRolesResponse', 'SUCCESS', 'MdRoles fetched successfully', { result: { mdRoles }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdRoleErrorResponse', 'FAILURE', 'Error getting mdRoles', { errors: [{ field: 'getMdRoles', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdRole: async (_, { name, description }) => {
            try {
                const mdRole = new MdRole({ name, description });
                await mdRole.save();
                return createResponse('MdRoleResponse', 'SUCCESS', 'MdRole created successfully', { result: { mdRole } });
            } catch (error) {
                console.error(error);
                return createResponse('MdRoleErrorResponse', 'FAILURE', 'Error creating mdRole', { errors: [{ field: 'createMdRole', message: error.message }] });
            }
        },
        updateMdRole: async (_, { id, name, description }) => {
            try {
                const mdRole = await MdRole.findByIdAndUpdate(id, { name, description }, { new: true });
                if (!mdRole) {
                    return createResponse('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'updateMdRole', message: 'MdRole not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdRoleResponse', 'SUCCESS', 'MdRole updated successfully', { result: { mdRole } });
            } catch (error) {
                console.error(error);
                return createResponse('MdRoleErrorResponse', 'FAILURE', 'Error updating mdRole', { errors: [{ field: 'updateMdRole', message: error.message }] });
            }
        },
        deleteMdRole: async (_, { id }) => {
            try {
                const mdRole = await MdRole.findByIdAndDelete(id);
                if (!mdRole) {
                    return createResponse('MdRoleErrorResponse', 'FAILURE', 'MdRole not found', { errors: [{ field: 'deleteMdRole', message: 'MdRole not found' }] });
                }

                await clearCacheById(id);
                return createResponse('MdRoleResponse', 'SUCCESS', 'MdRole deleted successfully', { result: { mdRole } });
            } catch (error) {
                console.error(error);
                return createResponse('MdRoleErrorResponse', 'FAILURE', 'Error deleting mdRole', { errors: [{ field: 'deleteMdRole', message: error.message }] });
            }
        }
    }
}

module.exports = mdRoleResolvers;
const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mediaSchema = new Schema({
    url: { type: String, required: true },
    title: { type: String, required: true },
    description: { type: String },
    party: { type: Schema.Types.ObjectId, ref: 'Party', required: false },
    uploadedAt: { type: Date, default: Date.now },
    tags: [{ type: Schema.Types.ObjectId, ref: 'Tag' }],
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true }
}, { timestamps: true });

const Media = model('Media', mediaSchema);

module.exports = Media;
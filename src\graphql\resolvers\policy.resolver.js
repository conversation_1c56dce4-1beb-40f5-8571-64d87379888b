const Policy = require('../../models/Policy');
const Vendor = require('../../models/Vendor');
const Tag = require('../../models/Tag');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildPolicyQuery = require('./filters/policy.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');

const basePolicyIdSchema = {
    vendor: { type: 'single', model: Vendor },
    tags: { type: 'array', required: false, model: Tag }
};

const policyIdSchema = { ...basePolicyIdSchema };

const createPolicyIdSchema = {
    ...basePolicyIdSchema,
    vendor: { ...basePolicyIdSchema.vendor, required: true }
};

const policyResolvers = {
    Policy: {
        vendor: async (parent) => {
            try {
                return await Vendor.findById(parent.vendor);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor');
            }
        },
        tags: async (parent) => {
            try {
                return await Tag.find({ _id: { $in: parent.tags } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting tags');
            }
        }
    },

    Query: {
        getPolicyById: async (_, { id }) => {
            try {
                let policy = await getByIdCache(id);
                if (!policy) {
                    policy = await Policy.findById(id)
                        .populate('vendor')
                        .populate('tags');
                    
                    if (!policy) {
                        return createResponse('PolicyErrorResponse', 'FAILURE', 'Policy not found', {
                            errors: [{ field: 'id', message: 'Policy not found' }]
                        });
                    }
                    await setCache(id, policy);
                }

                return createResponse('PolicyResponse', 'SUCCESS', 'Policy retrieved successfully', {
                    result: { policy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PolicyErrorResponse', 'FAILURE', 'Error retrieving policy', {
                    errors: [{ field: 'getPolicyById', message: error.message }]
                });
            }
        },

        getPolicies: async (_, { filter, pagination }) => {
            try {
                const query = await buildPolicyQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Policy, query, limit, skip);

                const policies = await Policy.find(query)
                    .populate('vendor')
                    .populate('tags')
                    .limit(limit)
                    .skip(skip)
                    .sort({ order: 1 });

                if (policies.length === 0) {
                    return createResponse('PoliciesResponse', 'FAILURE', 'No policies found', {
                        result: { policies },
                        pagination: paginationInfo
                    });
                }

                return createResponse('PoliciesResponse', 'SUCCESS', 'Policies fetched successfully', {
                    result: { policies },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('PolicyErrorResponse', 'FAILURE', 'Error retrieving policies', {
                    errors: [{ field: 'getPolicies', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createPolicy: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createPolicyIdSchema, 'Policy');
                if (validationError) {
                    return createResponse('PolicyErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const policy = new Policy(input);
                await policy.save();

                const populatedPolicy = await Policy.findById(policy._id)
                    .populate('vendor')
                    .populate('tags');

                return createResponse('PolicyResponse', 'SUCCESS', 'Policy created successfully', {
                    result: { policy: populatedPolicy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PolicyErrorResponse', 'FAILURE', 'Error creating policy', {
                    errors: [{ field: 'createPolicy', message: error.message }]
                });
            }
        },

        updatePolicy: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, policyIdSchema, 'Policy');
                if (validationError) {
                    return createResponse('PolicyErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const policy = await Policy.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                )
                .populate('vendor')
                .populate('tags');

                if (!policy) {
                    return createResponse('PolicyErrorResponse', 'FAILURE', 'Policy not found', {
                        errors: [{ field: 'id', message: 'Policy not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('PolicyResponse', 'SUCCESS', 'Policy updated successfully', {
                    result: { policy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PolicyErrorResponse', 'FAILURE', 'Error updating policy', {
                    errors: [{ field: 'updatePolicy', message: error.message }]
                });
            }
        },

        deletePolicy: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Policy');
                if (references.length > 0) {
                    return createResponse('PolicyErrorResponse', 'FAILURE', 'Policy cannot be deleted', {
                        errors: [{ field: 'id', message: `Policy cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const policy = await Policy.findByIdAndDelete(id);
                
                if (!policy) {
                    return createResponse('PolicyErrorResponse', 'FAILURE', 'Policy not found', {
                        errors: [{ field: 'id', message: 'Policy not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('PolicyResponse', 'SUCCESS', 'Policy deleted successfully', {
                    result: { policy }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PolicyErrorResponse', 'FAILURE', 'Error deleting policy', {
                    errors: [{ field: 'deletePolicy', message: error.message }]
                });
            }
        }
    }
};

module.exports = policyResolvers; 
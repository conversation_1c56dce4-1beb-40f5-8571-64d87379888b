const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const articleTypeDefs = gql`
  ${sharedTypeDef}

  type Source {
    link: String
    mainUrl: String
  }

  type Article {
    id: ID
    headline: String
    description: String
    image: String
    source: Source
    date: String
    approved: Boolean
    userArticleActivity: UserArticleActivity
  }

  type ArticleWrapper {
    article: Article
  }

  type ArticlesWrapper {
    articles: [Article]
  }

  type ArticlesResponse implements Response {
    status: ResponseStatus!
    message: String!
    result: ArticlesWrapper!
    pagination: PaginationInfo!
  }

  type ArticleResponse implements Response {
    status: ResponseStatus!
    message: String!
    result: ArticleWrapper!
  }

  type ArticleErrorResponse implements Response {
    status: ResponseStatus!
    message: String!
    errors: [Error!]!
  }

  union ArticleResult = ArticleResponse | ArticleErrorResponse
  union ArticlesResult = ArticlesResponse | ArticleErrorResponse

  input SourceFilter {
    link: String
    mainUrl: String
  }

  input ArticleFilter {
    id: String
    headline: String
    description: String
    image: String
    source: SourceFilter
    date: String
    dateRange: DateTimeRangeInput
    search: String
  }

  input GetAllArticlesFilter {
    id: String
    headline: String
    approved: Boolean
    search: String
    sort_by: String
    sort_order: Int
    start_date: DateTime
    end_date: DateTime
  }

  input GetAllArticlesPagination {
    skip: Int
    limit: Int
  }

  input UpdateArticleInput {
    headline: String
    description: String
    image: String
    source: SourceInput
    approved: Boolean
  }

  input SourceInput {
    link: String!
    mainUrl: String!
  }

  input CreateArticleInput {
    headline: String!
    description: String!
    image: String!
    source: SourceInput!
    date: DateTime!
    approved: Boolean!
  }

  extend type Query {
    getArticles(filter: ArticleFilter, pagination: PaginationInput): ArticlesResult
    getAllArticles(
      filter: GetAllArticlesFilter
      pagination: GetAllArticlesPagination
    ): ArticlesResult
  }

  extend type Mutation {
    updateArticle(id: ID!, input: UpdateArticleInput!): ArticleResult
    createArticle(input: CreateArticleInput!): ArticleResult
    deleteArticle(id: ID!): ArticleResult
  }
`;

module.exports = articleTypeDefs; 
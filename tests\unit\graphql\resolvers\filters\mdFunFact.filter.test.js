const mongoose = require('mongoose');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');
const buildMdEventTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdEventType.filter');
const buildMdFunFactQuery = require('../../../../../src/graphql/resolvers/filters/mdFunFact.filter');
const MdEventType = require('../../../../../src/models/MdEventType');

jest.mock('../../../../../src/utils/validation.util');
jest.mock('../../../../../src/graphql/resolvers/filters/mdEventType.filter');
jest.mock('../../../../../src/models/MdEventType');

describe('buildMdFunFactQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter provided', async () => {
        const result = await buildMdFunFactQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid id', async () => {
        isValidObjectId.mockReturnValue(true);
        const filter = { id: 'validId123' };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(result).toEqual({ _id: 'validId123' });
        expect(isValidObjectId).toHaveBeenCalledWith('validId123');
    });

    it('should throw error for invalid id', async () => {
        isValidObjectId.mockReturnValue(false);
        const filter = { id: 'invalidId' };
        
        await expect(buildMdFunFactQuery(filter)).rejects.toThrow('Invalid id provided');
        expect(isValidObjectId).toHaveBeenCalledWith('invalidId');
    });

    it('should build query with funFact text search', async () => {
        const filter = { funFact: 'test fact' };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(result).toEqual({
            funFact: { $regex: 'test fact', $options: 'i' }
        });
    });

    it('should handle string eventTypes with valid ID', async () => {
        isValidObjectId.mockReturnValue(true);
        const filter = { eventTypes: 'validEventTypeId' };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(result).toEqual({
            eventTypes: 'validEventTypeId'
        });
    });

    it('should throw error for invalid eventTypes ID', async () => {
        isValidObjectId.mockReturnValue(false);
        const filter = { eventTypes: 'invalidEventTypeId' };
        
        await expect(buildMdFunFactQuery(filter)).rejects.toThrow('Invalid eventTypes ID provided');
    });

    it('should handle object eventTypes with matching results', async () => {
        const eventTypeFilter = { name: 'Birthday' };
        const eventTypeQuery = { name: 'Birthday' };
        const matchingEventTypes = [
            { _id: 'eventType1' },
            { _id: 'eventType2' }
        ];

        buildMdEventTypeQuery.mockResolvedValue(eventTypeQuery);
        MdEventType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue(matchingEventTypes)
        });

        const filter = { eventTypes: eventTypeFilter };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(buildMdEventTypeQuery).toHaveBeenCalledWith(eventTypeFilter);
        expect(MdEventType.find).toHaveBeenCalledWith(eventTypeQuery);
        expect(result).toEqual({
            eventTypes: { $in: ['eventType1', 'eventType2'] }
        });
    });

    it('should handle object eventTypes with no matching results', async () => {
        const eventTypeFilter = { name: 'NonExistent' };
        const eventTypeQuery = { name: 'NonExistent' };

        buildMdEventTypeQuery.mockResolvedValue(eventTypeQuery);
        MdEventType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([])
        });

        const filter = { eventTypes: eventTypeFilter };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(buildMdEventTypeQuery).toHaveBeenCalledWith(eventTypeFilter);
        expect(MdEventType.find).toHaveBeenCalledWith(eventTypeQuery);
        expect(result).toEqual({
            eventTypes: { $in: [] }
        });
    });

    it('should handle object eventTypes with empty query', async () => {
        const eventTypeFilter = {};
        buildMdEventTypeQuery.mockResolvedValue({});

        const filter = { eventTypes: eventTypeFilter };
        
        const result = await buildMdFunFactQuery(filter);
        
        expect(buildMdEventTypeQuery).toHaveBeenCalledWith(eventTypeFilter);
        expect(MdEventType.find).not.toHaveBeenCalled();
        expect(result).toEqual({});
    });
});

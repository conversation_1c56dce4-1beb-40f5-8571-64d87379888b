const buildMdThemeQuery = require('../../../../../src/graphql/resolvers/filters/mdTheme.filter');
const { isValidObjectId } = require('../../../../../src/utils/validation.util');

jest.mock('../../../../../src/utils/validation.util');

describe('buildMdThemeQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query when no filter is provided', () => {
        const result = buildMdThemeQuery();
        expect(result).toEqual({});
    });

    it('should build query with valid id filter', () => {
        const validId = '507f1f77bcf86cd799439011';
        isValidObjectId.mockReturnValue(true);

        const result = buildMdThemeQuery({ id: validId });

        expect(isValidObjectId).toHaveBeenCalledWith(validId);
        expect(result).toEqual({ _id: validId });
    });

    it('should throw error with invalid id filter', () => {
        const invalidId = 'invalid-id';
        isValidObjectId.mockReturnValue(false);

        expect(() => buildMdThemeQuery({ id: invalidId }))
            .toThrow('Invalid id provided');
        expect(isValidObjectId).toHaveBeenCalledWith(invalidId);
    });

    it('should build query with name filter', () => {
        const name = 'Birthday Theme';
        const result = buildMdThemeQuery({ name });

        expect(result).toEqual({
            name: { $regex: name, $options: 'i' }
        });
    });

    it('should build query with description filter', () => {
        const description = 'A beautiful theme';
        const result = buildMdThemeQuery({ description });

        expect(result).toEqual({
            description: { $regex: description, $options: 'i' }
        });
    });

    it('should build query with multiple filters', () => {
        const validId = '507f1f77bcf86cd799439011';
        const name = 'Birthday Theme';
        const description = 'A beautiful theme';
        isValidObjectId.mockReturnValue(true);

        const result = buildMdThemeQuery({
            id: validId,
            name,
            description
        });

        expect(isValidObjectId).toHaveBeenCalledWith(validId);
        expect(result).toEqual({
            _id: validId,
            name: { $regex: name, $options: 'i' },
            description: { $regex: description, $options: 'i' }
        });
    });
}); 
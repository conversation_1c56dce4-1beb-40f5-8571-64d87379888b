const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Task {
        id: ID!
        title: String!
        description: String
        attachments: [Document!]
        comments: [Comment!]
        collaborators: [TaskCollaborator!]
        party: Party!
        status: TaskStatus!
        dueDate: DateTime
        createdBy: User!
        assignedTo: [User!]
    }

    enum TaskStatus {
        IN_PROGRESS
        COMPLETED
        CLOSED
    }

    input TaskInput {
        title: String!
        description: String
        status: TaskStatus
        attachments: [ID!]
        party: ID!
        dueDate: Date
        assignedTo: [ID!]
    }

    input TaskUpdateInput {
        title: String
        description: String
        status: TaskStatus
        attachments: [ID!]
        party: ID
        dueDate: Date
        assignedTo: [ID!]
    }

    input TaskFilter {
        title: String
        description: String
        attachments: DocumentFilterInput
        party: PartyFilterInput
        status: [TaskStatus]
        createdBy: UserFilterInput
        assignedTo: [AssignedToInput]
        dueDate: DateRangeInput
    }

    input AssignedToInput {
        id: ID
        filter: UserFilterInput
    }

    type TaskWrapper {
        task: Task!
    }

    type TasksWrapper {
        tasks: [Task]!
    }

    type TaskResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TaskWrapper!
    }

    type TasksResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TasksWrapper!
        pagination: PaginationInfo!
    }

    type TaskErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union TaskResult = TaskResponse | TaskErrorResponse
    union TasksResult = TasksResponse | TaskErrorResponse

    type Query {
        getTaskById(id: ID!): TaskResult!
        getTasks(pagination: PaginationInput, filter: TaskFilter): TasksResult!
    }

    type Mutation {
        createTask(input: TaskInput!): TaskResult!
        updateTask(id: ID!, input: TaskUpdateInput!): TaskResult!
        deleteTask(id: ID!): TaskResult!

        createTaskComment(taskId: ID!, input: CommentInput!): CommentResult!
        updateTaskComment(taskId: ID!, commentId: ID!, input: CommentUpdateInput!): CommentResult!
        deleteTaskComment(id: ID!): CommentResult!

        createTaskAttachment(taskId: ID!, input: DocumentInput!): DocumentResult!
        deleteTaskAttachment(id: ID!): DocumentResult!
    }
`;
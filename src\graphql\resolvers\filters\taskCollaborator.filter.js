const { isValidObjectId } = require("../../../utils/validation.util");

const buildTaskCollaboratorQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.taskId) {
            if (isValidObjectId(filter.taskId)) {
                query.task = filter.taskId;
            } else {
                throw new Error('Invalid task ID provided');
            }
        }

        if (filter.userId) {
            if (isValidObjectId(filter.userId)) {
                query.user = filter.userId;
            } else {
                throw new Error('Invalid user ID provided');
            }
        }

        if (filter.assignedOn) {
            query.assignedOn = {};
            if (filter.assignedOn.start) {
                query.assignedOn.$gte = filter.assignedOn.start;
            }
            if (filter.assignedOn.end) {
                query.assignedOn.$lte = filter.assignedOn.end;
            }
        }
    }

    return query;
};

module.exports = buildTaskCollaboratorQuery; 
const buildPartyServiceQuery = require('../../../../../src/graphql/resolvers/filters/partyService.filter');

describe('buildPartyServiceQuery', () => {
    it('should return empty query when no filters provided', () => {
        const result = buildPartyServiceQuery();
        expect(result).toEqual({});
    });

    it('should build query with vendorType filter', () => {
        const filter = {
            vendorType: 'someVendorType'
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            vendorType: 'someVendorType'
        });
    });

    it('should build query with vendor filter', () => {
        const filter = {
            vendor: 'someVendor'
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            vendor: 'someVendor'
        });
    });

    it('should build query with city filter', () => {
        const filter = {
            city: 'New York'
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            city: { $regex: /New York/i }
        });
    });

    it('should build query with timeRange filter', () => {
        const filter = {
            timeRange: {
                start: new Date('2023-01-01'),
                end: new Date('2023-12-31')
            }
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            time: {
                $gte: filter.timeRange.start,
                $lte: filter.timeRange.end
            }
        });
    });

    it('should build query with budget range filters', () => {
        const filter = {
            minBudget: 1000,
            maxBudget: 5000
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            budget: {
                $gte: 1000,
                $lte: 5000
            }
        });
    });

    it('should build query with only minBudget filter', () => {
        const filter = {
            minBudget: 1000
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            budget: {
                $gte: 1000
            }
        });
    });

    it('should build query with only maxBudget filter', () => {
        const filter = {
            maxBudget: 5000
        };
        const result = buildPartyServiceQuery(filter);
        expect(result).toEqual({
            budget: {
                $lte: 5000
            }
        });
    });

    it('should handle expenditure range filters correctly', () => {
        expect(buildPartyServiceQuery({ minExpenditure: 100 }))
            .toEqual({
                expenditure: { $gte: 100 }
            });

        expect(buildPartyServiceQuery({ maxExpenditure: 500 }))
            .toEqual({
                expenditure: { $lte: 500 }
            });

        expect(buildPartyServiceQuery({ minExpenditure: 100, maxExpenditure: 500 }))
            .toEqual({
                expenditure: {
                    $gte: 100,
                    $lte: 500
                }
            });

        expect(buildPartyServiceQuery({ minExpenditure: undefined, maxExpenditure: undefined }))
            .toEqual({});
    });
});

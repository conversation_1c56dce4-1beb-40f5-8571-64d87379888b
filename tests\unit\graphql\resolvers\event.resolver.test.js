const mongoose = require('mongoose');
const eventResolvers = require('../../../../src/graphql/resolvers/event.resolver');
const Event = require('../../../../src/models/Event');
const Host = require('../../../../src/models/Host');
const Guest = require('../../../../src/models/Guest');
const MdEventType = require('../../../../src/models/MdEventType');
const MdServiceLocation = require('../../../../src/models/MdServiceLocation');
const Address = require('../../../../src/models/Address');
const Party = require('../../../../src/models/Party');
const PartyService = require('../../../../src/models/PartyService');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildEventQuery = require('../../../../src/graphql/resolvers/filters/event.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const { processCoHosts } = require('../../../../src/utils/coHost.util');

const baseEventIdSchema = {
  eventType: { type: 'single', model: MdEventType },
  mainHost: { type: 'single', model: Host },
  coHosts: { type: 'array', required: false, model: Host },
  guests: { type: 'array', required: false, model: Guest },
  location: { type: 'single', model: Address }
};

const eventIdSchema = { ...baseEventIdSchema };

const createEventIdSchema = {
  ...baseEventIdSchema,
  eventType: { ...baseEventIdSchema.eventType, required: true },
  mainHost: { ...baseEventIdSchema.mainHost, required: true },
  location: { ...baseEventIdSchema.location, required: true }
};

jest.mock('../../../../src/models/Event');
jest.mock('../../../../src/models/Host');
jest.mock('../../../../src/models/Guest');
jest.mock('../../../../src/models/MdEventType');
jest.mock('../../../../src/models/Address');
jest.mock('../../../../src/models/Party');
jest.mock('../../../../src/models/PartyService');
jest.mock('../../../../src/models/MdServiceLocation');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/event.filter');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util');
jest.mock('../../../../src/utils/coHost.util');

describe('Event Resolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Event Field Resolvers', () => {
        describe('eventType', () => {
            it('should return event type successfully', async () => {
                const parent = { eventType: 'eventTypeId' };
                const mockEventType = { _id: 'eventTypeId', name: 'Birthday' };
                
                MdEventType.findById.mockResolvedValue(mockEventType);
                
                const result = await eventResolvers.Event.eventType(parent);
                
                expect(MdEventType.findById).toHaveBeenCalledWith(parent.eventType);
                expect(result).toEqual(mockEventType);
            });

            it('should handle error when getting event type', async () => {
                const parent = { eventType: 'eventTypeId' };
                MdEventType.findById.mockRejectedValue(new Error('Database error'));
                
                await expect(eventResolvers.Event.eventType(parent))
                    .rejects
                    .toThrow('Error getting event type');
            });
        });

        describe('location', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return location when found', async () => {
                const mockLocation = { _id: 'locationId', name: 'Test Location' };
                const parent = { location: 'locationId' };
                
                MdServiceLocation.findById.mockResolvedValue(mockLocation);
                
                const result = await eventResolvers.Event.location(parent);
                
                expect(MdServiceLocation.findById).toHaveBeenCalledWith('locationId');
                expect(result).toEqual(mockLocation);
            });

            it('should throw error when location lookup fails', async () => {
                const parent = { location: 'locationId' };
                const error = new Error('Database error');
                
                MdServiceLocation.findById.mockRejectedValue(error);
                
                await expect(eventResolvers.Event.location(parent))
                    .rejects
                    .toThrow('Error getting location');
                    
                expect(MdServiceLocation.findById).toHaveBeenCalledWith('locationId');
            });

            it('should handle null location ID gracefully', async () => {
                const parent = { location: null };
                const mockLocation = null;

                MdServiceLocation.findById.mockResolvedValue(mockLocation);

                const result = await eventResolvers.Event.location(parent);
                expect(result).toBeNull();
            });
        });

        describe('parties', () => {
            it('should return empty array when parent has no parties', async () => {
                const parent = { _id: 'eventId', parties: [] };
                
                Party.find.mockResolvedValue([]);

                const result = await eventResolvers.Event.parties(parent);
                
                expect(Party.find).toHaveBeenCalledWith({ eventId: parent._id });
                expect(result).toEqual([]);
            });

            it('should return empty array when parent parties field is undefined', async () => {
                const parent = { _id: 'eventId' };
                
                Party.find.mockResolvedValue([]);

                const result = await eventResolvers.Event.parties(parent);
                
                expect(Party.find).toHaveBeenCalledWith({ eventId: parent._id });
                expect(result).toEqual([]);
            });

            it('should return parties when they exist', async () => {
                const mockParties = [
                    { _id: 'party1', name: 'Party 1' },
                    { _id: 'party2', name: 'Party 2' }
                ];
                const parent = { 
                    _id: 'eventId',
                    parties: ['party1', 'party2']
                };

                Party.find.mockResolvedValue(mockParties);

                const result = await eventResolvers.Event.parties(parent);
                
                expect(Party.find).toHaveBeenCalledWith({ eventId: parent._id });
                expect(result).toEqual(mockParties);
            });

            it('should throw error when database query fails', async () => {
                const parent = { 
                    _id: 'eventId',
                    parties: ['party1', 'party2']
                };

                Party.find.mockRejectedValue(new Error('Database error'));

                await expect(eventResolvers.Event.parties(parent))
                    .rejects
                    .toThrow('Error getting parties');
                
                expect(Party.find).toHaveBeenCalledWith({ eventId: parent._id });
            });
        });

        describe('mainHost', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should successfully fetch main host', async () => {
                const mockHost = { _id: 'hostId', name: 'Test Host' };
                const parent = { mainHost: 'hostId' };

                Host.findById = jest.fn().mockResolvedValue(mockHost);

                const result = await eventResolvers.Event.mainHost(parent);

                expect(Host.findById).toHaveBeenCalledWith('hostId');
                expect(result).toEqual(mockHost);
            });

            it('should throw error when Host.findById fails', async () => {
                const parent = { mainHost: 'hostId' };
                const error = new Error('Database error');

                Host.findById = jest.fn().mockRejectedValue(error);

                console.error = jest.fn();

                await expect(eventResolvers.Event.mainHost(parent)).rejects.toThrow('Error getting main host');
                
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });

        describe('coHosts', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return combined unique co-hosts from event and parties', async () => {
                // Mock data
                const eventId = new mongoose.Types.ObjectId();
                const hostId1 = new mongoose.Types.ObjectId();
                const hostId2 = new mongoose.Types.ObjectId();
                const hostId3 = new mongoose.Types.ObjectId();

                const mockParent = {
                    _id: eventId,
                    coHosts: [hostId1, hostId2] // Event's direct co-hosts
                };

                const mockParties = [
                    { coHosts: [hostId2, hostId3] }, // Party with some overlapping co-hosts
                    { coHosts: [] }, // Party with no co-hosts
                    { coHosts: [hostId3] } // Party with duplicate co-host
                ];

                const mockHosts = [
                    { _id: hostId1, name: 'Host 1' },
                    { _id: hostId2, name: 'Host 2' },
                    { _id: hostId3, name: 'Host 3' }
                ];

                // Setup mocks
                Party.find.mockResolvedValue(mockParties);
                Host.find.mockResolvedValue(mockHosts);

                // Execute resolver
                const result = await eventResolvers.Event.coHosts(mockParent);

                // Verify results
                expect(Party.find).toHaveBeenCalledWith({ eventId: eventId });
                expect(Host.find).toHaveBeenCalledWith({
                    _id: { $in: expect.arrayContaining([hostId1, hostId2, hostId3]) }
                });
                expect(result).toEqual(mockHosts);
            });

            it('should handle event with no co-hosts', async () => {
                const eventId = new mongoose.Types.ObjectId();
                const mockParent = {
                    _id: eventId,
                    coHosts: [] // No direct co-hosts
                };

                const mockParties = [
                    { coHosts: [] } // No party co-hosts
                ];

                Party.find.mockResolvedValue(mockParties);
                Host.find.mockResolvedValue([]);

                const result = await eventResolvers.Event.coHosts(mockParent);

                expect(Party.find).toHaveBeenCalledWith({ eventId: eventId });
                expect(Host.find).toHaveBeenCalledWith({ _id: { $in: [] } });
                expect(result).toEqual([]);
            });

            it('should handle undefined coHosts in event', async () => {
                const eventId = new mongoose.Types.ObjectId();
                const hostId = new mongoose.Types.ObjectId();
                
                const mockParent = {
                    _id: eventId,
                    // coHosts field not defined
                };

                const mockParties = [
                    { coHosts: [hostId] }
                ];

                const mockHosts = [
                    { _id: hostId, name: 'Host 1' }
                ];

                Party.find.mockResolvedValue(mockParties);
                Host.find.mockResolvedValue(mockHosts);

                const result = await eventResolvers.Event.coHosts(mockParent);

                expect(Party.find).toHaveBeenCalledWith({ eventId: eventId });
                expect(Host.find).toHaveBeenCalledWith({ _id: { $in: [hostId] } });
                expect(result).toEqual(mockHosts);
            });

            it('should handle database error gracefully', async () => {
                const mockParent = {
                    _id: new mongoose.Types.ObjectId()
                };

                Party.find.mockRejectedValue(new Error('Database error'));

                await expect(eventResolvers.Event.coHosts(mockParent))
                    .rejects
                    .toThrow('Error getting co-hosts');

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockParent._id });
            });
        });

        describe('budget', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should calculate total budget from all party services', async () => {
                const mockEventId = new mongoose.Types.ObjectId();
                const mockParty1Id = new mongoose.Types.ObjectId();
                const mockParty2Id = new mongoose.Types.ObjectId();
                
                // Mock parties data
                const mockParties = [
                    { 
                        _id: mockParty1Id,
                        services: ['service1', 'service2']
                    },
                    {
                        _id: mockParty2Id,
                        services: ['service3']
                    }
                ];

                // Mock party services data
                const mockPartyServices = [
                    { _id: 'service1', budget: 1000 },
                    { _id: 'service2', budget: 2000 },
                    { _id: 'service3', budget: 1500 }
                ];

                // Setup mocks
                Party.find.mockResolvedValue(mockParties);
                PartyService.find.mockResolvedValueOnce([mockPartyServices[0], mockPartyServices[1]])  // For first party
                                         .mockResolvedValueOnce([mockPartyServices[2]]);  // For second party

                const result = await eventResolvers.Event.budget({ _id: mockEventId });

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockEventId });
                expect(PartyService.find).toHaveBeenCalledTimes(2);
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: ['service1', 'service2'] } });
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: ['service3'] } });
                expect(result).toBe(4500); // 1000 + 2000 + 1500
            });

            it('should handle parties with no services', async () => {
                const mockEventId = new mongoose.Types.ObjectId();
                
                // Mock party with no services
                const mockParties = [
                    { _id: new mongoose.Types.ObjectId(), services: [] }
                ];

                Party.find.mockResolvedValue(mockParties);
                PartyService.find.mockResolvedValue([]);

                const result = await eventResolvers.Event.budget({ _id: mockEventId });

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockEventId });
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: [] } });
                expect(result).toBe(0);
            });

            it('should handle party services with no budget', async () => {
                const mockEventId = new mongoose.Types.ObjectId();
                
                const mockParties = [
                    { _id: new mongoose.Types.ObjectId(), services: ['service1'] }
                ];

                const mockPartyServices = [
                    { _id: 'service1' } // No budget field
                ];

                Party.find.mockResolvedValue(mockParties);
                PartyService.find.mockResolvedValue(mockPartyServices);

                const result = await eventResolvers.Event.budget({ _id: mockEventId });

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockEventId });
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: ['service1'] } });
                expect(result).toBe(0);
            });

            it('should throw error when Party.find fails', async () => {
                const mockEventId = new mongoose.Types.ObjectId();
                const error = new Error('Database error');
                
                Party.find.mockRejectedValue(error);

                await expect(eventResolvers.Event.budget({ _id: mockEventId }))
                    .rejects
                    .toThrow('Error calculating total budget');

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockEventId });
            });

            it('should throw error when PartyService.find fails', async () => {
                const mockEventId = new mongoose.Types.ObjectId();
                const error = new Error('Database error');
                
                const mockParties = [
                    { _id: new mongoose.Types.ObjectId(), services: ['service1'] }
                ];

                Party.find.mockResolvedValue(mockParties);
                PartyService.find.mockRejectedValue(error);

                await expect(eventResolvers.Event.budget({ _id: mockEventId }))
                    .rejects
                    .toThrow('Error calculating total budget');

                expect(Party.find).toHaveBeenCalledWith({ eventId: mockEventId });
                expect(PartyService.find).toHaveBeenCalledWith({ _id: { $in: ['service1'] } });
            });
        });

        describe('guests', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return all guests associated with the event through parties', async () => {
                // Mock data
                const eventId = new mongoose.Types.ObjectId();
                const partyId1 = new mongoose.Types.ObjectId();
                const partyId2 = new mongoose.Types.ObjectId();
                const guestId1 = new mongoose.Types.ObjectId();
                const guestId2 = new mongoose.Types.ObjectId();

                const mockParties = [
                    { _id: partyId1, eventId },
                    { _id: partyId2, eventId }
                ];

                const mockGuests = [
                    { _id: guestId1, party: partyId1 },
                    { _id: guestId2, party: partyId2 }
                ];

                // Setup mocks
                Party.find.mockResolvedValue(mockParties);
                Guest.find.mockResolvedValue(mockGuests);

                // Execute resolver
                const result = await eventResolvers.Event.guests({ _id: eventId });

                // Verify results
                expect(Party.find).toHaveBeenCalledWith({ eventId });
                expect(Guest.find).toHaveBeenCalledWith({ 
                    party: { $in: [partyId1, partyId2] } 
                });
                expect(result).toEqual(mockGuests);
            });

            it('should return empty array when no parties found', async () => {
                const eventId = new mongoose.Types.ObjectId();
                
                Party.find.mockResolvedValue([]);
                Guest.find.mockResolvedValue([]);

                const result = await eventResolvers.Event.guests({ _id: eventId });

                expect(Party.find).toHaveBeenCalledWith({ eventId });
                expect(Guest.find).toHaveBeenCalledWith({ party: { $in: [] } });
                expect(result).toEqual([]);
            });

            it('should throw error when Party.find fails', async () => {
                const eventId = new mongoose.Types.ObjectId();
                const error = new Error('Database error');

                Party.find.mockRejectedValue(error);

                await expect(eventResolvers.Event.guests({ _id: eventId }))
                    .rejects
                    .toThrow('Error getting guests');

                expect(Party.find).toHaveBeenCalledWith({ eventId });
                expect(Guest.find).not.toHaveBeenCalled();
            });

            it('should throw error when Guest.find fails', async () => {
                const eventId = new mongoose.Types.ObjectId();
                const partyId = new mongoose.Types.ObjectId();
                const error = new Error('Database error');

                Party.find.mockResolvedValue([{ _id: partyId, eventId }]);
                Guest.find.mockRejectedValue(error);

                await expect(eventResolvers.Event.guests({ _id: eventId }))
                    .rejects
                    .toThrow('Error getting guests');

                expect(Party.find).toHaveBeenCalledWith({ eventId });
                expect(Guest.find).toHaveBeenCalledWith({ 
                    party: { $in: [partyId] } 
                });
            });
        });

        describe('Date Resolvers', () => {
            const mockParent = { _id: 'event123' };
            
            beforeEach(() => {
                jest.clearAllMocks();
            });

            describe('startDate', () => {
                it('should return earliest party time when parties exist', async () => {
                    const mockParties = [{
                        time: new Date('2024-01-01')
                    }];

                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue(mockParties)
                    });

                    const result = await eventResolvers.Event.startDate(mockParent);
                    
                    expect(Party.find).toHaveBeenCalledWith({ eventId: mockParent._id });
                    expect(result).toEqual(mockParties[0].time);
                });

                it('should return event startDate when no parties exist', async () => {
                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue([])
                    });

                    const mockEvent = {
                        startDate: new Date('2024-01-01')
                    };

                    Event.findById.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue(mockEvent)
                    });

                    const result = await eventResolvers.Event.startDate(mockParent);
                    
                    expect(Event.findById).toHaveBeenCalledWith(mockParent._id);
                    expect(result).toEqual(mockEvent.startDate);
                });

                it('should return null when no dates are found', async () => {
                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue([])
                    });

                    Event.findById.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue({})
                    });

                    const result = await eventResolvers.Event.startDate(mockParent);
                    expect(result).toBeNull();
                });

                it('should throw error when database query fails', async () => {
                    Party.find.mockImplementation(() => {
                        throw new Error('Database error');
                    });

                    await expect(eventResolvers.Event.startDate(mockParent))
                        .rejects
                        .toThrow('Error getting start date');
                });
            });

            describe('endDate', () => {
                it('should return latest party time when parties exist', async () => {
                    const mockParties = [{
                        time: new Date('2024-12-31')
                    }];

                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue(mockParties)
                    });

                    const result = await eventResolvers.Event.endDate(mockParent);
                    
                    expect(Party.find).toHaveBeenCalledWith({ eventId: mockParent._id });
                    expect(result).toEqual(mockParties[0].time);
                });

                it('should return event endDate when no parties exist', async () => {
                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue([])
                    });

                    const mockEvent = {
                        endDate: new Date('2024-12-31')
                    };

                    Event.findById.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue(mockEvent)
                    });

                    const result = await eventResolvers.Event.endDate(mockParent);
                    
                    expect(Event.findById).toHaveBeenCalledWith(mockParent._id);
                    expect(result).toEqual(mockEvent.endDate);
                });

                it('should return null when no dates are found', async () => {
                    Party.find.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue([])
                    });

                    Event.findById.mockReturnValue({
                        select: jest.fn().mockReturnThis(),
                        lean: jest.fn().mockResolvedValue({})
                    });

                    const result = await eventResolvers.Event.endDate(mockParent);
                    expect(result).toBeNull();
                });

                it('should throw error when database query fails', async () => {
                    Party.find.mockImplementation(() => {
                        throw new Error('Database error');
                    });

                    await expect(eventResolvers.Event.endDate(mockParent))
                        .rejects
                        .toThrow('Error getting end date');
                });
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getEventById', () => {
            it('should return cached event if available', async () => {
                const id = 'eventId';
                const mockEvent = { _id: id, name: 'Test Event' };
                
                getByIdCache.mockResolvedValue(mockEvent);
                createResponse.mockReturnValue('successResponse');

                const result = await eventResolvers.Query.getEventById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Event.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventResponse',
                    'SUCCESS',
                    'Event retrieved successfully',
                    { result: { event: mockEvent } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache event if not in cache', async () => {
                const id = 'eventId';
                const mockEvent = { _id: id, name: 'Test Event' };
                
                getByIdCache.mockResolvedValue(null);
                Event.findById.mockResolvedValue(mockEvent);
                createResponse.mockReturnValue('successResponse');

                const result = await eventResolvers.Query.getEventById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Event.findById).toHaveBeenCalledWith(id);
                expect(setCache).toHaveBeenCalledWith(id, mockEvent);
                expect(result).toBe('successResponse');
            });

            it('should handle errors when retrieving event', async () => {
                const testId = 'testEventId';
                const testError = new Error('Database connection failed');
                
                getByIdCache.mockResolvedValue(null);
                Event.findById.mockRejectedValue(testError);
                
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error retrieving event',
                    errors: [{ field: 'getEventById', message: 'Database connection failed' }]
                });

                const result = await eventResolvers.Query.getEventById(null, { id: testId });

                expect(getByIdCache).toHaveBeenCalledWith(testId);
                expect(Event.findById).toHaveBeenCalledWith(testId);
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Error retrieving event',
                    {
                        errors: [{ field: 'getEventById', message: 'Database connection failed' }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error retrieving event',
                    errors: [{ field: 'getEventById', message: 'Database connection failed' }]
                });
            });

            it('should return error response when event is not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                Event.findById.mockResolvedValue(null);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Event not found',
                    errors: [{ field: 'id', message: 'Event not found' }]
                });

                const result = await eventResolvers.Query.getEventById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Event.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Event not found',
                    {
                        errors: [{ field: 'id', message: 'Event not found' }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Event not found',
                    errors: [{ field: 'id', message: 'Event not found' }]
                });
            });
        });

        describe('getEvents', () => {
            it('should return events successfully with pagination', async () => {
                const mockEvents = [
                    { _id: 'event1', name: 'Event 1' },
                    { _id: 'event2', name: 'Event 2' }
                ];
                const mockPaginationInfo = { totalPages: 1, totalItems: 2 };

                // Mock buildEventQuery
                buildEventQuery.mockResolvedValue({
                    query: {},
                    applyPostQueryFilters: jest.fn().mockResolvedValue(mockEvents)
                });

                // Mock getPaginationInfo
                getPaginationInfo.mockResolvedValue(mockPaginationInfo);

                // Mock Event.find chain
                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockEvents)
                };
                Event.find.mockReturnValue(mockFind);

                // Mock createResponse
                createResponse.mockReturnValue('successResponse');

                const result = await eventResolvers.Query.getEvents(null, {
                    pagination: { limit: 10, skip: 0 }
                });

                expect(Event.find).toHaveBeenCalledWith({});
                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
                expect(getPaginationInfo).toHaveBeenCalledWith(Event, {}, 10, 0);
                expect(createResponse).toHaveBeenCalledWith(
                    'EventsResponse',
                    'SUCCESS',
                    'Events fetched successfully',
                    {
                        result: { events: mockEvents },
                        pagination: mockPaginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should return failure response when no events found', async () => {
                const mockEvents = [];
                const mockPaginationInfo = {
                    totalCount: 0,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                buildEventQuery.mockResolvedValue({
                    query: {},
                    applyPostQueryFilters: jest.fn().mockResolvedValue(mockEvents)
                });

                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockEvents)
                };
                Event.find.mockReturnValue(mockFind);

                getPaginationInfo.mockResolvedValue(mockPaginationInfo);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await eventResolvers.Query.getEvents(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

                expect(result.status).toBe('FAILURE');
                expect(result.message).toBe('No events found');
                expect(result.result.events).toEqual([]);
            });

            it('should handle errors and return error response', async () => {
                const error = new Error('Database error');
                buildEventQuery.mockRejectedValue(error);

                createResponse.mockImplementation((type, status, message, data) => ({
                    type, status, message, ...data
                }));

                const result = await eventResolvers.Query.getEvents(null, { filter: {}, pagination: { limit: 10, skip: 0 } });

                expect(result.status).toBe('FAILURE');
                expect(result.type).toBe('EventErrorResponse');
                expect(result.errors).toEqual([
                    { field: 'getEvents', message: 'Database error' }
                ]);
            });

            it('should use default pagination values when not provided', async () => {
                const mockEvents = [{ id: '1', name: 'Event 1' }];
                const mockPaginationInfo = {
                    totalCount: 1,
                    hasNextPage: false,
                    hasPreviousPage: false
                };

                buildEventQuery.mockResolvedValue({
                    query: {},
                    applyPostQueryFilters: jest.fn().mockResolvedValue(mockEvents)
                });

                const mockFind = {
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mockEvents)
                };
                Event.find.mockReturnValue(mockFind);

                getPaginationInfo.mockResolvedValue(mockPaginationInfo);

                await eventResolvers.Query.getEvents(null, { filter: {} });

                expect(mockFind.limit).toHaveBeenCalledWith(10);
                expect(mockFind.skip).toHaveBeenCalledWith(0);
            });
        });

        describe('getUserEvents', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should handle errors gracefully', async () => {
                const userId = 'testUserId';
                const error = new Error('Database error');

                Host.findOne.mockRejectedValue(error);
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await eventResolvers.Query.getUserEvents(null, {
                    userId,
                    pagination: { limit: 10, skip: 0 }
                });

                expect(result).toEqual({
                    type: 'EventErrorResponse',
                    status: 'FAILURE',
                    message: 'Error retrieving user events',
                    errors: [{ field: 'getUserEvents', message: error.message }]
                });
            });

            it('should correctly identify user with multiple roles', async () => {
                const userId = 'testUserId';
                const hostId = 'hostId';
                const partyId = 'partyId';
                const eventId = 'eventId';

                const mockHost = { _id: hostId, userId };
                const mockGuest = { _id: 'guestId', party: partyId };
                const mockParty = { _id: partyId, eventId };
                const mockEvent = {
                    _id: eventId,
                    mainHost: {
                        equals: jest.fn().mockReturnValue(true)
                    },
                    coHosts: [{
                        equals: jest.fn().mockReturnValue(true)
                    }],
                    toObject: () => ({
                        _id: eventId,
                        mainHost: hostId,
                        coHosts: [hostId]
                    })
                };

                Host.findOne.mockResolvedValue(mockHost);
                Guest.find.mockResolvedValue([mockGuest]);
                Party.find.mockResolvedValue([mockParty]);
                Party.exists.mockResolvedValue(true);
                Host.exists.mockResolvedValue(true);
                buildEventQuery.mockResolvedValue({
                    query: {},
                    applyPostQueryFilters: events => events
                });
                Event.find.mockReturnValue({
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue([mockEvent])
                });
                getPaginationInfo.mockResolvedValue({
                    totalItems: 1,
                    totalPages: 1,
                    currentPage: 1,
                    pageSize: 10,
                    skip: 0
                });
                createResponse.mockImplementation((type, status, message, data) => ({
                    type,
                    status,
                    message,
                    ...data
                }));

                const result = await eventResolvers.Query.getUserEvents(null, {
                    userId,
                    pagination: { limit: 10, skip: 0 }
                });

                expect(result.result.events[0].status).toEqual(['MAIN_HOST', 'CO_HOST', 'GUEST']);
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createEvent', () => {
            const mockInput = {
                name: 'Test Event',
                description: 'Test Description',
                eventType: 'eventTypeId',
                location: 'locationId',
                coHosts: [
                    { name: 'Co-Host 1', email: '<EMAIL>' },
                    { name: 'Co-Host 2', email: '<EMAIL>' }
                ]
            };

            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return error when reference validation fails', async () => {
                const validationError = {
                    message: 'Invalid references',
                    errors: [{ field: 'eventType', message: 'Invalid event type reference' }]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });

                const result = await eventResolvers.Mutation.createEvent(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(processCoHosts).not.toHaveBeenCalled();
                expect(Event.prototype.save).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });
            });

            it('should return error when co-host processing fails', async () => {
                const coHostError = {
                    errors: [{ field: 'coHosts', message: 'Invalid co-host email' }]
                };

                validateReferences.mockResolvedValue(null);
                processCoHosts.mockResolvedValue(coHostError);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Invalid co-host information',
                    errors: coHostError.errors
                });

                const result = await eventResolvers.Mutation.createEvent(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(processCoHosts).toHaveBeenCalledWith(mockInput.coHosts);
                expect(Event.prototype.save).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Invalid co-host information',
                    { errors: coHostError.errors }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Invalid co-host information',
                    errors: coHostError.errors
                });
            });

            it('should handle unexpected errors during event creation', async () => {
                const error = new Error('Database error');

                validateReferences.mockResolvedValue(null);
                processCoHosts.mockResolvedValue({ coHostIds: ['coHostId1'] });
                Event.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating event',
                    errors: [{ field: 'createEvent', message: error.message }]
                });

                const result = await eventResolvers.Mutation.createEvent(null, { input: mockInput });

                expect(validateReferences).toHaveBeenCalled();
                expect(processCoHosts).toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Error creating event',
                    { errors: [{ field: 'createEvent', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating event',
                    errors: [{ field: 'createEvent', message: error.message }]
                });
            });
        });

        describe('deleteEvent', () => {
            it('should check for references before deletion', async () => {
                const id = 'eventId';
                const references = ['Collection1', 'Collection2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await eventResolvers.Mutation.deleteEvent(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Event');
                expect(Event.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Event cannot be deleted',
                    {
                        errors: [{ 
                            field: 'deleteEvent', 
                            message: 'Event cannot be deleted as it is being used in: Collection1, Collection2' 
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should delete event if no references exist', async () => {
                const id = 'eventId';
                const mockEvent = { _id: id, name: 'Test Event' };
                
                findReferences.mockResolvedValue([]);
                Event.findByIdAndDelete.mockResolvedValue(mockEvent);
                createResponse.mockReturnValue('successResponse');

                const result = await eventResolvers.Mutation.deleteEvent(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Event');
                expect(Event.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'EventResponse',
                    'SUCCESS',
                    'Event deleted successfully',
                    { result: { event: mockEvent } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response if an error occurs during deletion', async () => {
                const id = 'eventId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                Event.findByIdAndDelete.mockRejectedValue(error);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting event',
                    errors: [{ field: 'deleteEvent', message: error.message }]
                });

                const result = await eventResolvers.Mutation.deleteEvent(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Event');
                expect(Event.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Error deleting event',
                    { errors: [{ field: 'deleteEvent', message: error.message }] }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting event',
                    errors: [{ field: 'deleteEvent', message: error.message }]
                });
            });
        });

        describe('updateEvent', () => {
            const mockId = 'eventId123';
            const mockInput = {
                name: 'Updated Event',
                description: 'Updated description',
                coHosts: ['coHostId1', 'coHostId2']
            };

            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should return error when validation fails', async () => {
                const validationError = {
                    message: 'Validation failed',
                    errors: [{ field: 'name', message: 'Invalid name' }]
                };

                validateReferences.mockResolvedValueOnce(validationError);
                createResponse.mockReturnValueOnce('errorResponse');

                const result = await eventResolvers.Mutation.updateEvent(null, { 
                    id: mockId, 
                    input: mockInput 
                });

                expect(validateReferences).toHaveBeenCalled();
                expect(Event.findById).not.toHaveBeenCalled();
                expect(Event.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error when event is not found', async () => {
                validateReferences.mockResolvedValueOnce(null);
                Event.findById.mockResolvedValueOnce(null);
                createResponse.mockReturnValueOnce('errorResponse');

                const result = await eventResolvers.Mutation.updateEvent(null, { 
                    id: mockId, 
                    input: mockInput 
                });

                expect(Event.findById).toHaveBeenCalledWith(mockId);
                expect(Event.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Event not found',
                    { errors: [{ field: 'id', message: 'Event not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error when coHost processing fails', async () => {
                const mockEvent = { _id: mockId };
                const coHostError = { 
                    errors: [{ field: 'coHost', message: 'Invalid coHost' }] 
                };

                validateReferences.mockResolvedValueOnce(null);
                Event.findById.mockResolvedValueOnce(mockEvent);
                processCoHosts.mockResolvedValueOnce(coHostError);
                createResponse.mockReturnValueOnce('errorResponse');

                const result = await eventResolvers.Mutation.updateEvent(null, { 
                    id: mockId, 
                    input: mockInput 
                });

                expect(Event.findByIdAndUpdate).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Invalid co-host information',
                    { errors: coHostError.errors }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors', async () => {
                const error = new Error('Database error');
                
                validateReferences.mockResolvedValueOnce(null);
                Event.findById.mockRejectedValueOnce(error);
                createResponse.mockReturnValueOnce('errorResponse');

                const result = await eventResolvers.Mutation.updateEvent(null, { 
                    id: mockId, 
                    input: mockInput 
                });

                expect(createResponse).toHaveBeenCalledWith(
                    'EventErrorResponse',
                    'FAILURE',
                    'Error updating event',
                    { errors: [{ field: 'updateEvent', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
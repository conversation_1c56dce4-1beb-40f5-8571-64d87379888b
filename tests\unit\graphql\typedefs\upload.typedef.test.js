const { gql } = require('graphql-tag');
const uploadTypeDef = require('../../../../src/graphql/typedefs/upload.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('uploadTypeDef', () => {
  it('should contain the Upload type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      scalar Upload

      type File {
        url: String!
        key: String!
        originalName: String
      }

      type DeletedFile {
        key: String!
        url: String!
      }

      type UploadError {
        filename: String
        message: String
      }

      type BulkUploadResult {
        successful: [File!]!
        failures: [UploadError!]!
        totalProcessed: Int!
        successCount: Int!
        failureCount: Int!
      }

      type FileUploadWrapper {
        bulkResult: BulkUploadResult
      }

      type FileDeleteWrapper {
        file: DeletedFile
      }

      type FileUploadResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: FileUploadWrapper
      }

      type FileDeleteResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: FileDeleteWrapper
      }

      type FileErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union FileResult = FileUploadResponse | FileErrorResponse
      union FileDeleteResult = FileDeleteResponse | FileErrorResponse

      extend type Mutation {
        uploadFiles(files: [Upload!]!, containerName: String): FileResult!
        deleteFile(key: String!, containerName: String): FileDeleteResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(uploadTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
}); 
const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const liveLocationTypeDefs = gql`
    ${sharedTypeDef}

    enum LiveLocationTrackingStatus {
        ALLOW
        DISALLOW
        PENDING
    }

    type LiveLocationCoordinates {
        latitude: Float
        longitude: Float
    }

    type LiveLocation {
        id: ID!
        locationId: String
        coordinates: LiveLocationCoordinates
        allowLiveTracking: LiveLocationTrackingStatus!
        ETA: String
        guest: Guest!
    }

    type LiveLocationWrapper {
        liveLocation: LiveLocation!
    }

    type LiveLocationsWrapper {
        liveLocations: [LiveLocation!]!
    }

    type LiveLocationMessageWrapper {
        message: String!
    }

    type LiveLocationResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: LiveLocationWrapper!
    }

    type LiveLocationMessageResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: LiveLocationMessageWrapper!
    }

    type SendLiveLocationReminderResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: Boolean!
    }

    type LiveLocationsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: LiveLocationsWrapper!
        pagination: PaginationInfo!
    }

    type LiveLocationErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    input LiveLocationFilter {
        allowLiveTracking: Boolean
    }

    union LiveLocationResult = LiveLocationResponse | LiveLocationErrorResponse
    union LiveLocationsResult = LiveLocationsResponse | LiveLocationErrorResponse
    union LiveLocationMessageResult = LiveLocationMessageResponse | LiveLocationErrorResponse
    union SendLiveLocationReminderResult = SendLiveLocationReminderResponse | LiveLocationErrorResponse

    type Query {
        getLiveLocationByGuestId(guestId: ID!): LiveLocationResult!
        getLiveLocationsByPartyId(partyId: ID!, filter: LiveLocationFilter, pagination: PaginationInput): LiveLocationsResult!
        requestLiveLocationFormClient(eventId: ID!, partyId: ID!): LiveLocationMessageResult!
    }

    input UpdateLiveLocationInput {
        locationId: String
        coordinates: CoordinatesInput
        allowLiveTracking: LiveLocationTrackingStatus
    }

    input CoordinatesInput {
        latitude: Float
        longitude: Float
    }

    type Mutation {
        updateLiveLocation(guestId: ID!, input: UpdateLiveLocationInput!): LiveLocationResult!
        sendLiveLocationReminder(partyId: ID!): SendLiveLocationReminderResult!
    }
`

module.exports = liveLocationTypeDefs;

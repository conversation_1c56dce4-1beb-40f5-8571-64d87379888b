const buildTransactionQuery = (filters) => {
    const query = {};

    if (filters) {
        if (filters.description) {
            query.description = { $regex: filters.description, $options: 'i' };
        }
        if (filters.type) {
            query.type = filters.type;
        }
        if (filters.method) {
            query.method = filters.method;
        }
        if (filters.timeStamp) {
            if (filters.timeStamp.start && filters.timeStamp.end) {
                query.timeStamp = { $gte: filters.timeStamp.start, $lte: filters.timeStamp.end };
            } else if (filters.timeStamp.start) {
                query.timeStamp = { $gte: filters.timeStamp.start };
            } else if (filters.timeStamp.end) {
                query.timeStamp = { $lte: filters.timeStamp.end };
            }
        }
        if (filters.minAmount !== undefined) {
            query.amount = query.amount || {};
            query.amount.$gte = filters.minAmount;
        }
        if (filters.maxAmount !== undefined) {
            query.amount = query.amount || {};
            query.amount.$lte = filters.maxAmount;
        }
    }

    return query;
};

module.exports = buildTransactionQuery;
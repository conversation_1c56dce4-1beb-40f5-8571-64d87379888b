const { isValidObjectId } = require("../../../utils/validation.util");
const buildMdPartyTypeQuery = require("./mdPartyType.filter");
const MdPartyType = require("../../../models/MdPartyType");

const buildMdChecklistQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.title) {
            query.title = { $regex: filter.title, $options: 'i' };
        }

        if (filter.description) {
            query.description = { $regex: filter.description, $options: 'i' };
        }

        if (filter.partyType) {
            if (typeof filter.partyType === 'string') {
                if (isValidObjectId(filter.partyType)) {
                    query.partyType = filter.partyType;
                } else {
                    throw new Error('Invalid partyType ID provided');
                }
            } else if (typeof filter.partyType === 'object') {
                const partyTypeQuery = await buildMdPartyTypeQuery(filter.partyType);
                if (Object.keys(partyTypeQuery).length > 0) {
                    const matchingPartyTypes = await MdPartyType.find(partyTypeQuery).select('_id');
                    if (matchingPartyTypes.length > 0) {
                        query.partyType = { $in: matchingPartyTypes.map(pt => pt._id) };
                    } else {
                        query.partyType = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMdChecklistQuery;

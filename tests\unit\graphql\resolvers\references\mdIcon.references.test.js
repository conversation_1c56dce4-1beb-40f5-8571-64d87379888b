const findReferences = require('../../../../../src/graphql/resolvers/references/mdIcon.references');
const MdFeature = require('../../../../../src/models/MdFeature');
const MdPartner = require('../../../../../src/models/MdPartner');
const MdPartnerCategory = require('../../../../../src/models/MdPartnerCategory');
const MdHostLayout = require('../../../../../src/models/MdHostLayout');
const MdVendorType = require('../../../../../src/models/MdVendorType');
const MdAppSettings = require('../../../../../src/models/MdAppSettings');

jest.mock('../../../../../src/models/MdFeature');
jest.mock('../../../../../src/models/MdPartner');
jest.mock('../../../../../src/models/MdPartnerCategory');
jest.mock('../../../../../src/models/MdHostLayout');
jest.mock('../../../../../src/models/MdVendorType');
jest.mock('../../../../../src/models/MdAppSettings');

describe('findReferences', () => {
    const iconId = 'test-icon-id';

    beforeEach(() => {
        MdFeature.find.mockClear();
        MdPartner.find.mockClear();
        MdPartnerCategory.find.mockClear();
        MdHostLayout.find.mockClear();
        MdVendorType.find.mockClear();
        MdAppSettings.find.mockClear();
    });

    test('should return MdFeature when references exist', async () => {
        MdFeature.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdFeature');
    });

    test('should return MdPartner when references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdPartner');
    });

    test('should return MdPartnerCategory when references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([]);
        MdPartnerCategory.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdPartnerCategory');
    });

    test('should return MdHostLayout (navBar fields) when references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([]);
        MdPartnerCategory.find.mockResolvedValue([]);
        MdHostLayout.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdHostLayout (navBar fields)');
    });

    test('should return MdVendorType when references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([]);
        MdPartnerCategory.find.mockResolvedValue([]);
        MdHostLayout.find.mockResolvedValue([]);
        MdVendorType.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdVendorType');
    });

    test('should return MdAppSettings when references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([]);
        MdPartnerCategory.find.mockResolvedValue([]);
        MdHostLayout.find.mockResolvedValue([]);
        MdVendorType.find.mockResolvedValue([]);
        MdAppSettings.find.mockResolvedValue([{}]);
        const result = await findReferences(iconId);
        expect(result).toContain('MdAppSettings');
    });

    test('should return an empty array when no references exist', async () => {
        MdFeature.find.mockResolvedValue([]);
        MdPartner.find.mockResolvedValue([]);
        MdPartnerCategory.find.mockResolvedValue([]);
        MdHostLayout.find.mockResolvedValue([]);
        MdVendorType.find.mockResolvedValue([]);
        MdAppSettings.find.mockResolvedValue([]);
        const result = await findReferences(iconId);
        expect(result).toEqual([]);
    });
});
const MdHostLayout = require('../../models/MdHostLayout');
const MdIcon = require('../../models/MdIcon');
const { createResponse } = require('../../utils/response.util');

const HostLayoutResolvers = {

    HostLayout: {
        navBar: {
            home: async (parent) => {
                return await MdIcon.findById(parent.home);
            },
            planning: async (parent) => {
                return await MdIcon.findById(parent.planning);
            },
            guests: async (parent) => {
                return await MdIcon.findById(parent.guests);
            },
            apps: async (parent) => {
                return await MdIcon.findById(parent.apps);
            },
            messages: async (parent) => {
                return await MdIcon.findById(parent.messages);
            }
        }
    },

    Query: {
        getHostLayout: async () => {
            try {
                const hostLayout = await MdHostLayout.findOne().populate({
                    path: 'navBar',
                    populate: {
                        path: 'home planning guests apps messages',
                        model: 'MdIcon'
                    }
                });

                if (!hostLayout) {
                    return createResponse('HostLayoutErrorResponse', 'FAILURE', 'MdHostLayout not found', { errors: [{ field: 'getHostLayout', message: 'MdHostLayout not found' }] });
                }

                return createResponse('HostLayoutResponse', 'SUCCESS', 'MdHostLayout fetched successfully', { result: { hostLayout } });
            } catch (error) {
                return createResponse('HostLayoutErrorResponse', 'FAILURE', 'Error getting MdHostLayout', { errors: [{ field: 'getHostLayout', message: error.message }] });
            }
        },


       
    },

    Mutation: {
        createHostLayout: async (_, { input }) => {
            try {
                const { navBar } = input;

                // Create NavBar
                const newHostLayout = new MdHostLayout({
                    navBar: {
                        home: navBar.home,
                        planning: navBar.planning,
                        guests: navBar.guests,
                        apps: navBar.apps,
                        messages: navBar.messages
                    }
                });

                await newHostLayout.save();

                const populatedHostLayout = await MdHostLayout.findById(newHostLayout._id).populate({
                    path: 'navBar',
                    populate: {
                        path: 'home planning guests apps messages',
                        model: 'MdIcon'
                    }
                });

                return createResponse('HostLayoutResponse', 'SUCCESS', 'MdHostLayout created successfully', { result: { hostLayout: populatedHostLayout } });
            } catch (error) {
                return createResponse('HostLayoutErrorResponse', 'FAILURE', 'Error creating MdHostLayout', { errors: [{ field: 'createHostLayout', message: error.message }] });
            }
        },

        updateHostLayout: async (_, { input }) => {
            try {
                const { id, navBar } = input;

                const hostLayout = await MdHostLayout.findById(id);

                if (!hostLayout) {
                    return createResponse('HostLayoutErrorResponse', 'FAILURE', 'MdHostLayout not found', { errors: [{ field: 'updateHostLayout', message: 'MdHostLayout not found' }] });
                }

                // Update NavBar fields if provided
                if (navBar) {
                    Object.keys(navBar).forEach(key => {
                        if (navBar[key]) {
                            hostLayout.navBar[key] = navBar[key];
                        }
                    });
                }

                await hostLayout.save();

                const updatedHostLayout = await MdHostLayout.findById(id).populate({
                    path: 'navBar',
                    populate: {
                        path: 'home planning guests apps messages',
                        model: 'MdIcon'
                    }
                });

                return createResponse('HostLayoutResponse', 'SUCCESS', 'MdHostLayout updated successfully', { result: { hostLayout: updatedHostLayout } });
            } catch (error) {
                return createResponse('HostLayoutErrorResponse', 'FAILURE', 'Error updating MdHostLayout', { errors: [{ field: 'updateHostLayout', message: error.message }] });
            }
        },

        deleteHostLayout: async (_, { id }) => {
            try {
                const hostLayout = await MdHostLayout.findById(id);

                if (!hostLayout) {
                    return createResponse('HostLayoutErrorResponse', 'FAILURE', 'MdHostLayout not found', { errors: [{ field: 'deleteHostLayout', message: 'MdHostLayout not found' }] });
                }

                await MdHostLayout.findByIdAndDelete(id);

                return createResponse('HostLayoutResponse', 'SUCCESS', 'MdHostLayout deleted successfully', { result: { hostLayout } });
            } catch (error) {
                return createResponse('HostLayoutErrorResponse', 'FAILURE', 'Error deleting MdHostLayout', { errors: [{ field: 'deleteHostLayout', message: error.message }] });
            }
        }
    }

}

module.exports = HostLayoutResolvers;

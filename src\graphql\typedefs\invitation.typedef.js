const { gql } = require('apollo-server-express');

const invitationTypeDefs = gql`
    type Invitation {
        _id: ID!
        message: String
        media: [Media]
        savedGuests: [Guest]
        sentToGuests: [Guest]
    }

    type InvitationSettings {
        _id: ID!
        is_guest_list_public: Boolean
        additional_guest_allowed: Boolean
        additional_guest_limit: Int
        allow_guest_to_add_photo: Boolean
        send_auto_reminder_to_all_guests: Boolean
    }

    type InvitationWrapper {
        invitation: Invitation!
    }

    type InvitationResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: InvitationWrapper!
    }
    
    type InvitationErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    input UpdateInvitationInput {
        message: String
        media: [ID!]
    }

    input SendInvitationToInput {
        invitationId: ID!
        addGuests: [ID!]
        save: Boolean
        message: String
        media: [ID!]
    }

    union InvitationResult = InvitationResponse | InvitationErrorResponse
    
    type Mutation {
        addMediaToInvitation(invitationId: ID!, media: [ID!]!): InvitationResult!
        updateInvitation(invitationId: ID!, invitation: InvitationInput!): InvitationResult!
        removeMediaFromInvitation(invitationId: ID!, media: [ID!]!): InvitationResult!
        
        sendInvitationTo(sendInvitationToInput: SendInvitationToInput!): InvitationResult!
    }
`

module.exports = invitationTypeDefs;
const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mdInvitationTemplateSchema = new Schema({
    name: {type: String,required: true,maxlength: 50},
    imageUrl: {type: String,required: true},
    tags: [{type: mongoose.Schema.Types.ObjectId,ref: 'Tag'}]
}, {timestamps: true,collection: 'md_invitation_templates'});

const MdInvitationTemplate = model('MdInvitationTemplate', mdInvitationTemplateSchema);

module.exports = MdInvitationTemplate;
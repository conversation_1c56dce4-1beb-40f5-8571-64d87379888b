const { model, Schema } = require('mongoose');

const mdVendorTypeSchema = new Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    primaryFeature: { type: Schema.Types.ObjectId, ref: 'MdFeature' },
    features: [{ type: Schema.Types.ObjectId, ref: 'MdFeature' }],
    mainFeatures: [{ type: Schema.Types.ObjectId, ref: 'MdFeature' }],
    bannerImage: { type: String },
    speciality: { type: String },
    icon: { type: Schema.Types.ObjectId, ref: 'MdIcon' },
    partners: [{ type: Schema.Types.ObjectId, ref: 'MdPartner' }]
}, { timestamps: true, collection: 'md_vendor_types' });

const MdVendorType = model('MdVendorType', mdVendorTypeSchema);

module.exports = MdVendorType;
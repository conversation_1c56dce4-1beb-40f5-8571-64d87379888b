const Media = require('../../models/Media');
const Tag = require('../../models/Tag');
const Party = require('../../models/Party');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildMediaQuery = require('./filters/media.filter');
const { mongoose } = require('mongoose');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');
const MediaFolder = require('../../models/MediaFolder');
const { getArchivesAlbum, deleteMediaAndRelatedData } = require('../../utils/media.util');

const baseMediaIdSchema = {
    tags: { type: 'array', model: Tag },
    party: { type: 'single', model: Party }
};

const mediaIdSchema = { ...baseMediaIdSchema };

const createMediaIdSchema = {
    ...baseMediaIdSchema, 
};

const mediaResolvers = {
    Media: {
        tags: async (media) => {
            try{
                return await Tag.find({ _id: { $in: media.tags } });
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        owner: async (media) => {
            try {
                return await User.findById(media.owner);
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        party: async (media) => {
            try {
                return await Party.findById(media.party);
            } catch (error) {
                console.error(error);
                throw error;
            }
        }
    },
    Query: {
        getMediaById: async (_, { id }) => {
            try {
                let media = await getByIdCache(id);
                if (!media) {
                    media = await Media.findById(id).populate('tags');
                    if (!media) {
                        return createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { 
                            errors: [{ field: 'id', message: 'Media not found' }] 
                        });
                    }
                    await setCache(id, media);
                }
                return createResponse('MediaResponse', 'SUCCESS', 'Media fetched successfully', { 
                    result: { media } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error retrieving media', { 
                    errors: [{ field: 'getMediaById', message: error.message }] 
                });
            }
        },
        getMedias: async (_, { filter, pagination }) => {
            try {
                const query = await buildMediaQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Media, query, limit, skip);

                const medias = await Media.find(query)
                    .populate('tags')
                    .sort({ createdAt: -1 })
                    .limit(limit)
                    .skip(skip);

                if (medias.length === 0) {
                    return createResponse('MediasResponse', 'FAILURE', 'No media found', { result: { medias }, pagination: paginationInfo });
                }
                return createResponse('MediasResponse', 'SUCCESS', 'Media fetched successfully', { result: { medias }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error retrieving media', { errors: [{ field: 'getMedias', message: error.message }] });
            }
        }
    },
    Mutation: {
        createMedia: async (_, { input }, context) => {
            try {
                const referenceValidationErrors = await validateReferences(input, createMediaIdSchema, 'Media');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createMedia',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('MediaErrorResponse', 'FAILURE', 'Invalid media information', { errors });
                }
                const media = new Media({ ...input, owner: context.user._id });
                await media.save();
                await media.populate('tags');
                return createResponse('MediaResponse', 'SUCCESS', 'Media created successfully', { result: { media } });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error creating media', { errors: [{ field: 'createMedia', message: error.message }] });
            }
        },
        updateMedia: async (_, { id, input }) => {
            try {
                const referenceValidationErrors = await validateReferences(input, mediaIdSchema, 'Media');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'updateMedia',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('MediaErrorResponse', 'FAILURE', 'Invalid media information', { errors });
                }

                const media = await Media.findByIdAndUpdate(id, input, { new: true }).populate('tags').populate('owner');
                if (!media) {
                    return createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { errors: [{ field: 'id', message: 'Media not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MediaResponse', 'SUCCESS', 'Media updated successfully', { result: { media } });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error updating media', { errors: [{ field: 'updateMedia', message: error.message }] });
            }
        },
        favoriteMedia: async (_, { id }, context) => {
            try {
                const media = await Media.findById(id);
                if (!media) {
                    return createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { errors: [{ field: 'id', message: 'Media not found' }] });
                }
                const userId = context.user?._id;
                if (!userId) {
                    return createResponse('MediaErrorResponse', 'FAILURE', 'User not authenticated', { 
                        errors: [{ field: 'favoriteMedia', message: 'User must be authenticated to favorite media' }] 
                    });
                }
                // Find or create "MY FAVORITES ALBUM" for the user
                let favoritesAlbum = await MediaFolder.findOne({
                    owner: userId,
                    name: 'MY FAVORITES ALBUM'
                });

                if (!favoritesAlbum) {
                    favoritesAlbum = new MediaFolder({
                        name: 'MY FAVORITES ALBUM',
                        owner: userId,
                        category: 'ALBUM'
                    });
                    await favoritesAlbum.save();
                }

                // Add media to favorites album if not already present
                if (!favoritesAlbum.media.includes(media._id)) {
                    favoritesAlbum.media.push(media._id);
                    await favoritesAlbum.save();
                }
               
                return createResponse('MediaResponse', 'SUCCESS', 'Media favorited successfully', { result: { media } });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error favoriting media', { errors: [{ field: 'favoriteMedia', message: error.message }] });
            }
        },

        deleteMedia: async (_, { id }, context) => {
            try {
                const media = await Media.findById(id);
                if (!media) {
                    return createResponse('MediaErrorResponse', 'FAILURE', 'Media not found', { 
                        errors: [{ field: 'id', message: 'Media not found' }] 
                    });
                }
                const userId = context.user?._id;
                if (!userId) {
                    return createResponse('MediaErrorResponse', 'FAILURE', 'User not authenticated', { 
                        errors: [{ field: 'archiveMedia', message: 'User must be authenticated to archive media' }] 
                    });
                }

                // Find user's archive album
                const archivesAlbum = await getArchivesAlbum(userId);

                // If media is already in archives, delete it
                if (archivesAlbum.media.length > 0 && archivesAlbum.media.includes(media._id)) {
                    // Check for references first before proceeding with any operations
                    const references = await findReferences(id, 'Media');
                    if (references.length > 0) {
                        return createResponse('MediaErrorResponse', 'FAILURE', 'Media cannot be deleted', {
                            errors: [{ field: 'archiveMedia', message: `Media cannot be deleted as it has references in the following collections: ${references.join(', ')}` }],
                        });
                    }

                    await deleteMediaAndRelatedData(media._id);
                    await clearCacheById(media._id);

                    await archivesAlbum.updateOne({ $pull: { media: media._id } });

                    return createResponse('MediaResponse', 'SUCCESS', 'Media deleted successfully', { result: { media } });
                }

                // If not in archives, move it to archives
                await MediaFolder.updateMany(
                    { media: media._id },
                    { $pull: { media: media._id } }
                );

                // Add media to archives album
                archivesAlbum.media.push(media._id);
                await archivesAlbum.save();

                return createResponse('MediaResponse', 'SUCCESS', 'Media archived successfully', { result: { media } });
            } catch (error) {
                console.error(error);
                return createResponse('MediaErrorResponse', 'FAILURE', 'Error archiving media', { 
                    errors: [{ field: 'archiveMedia', message: error.message }] 
                });
            }
        },

    }
};

module.exports = mediaResolvers;
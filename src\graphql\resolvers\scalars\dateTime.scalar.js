const { GraphQLScalarType, Kind } = require('graphql');

const dateTimeScalar = new GraphQLScalarType({
    name: 'DateTime',
    description: 'Custom scalar type for DateTime',
    parseValue(value) {
        return new Date(value).toISOString();
    },
    serialize(value) {
        return new Date(value).toISOString();
    },
    parseLiteral(ast) {
        if (ast.kind === Kind.STRING) {
            return new Date(ast.value).toISOString();
        }
        return null;
    },
});

module.exports = {
    DateTime: dateTimeScalar,
};
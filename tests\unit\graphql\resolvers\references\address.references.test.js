const mongoose = require('mongoose');
const Vendor = require('../../../../../src/models/Vendor');
const findReferences = require('../../../../../src/graphql/resolvers/references/address.references');

jest.mock('../../../../../src/models/Vendor');

describe('Address References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        Vendor.find.mockResolvedValue([]);

        const addressId = new mongoose.Types.ObjectId().toString();
        const result = await findReferences(addressId);

        expect(Vendor.find).toHaveBeenCalledWith({ 'businessAddress': addressId });
        expect(result).toEqual([]);
    });

    it('should return ["Vendor"] when vendor references exist', async () => {
        const mockVendor = {
            _id: new mongoose.Types.ObjectId(),
            businessAddress: new mongoose.Types.ObjectId()
        };

        Vendor.find.mockResolvedValue([mockVendor]);

        const addressId = mockVendor.businessAddress.toString();
        const result = await findReferences(addressId);

        expect(Vendor.find).toHaveBeenCalledWith({ 'businessAddress': addressId });
        expect(result).toEqual(['Vendor']);
    });

    it('should handle database errors gracefully', async () => {
        const error = new Error('Database error');
        Vendor.find.mockRejectedValue(error);

        const addressId = new mongoose.Types.ObjectId().toString();
        
        await expect(findReferences(addressId)).rejects.toThrow('Database error');
        expect(Vendor.find).toHaveBeenCalledWith({ 'businessAddress': addressId });
    });
});

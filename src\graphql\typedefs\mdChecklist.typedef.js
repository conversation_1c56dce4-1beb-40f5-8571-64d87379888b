const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type ChecklistItem {
        order: Int!
        title: String!
        description: String!
    }

    type MdChecklist {
        id: ID!
        partyType: MdPartyType!
        title: String!
        description: String!
        items: [ChecklistItem!]!
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdChecklistFilterInput {
        id: String
        title: String
        description: String
        partyType: MdPartyTypeFilterInput
    }

    type MdChecklistWrapper {
        mdChecklist: MdChecklist!
    }

    type MdChecklistsWrapper {
        mdChecklists: [MdChecklist]!
    }

    type MdChecklistResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdChecklistWrapper!
    }

    type MdChecklistsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdChecklistsWrapper!
        pagination: PaginationInfo!
    }

    type MdChecklistErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdChecklistResult = MdChecklistResponse | MdChecklistErrorResponse
    union MdChecklistsResult = MdChecklistsResponse | MdChecklistErrorResponse

    type Query {
        getMdChecklistById(id: ID!): MdChecklistResult!
        getMdChecklists(filter: MdChecklistFilterInput, pagination: PaginationInput): MdChecklistsResult!
    }

    input ChecklistItemInput {
        order: Int!
        title: String!
        description: String!
    }

    input MdChecklistInput {
        partyType: ID!
        title: String!
        description: String!
        items: [ChecklistItemInput!]!
    }

    input MdChecklistUpdateInput {
        partyType: ID
        title: String
        description: String
        items: [ChecklistItemInput!]
    }

    type Mutation {
        createMdChecklist(input: MdChecklistInput!): MdChecklistResult!
        updateMdChecklist(id: ID!, input: MdChecklistUpdateInput!): MdChecklistResult!
        deleteMdChecklist(id: ID!): MdChecklistResult!
    }
`;

const { model, Schema } = require('mongoose');

const mdVendorSubTypeSchema = new Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    icon: { type: Schema.Types.ObjectId, ref: 'MdIcon' }
}, { timestamps: true, collection: 'md_vendor_sub_types' });

const MdVendorSubType = model('MdVendorSubType', mdVendorSubTypeSchema);

module.exports = MdVendorSubType; 
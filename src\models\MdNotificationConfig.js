const { model, Schema } = require('mongoose');

const RECEIVER_TYPES = ['Host', 'Cohost', 'Collaborators', 'Guest', 'Organizer', 'Member'];

function extractVariables(text) {
    const matches = text.match(/<([^>]+)>/g) || [];
    return matches.map(match => match.slice(1, -1));
}

const templateSchema = new Schema({
    subject: { type: String, required: true },
    message: { type: String, required: true }
});

const channelsSchema = new Schema({
    activity: { type: Boolean, default: false },
    sms: { type: Boolean, default: false },
    whatsapp: { type: Boolean, default: false },
    inApp: { type: Boolean, default: false },
    push: { type: Boolean, default: false },
    email: { type: Boolean, default: false }
});

const notificationEventSchema = new Schema({
    receivers: [{
        type: String,
        required: true,
        enum: RECEIVER_TYPES,
        validate: {
            validator: function(receivers) {
                return receivers.length > 0;
            },
            message: 'At least one receiver is required'
        }
    }],
    channels: { type: channelsSchema, required: true },
    templates: { type: templateSchema, required: true },
    requiredData: [{ type: String }] 
});

notificationEventSchema.pre('save', function(next) {
    const subjectVars = extractVariables(this.templates.subject);
    const messageVars = extractVariables(this.templates.message);
    
    this.requiredData = [...new Set([...subjectVars, ...messageVars])];
    next();
});

const notificationConfigSchema = new Schema({
    eventCategory: { type: String, required: true },
    eventType: { type: String, required: true },
    config: { type: notificationEventSchema, required: true }
}, { timestamps: true });

const NotificationConfig = model('md_notification_configs', notificationConfigSchema);

module.exports = NotificationConfig;
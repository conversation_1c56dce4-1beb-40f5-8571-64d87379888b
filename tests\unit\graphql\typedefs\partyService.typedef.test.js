const { gql } = require('graphql-tag');
const partyServiceTypeDef = require('../../../../src/graphql/typedefs/partyService.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('partyServiceTypeDef', () => {
  it('should contain the PartyService type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type PartyService {
        id: ID!
        vendorType: MdVendorType!
        vendor: Vendor!
        time: DateTime!
        city: String!
        address: String!
        tasks: [Task!]
        budget: Float!
        expenditure: Float!
        transactions: [Transaction!]
      }

      input PartyServiceFilterInput {
        vendorType: ID
        vendor: ID
        city: String
        timeRange: DateTimeRangeInput
        minBudget: Float
        maxBudget: Float
        minExpenditure: Float
        maxExpenditure: Float
      }

      type PartyServiceWrapper {
        partyService: PartyService!
      }

      type PartyServicesWrapper {
        partyServices: [PartyService]!
      }

      type PartyServiceResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartyServiceWrapper!
      }

      type PartyServicesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartyServicesWrapper!
        pagination: PaginationInfo!
      }

      type PartyServiceErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union PartyServiceResult = PartyServiceResponse | PartyServiceErrorResponse
      union PartyServicesResult = PartyServicesResponse | PartyServiceErrorResponse

      type Query {
        getPartyServiceById(id: ID!): PartyServiceResult!
        getPartyServices(filter: PartyServiceFilterInput, pagination: PaginationInput): PartyServicesResult!
      }

      input PartyServiceInput {
        vendorType: ID!
        vendor: ID!
        time: DateTime!
        city: String!
        address: String!
        tasks: [ID!]
        budget: Float!
        expenditure: Float
        transactions: [ID!]
      }

      input PartyServiceUpdateInput {
        vendorType: ID
        vendor: ID
        time: DateTime
        city: String
        address: String
        tasks: [ID!]
        budget: Float
        expenditure: Float
        transactions: [ID!]
      }

      type Mutation {
        createPartyService(input: PartyServiceInput!): PartyServiceResult!
        updatePartyService(id: ID!, input: PartyServiceUpdateInput!): PartyServiceResult!
        deletePartyService(id: ID!): PartyServiceResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(partyServiceTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
}); 
const { model, Schema } = require('mongoose');

const themeSchema = new Schema({
    light: {
        backgroundColor: { type: String, required: true },
        primaryColor: { type: String, required: true },
        secondaryColor: { type: String, required: true },
        textColor: { type: String, required: true },
    },
    dark: {
        backgroundColor: { type: String, required: true },
        primaryColor: { type: String, required: true },
        secondaryColor: { type: String, required: true },
        textColor: { type: String, required: true },
    },
}, { _id: false });

const navBarSchema = new Schema({
    userDashboard: {
        tabs: {
            home: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            myEvents: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            publicEvents: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            photos: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
        },
    },
    hostEventDashboard: {
        tabs: {
            eventsDb: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            tasks: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            guests: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            apps: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
            messages: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
        },
    },
}, { _id: false });

const mdAppSettingsSchema = new Schema({
    version: { type: String, required: true },
    themes: themeSchema,
    navBars: navBarSchema,
}, { timestamps: true, collection: 'md_app_settings' });

const MdAppSettings = model('MdAppSettings', mdAppSettingsSchema);

module.exports = MdAppSettings;
const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mediaFolderSchema = new Schema({
    name: { type: String, required: true },
    event: { type: Schema.Types.ObjectId, ref: 'Event' },
    party: { type: Schema.Types.ObjectId, ref: 'Party' },
    media: [{ type: Schema.Types.ObjectId, ref: 'Media' }],
    owner: [{ type: Schema.Types.ObjectId, ref: 'User', required: true }],
    sharedWith: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    contributors: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    publish: { type: Boolean, default: false },
    category: { 
        type: String, 
        enum: ['ALBUM', 'COLLECTION'],
        required: true 
    },
    tags: [{ type: Schema.Types.ObjectId, ref: 'Tag' }],
    albumCover: {
        type: [String],
        validate: {
            validator: function(v) {
                return !v || v.length <= 2;
            },
            message: 'Album cover can only have a maximum of 2 images'
        },
        default: null
    }
}, { timestamps: true, collection: 'media_folders' });

mediaFolderSchema.pre('save', async function(next) {
    try {
        if (!this.media || this.media.length === 0) {
            this.albumCover = null;
            return next();
        }

        const Media = mongoose.model('Media');
        const mediaItems = await Media.find({ 
            _id: { $in: this.media },
            url: { $regex: /\.(jpg|jpeg|png|gif|webp)$/i }
        });

        if (mediaItems.length === 0) {
            this.albumCover = null;
        } else {
            const shuffled = mediaItems.map(m => m.url).sort(() => 0.5 - Math.random());
            this.albumCover = shuffled.slice(0, Math.min(2, shuffled.length));
        }
        next();
    } catch (error) {
        next(error);
    }
});

const MediaFolder = model('MediaFolder', mediaFolderSchema);

module.exports = MediaFolder; 
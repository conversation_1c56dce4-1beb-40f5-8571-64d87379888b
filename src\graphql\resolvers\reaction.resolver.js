const Reaction = require('../../models/Reaction');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildReactionQuery = require('./filters/reaction.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const isValidEmoji = require('../../utils/reaction.util');

const reactionResolvers = {
    Reaction: {
        user: async (parent) => {
            try {
                return await User.findById(parent.user);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        }
    },

    Query: {
        getReactionById: async (_, { id }) => {
            try {
                const cachedReaction = await getByIdCache(id);
                if (cachedReaction) {
                    return createResponse('ReactionResponse', 'SUCCESS', 'Reaction retrieved successfully from cache', {
                        result: { reaction: cachedReaction }
                    });
                }

                const reaction = await Reaction.findById(id).populate('user');
                if (!reaction) {
                    return createResponse('ReactionErrorResponse', 'FAILURE', 'Reaction not found', {
                        errors: [{ field: 'id', message: 'Reaction not found' }]
                    });
                }

                await setCache(id, reaction);
                return createResponse('ReactionResponse', 'SUCCESS', 'Reaction retrieved successfully', {
                    result: { reaction }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReactionErrorResponse', 'FAILURE', 'Error retrieving reaction', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        },

        getReactions: async (_, { filter, pagination }) => {
            try {
                const query = await buildReactionQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Reaction, query, limit, skip);
                const reactions = await Reaction.find(query)
                    .populate('user')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (reactions.length === 0) {
                    return createResponse('ReactionsResponse', 'FAILURE', 'No reactions found', {
                        result: { reactions },
                        pagination: paginationInfo
                    });
                }

                return createResponse('ReactionsResponse', 'SUCCESS', 'Reactions fetched successfully', {
                    result: { reactions },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReactionErrorResponse', 'FAILURE', 'Error retrieving reactions', {
                    errors: [{ field: 'getReactions', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createReaction: async (_, { input }, context) => {
            try {
                if (!isValidEmoji(input.reactionUnicode)) {
                    return createResponse('ReactionErrorResponse', 'FAILURE', 'Invalid emoji provided', {
                        errors: [{ field: 'reactionUnicode', message: 'Invalid emoji provided' }]
                    });
                }

                const reaction = new Reaction({ ...input, user: context.user._id });
                await reaction.save();
                
                const populatedReaction = await Reaction.findById(reaction._id).populate('user');
                
                return createResponse('ReactionResponse', 'SUCCESS', 'Reaction created successfully', {
                    result: { reaction: populatedReaction }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReactionErrorResponse', 'FAILURE', 'Error creating reaction', {
                    errors: [{ field: 'createReaction', message: error.message }]
                });
            }
        },

        deleteReaction: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Reaction');
                if (references.length > 0) {
                    return createResponse('ReactionErrorResponse', 'FAILURE', 'Reaction cannot be deleted', {
                        errors: [{ field: 'id', message: `Reaction cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const reaction = await Reaction.findByIdAndDelete(id).populate('user');
                if (!reaction) {
                    return createResponse('ReactionErrorResponse', 'FAILURE', 'Reaction not found', {
                        errors: [{ field: 'id', message: 'Reaction not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('ReactionResponse', 'SUCCESS', 'Reaction deleted successfully', {
                    result: { reaction }
                });
            } catch (error) {
                console.error(error);
                return createResponse('ReactionErrorResponse', 'FAILURE', 'Error deleting reaction', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        }
    }
};

module.exports = reactionResolvers; 
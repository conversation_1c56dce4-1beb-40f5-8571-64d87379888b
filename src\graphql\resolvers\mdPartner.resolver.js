const MdIcon = require('../../models/MdIcon');
const MdPartner = require('../../models/MdPartner');
const MdPartnerCategory = require('../../models/MdPartnerCategory');
const Tag = require('../../models/Tag');
const { clearCacheById, setCache, getByIdCache } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { createResponse } = require('../../utils/response.util');
const { validateReferences } = require('../../utils/validation.util');
const buildMdPartnerQuery = require('./filters/mdPartner.filter');

const basePartnerIdSchema = {
    icon: { type: 'single', model: MdIcon },
    categories: { type: 'array', model: Tag }
};

const mdPartnerIdSchema = {
    ...basePartnerIdSchema
};

const createMdPartnerIdSchema = {
    ...basePartnerIdSchema,
    icon: { ...basePartnerIdSchema.icon, required: true },
    categories: { ...basePartnerIdSchema.categories, required: true }
};

const mdPartnerResolvers = {
    MdPartner: {
        icon: async (parent) => {
            try {
                const icon = await MdIcon.findById(parent.icon);
                if (!icon) {
                    throw new Error('Error getting icon');
                }
                return icon;
            } catch (error) {
                throw new Error('Error getting icon');
            }
        },
        categories: async (parent) => {
            try {
                return await Tag.find({ _id: { $in: parent.categories } });
            } catch (error) {
                throw new Error('Error getting categories');
            }
        }
    },

    Query: {
        getMdPartnerById: async (_, { id }) => {
            try {
                let mdPartner = await getByIdCache(id);
                if (!mdPartner) {
                    mdPartner = await MdPartner.findById(id);
                    if (!mdPartner) {
                        return createResponse('MdPartnerErrorResponse', 'FAILURE', 'MdPartner not found', { 
                            errors: [{ field: 'getMdPartnerById', message: 'MdPartner not found' }] 
                        });
                    }
                    mdPartner = await mdPartner.populate('categories');
                    mdPartner = await mdPartner.populate('icon');
                    await setCache(id, mdPartner);
                }
                return createResponse('MdPartnerResponse', 'SUCCESS', 'MdPartner fetched successfully', { 
                    result: { mdPartner } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerErrorResponse', 'FAILURE', 'Error retrieving MdPartner', { 
                    errors: [{ field: 'getMdPartnerById', message: error.message }] 
                });
            }
        },
        getMdPartners: async (_, { filter, pagination }) => {
            try {
                const query = await buildMdPartnerQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdPartner, query, limit, skip);

                const mdPartners = await MdPartner.find(query).populate('categories').populate('icon').limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdPartners.length === 0) {
                    return createResponse('MdPartnersResponse', 'FAILURE', 'No mdPartners found', { result: { mdPartners }, pagination: paginationInfo });
                }
                return createResponse('MdPartnersResponse', 'SUCCESS', 'MdPartners fetched successfully', { result: { mdPartners }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerErrorResponse', 'FAILURE', 'Error retrieving MdPartners', { errors: [{ field: 'getMdPartners', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdPartner: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createMdPartnerIdSchema, 'MdPartner');
                if (validationError) {
                    return createResponse('MdPartnerErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const mdPartner = new MdPartner(input);
                await mdPartner.save();
                return createResponse('MdPartnerResponse', 'SUCCESS', 'MdPartner created successfully', { result: { mdPartner } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerErrorResponse', 'FAILURE', 'Error creating MdPartner', { errors: [{ field: 'createMdPartner', message: error.message }] });
            }
        },
        updateMdPartner: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdPartnerIdSchema, 'MdPartner');
                if (validationError) {
                    return createResponse('MdPartnerErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }
                const mdPartner = await MdPartner.findByIdAndUpdate(id, input, { new: true }).populate('icon');
                if (!mdPartner) {
                    return createResponse('MdPartnerErrorResponse', 'FAILURE', 'MdPartner not found', { errors: [{ field: 'updateMdPartner', message: 'MdPartner not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdPartnerResponse', 'SUCCESS', 'MdPartner updated successfully', { result: { mdPartner } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerErrorResponse', 'FAILURE', 'Error updating MdPartner', { errors: [{ field: 'updateMdPartner', message: error.message }] });
            }
        },
        deleteMdPartner: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdPartner');
                if (references.length > 0) {
                    return createResponse('MdPartnerErrorResponse', 'FAILURE', 'MdPartner cannot be deleted', {
                        errors: [{ field: 'deleteMdPartner', message: `MdPartner cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }
                const mdPartner = await MdPartner.findByIdAndDelete(id);
                if (!mdPartner) {
                    return createResponse('MdPartnerErrorResponse', 'FAILURE', 'MdPartner not found', { errors: [{ field: 'deleteMdPartner', message: 'MdPartner not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdPartnerResponse', 'SUCCESS', 'MdPartner deleted successfully', { result: { mdPartner } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerErrorResponse', 'FAILURE', 'Error deleting MdPartner', { errors: [{ field: 'deleteMdPartner', message: error.message }] });
            }
        }
    }
};

module.exports = mdPartnerResolvers;
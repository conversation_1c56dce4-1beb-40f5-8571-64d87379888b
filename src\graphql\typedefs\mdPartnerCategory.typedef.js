const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdPartnerCategory {
        id: ID!
        name: String!
        icon: MdIcon!
        description: String
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdPartnerCategoryFilterInput {
        id: String!
        name: String
    }

    type MdPartnerCategoryWrapper {
        mdPartnerCategory: MdPartnerCategory!
    }

    type MdPartnerCategoriesWrapper {
        mdPartnerCategories: [MdPartnerCategory]!
    }

    type MdPartnerCategoriesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartnerCategoriesWrapper!
        pagination: PaginationInfo!
    }

    type MdPartnerCategoryResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdPartnerCategoryWrapper!
    }

    type MdPartnerCategoryErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdPartnerCategoryResult = MdPartnerCategoryResponse | MdPartnerCategoryErrorResponse
    union MdPartnerCategoriesResult = MdPartnerCategoriesResponse | MdPartnerCategoryErrorResponse

    type Query {
        getMdPartnerCategoryById(id: ID!): MdPartnerCategoryResult!
        getMdPartnerCategories(filter: MdPartnerCategoryFilterInput, pagination: PaginationInput): MdPartnerCategoriesResult!
    }

    input MdPartnerCategoryInput {
        name: String!
        icon: ID!
        description: String
    }

    input MdPartnerCategoryUpdateInput {
        name: String
        icon: ID
        description: String
    }

    type Mutation {
        createMdPartnerCategory(input: MdPartnerCategoryInput!): MdPartnerCategoryResult!
        updateMdPartnerCategory(id: ID!, input: MdPartnerCategoryUpdateInput!): MdPartnerCategoryResult!
        deleteMdPartnerCategory(id: ID!): MdPartnerCategoryResult!
    }
`;
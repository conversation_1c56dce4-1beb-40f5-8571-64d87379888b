const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Address = require('../../../src/models/Address');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Address Model Test', () => {
    it('should create and save an address successfully', async () => {
        const validAddress = new Address({
            addressType: 'HOME',
            street1: '123 Main St',
            street2: 'Apt 4B',
            city: 'New York',
            state: 'NY',
            postalCode: '10001',
            country: 'USA',
            isPrimary: true
        });

        const savedAddress = await validAddress.save();

        expect(savedAddress._id).toBeDefined();
        expect(savedAddress.addressType).toBe('HOME');
        expect(savedAddress.street1).toBe('123 Main St');
        expect(savedAddress.street2).toBe('Apt 4B');
        expect(savedAddress.city).toBe('New York');
        expect(savedAddress.state).toBe('NY');
        expect(savedAddress.postalCode).toBe('10001');
        expect(savedAddress.country).toBe('USA');
        expect(savedAddress.isPrimary).toBe(true);
        expect(savedAddress.createdAt).toBeDefined();
        expect(savedAddress.updatedAt).toBeDefined();
    });

    it('should fail to create an address without required fields', async () => {
        const invalidAddress = new Address({
            street2: 'Apt 4B'
        });

        let err;
        try {
            await invalidAddress.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.addressType).toBeDefined();
        expect(err.errors.street1).toBeDefined();
        expect(err.errors.city).toBeDefined();
        expect(err.errors.state).toBeDefined();
        expect(err.errors.postalCode).toBeDefined();
        expect(err.errors.country).toBeDefined();
    });

    it('should fail to create an address with invalid addressType', async () => {
        const invalidAddress = new Address({
            addressType: 'INVALID_TYPE',
            street1: '123 Main St',
            city: 'New York',
            state: 'NY',
            postalCode: '10001',
            country: 'USA',
            isPrimary: true
        });

        let err;
        try {
            await invalidAddress.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.addressType).toBeDefined();
        expect(err.errors.addressType.message).toContain('`INVALID_TYPE` is not a valid enum value');
    });

    it('should create an address with minimal required fields', async () => {
        const minimalAddress = new Address({
            addressType: 'WORK',
            street1: '456 Business Ave',
            city: 'Chicago',
            state: 'IL',
            postalCode: '60601',
            country: 'USA'
        });

        const savedAddress = await minimalAddress.save();

        expect(savedAddress._id).toBeDefined();
        expect(savedAddress.addressType).toBe('WORK');
        expect(savedAddress.street1).toBe('456 Business Ave');
        expect(savedAddress.street2).toBeUndefined();
        expect(savedAddress.city).toBe('Chicago');
        expect(savedAddress.state).toBe('IL');
        expect(savedAddress.postalCode).toBe('60601');
        expect(savedAddress.country).toBe('USA');
        expect(savedAddress.isPrimary).toBe(false);
    });
});

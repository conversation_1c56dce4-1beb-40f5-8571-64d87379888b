const { model, Schema } = require('mongoose');

const mdPartnerSchema = new Schema({
    name: { type: String, required: true },
    categories: [{ type: Schema.Types.ObjectId, ref: 'Tag', required: true }],
    description: { type: String, required: false },
    icon: { type: Schema.Types.ObjectId, ref: 'MdIcon', required: true },
    banner_image_url: { type: String, required: false },
}, { timestamps: true, collection: 'md_partners' });

const MdPartner = model('MdPartner', mdPartnerSchema);

module.exports = MdPartner;
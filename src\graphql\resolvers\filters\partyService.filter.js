const buildPartyServiceQuery = (filter = {}) => {
    const query = {};

    if (filter.vendorType) {
        query.vendorType = filter.vendorType;
    }

    if (filter.vendor) {
        query.vendor = filter.vendor;
    }

    if (filter.city) {
        query.city = { $regex: new RegExp(filter.city, 'i') };
    }

    if (filter.timeRange) {
        query.time = {
            $gte: filter.timeRange.start,
            $lte: filter.timeRange.end
        };
    }

    if (filter.minBudget !== undefined || filter.maxBudget !== undefined) {
        query.budget = {};
        if (filter.minBudget !== undefined) {
            query.budget.$gte = filter.minBudget;
        }
        if (filter.maxBudget !== undefined) {
            query.budget.$lte = filter.maxBudget;
        }
    }

    if (filter.minExpenditure !== undefined || filter.maxExpenditure !== undefined) {
        query.expenditure = {};
        if (filter.minExpenditure !== undefined) {
            query.expenditure.$gte = filter.minExpenditure;
        }
        if (filter.maxExpenditure !== undefined) {
            query.expenditure.$lte = filter.maxExpenditure;
        }
    }

    return query;
};

module.exports = buildPartyServiceQuery;

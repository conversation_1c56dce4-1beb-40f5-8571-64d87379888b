const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    enum UserRole {
        admin
        user
        super_admin
    }

    type User {
        id: ID
        role: [UserRole!]!
        firstName: String!
        lastName: String
        email: String
        phone: String!
        profilePicture: String
        isRegistered: Boolean!
        emailVerified: Boolean!
        phoneVerified: Boolean!
        externalId: String
        isActive: Boolean!
    }

    input UserFilterInput {
        role: UserRole
        firstName: String
        lastName: String
        email: String
        phone: String
        isRegistered: Boolean
        emailVerified: Boolean
        phoneVerified: Boolean
    }

    type UserWrapper {
        user: User!
    }

    type UsersWrapper {
        users: [User]!
    }

    type UsersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: UsersWrapper!
        pagination: PaginationInfo!
    }

    type UserResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: UserWrapper!
    }

    type UserErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union UserResult = UserResponse | UserErrorResponse
    union UsersResult = UsersResponse | UserErrorResponse

    type Query {
        getUsers(filters: UserFilterInput, pagination: PaginationInput): UsersResult!
        getUser: UserResult!
    }

    input RegisterInput {
        role: [UserRole!]!
        firstName: String!
        lastName: String
        email: String
        phone: String!
        profilePicture: String
        isRegistered: Boolean!
        externalId: String
    }

    input UserUpdateInput {
        firstName: String
        lastName: String
        email: String
        phone: String
        profilePicture: String
        externalId: String
        isActive: Boolean
        emailVerified: Boolean
        phoneVerified: Boolean
    }

    input VerifyUserInput {
        email: String
        phone: String
    }

    input UserRoleUpdateInput {
        userId: ID!
        role: [UserRole!]!
    }

    type Mutation {
        register(input: RegisterInput!): UserResult!
        verifyUser(input: VerifyUserInput!): UserResult!
        updateUser(input: UserUpdateInput!): UserResult!
        updateUserRole(input: UserRoleUpdateInput!): UserResult!
        deleteUser: UserResult!
    }
`;
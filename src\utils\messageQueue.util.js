const getRedisClient = require("../infrastructure/redisClient");

const addMessageToActivityQueue = async(partyId, message) => {
    const redisClient = getRedisClient();
    try {
        const messageString = JSON.stringify(message);

        const queue_name = 'activity_messages_' + partyId;
        
        await redisClient.rpush(queue_name , messageString);
        
    } catch (error) {
        console.error('Error adding message to activity queue:', error);
    }
};

const readActivityMessages = async(partyId) => {
    const redis = getRedisClient();
    
    try {
      console.log(`Reading messages for partyId: ${partyId}`);
      const messages = await redis.lrange(`activity_messages_${partyId}`, 0, -1);
      
      const parsedMessages = messages.map(msg => JSON.parse(msg));
      
      if (messages.length > 0) {
        await redis.ltrim(`activity_messages_${partyId}`, messages.length, -1);
      }
  
      return parsedMessages;
    } catch (error) {
      console.error('Error reading from Redis:', error);
      return [];
    }
  };

module.exports = {
    addMessageToActivityQueue,
    readActivityMessages
};      
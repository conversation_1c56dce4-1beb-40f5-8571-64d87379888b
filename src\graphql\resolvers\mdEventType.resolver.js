const MdEventType = require("../../models/MdEventType");
const MdPartyType = require("../../models/MdPartyType");
const { getByIdCache, clearCacheById, setCache } = require("../../utils/cache.util");
const { createResponse } = require("../../utils/response.util");
const { getPaginationInfo } = require("../../utils/paginationInfo.util");
const buildMdEventTypeQuery = require("./filters/mdEventType.filter");
const { findReferences } = require("../../utils/referenceCheck.util");
const { validateReferences } = require("../../utils/validation.util");

const mdEventTypeIdSchema = {
    partyTypes: { type: 'array', required: true, model: MdPartyType }
}

const mdEventTypeResolvers = {
    MdEventType: {
        partyTypes: async (mdEventType) => {
            try {
                return await MdPartyType.find({ _id: { $in: mdEventType.partyTypes } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party types');
            }
        }
    },

    Query: {
        getMdEventTypeById: async (_, { id }) => {
            try {
                let mdEventType = await getByIdCache(id) || await MdEventType.findById(id);
                if (mdEventType) {
                    await setCache(id, mdEventType);
                } else {
                    return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'MdEventType not found', { errors: [{ field: 'getMdEventTypeById', message: 'MdEventType not found' }] });
                }
                return createResponse('MdEventTypeResponse', 'SUCCESS', 'MdEventType fetched successfully', { result: { mdEventType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'Error getting MdEventType', { errors: [{ field: 'getMdEventTypeById', message: error.message }] });
            }
        },

        getMdEventTypes: async (_, { filter, pagination }) => {
            try {
                const query = buildMdEventTypeQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdEventType, query, limit, skip);

                const mdEventTypes = await MdEventType.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdEventTypes.length === 0) {
                    return createResponse('MdEventTypesResponse', 'FAILURE', 'No MdEventTypes found', { result: { mdEventTypes }, pagination: paginationInfo });
                }
                return createResponse('MdEventTypesResponse', 'SUCCESS', 'MdEventTypes fetched successfully', { result: { mdEventTypes }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'Error getting MdEventTypes', { errors: [{ field: 'getMdEventTypes', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdEventType: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, mdEventTypeIdSchema, 'MdEventType');
                if (validationError) {
                    return createResponse('MdEventTypeErrorResponse', 'FAILURE', validationError.message, { errors: validationError.errors });
                }
                const mdEventType = new MdEventType(input);
                await mdEventType.save();
                return createResponse('MdEventTypeResponse', 'SUCCESS', 'MdEventType created successfully', { result: { mdEventType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'Error creating MdEventType', { errors: [{ field: 'createMdEventType', message: error.message }] });
            }
        },

        updateMdEventType: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, mdEventTypeIdSchema, 'MdEventType');
                if (validationError) {
                    return createResponse('MdEventTypeErrorResponse', 'FAILURE', validationError.message, { errors: validationError.errors });
                }
                const mdEventType = await MdEventType.findByIdAndUpdate(id, input, { new: true });
                if (!mdEventType) {
                    return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'MdEventType not found', { errors: [{ field: 'updateMdEventType', message: 'MdEventType not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdEventTypeResponse', 'SUCCESS', 'MdEventType updated successfully', { result: { mdEventType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'Error updating MdEventType', { errors: [{ field: 'updateMdEventType', message: error.message }] });
            }
        },

        deleteMdEventType: async (_, { id }) => {
            const references = await findReferences(id, 'MdEventType');
            if (references.length > 0) {
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'MdEventType cannot be deleted', { errors: [{ field: 'id', message: `MdEventType cannot be deleted as it is being used in: ${references.join(', ')}` }] });
            }
            try {
                const mdEventType = await MdEventType.findByIdAndDelete(id);
                if (!mdEventType) {
                    return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'MdEventType not found', { errors: [{ field: 'deleteMdEventType', message: 'MdEventType not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdEventTypeResponse', 'SUCCESS', 'MdEventType deleted successfully', { result: { mdEventType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdEventTypeErrorResponse', 'FAILURE', 'Error deleting MdEventType', { errors: [{ field: 'deleteMdEventType', message: error.message }] });
            }
        }
    }
};

module.exports = mdEventTypeResolvers;
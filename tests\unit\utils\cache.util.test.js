const { getByIdCache, setCache, clearCacheById, clearAllCache, clearCacheBySubstring, getBySubstring } = require('../../../src/utils/cache.util');
const getRedisClient = require('../../../src/infrastructure/redisClient');

jest.mock('../../../src/infrastructure/redisClient');

describe('Cache Utility Functions', () => {
    let redis;

    beforeEach(() => {
        redis = {
            get: jest.fn(),
            set: jest.fn(),
            keys: jest.fn(),
            del: jest.fn().mockResolvedValue(true),
            flushall: jest.fn().mockResolvedValue(true),
        };

        getRedisClient.mockReturnValue(redis);
    });

    afterEach(() => {
        jest.clearAllMocks(); 
    });

    describe('getByIdCache', () => {
        it('should return data from cache if it exists', async () => {
            const id = 'test-id';
            const cachedData = JSON.stringify({ _id: id, name: 'test' });
            redis.get.mockResolvedValue(cachedData);
    
            const result = await getByIdCache(id);
    
            expect(result).toEqual({ _id: id, id: id, name: 'test' });
        });

        it('should return null if data is not found in cache', async () => {
            const id = 'test-id';
            redis.get.mockResolvedValue(null);  

            const result = await getByIdCache(id);

            expect(redis.get).toHaveBeenCalledWith(id);  
            expect(result).toBeNull();  
        });

        it('should log an error if fetching from cache fails', async () => {
            const id = 'test-id';
            const error = new Error('Redis error');
            redis.get.mockRejectedValue(error);  

            console.error = jest.fn();  

            const result = await getByIdCache(id);

            expect(redis.get).toHaveBeenCalledWith(id);  
            expect(console.error).toHaveBeenCalledWith('Error fetching from cache:', error);  
            expect(result).toBeNull();  
        });
    });

    describe('getBySubstring', () => {
        it('should return the latest data from cache when keys are found', async () => {
            const substring = 'test';
            const keys = ['key1', 'key2'];
            const cachedData = [
                JSON.stringify({ _id: '1', createdAt: '2023-01-01T00:00:00Z' }),
                JSON.stringify({ _id: '2', createdAt: '2023-01-02T00:00:00Z' }),
            ];

            redis.keys.mockResolvedValue(keys);
            redis.get.mockImplementation((key) => {
                return Promise.resolve(cachedData[key === 'key1' ? 0 : 1]);
            });

            const result = await getBySubstring(substring);

            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(redis.get).toHaveBeenCalledTimes(2);
            expect(result).toEqual({
                _id: '2',
                id: '2',
                createdAt: '2023-01-02T00:00:00Z'
            });
        });

        it('should return null when no keys are found', async () => {
            const substring = 'test';
            redis.keys.mockResolvedValue([]);

            const result = await getBySubstring(substring);

            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(result).toBeNull();
        });

        it('should handle errors and return null', async () => {
            const substring = 'test';
            redis.keys.mockRejectedValue(new Error('Redis error'));

            const result = await getBySubstring(substring);

            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(result).toBeNull();
        });
    });

    describe('setCache', () => {
        it('should set data in cache successfully', async () => {
            const id = 'test-id';
            const data = { name: 'test' };

            await setCache(id, data);

            expect(redis.set).toHaveBeenCalledWith(id, JSON.stringify(data), 'EX', process.env.REDIS_VARIABLE_EXPIRATION);  
        });

        it('should log an error if setting cache fails', async () => {
            const id = 'test-id';
            const data = { name: 'test' };
            const error = new Error('Redis error');
            redis.set.mockRejectedValue(error);  

            console.error = jest.fn();  

            await setCache(id, data);

            expect(redis.set).toHaveBeenCalledWith(id, JSON.stringify(data), 'EX', process.env.REDIS_VARIABLE_EXPIRATION);  
            expect(console.error).toHaveBeenCalledWith('Error setting cache:', error);  
        });
    });

    describe('clearCacheById', () => {
        it('should clear cache for the given ID', async () => {
            const id = 'test-id';

            await clearCacheById(id);

            expect(redis.del).toHaveBeenCalledWith(id);  
            expect(redis.del).toHaveBeenCalledTimes(1);  
        });

        it('should log an error if clearing cache fails', async () => {
            const id = 'test-id';
            const error = new Error('Redis error');
            redis.del.mockRejectedValue(error);  

            console.error = jest.fn();  

            await clearCacheById(id);

            expect(redis.del).toHaveBeenCalledWith(id);  
            expect(console.error).toHaveBeenCalledWith('Error clearing cache:', error);  
        });
    });

    describe('clearAllCache', () => {
        it('should clear all cache successfully', async () => {
            await clearAllCache();

            expect(redis.flushall).toHaveBeenCalled();  
        });

        it('should log an error if clearing all cache fails', async () => {
            const error = new Error('Redis error');
            redis.flushall.mockRejectedValue(error);  

            console.error = jest.fn();  

            await clearAllCache();

            expect(redis.flushall).toHaveBeenCalled();  
            expect(console.error).toHaveBeenCalledWith('Error clearing all cache:', error);  
        });
    });

    describe('clearCacheBySubstring', () => {
        it('should clear cache for keys matching the substring', async () => {
            const substring = 'test';
            const keys = ['key1_test', 'key2_test'];
    
            redis.keys.mockResolvedValue(keys);
            await clearCacheBySubstring(substring);
    
            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(redis.del).toHaveBeenCalledTimes(keys.length);
            keys.forEach(key => {
                expect(redis.del).toHaveBeenCalledWith(key);
            });
        });
    
        it('should log when no keys are found', async () => {
            const substring = 'test';
            redis.keys.mockResolvedValue([]);
    
            console.log = jest.fn();
            await clearCacheBySubstring(substring);
    
            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(redis.del).not.toHaveBeenCalled();
            expect(console.log).toHaveBeenCalledWith(`No cache keys found matching substring ${substring}`);
        });
    
        it('should handle errors when clearing cache', async () => {
            const substring = 'test';
            const errorMessage = 'Redis error';
            redis.keys.mockRejectedValue(new Error(errorMessage));
    
            console.error = jest.fn();
            await clearCacheBySubstring(substring);
    
            expect(redis.keys).toHaveBeenCalledWith('*' + substring + '*');
            expect(redis.del).not.toHaveBeenCalled();
            expect(console.error).toHaveBeenCalledWith('Error clearing cache:', expect.any(Error));
        });
    });
});

const mongoose = require('mongoose');
const MdVendorType = require("../../../../../src/models/MdVendorType");
const MdEventType = require("../../../../../src/models/MdEventType");
const findReferences = require("../../../../../src/graphql/resolvers/references/mdPartyType.references");

jest.mock("../../../../../src/models/MdVendorType");
jest.mock("../../../../../src/models/MdEventType");

describe('mdPartyType References', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return empty array when no references found', async () => {
    MdEventType.find.mockResolvedValue([]);

    const result = await findReferences('someId');

    expect(result).toEqual([]);
    expect(MdEventType.find).toHaveBeenCalledWith({ partyTypes: 'someId' });
  });

  it('should return MdEventType when references found', async () => {
    MdEventType.find.mockResolvedValue([{ _id: 'eventTypeId' }]);

    const result = await findReferences('someId');

    expect(result).toEqual(['MdEventType']);
    expect(MdEventType.find).toHaveBeenCalledWith({ partyTypes: 'someId' });
  });

  it('should handle errors gracefully', async () => {
    const error = new Error('Database error');
    MdEventType.find.mockRejectedValue(error);

    await expect(findReferences('someId')).rejects.toThrow('Database error');
  });
});
const MdServiceLocation = require('../../models/MdServiceLocation');
const { getByIdCache, clearCacheById, setCache } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { createResponse } = require('../../utils/response.util');
const buildMdServiceLocationQuery = require('./filters/mdServiceLocation.filter');

const mdServiceLocationResolvers = {
    Query: {
        getMdServiceLocationById: async (_, { id }) => {
            try {
                let mdServiceLocation = await getByIdCache(id) || await MdServiceLocation.findById(id);
                if (mdServiceLocation) {
                    await setCache(id, mdServiceLocation);
                } else {
                    return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'MdServiceLocation not found', { errors: [{ field: 'getMdServiceLocationById', message: 'MdServiceLocation not found' }] });
                }
                return createResponse('MdServiceLocationResponse', 'SUCCESS', 'MdServiceLocation fetched successfully', { result: { mdServiceLocation } });
            } catch (error) {
                console.error(error);
                return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'Error getting MdServiceLocation', { errors: [{ field: 'getMdServiceLocationById', message: error.message }] });
            }
        },
        getMdServiceLocations: async (_, { filter, pagination }) => {
            try {
                const query = buildMdServiceLocationQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdServiceLocation, query, limit, skip);

                const mdServiceLocations = await MdServiceLocation.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdServiceLocations.length === 0) {
                    return createResponse('MdServiceLocationsResponse', 'FAILURE', 'No MdServiceLocations found', { result: { mdServiceLocations }, pagination: paginationInfo });
                }
                return createResponse('MdServiceLocationsResponse', 'SUCCESS', 'MdServiceLocations fetched successfully', { result: { mdServiceLocations }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'Error getting MdServiceLocations', { errors: [{ field: 'getMdServiceLocations', message: error.message }] });
            }
        }
    },
    Mutation: {
        createMdServiceLocation: async (_, { input }) => {
            try {
                const mdServiceLocation = new MdServiceLocation(input);
                await mdServiceLocation.save();
                return createResponse('MdServiceLocationResponse', 'SUCCESS', 'MdServiceLocation created successfully', { result: { mdServiceLocation } });
            } catch (error) {
                console.error(error);
                return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'Error creating MdServiceLocation', { errors: [{ field: 'createMdServiceLocation', message: error.message }] });
            }
        },
        updateMdServiceLocation: async (_, { id, input }) => {
            try {
                const mdServiceLocation = await MdServiceLocation.findByIdAndUpdate(id, input, { new: true });
                if (!mdServiceLocation) {
                    return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'MdServiceLocation not found', { errors: [{ field: 'updateMdServiceLocation', message: 'MdServiceLocation not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdServiceLocationResponse', 'SUCCESS', 'MdServiceLocation updated successfully', { result: { mdServiceLocation } });
            } catch (error) {
                console.error(error);
                return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'Error updating MdServiceLocation', { errors: [{ field: 'updateMdServiceLocation', message: error.message }] });
            }
        },
        deleteMdServiceLocation: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'MdServiceLocation');
                if (references.length > 0) {
                    return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'MdServiceLocation cannot be deleted', { errors: [{ field: 'id', message: `MdServiceLocation cannot be deleted as it is being used in: ${references.join(', ')}` }] });
                }

                const mdServiceLocation = await MdServiceLocation.findByIdAndDelete(id);
                
                if (!mdServiceLocation) {
                    return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'MdServiceLocation not found', { errors: [{ field: 'deleteMdServiceLocation', message: 'MdServiceLocation not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdServiceLocationResponse', 'SUCCESS', 'MdServiceLocation deleted successfully', { result: { mdServiceLocation } });
            } catch (error) {
                console.error(error);
                return createResponse('MdServiceLocationErrorResponse', 'FAILURE', 'Error deleting MdServiceLocation', { errors: [{ field: 'deleteMdServiceLocation', message: error.message }] });
            }
        }
    }
};

module.exports = mdServiceLocationResolvers;
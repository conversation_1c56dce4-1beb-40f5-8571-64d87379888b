const buildMdInterestQuery = require("./mdInterest.filter");
const MdInterest = require("../../../models/MdInterest");
const { isValidObjectId } = require('mongoose');

const buildMdInterestCategoryQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }

        if (filter.title) {
            query.title = { $regex: filter.title, $options: 'i' };
        }

        if (filter.interests && filter.interests.length > 0) {
            const interestQueries = await Promise.all(
                filter.interests.map(async (interestFilter) => {
                    const interestQuery = await buildMdInterestQuery(interestFilter);
                    if (Object.keys(interestQuery).length > 0) {
                        const matchingInterests = await MdInterest.find(interestQuery).select('_id');
                        return matchingInterests.map(interest => interest._id);
                    }
                    return [];
                })
            );

            const flattenedInterests = interestQueries.flat().filter(Boolean);
            if (flattenedInterests.length > 0) {
                query.interests = { $in: flattenedInterests };
            } else {
                query.interests = { $in: [] };
            }
        }
    }

    return query;
};

module.exports = buildMdInterestCategoryQuery;

const path = require('path');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeResolvers } = require('@graphql-tools/merge');

jest.mock('@graphql-tools/load-files', () => ({
  loadFilesSync: jest.fn(),
}));

jest.mock('@graphql-tools/merge', () => ({
  mergeResolvers: jest.fn(),
}));

describe('resolvers', () => {
  it('should load and merge resolvers correctly', () => {
    const mockResolversArray = [
      {
        Query: {
          hello: () => 'Hello World',
        },
      },
      {
        Mutation: {
          sayHello: (_, { name }) => `Hello ${name}`,
        },
      },
    ];
    const mockMergedResolvers = {
      Query: {
        hello: () => 'Hello World',
      },
      Mutation: {
        sayHello: (_, { name }) => `Hello ${name}`,
      },
    };

    loadFilesSync.mockReturnValue(mockResolversArray);
    mergeResolvers.mockReturnValue(mockMergedResolvers);

    const resolvers = require('../../../src/graphql/resolvers');

    expect(loadFilesSync).toHaveBeenCalledWith(path.join(__dirname, '../../../src/graphql/resolvers'));
    expect(mergeResolvers).toHaveBeenCalledWith(mockResolversArray);
    expect(resolvers).toBe(mockMergedResolvers);
  });
});
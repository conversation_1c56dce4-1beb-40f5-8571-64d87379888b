const mongoose = require('mongoose');
const buildPartyQuery = require('../../../../../src/graphql/resolvers/filters/party.filter');
const Guest = require('../../../../../src/models/Guest');
const PartyService = require('../../../../../src/models/PartyService');
const MdServiceLocation = require('../../../../../src/models/MdServiceLocation');
const buildServiceLocationQuery = require('../../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');
const { isValidObjectId } = require('mongoose');
const MdVendorType = require('../../../../../src/models/MdVendorType');
const VendorService = require('../../../../../src/models/VendorService');
const buildMdVendorTypeQuery = require('../../../../../src/graphql/resolvers/filters/mdVendorType.filter');

jest.mock('../../../../../src/models/Guest');
jest.mock('../../../../../src/models/PartyService');
jest.mock('../../../../../src/models/MdServiceLocation');
jest.mock('../../../../../src/graphql/resolvers/filters/mdServiceLocation.filter');
jest.mock('mongoose', () => ({
    ...jest.requireActual('mongoose'),
    isValidObjectId: jest.fn()
}));

jest.mock('../../../../../src/models/MdVendorType', () => ({
    find: jest.fn().mockReturnThis(),
    select: jest.fn()
}));

jest.mock('../../../../../src/graphql/resolvers/filters/mdVendorType.filter');

describe('buildPartyQuery', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty query and filters when no filters provided', async () => {
        const result = await buildPartyQuery();
        
        expect(result.query).toEqual({});
        expect(result.postQueryFilters).toEqual({});
    });

    it('should build time range query correctly', async () => {
        const startDate = new Date('2024-01-01');
        const endDate = new Date('2024-12-31');
        
        const result = await buildPartyQuery({
            timeRange: {
                start: startDate,
                end: endDate
            }
        });

        expect(result.query).toEqual({
            time: {
                $gte: startDate,
                $lte: endDate
            }
        });
    });

    it('should build guest count filters correctly', async () => {
        const result = await buildPartyQuery({
            minExpectedGuests: 10,
            maxExpectedGuests: 50
        });

        expect(result.postQueryFilters).toEqual({
            expectedGuests: {
                min: 10,
                max: 50
            }
        });
    });

    it('should build budget filters correctly', async () => {
        const result = await buildPartyQuery({
            minBudget: 1000,
            maxBudget: 5000
        });

        expect(result.postQueryFilters).toEqual({
            budget: {
                min: 1000,
                max: 5000
            }
        });
    });

    it('should build expenditure filters correctly', async () => {
        const result = await buildPartyQuery({
            minExpenditure: 500,
            maxExpenditure: 2500
        });

        expect(result.postQueryFilters).toEqual({
            expenditure: {
                min: 500,
                max: 2500
            }
        });
    });

    describe('serviceLocation filter', () => {
        it('should handle string serviceLocation with valid ObjectId', async () => {
            const validId = '507f1f77bcf86cd799439011';
            isValidObjectId.mockReturnValue(true);

            const result = await buildPartyQuery({
                serviceLocation: validId
            });

            expect(result.query.serviceLocation).toBe(validId);
            expect(isValidObjectId).toHaveBeenCalledWith(validId);
        });

        it('should throw error for invalid ObjectId in string serviceLocation', async () => {
            const invalidId = 'invalid-id';
            isValidObjectId.mockReturnValue(false);

            await expect(buildPartyQuery({
                serviceLocation: invalidId
            })).rejects.toThrow('Invalid serviceLocation ID provided');
            
            expect(isValidObjectId).toHaveBeenCalledWith(invalidId);
        });

        it('should handle object serviceLocation with matching locations', async () => {
            const locationFilters = { city: 'New York' };
            const matchingLocations = [
                { _id: '507f1f77bcf86cd799439011' },
                { _id: '507f1f77bcf86cd799439012' }
            ];

            buildServiceLocationQuery.mockReturnValue({ city: 'New York' });
            MdServiceLocation.find.mockReturnValue({
                select: jest.fn().mockResolvedValue(matchingLocations)
            });

            const result = await buildPartyQuery({
                serviceLocation: locationFilters
            });

            expect(buildServiceLocationQuery).toHaveBeenCalledWith(locationFilters);
            expect(MdServiceLocation.find).toHaveBeenCalledWith({ city: 'New York' });
            expect(result.query.serviceLocation).toEqual({
                $in: matchingLocations.map(loc => loc._id)
            });
        });

        it('should handle object serviceLocation with no matching locations', async () => {
            const locationFilters = { city: 'NonexistentCity' };
            
            buildServiceLocationQuery.mockReturnValue({ city: 'NonexistentCity' });
            MdServiceLocation.find.mockReturnValue({
                select: jest.fn().mockResolvedValue([])
            });

            const result = await buildPartyQuery({
                serviceLocation: locationFilters
            });

            expect(buildServiceLocationQuery).toHaveBeenCalledWith(locationFilters);
            expect(MdServiceLocation.find).toHaveBeenCalledWith({ city: 'NonexistentCity' });
            expect(result.query.serviceLocation).toEqual({ $in: [] });
        });

        it('should handle object serviceLocation with empty query', async () => {
            const locationFilters = {};
            
            buildServiceLocationQuery.mockReturnValue({});

            const result = await buildPartyQuery({
                serviceLocation: locationFilters
            });

            expect(buildServiceLocationQuery).toHaveBeenCalledWith(locationFilters);
            expect(MdServiceLocation.find).not.toHaveBeenCalled();
            expect(result.query.serviceLocation).toBeUndefined();
        });
    });

    describe('applyPostQueryFilters', () => {
        const mockParties = [
            { _id: 'party1', services: ['service1', 'service2'] },
            { _id: 'party2', services: ['service3'] }
        ];

        it('should return original parties when no post-query filters', async () => {
            const { applyPostQueryFilters } = await buildPartyQuery();
            const result = await applyPostQueryFilters(mockParties);
            expect(result).toEqual(mockParties);
        });

        it('should apply all filters correctly', async () => {
            Guest.countDocuments.mockImplementation(async ({ party }) => {
                return party === 'party1' ? 20 : 5;
            });

            PartyService.find.mockImplementation(async ({ _id }) => {
                if (_id.$in.includes('service1')) {
                    return [
                        { budget: 1000, expenditure: 800 },
                        { budget: 500, expenditure: 400 }
                    ];
                }
                return [
                    { budget: 300, expenditure: 200 }
                ];
            });

            const { applyPostQueryFilters } = await buildPartyQuery({
                minExpectedGuests: 10,
                maxExpectedGuests: 30,
                minBudget: 1000,
                maxBudget: 2000,
                minExpenditure: 1000,
                maxExpenditure: 1500
            });

            const filteredParties = await applyPostQueryFilters(mockParties);

            expect(filteredParties).toHaveLength(1);
            expect(filteredParties[0]._id).toBe('party1');
            expect(filteredParties[0].expectedGuestCount).toBe(20);
            expect(filteredParties[0].totalBudget).toBe(1500);
            expect(filteredParties[0].totalExpenditure).toBe(1200);
        });

        it('should handle undefined min/max values correctly', async () => {
            Guest.countDocuments.mockResolvedValue(15);
            PartyService.find.mockResolvedValue([
                { budget: 1000, expenditure: 800 }
            ]);

            const { applyPostQueryFilters } = await buildPartyQuery({
                minExpectedGuests: undefined,
                maxExpectedGuests: 20,
                minBudget: 500,
                maxBudget: undefined
            });

            const filteredParties = await applyPostQueryFilters(mockParties);

            expect(filteredParties).toHaveLength(2);
            expect(Guest.countDocuments).toHaveBeenCalledTimes(2);
            expect(PartyService.find).toHaveBeenCalledTimes(2);
        });

        it('should handle null or undefined service values', async () => {
            Guest.countDocuments.mockResolvedValue(15);
            PartyService.find.mockResolvedValue([
                { budget: null, expenditure: undefined },
                { budget: 1000, expenditure: 800 }
            ]);

            const { applyPostQueryFilters } = await buildPartyQuery({
                minBudget: 500,
                minExpenditure: 500
            });

            const filteredParties = await applyPostQueryFilters(mockParties);

            expect(filteredParties).toHaveLength(2);
            filteredParties.forEach(party => {
                expect(party.totalBudget).toBe(1000);
                expect(party.totalExpenditure).toBe(800);
            });
        });
    });

    it('should build name filter correctly', async () => {
        const result = await buildPartyQuery({
            name: 'test party'
        });

        expect(result.query).toEqual({
            name: { $regex: /test party/i }
        });
    });

    it('should handle string vendorTypes ID correctly', async () => {
        const validId = 'validVendorTypeId';
        isValidObjectId.mockReturnValue(true);

        const result = await buildPartyQuery({
            vendorTypes: validId
        });

        expect(result.query).toEqual({
            vendorTypes: validId
        });
        expect(isValidObjectId).toHaveBeenCalledWith(validId);
    });

    it('should throw error for invalid vendorTypes ID', async () => {
        const invalidId = 'invalidId';
        isValidObjectId.mockReturnValue(false);

        await expect(buildPartyQuery({
            vendorTypes: invalidId
        })).rejects.toThrow('Invalid vendorTypes ID provided');
    });

    it('should handle object vendorTypes filter correctly', async () => {
        const vendorTypeFilter = { name: 'Test Vendor Type' };
        const mockVendorTypes = [
            { _id: 'vendorType1' },
            { _id: 'vendorType2' }
        ];

        buildMdVendorTypeQuery.mockResolvedValue({ name: 'Test Vendor Type' });
        MdVendorType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue(mockVendorTypes)
        });

        const result = await buildPartyQuery({
            vendorTypes: vendorTypeFilter
        });

        expect(buildMdVendorTypeQuery).toHaveBeenCalledWith(vendorTypeFilter);
        expect(MdVendorType.find).toHaveBeenCalledWith({ name: 'Test Vendor Type' });
        expect(result.query.vendorTypes).toEqual({ $in: mockVendorTypes.map(vt => vt._id) });
    });

    it('should handle empty vendorTypes results correctly', async () => {
        const vendorTypeFilter = { name: 'Nonexistent Type' };

        buildMdVendorTypeQuery.mockResolvedValue({ name: 'Nonexistent Type' });
        MdVendorType.find.mockReturnValue({
            select: jest.fn().mockResolvedValue([])
        });

        const result = await buildPartyQuery({
            vendorTypes: vendorTypeFilter
        });

        expect(buildMdVendorTypeQuery).toHaveBeenCalledWith(vendorTypeFilter);
        expect(MdVendorType.find).toHaveBeenCalledWith({ name: 'Nonexistent Type' });
        expect(result.query.vendorTypes).toEqual({ $in: [] });
    });

    it('should combine multiple filters correctly', async () => {
        const timeRange = {
            start: new Date('2023-01-01'),
            end: new Date('2023-12-31')
        };

        isValidObjectId.mockReturnValue(true);
        const validVendorTypeId = 'validVendorTypeId';

        const result = await buildPartyQuery({
            name: 'test party',
            timeRange,
            minBudget: 500,
            maxBudget: 2500,
            vendorTypes: validVendorTypeId
        });

        expect(result.query).toEqual({
            name: { $regex: /test party/i },
            time: {
                $gte: timeRange.start,
                $lte: timeRange.end
            },
            vendorTypes: validVendorTypeId
        });

        expect(result.postQueryFilters).toEqual({
            budget: {
                min: 500,
                max: 2500
            }
        });
    });

    it('should handle eventId filter correctly', async () => {
        const validEventId = 'validEventId123';
        isValidObjectId.mockReturnValue(true);

        const result = await buildPartyQuery({
            eventId: validEventId
        });

        expect(result.query).toEqual({
            eventId: validEventId
        });
        expect(isValidObjectId).toHaveBeenCalledWith(validEventId);
    });

    it('should throw error for invalid eventId', async () => {
        const invalidEventId = 'invalid';
        isValidObjectId.mockReturnValue(false);

        await expect(buildPartyQuery({
            eventId: invalidEventId
        })).rejects.toThrow('Invalid eventId provided');
        expect(isValidObjectId).toHaveBeenCalledWith(invalidEventId);
    });
}); 
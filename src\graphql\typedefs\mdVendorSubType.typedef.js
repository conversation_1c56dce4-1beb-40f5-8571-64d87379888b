const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdVendorSubType {
        id: ID!
        name: String!
        description: String!
        icon: MdIcon
        createdAt: DateTime
        updatedAt: DateTime
    }

    input MdVendorSubTypeFilterInput {
        id: String
        name: String
        description: String
    }

    type MdVendorSubTypeWrapper {
        mdVendorSubType: MdVendorSubType!
    }

    type MdVendorSubTypesWrapper {
        mdVendorSubTypes: [MdVendorSubType]!
    }

    type MdVendorSubTypeResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdVendorSubTypeWrapper!
    }

    type MdVendorSubTypesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdVendorSubTypesWrapper!
        pagination: PaginationInfo!
    }

    type MdVendorSubTypeErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdVendorSubTypeResult = MdVendorSubTypeResponse | MdVendorSubTypeErrorResponse
    union MdVendorSubTypesResult = MdVendorSubTypesResponse | MdVendorSubTypeErrorResponse

    type Query {
        getMdVendorSubTypeById(id: ID!): MdVendorSubTypeResult!
        getMdVendorSubTypes(filter: MdVendorSubTypeFilterInput, pagination: PaginationInput): MdVendorSubTypesResult!
    }

    input MdVendorSubTypeInput {
        name: String!
        description: String!
        icon: ID
    }

    input MdVendorSubTypeUpdateInput {
        name: String
        description: String
        icon: ID
    }

    type Mutation {
        createMdVendorSubType(input: MdVendorSubTypeInput!): MdVendorSubTypeResult!
        updateMdVendorSubType(id: ID!, input: MdVendorSubTypeUpdateInput!): MdVendorSubTypeResult!
        deleteMdVendorSubType(id: ID!): MdVendorSubTypeResult!
    }
`; 
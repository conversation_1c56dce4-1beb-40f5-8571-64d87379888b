const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type MdEventType {
        id: ID!
        name: String!
        description: String!
        duration: EventDuration!
        partyTypes: [MdPartyType!]
        bannerImage: String
        createdAt: DateTime
        updatedAt: DateTime
    }

    enum EventDuration {
        ONE_DAY
        MULTI_DAY
    }

    input MdEventTypeFilterInput {
        id: String
        name: String
        duration: EventDuration
        description: String
    }

    type MdEventTypeWrapper {
        mdEventType: MdEventType!
    }

    type MdEventTypesWrapper {
        mdEventTypes: [MdEventType]!
    }

    type MdEventTypeResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdEventTypeWrapper!
    }

    type MdEventTypesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdEventTypesWrapper!
        pagination: PaginationInfo!
    }

    type MdEventTypeErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdEventTypeResult = MdEventTypeResponse | MdEventTypeErrorResponse
    union MdEventTypesResult = MdEventTypesResponse | MdEventTypeErrorResponse

    type Query {
        getMdEventTypeById(id: ID!): MdEventTypeResult!
        getMdEventTypes(filter: MdEventTypeFilterInput, pagination: PaginationInput): MdEventTypesResult!
    }

    input MdEventTypeInput {
        name: String!
        description: String!
        duration: EventDuration!
        partyTypes: [ID!]
        bannerImage: String
    }

    input MdEventTypeUpdateInput {
        name: String
        description: String
        duration: EventDuration
        partyTypes: [ID!]
        bannerImage: String
    }

    type Mutation {
        createMdEventType(input: MdEventTypeInput!): MdEventTypeResult!
        updateMdEventType(id: ID!, input: MdEventTypeUpdateInput!): MdEventTypeResult!
        deleteMdEventType(id: ID!): MdEventTypeResult!
    }
`;
const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

const mdInterestTypeDefs = gql`
    ${sharedTypeDef}

    type MdInterest {
        id: ID!
        title: String!
        icon: MdIcon
    }

    type MdInterestWrapper {
        mdInterest: MdInterest!
    }

    type MdInterestsWrapper {
        mdInterests: [MdInterest]!
    }
    
    type MdInterestResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInterestWrapper!
    }

    type MdInterestsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdInterestsWrapper!
        pagination: PaginationInfo!
    }

    type MdInterestErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    input MdInterestFilterInput {
        id: ID
        title: String
    }

    union MdInterestResult = MdInterestResponse | MdInterestErrorResponse
    union MdInterestsResult = MdInterestsResponse | MdInterestErrorResponse

    type Query {
        getMdInterestById(id: ID!): MdInterestResult!
        getMdInterests(filter: MdInterestFilterInput, pagination: PaginationInput): MdInterestsResult!
    }

    input MdInterestInput {
        title: String!
        icon: ID
    }

    input MdInterestUpdateInput {
        title: String
        icon: ID
    }

    type Mutation {
        createMdInterest(input: MdInterestInput!): MdInterestResult!
        updateMdInterest(id: ID!, input: MdInterestUpdateInput!): MdInterestResult!
        deleteMdInterest(id: ID!): MdInterestResult!
    }
`;

module.exports = mdInterestTypeDefs;

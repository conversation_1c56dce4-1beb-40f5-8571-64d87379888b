const mongoose = require('mongoose');
const MdPromotion = require('../../../../src/models/MdPromotion');
const MdPartner = require('../../../../src/models/MdPartner');
const Tag = require('../../../../src/models/Tag');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildMdPromotionQuery = require('../../../../src/graphql/resolvers/filters/mdPromotion.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const mdPromotionResolvers = require('../../../../src/graphql/resolvers/mdPromotion.resolver');

jest.mock('../../../../src/models/MdPromotion');
jest.mock('../../../../src/models/MdPartner');
jest.mock('../../../../src/models/Tag');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/mdPromotion.filter');
jest.mock('../../../../src/utils/referenceCheck.util');

describe('mdPromotionResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('MdPromotion Field Resolvers', () => {
        it('should resolve partner field', async () => {
            const parent = { partner: 'partnerId' };
            const mockPartner = { _id: 'partnerId', name: 'Test Partner' };
            
            MdPartner.findById.mockResolvedValue(mockPartner);
            
            const result = await mdPromotionResolvers.MdPromotion.partner(parent);
            
            expect(MdPartner.findById).toHaveBeenCalledWith(parent.partner);
            expect(result).toEqual(mockPartner);
        });

        it('should handle error in partner resolver', async () => {
            const parent = { partner: 'partnerId' };
            const error = new Error('Database error');
            
            MdPartner.findById.mockRejectedValue(error);
            
            await expect(mdPromotionResolvers.MdPromotion.partner(parent))
                .rejects.toThrow('Error getting partner');
        });

        it('should resolve tags field', async () => {
            const parent = { tags: ['tag1', 'tag2'] };
            const mockTags = [
                { _id: 'tag1', name: 'Tag 1' },
                { _id: 'tag2', name: 'Tag 2' }
            ];
            
            Tag.find.mockResolvedValue(mockTags);
            
            const result = await mdPromotionResolvers.MdPromotion.tags(parent);
            
            expect(Tag.find).toHaveBeenCalledWith({ _id: { $in: parent.tags } });
            expect(result).toEqual(mockTags);
        });

        it('should handle error in tags resolver', async () => {
            const parent = { tags: ['tag1', 'tag2'] };
            const error = new Error('Database error');
            
            Tag.find.mockRejectedValue(error);
            
            await expect(mdPromotionResolvers.MdPromotion.tags(parent))
                .rejects.toThrow('Error getting tags');
        });
    });

    describe('Query Resolvers', () => {
        describe('getMdPromotionById', () => {
            it('should return promotion from cache if available', async () => {
                const id = 'promotionId';
                const cachedPromotion = { _id: id, name: 'Cached Promotion' };
                
                getByIdCache.mockResolvedValue(cachedPromotion);
                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPromotion.findById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionResponse',
                    'SUCCESS',
                    'MdPromotion fetched successfully',
                    { result: { mdPromotion: cachedPromotion } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache promotion if not in cache', async () => {
                const id = 'promotionId';
                const promotion = { _id: id, name: 'Test Promotion' };
                
                getByIdCache.mockResolvedValue(null);
                const mockPopulatePartner = jest.fn().mockReturnThis();
                const mockPopulateTags = jest.fn().mockResolvedValue(promotion);
                
                MdPromotion.findById.mockReturnValue({
                    populate: mockPopulatePartner
                });
                mockPopulatePartner.mockReturnValue({
                    populate: mockPopulateTags
                });
                
                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPromotion.findById).toHaveBeenCalledWith(id);
                expect(mockPopulatePartner).toHaveBeenCalledWith('partner');
                expect(mockPopulateTags).toHaveBeenCalledWith('tags');
                expect(setCache).toHaveBeenCalledWith(id, promotion);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionResponse',
                    'SUCCESS',
                    'MdPromotion fetched successfully',
                    { result: { mdPromotion: promotion } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors and return error response', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                
                getByIdCache.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Error getting MdPromotion',
                    {
                        errors: [{ field: 'getMdPromotionById', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error response when promotion not found', async () => {
                const id = 'nonexistentId';
                getByIdCache.mockResolvedValue(null);
                
                const mockPopulateChain = {
                    populate: jest.fn().mockReturnThis(),
                };
                mockPopulateChain.populate.mockReturnValueOnce(mockPopulateChain)
                    .mockReturnValueOnce(null);
                
                MdPromotion.findById.mockReturnValue(mockPopulateChain);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(MdPromotion.findById).toHaveBeenCalledWith(id);
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('partner');
                expect(mockPopulateChain.populate).toHaveBeenCalledWith('tags');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'MdPromotion not found',
                    {
                        errors: [{ field: 'id', message: 'MdPromotion not found' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getMdPromotions', () => {
            it('should return promotions with pagination', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'Test', $options: 'i' } };
                const promotions = [{ _id: '1', name: 'Test Promotion' }];
                const paginationInfo = { total: 1, hasMore: false };

                buildMdPromotionQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                const mockPopulate = jest.fn().mockReturnThis();
                const mockLimit = jest.fn().mockReturnThis();
                const mockSkip = jest.fn().mockReturnThis();
                const mockSort = jest.fn().mockResolvedValue(promotions);

                MdPromotion.find.mockReturnValue({
                    populate: mockPopulate,
                    limit: mockLimit,
                    skip: mockSkip,
                    sort: mockSort
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotions(null, { filter, pagination });

                expect(buildMdPromotionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdPromotion, query, 10, 0);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionsResponse',
                    'SUCCESS',
                    'Promotions fetched successfully',
                    {
                        result: { mdPromotions: promotions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors and return error response', async () => {
                const filter = { name: 'Test' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');
                
                buildMdPromotionQuery.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotions(null, { filter, pagination });

                expect(buildMdPromotionQuery).toHaveBeenCalledWith(filter);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Error getting promotions',
                    {
                        errors: [{ field: 'getMdPromotions', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return failure response when no promotions found', async () => {
                const filter = { name: 'nonexistent' };
                const pagination = { limit: 10, skip: 0 };
                const query = { name: { $regex: 'nonexistent', $options: 'i' } };
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
                const mdPromotions = [];

                buildMdPromotionQuery.mockReturnValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(mdPromotions),
                };

                MdPromotion.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('failureResponse');

                const result = await mdPromotionResolvers.Query.getMdPromotions(null, { filter, pagination });

                expect(buildMdPromotionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(MdPromotion, query, pagination.limit, pagination.skip);
                expect(MdPromotion.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith('partner');
                expect(mockFind.populate).toHaveBeenCalledWith('tags');
                expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
                expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
                expect(mockFind.sort).toHaveBeenCalledWith({ createdAt: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionsResponse',
                    'FAILURE',
                    'No promotions found',
                    {
                        result: { mdPromotions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('failureResponse');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createMdPromotion', () => {
            it('should create promotion successfully', async () => {
                const input = {
                    name: 'New Promotion',
                    partner: 'partnerId',
                    tags: ['tag1']
                };
                
                const savedPromotion = { _id: 'newId', ...input };
                const populatedPromotion = { 
                    ...savedPromotion, 
                    partner: { _id: 'partnerId' }, 
                    tags: [{ _id: 'tag1' }] 
                };
                
                MdPromotion.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(savedPromotion)
                }));

                const mockPopulatePartner = jest.fn().mockReturnThis();
                const mockPopulateTags = jest.fn().mockResolvedValue(populatedPromotion);
                
                MdPromotion.findById.mockReturnValue({
                    populate: mockPopulatePartner
                });
                mockPopulatePartner.mockReturnValue({
                    populate: mockPopulateTags
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Mutation.createMdPromotion(null, { input });

                expect(MdPromotion).toHaveBeenCalledWith(input);
                expect(mockPopulatePartner).toHaveBeenCalledWith('partner');
                expect(mockPopulateTags).toHaveBeenCalledWith('tags');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionResponse',
                    'SUCCESS',
                    'Promotion created successfully',
                    { result: { mdPromotion: populatedPromotion } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle error when creating promotion fails', async () => {
                const input = {
                    name: 'Test Promotion',
                    partner: 'partnerId',
                    description: 'Test description',
                    thumbnail: 'thumbnail.jpg',
                    mediaUrl: 'media.mp4',
                    ctaLink: 'https://example.com',
                    startDate: new Date('2024-01-01'),
                    endDate: new Date('2024-12-31')
                };
                
                const error = new Error('Database error');
                
                MdPromotion.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.createMdPromotion(null, { input });

                expect(MdPromotion).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Error creating promotion',
                    {
                        errors: [{ field: 'createMdPromotion', message: error.message }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('deleteMdPromotion', () => {
            it('should delete promotion successfully', async () => {
                const id = 'testId';
                const mockPromotion = { _id: id, name: 'Test Promotion' };
                
                findReferences.mockResolvedValue([]);
                MdPromotion.findByIdAndDelete.mockResolvedValue(mockPromotion);
                clearCacheById.mockResolvedValue();
                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Mutation.deleteMdPromotion(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPromotion');
                expect(MdPromotion.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionResponse',
                    'SUCCESS',
                    'Promotion deleted successfully',
                    { result: { mdPromotion: mockPromotion } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error if promotion has references', async () => {
                const id = 'testId';
                const references = ['Reference1', 'Reference2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.deleteMdPromotion(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPromotion');
                expect(MdPromotion.findByIdAndDelete).not.toHaveBeenCalled();
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Promotion cannot be deleted',
                    { errors: [{ field: 'id', message: `Promotion cannot be deleted as it is being used in: ${references.join(', ')}` }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return error if promotion not found', async () => {
                const id = 'testId';
                
                findReferences.mockResolvedValue([]);
                MdPromotion.findByIdAndDelete.mockResolvedValue(null);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.deleteMdPromotion(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPromotion');
                expect(MdPromotion.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Promotion not found',
                    { errors: [{ field: 'id', message: 'Promotion not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle unexpected errors', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                
                findReferences.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.deleteMdPromotion(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'MdPromotion');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Error deleting promotion',
                    { errors: [{ field: 'deleteMdPromotion', message: error.message }] }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('updateMdPromotion', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should update promotion successfully', async () => {
                const id = 'testId';
                const input = {
                    name: 'Updated Promotion',
                    description: 'Updated description'
                };

                const updatedPromotion = {
                    _id: id,
                    ...input,
                    partner: { _id: 'partnerId' },
                    tags: [{ _id: 'tagId' }]
                };

                const mockPopulatePartner = jest.fn().mockReturnThis();
                const mockPopulateTags = jest.fn().mockResolvedValue(updatedPromotion);

                MdPromotion.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulatePartner
                });
                mockPopulatePartner.mockReturnValue({
                    populate: mockPopulateTags
                });

                createResponse.mockReturnValue('successResponse');

                const result = await mdPromotionResolvers.Mutation.updateMdPromotion(null, { id, input });

                expect(MdPromotion.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(mockPopulatePartner).toHaveBeenCalledWith('partner');
                expect(mockPopulateTags).toHaveBeenCalledWith('tags');
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionResponse',
                    'SUCCESS',
                    'Promotion updated successfully',
                    { result: { mdPromotion: updatedPromotion } }
                );
                expect(result).toBe('successResponse');
            });

            it('should return error response when promotion not found', async () => {
                const id = 'nonexistentId';
                const input = { name: 'Updated Promotion' };

                const mockPopulatePartner = jest.fn().mockReturnThis();
                const mockPopulateTags = jest.fn().mockResolvedValue(null);

                MdPromotion.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulatePartner
                });
                mockPopulatePartner.mockReturnValue({
                    populate: mockPopulateTags
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.updateMdPromotion(null, { id, input });

                expect(clearCacheById).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Promotion not found',
                    { errors: [{ field: 'id', message: 'Promotion not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle database errors', async () => {
                const id = 'testId';
                const input = { 
                    name: 'Updated Promotion',
                    description: 'Test description'
                };
                const error = new Error('Database error');

                const mockPopulatePartner = jest.fn().mockReturnThis();
                const mockPopulateTags = jest.fn().mockRejectedValue(error);

                MdPromotion.findByIdAndUpdate.mockReturnValue({
                    populate: mockPopulatePartner
                });
                mockPopulatePartner.mockReturnValue({
                    populate: mockPopulateTags
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await mdPromotionResolvers.Mutation.updateMdPromotion(null, { id, input });

                expect(MdPromotion.findByIdAndUpdate).toHaveBeenCalledWith(
                    id,
                    input,
                    { new: true }
                );
                expect(mockPopulatePartner).toHaveBeenCalledWith('partner');
                expect(mockPopulateTags).toHaveBeenCalledWith('tags');
                expect(createResponse).toHaveBeenCalledWith(
                    'MdPromotionErrorResponse',
                    'FAILURE',
                    'Error updating promotion',
                    { errors: [{ field: 'updateMdPromotion', message: 'Database error' }] }
                );
                expect(result).toBe('errorResponse');
            });
        });
    });
}); 
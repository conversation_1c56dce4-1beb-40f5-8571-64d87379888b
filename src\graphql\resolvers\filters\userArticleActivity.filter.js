const { isValidObjectId } = require("../../../utils/validation.util");
const buildArticleFilter = require('./article.filter');
const Article = require('../../../models/Article');

const buildUserArticleActivityFilter = async (filter, userId) => {
    const query = {};

    if (filter) {
        if (filter.article) {
            const articleFilter = buildArticleFilter(filter.article);
            const allArticles = await Article.find({});
            const matchingArticles = allArticles.filter(articleFilter);
            query.articleId = { $in: matchingArticles.map(article => article._id) };
        }

        if (filter.liked !== undefined) {
            query.liked = filter.liked;
        }

        if (filter.saved !== undefined) {
            query.saved = filter.saved;
        }

        if (filter.shared !== undefined) {
            query.shared = filter.shared;
        }

        if (filter.createdAt) {
            query.createdAt = {};
            if (filter.createdAt.from) {
                query.createdAt.$gte = filter.createdAt.from;
            }
            if (filter.createdAt.to) {
                query.createdAt.$lte = filter.createdAt.to;
            }
        }

        if (filter.updatedAt) {
            query.updatedAt = {};
            if (filter.updatedAt.from) {
                query.updatedAt.$gte = filter.updatedAt.from;
            }
            if (filter.updatedAt.to) {
                query.updatedAt.$lte = filter.updatedAt.to;
            }
        }

        // Always include userId in the query if provided
        if (userId) {
            query.userId = userId;
        }
    }

    return query;
};

module.exports = buildUserArticleActivityFilter; 
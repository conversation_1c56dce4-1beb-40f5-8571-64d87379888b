const MdInterestCategory = require('../../models/MdInterestCategory');
const MdInterest = require('../../models/MdInterest');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const buildMdInterestCategoryQuery = require('./filters/mdInterestCategory.filter');

const mdInterestCategoryResolvers = {
    MdInterestCategory: {
        interests: async (parent) => {
            try{
                return await MdInterest.find({ _id: { $in: parent.interests } });
            } catch (error) {
                console.error(error);
                throw new Error(error);
            }   
        }
    },

    Query: {
        getMdInterestCategoryById: async (_, { id }, context) => {
            try{
                let mdInterestCategory = await getByIdCache(id);
                if (!mdInterestCategory) {
                    mdInterestCategory = await MdInterestCategory.findById(id);
                }
                return createResponse('MdInterestCategoryResponse', 'SUCCESS', 'MdInterestCategory fetched successfully', { result: { mdInterestCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'Error fetching MdInterestCategory', { errors: [{ field: 'getMdInterestCategoryById', message: error.message }] });
            }
        },

        getMdInterestCategories: async (_, { filter, pagination }, context) => {
            try{
                const query = await buildMdInterestCategoryQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
                const mdInterestCategories = await MdInterestCategory.find(query).skip(skip).limit(limit).sort({ createdAt: -1 });
                const paginationInfo = await getPaginationInfo(MdInterestCategory, query, limit, skip);
                if (mdInterestCategories.length === 0) {
                    return createResponse('MdInterestCategoriesResponse', 'FAILURE', 'No MdInterestCategories found', { result: { mdInterestCategories }, pagination: paginationInfo });
                }
                return createResponse('MdInterestCategoriesResponse', 'SUCCESS', 'MdInterestCategories fetched successfully', { result: { mdInterestCategories }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'Error fetching MdInterestCategories', { errors: [{ field: 'getMdInterestCategories', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdInterestCategory: async (_, { input }, context) => {
            try{
                const mdInterestCategory = new MdInterestCategory(input);
                await mdInterestCategory.save();
                await setCache(mdInterestCategory._id, mdInterestCategory);
                return createResponse('MdInterestCategoryResponse', 'SUCCESS', 'MdInterestCategory created successfully', { result: { mdInterestCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'Error creating MdInterestCategory', { errors: [{ field: 'createMdInterestCategory', message: error.message }] });
            }
        },

        updateMdInterestCategory: async (_, { id, input }, context) => {
            try{
                const mdInterestCategory = await MdInterestCategory.findByIdAndUpdate(id, input, { new: true });
                if (!mdInterestCategory) {
                    return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'MdInterestCategory not found', { errors: [{ field: 'id', message: 'MdInterestCategory not found' }] });
                }
                await setCache(id, mdInterestCategory);
                return createResponse('MdInterestCategoryResponse', 'SUCCESS', 'MdInterestCategory updated successfully', { result: { mdInterestCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'Error updating MdInterestCategory', { errors: [{ field: 'updateMdInterestCategory', message: error.message }] });
            }
        },

        deleteMdInterestCategory: async (_, { id }, context) => {
            try{
                const mdInterestCategory = await MdInterestCategory.findByIdAndDelete(id);
                if (!mdInterestCategory) {
                    return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'MdInterestCategory not found', { errors: [{ field: 'id', message: 'MdInterestCategory not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdInterestCategoryResponse', 'SUCCESS', 'MdInterestCategory deleted successfully', { result: { mdInterestCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdInterestCategoryErrorResponse', 'FAILURE', 'Error deleting MdInterestCategory', { errors: [{ field: 'deleteMdInterestCategory', message: error.message }] });
            }
        }
    }
}

module.exports = mdInterestCategoryResolvers;
const MdIcon = require('../../models/MdIcon');
const MdPartnerCategory = require('../../models/MdPartnerCategory');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { createResponse } = require('../../utils/response.util');
const buildMdPartnerCategoryQuery = require('./filters/mdPatnerCategory.filter');

const mdPartnerCategoryResolvers = {
    MdPartnerCategory: {
        icon: async (parent) => {
            try {
                return await MdIcon.findById(parent.icon);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting icon');
            }
        },
    },

    Query: {
        getMdPartnerCategoryById: async (_, { id }) => {
            try {
                let mdPartnerCategory = await getByIdCache(id);
                if (!mdPartnerCategory) {
                    mdPartnerCategory = await MdPartnerCategory.findById(id);
                    if (!mdPartnerCategory) {
                        return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'MdPartnerCategory not found', { 
                            errors: [{ field: 'getMdPartnerCategoryById', message: 'MdPartnerCategory not found' }] 
                        });
                    }
                    mdPartnerCategory = await mdPartnerCategory.populate('icon');
                    await setCache(id, mdPartnerCategory);
                }
                return createResponse('MdPartnerCategoryResponse', 'SUCCESS', 'MdPartnerCategory fetched successfully', { 
                    result: { mdPartnerCategory } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error retrieving MdPartnerCategory', { 
                    errors: [{ field: 'getMdPartnerCategoryById', message: error.message }] 
                });
            }
        },
        getMdPartnerCategories: async (_, { filter, pagination }) => {
            try {
                const query = buildMdPartnerCategoryQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdPartnerCategory, query, limit, skip);

                const mdPartnerCategories = await MdPartnerCategory.find(query).populate('icon').limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdPartnerCategories.length === 0) {
                    return createResponse('MdPartnerCategoriesResponse', 'FAILURE', 'No mdPartnerCategories found', { result: { mdPartnerCategories }, pagination: paginationInfo });
                }
                return createResponse('MdPartnerCategoriesResponse', 'SUCCESS', 'MdPartnerCategories fetched successfully', { result: { mdPartnerCategories }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error retrieving MdPartnerCategories', { errors: [{ field: 'getMdPartnerCategories', message: error.message }] });
            }
        }
    },
    Mutation: {
        createMdPartnerCategory: async (_, { input }) => {
            try {
                const mdPartnerCategory = new MdPartnerCategory(input);
                await mdPartnerCategory.save();
                return createResponse('MdPartnerCategoryResponse', 'SUCCESS', 'MdPartnerCategory created successfully', { result: { mdPartnerCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error creating MdPartnerCategory', { errors: [{ field: 'createMdPartnerCategory', message: error.message }] });
            }
        },
        updateMdPartnerCategory: async (_, { id, input }) => {
            try {
                const mdPartnerCategory = await MdPartnerCategory.findByIdAndUpdate(id, input, { new: true }).populate('icon');
                if (!mdPartnerCategory) {
                    return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'MdPartnerCategory not found', { errors: [{ field: 'updateMdPartnerCategory', message: 'MdPartnerCategory not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdPartnerCategoryResponse', 'SUCCESS', 'MdPartnerCategory updated successfully', { result: { mdPartnerCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error updating MdPartnerCategory', { errors: [{ field: 'updateMdPartnerCategory', message: error.message }] });
            }
        },
        deleteMdPartnerCategory: async (_, { id }) => {
            try {
                const mdPartnerCategory = await MdPartnerCategory.findByIdAndDelete(id);
                if (!mdPartnerCategory) {
                    return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'MdPartnerCategory not found', { errors: [{ field: 'deleteMdPartnerCategory', message: 'MdPartnerCategory not found' }] });
                }
                await clearCacheById(id);
                return createResponse('MdPartnerCategoryResponse', 'SUCCESS', 'MdPartnerCategory deleted successfully', { result: { mdPartnerCategory } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartnerCategoryErrorResponse', 'FAILURE', 'Error deleting MdPartnerCategory', { errors: [{ field: 'deleteMdPartnerCategory', message: error.message }] });
            }
        },
    },
};

module.exports = mdPartnerCategoryResolvers;
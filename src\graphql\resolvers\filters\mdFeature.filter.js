const { isValidObjectId } = require("../../../utils/validation.util");

const buildMdFeatureQuery = (filter) => {
    const query = {};

    if (filter) {
        if(filter.id) {
            if(isValidObjectId(filter.id)) {
                query._id = filter.id;
            } else {
                throw new Error('Invalid id provided');
            }
        }
        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }
    }

    return query;
};

module.exports = buildMdFeatureQuery;
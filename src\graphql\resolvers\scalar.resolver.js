const { GraphQLScalarType, Kind } = require('graphql');

// JSON scalar type
const JSONScalar = new GraphQLScalarType({
  name: 'JSO<PERSON>',
  description: 'The JSON scalar type represents JSON objects as JSON strings',
  serialize(value) {
    return value;
  },
  parseValue(value) {
    return value;
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      try {
        return JSON.parse(ast.value);
      } catch (e) {
        return null;
      }
    }
    
    if (ast.kind === Kind.OBJECT) {
      const value = Object.create(null);
      ast.fields.forEach(field => {
        value[field.name.value] = parseLiteral(field.value);
      });
      return value;
    }
    
    if (ast.kind === Kind.LIST) {
      return ast.values.map(parseLiteral);
    }
    
    return null;
  }
});

// Helper function for recursive parsing
function parseLiteral(ast) {
  switch (ast.kind) {
    case Kind.STRING:
    case Kind.BOOLEAN:
      return ast.value;
    case Kind.INT:
    case Kind.FLOAT:
      return parseFloat(ast.value);
    case Kind.OBJECT:
      const value = Object.create(null);
      ast.fields.forEach(field => {
        value[field.name.value] = parseLiteral(field.value);
      });
      return value;
    case Kind.LIST:
      return ast.values.map(parseLiteral);
    case Kind.NULL:
      return null;
    default:
      return null;
  }
}

const scalarResolvers = {
  JSON: JSONScalar
};

module.exports = scalarResolvers; 
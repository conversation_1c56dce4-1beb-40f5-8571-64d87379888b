const InAppNotification = require('../../models/InAppNotification');
const { User } = require('../../models/User');
const { isValidObjectId } = require('../../utils/validation.util');
const buildInAppNotificationQuery = require('./filters/inAppNotification.filter');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { validateReferences } = require('../../utils/validation.util');
const { findReferences } = require('../../utils/referenceCheck.util');

const baseInAppNotificationIdSchema = {
    userIds: { type: 'array', required: true, model: User }
}

const inAppNotificationIdSchema = {
    ...baseInAppNotificationIdSchema,
    message: { type: 'single', required: true }
}

const updateInAppNotificationIdSchema = {
    message: { type: 'single', required: false },
    media: { type: 'single', required: false },
    link: { type: 'single', required: false },
    read: { type: 'boolean', required: false }
};

const inAppNotificationResolvers = {
    InAppNotification: {
        user: async (parent) => {
            try {
                return await User.findById(parent.userId);
            } catch (error) {
                console.error('Error in user resolver:', error);
                throw new Error('Error getting user');
            }
        },
        sender: async (parent) => {
            try {
                return await User.findById(parent.sender);
            } catch (error) {
                console.error('Error in sender resolver:', error);
                throw new Error('Error getting sender');
            }
        }
    },

    Query: {
        getInAppNotificationById: async (_, { id }, context) => {
            try {
                if (!isValidObjectId(id)) {
                    throw new Error('Invalid notification ID');
                }

                const notification = await InAppNotification.findById(id);
                if (!notification) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Notification not found', {
                        errors: [{ field: 'id', message: 'Notification not found' }]
                    });
                }

                if (notification.userId.toString() !== context.user._id.toString()) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this notification' }]
                    });
                }

                return createResponse('InAppNotificationResponse', 'SUCCESS', 'Notification retrieved successfully', {
                    result: { inAppNotification: notification }
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error retrieving notification', {
                    errors: [{ field: 'getInAppNotificationById', message: error.message }]
                });
            }
        },

        getInAppNotifications: async (_, { filter = {}, pagination }, context) => {
            try {
                // Convert context.user._id to string if it's an ObjectId
                const userId = context.user._id.toString();
                
                const userFilter = {
                    ...filter,
                    userId  // Pass the string ID
                };

                const query = await buildInAppNotificationQuery(userFilter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(InAppNotification, query, limit, skip);

                const notifications = await InAppNotification.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (notifications.length === 0) {
                    return createResponse('InAppNotificationsResponse', 'FAILURE', 'No notifications found', {
                        result: { inAppNotifications: [] },
                        pagination: paginationInfo
                    });
                }

                return createResponse('InAppNotificationsResponse', 'SUCCESS', 'Notifications retrieved successfully', {
                    result: { inAppNotifications: notifications },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error retrieving notifications', {
                    errors: [{ field: 'getInAppNotifications', message: error.message }]
                });
            }
        },

        getInAppNotificationCount: async (_, {}, context) => {
            try {
                const count = await InAppNotification.countDocuments({
                    userId: context.user._id,
                    read: false
                });

                return count;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
    },

    Mutation: {
        createInAppNotification: async (_, { input }, context) => {
            try {
                const { userIds, ...notificationData } = input;
                
                const validationError = await validateReferences(
                    { userIds }, 
                    baseInAppNotificationIdSchema, 
                    'InAppNotification'
                );
                if (validationError) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const notifications = await Promise.all(
                    userIds.map(async (userId) => {
                        const notification = new InAppNotification({
                            ...notificationData,
                            userId
                        });
                        await notification.save();
                        return notification;
                    })
                );

                // Populate all notifications
                const populatedNotifications = await InAppNotification.find({
                    _id: { $in: notifications.map(n => n._id) }
                }).populate('userId');

                return createResponse('CreateInAppNotificationsResponse', 'SUCCESS', 
                    `Notifications created successfully for ${userIds.length} users`, {
                    result: { inAppNotifications: populatedNotifications }
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error creating notifications', {
                    errors: [{ field: 'createInAppNotification', message: error.message }]
                });
            }
        },

        updateInAppNotification: async (_, { id, input }, context) => {
            try {
                if (!isValidObjectId(id)) {
                    throw new Error('Invalid notification ID');
                }

                const existingNotification = await InAppNotification.findById(id);
                if (!existingNotification) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Notification not found', {
                        errors: [{ field: 'id', message: 'Notification not found' }]
                    });
                }

                if (existingNotification.userId.toString() !== context.user._id.toString()) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have permission to update this notification' }]
                    });
                }

                const notification = await InAppNotification.findByIdAndUpdate(
                    id,
                    { $set: input },
                    { new: true }
                ).populate('userId');

                return createResponse('InAppNotificationResponse', 'SUCCESS', 'Notification updated successfully', {
                    result: { inAppNotification: notification }
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error updating notification', {
                    errors: [{ field: 'updateInAppNotification', message: error.message }]
                });
            }
        },

        deleteInAppNotification: async (_, { id }, context) => {
            try {
                if (!isValidObjectId(id)) {
                    throw new Error('Invalid notification ID');
                }

                const existingNotification = await InAppNotification.findById(id);
                if (!existingNotification) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Notification not found', {
                        errors: [{ field: 'id', message: 'Notification not found' }]
                    });
                }

                if (existingNotification.userId.toString() !== context.user._id.toString()) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have permission to delete this notification' }]
                    });
                }

                const notification = await InAppNotification.findByIdAndDelete(id)
                    .populate('userId');

                return createResponse('InAppNotificationResponse', 'SUCCESS', 'Notification deleted successfully', {
                    result: { inAppNotification: notification }
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error deleting notification', {
                    errors: [{ field: 'deleteInAppNotification', message: error.message }]
                });
            }
        },

        markNotificationAsRead: async (_, { id }, context) => {
            try {
                if (!isValidObjectId(id)) {
                    throw new Error('Invalid notification ID');
                }

                const existingNotification = await InAppNotification.findById(id);
                if (!existingNotification) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Notification not found', {
                        errors: [{ field: 'id', message: 'Notification not found' }]
                    });
                }

                if (existingNotification.userId.toString() !== context.user._id.toString()) {
                    return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have permission to mark this notification as read' }]
                    });
                }

                const notification = await InAppNotification.findByIdAndUpdate(
                    id,
                    { $set: { read: true } },
                    { new: true }
                ).populate('userId');

                return createResponse('InAppNotificationResponse', 'SUCCESS', 'Notification marked as read', {
                    result: { inAppNotification: notification }
                });
            } catch (error) {
                console.error(error);
                return createResponse('InAppNotificationErrorResponse', 'FAILURE', 'Error marking notification as read', {
                    errors: [{ field: 'markNotificationAsRead', message: error.message }]
                });
            }
        },

        markAllNotificationsAsRead: async (_, {}, context) => {
            try {
                await InAppNotification.updateMany(
                    { userId: context.user._id, read: false },
                    { $set: { read: true } }
                );

                return true;
            } catch (error) {
                console.error(error);
                throw error;
            }
        }
    }
};

module.exports = inAppNotificationResolvers;
const { jwtVerify } = require('jose');
const { createPublicKey } = require('crypto');
const { createClerkClient } = require("@clerk/backend");
const { GraphQLError } = require("graphql");

function isValidJWT(token) {
    const jwtRegex = /^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$/;

    if (!token || typeof token !== 'string' || token.length > 1024) {
        return false;
    }

    if (!jwtRegex.test(token)) {
        return false;
    }

    const segments = token.split('.');
    try {
        for (const segment of segments) {
            const padded = segment + '='.repeat((4 - segment.length % 4) % 4);
            Buffer.from(padded, 'base64');
        }
        return true;
    } catch {
        return false;
    }
}

async function verifyTokenLocally(token) {
    try {
        if (!process.env.CLERK_PUBLIC_KEY) {
            throw new Error('Configuration error');
        }

        if (!isValidJWT(token)) {
            throw new Error('Invalid token format');
        }

        const pemKey = `-----BEGIN PUBLIC KEY-----\n${process.env.CLERK_PUBLIC_KEY}\n-----END PUBLIC KEY-----`;
        const publicKey = createPublicKey({
            key: pemKey,
            format: 'pem',
            type: 'spki'
        });

        const verifyOptions = {
            issuer: process.env.CLERK_ISSUER_URL,
            algorithms: ['RS256'],
            clockTolerance: process.env.CLOCK_TOLERANCE.split('*')
                .reduce((acc, val) => acc * Number(val), 1),
            requiredClaims: ['sub', 'iss', 'exp', 'iat'],
            ignoreAudience: true
        };

        try {
            const { payload } = await jwtVerify(token, publicKey, verifyOptions);
            return payload;
        } catch (jwtError) {
            if (jwtError.code === 'ERR_JWT_EXPIRED') {
                throw new GraphQLError('Token has expired', {
                    extensions: {
                        code: 'TOKEN_EXPIRED',
                        http: { status: 401 }
                    }
                });
            }
            throw jwtError;
        }
    } catch (error) {
        console.error('Token verification error:', {
            name: error.name,
            code: error.code,
            message: error.message
        });

        throw new GraphQLError('Authentication failed', {
            extensions: {
                code: 'UNAUTHENTICATED',
                http: { status: 401 }
            }
        });
    }
}

async function verifyClerkUser(userId) {
    const clerkClient = createClerkClient({
        secretKey: process.env.CLERK_SECRET_KEY
    });

    try {
        return await clerkClient.users.getUser(userId);
    } catch (error) {
        console.log("error", error);
        throw new GraphQLError('Could not find user in clerk', {
            extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
        });
    }
}

async function updateUserRole(clerkUserId, role) {
    const clerkClient = createClerkClient({
        secretKey: process.env.CLERK_SECRET_KEY
    });

    try {
        await clerkClient.users.updateUserMetadata(clerkUserId, {
            publicMetadata: {
                role: role
            }
        });
    } catch (error) {
        console.error('Error updating Clerk user role:', error);
        throw new GraphQLError('Failed to update user role in authentication service', {
            extensions: { code: 'CLERK_ERROR', http: { status: 500 } }
        });
    }
}

module.exports = {
    isValidJWT,
    verifyTokenLocally,
    verifyClerkUser,
    updateUserRole
}; 
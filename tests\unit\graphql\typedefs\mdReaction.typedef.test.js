const { gql } = require('graphql-tag');
const mdReactionTypeDef = require('../../../../src/graphql/typedefs/mdReaction.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mdReactionTypeDef', () => {
  it('should contain the MdReaction type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type MdReaction {
        id: ID!
        name: String!
        icon: MdIcon!
        active: Boolean!
        createdAt: DateTime
        updatedAt: DateTime
      }

      input MdReactionFilterInput {
        id: String
        name: String
        icon: MdIconFilterInput
        active: Boolean
      }

      type MdReactionWrapper {
        mdReaction: MdReaction!
      }

      type MdReactionsWrapper {
        mdReactions: [MdReaction]!
      }

      type MdReactionsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdReactionsWrapper!
        pagination: PaginationInfo!
      }

      type MdReactionResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdReactionWrapper!
      }

      type MdReactionErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union MdReactionResult = MdReactionResponse | MdReactionErrorResponse
      union MdReactionsResult = MdReactionsResponse | MdReactionErrorResponse

      type Query {
        getMdReactionById(id: ID!): MdReactionResult!
        getMdReactions(filter: MdReactionFilterInput, pagination: PaginationInput): MdReactionsResult!
      }

      input MdReactionInput {
        name: String!
        icon: ID!
        active: Boolean!
      }

      input MdReactionUpdateInput {
        name: String
        icon: ID
        active: Boolean
      }

      type Mutation {
        createMdReaction(input: MdReactionInput!): MdReactionResult!
        updateMdReaction(id: ID!, input: MdReactionUpdateInput!): MdReactionResult!
        deleteMdReaction(id: ID!): MdReactionResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(mdReactionTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
}); 
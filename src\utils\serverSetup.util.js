const { ApolloServer } = require("apollo-server-express");
const mongoose = require("mongoose");
const { GraphQLUpload, graphqlUploadExpress } = require('graphql-upload');
const { User } = require('../models/User');
const { GraphQLError } = require("graphql");
const { createServer: createHttpServer } = require('http');
const { setupSocketIO } = require('./socket.util');
const { isValidJWT, verifyTokenLocally, verifyClerkUser } = require('./clerk.util');
const { initializeCache } = require('./cacheInit.util');
require("dotenv").config();

function configureMiddleware(app) {
  app.use((req, res, next) => {
    const originalJson = res.json;
    res.json = function (body) {
      if (body && body.errors) {
        const statusCode = body.errors[0].extensions?.http?.status || 500;
        res.status(statusCode);
      }
      originalJson.call(this, body);
    };
    next();
  });
}

async function createServer(app, typeDefs, resolvers) {
  app.use(graphqlUploadExpress());

  const apolloServer = new ApolloServer({
    typeDefs,
    resolvers: {
      Upload: GraphQLUpload,
      ...resolvers
    },
    status400ForVariableCoercionErrors: true,
    context: createContext,
    uploads: true,
  });

  await apolloServer.start();
  apolloServer.applyMiddleware({ app });

  // Create HTTP server
  const httpServer = createHttpServer(app);

  // Setup Socket.IO
  const io = setupSocketIO(httpServer);

  return {
    apolloServer,
    httpServer,
    io
  };
}

function getOperationName(query) {
  const unnamedQuery = query.match(/{\s*(\w+)/);
  if (unnamedQuery) {
    return unnamedQuery[1];
  }
  return null;
}

async function createContext({ req }) {
  try {
    if (req.body?.query) {
      const exceptedRoutes = ['__schema', 'register'];
      const operationName = getOperationName(req.body.query);
      console.log("operationName", operationName);
      if (operationName && !exceptedRoutes.includes(operationName)) {
        const requiredEnvVars = ['CLERK_SECRET_KEY', 'CLERK_PUBLIC_KEY', 'CLERK_ISSUER_URL'];
        for (const envVar of requiredEnvVars) {
          if (!process.env[envVar]) {
            throw new Error(`Missing required environment variable: ${envVar}`);
          }
        }

        const token = getTokenFromHeader(req);
        console.log("Token from client:", token);
        if (!token) {
          throw new GraphQLError('Authentication required', {
            extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
          });
        }

        const verifiedToken = await verifyTokenLocally(token);
        const roles = verifiedToken.metadata?.role || [];

        await verifyClerkUser(verifiedToken.sub);

        try {
          const dbUser = await User.findOne({ externalId: verifiedToken.sub });

          if (!dbUser) {
            throw new GraphQLError('Could not find user in db', {
              extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
            });
          }
  
          return {
            user: dbUser,
            token: verifiedToken,
            roles: roles
          };
        } catch (error) {
          console.log("error", error);
          throw new GraphQLError('Authentication failed', {
            extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
          });
        }
      }
      
      return {};
    }
    return {};
  } catch (error) {
    console.error("Authentication error:", error);
    throw new GraphQLError('Authentication failed', {
      extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
    });
  }
}

function getRolesFromHeader(req) {
  const roles = req.headers['roles'] || req.headers['roles'];
  return roles ? roles.split(',') : [];
}

function getTokenFromHeader(req) {
  try {
    const authHeader = req.headers.authorization || "";
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(" ");
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    const token = parts[1];
    if (!token || !isValidJWT(token)) {
      throw new GraphQLError('Invalid token format', {
        extensions: { code: 'UNAUTHENTICATED', http: { status: 401 } }
      });
    }

    return token;
  } catch (error) {
    throw new GraphQLError('Invalid authorization header', {
      extensions: { 
        code: 'UNAUTHENTICATED', 
        http: { status: 401 }
      }
    });
  }
}

async function connectToDbAndRunApp(app, server) {
  const MONGODB = process.env.MONGODB;
  const PORT = parseInt(process.env.PORT, 10) || 5010;
  const MONGODB_CONNECT_TIMEOUT = parseInt(process.env.MONGODB_CONNECT_TIMEOUT) || 10000;
  const MONGODB_SOCKET_TIMEOUT = parseInt(process.env.MONGODB_SOCKET_TIMEOUT) || 45000;
  let isConnectedBefore = false;
  let reconnectAttempts = 0;

  if (!MONGODB) {
    console.error("MONGODB environment variable is not set.");
    return Promise.reject(new Error("MONGODB environment variable is not set."));
  }
  
  const attemptReconnect = async () => {
    reconnectAttempts += 1;
    console.log(`MongoDB Reconnection attempt #${reconnectAttempts}...`);
    try {
      await mongoose.connect(MONGODB);
      isConnectedBefore = true;
      console.log("Reconnected to MongoDB.");
      reconnectAttempts = 0; 
    } catch (err) {
      console.error("MongoDB reconnection failed:", err);
      const retryDelay = Math.min(10000, reconnectAttempts * 1000);
      setTimeout(attemptReconnect, retryDelay);
    }
  };

  mongoose.connection.on('disconnected', () => {
    console.error("MongoDB disconnected!");
    if (!isConnectedBefore) {
      console.error("Initial MongoDB connection failed.");
    } else {
      console.error("MongoDB connection lost. Attempting to reconnect...");
      attemptReconnect(); 
    }
  });

  mongoose.connection.on('connected', () => {
    console.log("MongoDB connection established.");
    isConnectedBefore = true;
  });

  mongoose.connection.on('error', (err) => {
    console.error("MongoDB connection error:", err);
    if (!isConnectedBefore) {
      console.error("Unable to connect to MongoDB.");
    }
  });

  try {
    await mongoose.connect(MONGODB, {
      connectTimeoutMS: MONGODB_CONNECT_TIMEOUT,
      socketTimeoutMS: MONGODB_SOCKET_TIMEOUT,
    });
    console.log("MongoDB Connected");

    // Initialize cache after MongoDB connection
    try {
      await initializeCache();
      console.log("Cache initialized successfully");
    } catch (error) {
      console.error("Cache initialization failed:", error);
    }

    return new Promise((resolve, reject) => {
      server.httpServer.listen(PORT, (err) => {
        if (err) {
          console.error("Error starting server:", err);
          return reject(new Error("Server start error"));
        } else {
          console.log(`Server running at http://localhost:${PORT}${server.apolloServer.graphqlPath}`);
          console.log(`Socket.IO server is ready`);
          resolve();
        }
      });
    });
  } catch (err) {
    console.error("Initial MongoDB connection failed:", err);
    return Promise.reject(err);
  }
}

module.exports = {
  configureMiddleware,
  createServer,
  createContext,
  getTokenFromHeader,
  connectToDbAndRunApp
};
const { Kind } = require('graphql');
const { Date: dateScalar } = require('../../../../../src/graphql/resolvers/scalars/date.scalar');

describe('dateScalar', () => {
  describe('parseValue', () => {
    it('should parse a valid date string to ISO format', () => {
      const date = '2023-10-01T00:00:00.000Z';
      const result = dateScalar.parseValue(date);
      expect(result).toBe(new Date(date).toISOString());
    });

    it('should throw an error for an invalid date string', () => {
      const date = 'invalid-date';
      expect(() => dateScalar.parseValue(date)).toThrow();
    });
  });

  describe('serialize', () => {
    it('should serialize a Date object to ISO format', () => {
      const date = new Date('2023-10-01T00:00:00.000Z');
      const result = dateScalar.serialize(date);
      expect(result).toBe(date.toISOString());
    });

    it('should serialize a date string to ISO format', () => {
      const date = '2023-10-01T00:00:00.000Z';
      const result = dateScalar.serialize(date);
      expect(result).toBe(new Date(date).toISOString());
    });

    it('should throw an error for an invalid date', () => {
      const date = 'invalid-date';
      expect(() => dateScalar.serialize(date)).toThrow();
    });
  });

  describe('parseLiteral', () => {
    it('should parse a valid date string literal to ISO format', () => {
      const ast = { kind: Kind.STRING, value: '2023-10-01T00:00:00.000Z' };
      const result = dateScalar.parseLiteral(ast);
      expect(result).toBe(new Date(ast.value).toISOString());
    });

    it('should return null for a non-string literal', () => {
      const ast = { kind: Kind.INT, value: 1234567890 };
      const result = dateScalar.parseLiteral(ast);
      expect(result).toBeNull();
    });
  });
});
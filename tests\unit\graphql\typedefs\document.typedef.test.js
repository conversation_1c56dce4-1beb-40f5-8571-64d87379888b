const { gql } = require('graphql-tag');
const documentTypeDef = require('../../../../src/graphql/typedefs/document.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('documentTypeDef', () => {
    it('should contain the Document type definitions', () => {
        const expectedTypeDefs = gql`
            ${sharedTypeDef}

            enum DocumentType {
                RECEIPT
                INVOICE
                CONTRACT
                OTHER
            }

            type Document {
                id: ID!
                name: String!
                documentType: DocumentType!
                documentUrl: String!
                description: String
                createdBy: User!
            }

            input DocumentFilterInput {
                name: String
                documentType: DocumentType
                createdBy: ID
                creatorFirstName: String
                creatorLastName: String
                creatorEmail: String
            }

            type DocumentWrapper {
                document: Document!
            }

            type DocumentsWrapper {
                documents: [Document]!
            }

            type DocumentResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: DocumentWrapper!
            }

            type DocumentsResponse implements Response {
                status: ResponseStatus!
                message: String!
                result: DocumentsWrapper!
                pagination: PaginationInfo!
            }

            type DocumentErrorResponse implements Response {
                status: ResponseStatus!
                message: String!
                errors: [Error!]!
            }

            union DocumentResult = DocumentResponse | DocumentErrorResponse
            union DocumentsResult = DocumentsResponse | DocumentErrorResponse

            type Query {
                getDocumentById(id: ID!): DocumentResult!
                getDocuments(filter: DocumentFilterInput, pagination: PaginationInput): DocumentsResult!
            }

            input DocumentInput {
                name: String!
                documentType: DocumentType!
                documentUrl: String!
                description: String
                createdBy: ID!
            }

            input DocumentUpdateInput {
                name: String
                documentType: DocumentType
                documentUrl: String
                description: String
            }

            type Mutation {
                createDocument(input: DocumentInput!): DocumentResult!
                updateDocument(id: ID!, input: DocumentUpdateInput!): DocumentResult!
                deleteDocument(id: ID!): DocumentResult!
            }
        `;

        const normalize = str => str.replace(/\s+/g, ' ').trim();

        expect(normalize(documentTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
    });
});
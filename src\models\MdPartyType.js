const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const mdPartyTypeSchema = new mongoose.Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    vendorTypes: [{ type: Schema.Types.ObjectId, ref: 'MdVendorType', required: false }],
    landscapeImage: { type: String, required: false },
    portraitImage: { type: String, required: false }
}, { timestamps: true, collection: 'md_party_types' });
const MdPartyType = model('MdPartyType', mdPartyTypeSchema);

module.exports = MdPartyType;
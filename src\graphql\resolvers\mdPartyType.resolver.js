const MdPartyType = require("../../models/MdPartyType");
const { getByIdCache, clearCacheById, setCache } = require("../../utils/cache.util");
const { createResponse } = require("../../utils/response.util");
const { getPaginationInfo } = require("../../utils/paginationInfo.util");
const buildMdPartyTypeQuery = require("./filters/mdPartyType.filter");
const MdVendorType = require("../../models/MdVendorType");
const { findReferences } = require("../../utils/referenceCheck.util");

const mdPartyTypeResolvers = {
    MdPartyType: {
        vendorTypes: async (mdPartyType) => {
            try {
                return await MdVendorType.find({ _id: { $in: mdPartyType.vendorTypes } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor types');
            }
        }
    },
    Query: {
        getMdPartyTypeById: async (_, { id }) => {
            try {
                let mdPartyType = await getByIdCache(id) || await MdPartyType.findById(id);
                if (mdPartyType) {
                    await setCache(id, mdPartyType);
                } else {
                    return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType not found', { errors: [{ field: 'getMdPartyTypeById', message: 'MdPartyType not found' }] });
                }
                return createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType fetched successfully', { result: { mdPartyType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error getting MdPartyType', { errors: [{ field: 'getMdPartyTypeById', message: error.message }] });
            }
        },
        getMdPartyTypes: async (_, { filter, pagination }) => {
            try {
                const query = buildMdPartyTypeQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(MdPartyType, query, limit, skip);

                const mdPartyTypes = await MdPartyType.find(query).limit(limit).skip(skip).sort({ createdAt: -1 });

                if (mdPartyTypes.length === 0) {
                    return createResponse('MdPartyTypesResponse', 'FAILURE', 'No MdPartyTypes found', { result: { mdPartyTypes }, pagination: paginationInfo });
                }
                return createResponse('MdPartyTypesResponse', 'SUCCESS', 'MdPartyTypes fetched successfully', { result: { mdPartyTypes }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error getting MdPartyTypes', { errors: [{ field: 'getMdPartyTypes', message: error.message }] });
            }
        }
    },

    Mutation: {
        createMdPartyType: async (_, { input }) => {
            try {
                const mdPartyType = new MdPartyType(input);
                await mdPartyType.save();
                return createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType created successfully', { result: { mdPartyType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error creating MdPartyType', { errors: [{ field: 'createMdPartyType', message: error.message }] });
            }
        },
        updateMdPartyType: async (_, { id, input }) => {
            try {
                const mdPartyType = await MdPartyType.findByIdAndUpdate(id, input, { new: true });
                if (!mdPartyType) {
                    return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType not found', { errors: [{ field: 'updateMdPartyType', message: 'MdPartyType not found' }] });
                }
                await clearCacheById(id);

                return createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType updated successfully', { result: { mdPartyType } });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error updating MdPartyType', { errors: [{ field: 'updateMdPartyType', message: error.message }] });
            }
        },
        deleteMdPartyType: async (_, { id }) => {
            try {        
                const references = await findReferences(id, 'MdPartyType');
                if (references.length > 0) {
                    return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType cannot be deleted', {
                        errors: [{ 
                            field: 'deleteMdPartyType', 
                            message: `MdPartyType cannot be deleted as it is being used in: ${references.join(', ')}` 
                        }]
                    });
                }

                const mdPartyType = await MdPartyType.findByIdAndDelete(id);
                if (!mdPartyType) {
                    return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'MdPartyType not found', {
                        errors: [{ field: 'deleteMdPartyType', message: 'MdPartyType not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('MdPartyTypeResponse', 'SUCCESS', 'MdPartyType deleted successfully', { 
                    result: { mdPartyType } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdPartyTypeErrorResponse', 'FAILURE', 'Error deleting party type', {
                    errors: [{ field: 'deleteMdPartyType', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdPartyTypeResolvers;
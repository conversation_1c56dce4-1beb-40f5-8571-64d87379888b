const { gql } = require('graphql-tag');
const mdAppSettingsTypeDef = require('../../../../src/graphql/typedefs/mdAppSettings.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mdAppSettingsTypeDef', () => {
  it('should contain the MdAppSettings type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type ThemeSchema {
        backgroundColor: String!
        primaryColor: String!
        secondaryColor: String!
        textColor: String!
      }

      type Theme {
        light: ThemeSchema!
        dark: ThemeSchema!
      }

      type UserDashboardTabs {
        home: MdIcon!
        myEvents: MdIcon!
        publicEvents: MdIcon!
      }

      type HostEventDashboardTabs {
        eventsDb: MdIcon!
        tasks: MdIcon!
        guests: MdIcon!
        apps: MdIcon!
        messages: MdIcon!
      }

      type UserDashboard {
        tabs: UserDashboardTabs!
      }

      type HostEventDashboard {
        tabs: HostEventDashboardTabs!
      }

      type NavigationBar {
        userDashboard: UserDashboard!
        hostEventDashboard: HostEventDashboard!
      }

      type MdAppSettings {
        version: String!
        themes: Theme!
        navBars: NavigationBar!
      }

      type MdAppSettingsWrapper {
        mdAppSettings: MdAppSettings!
      }

      type MdAppSettingsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdAppSettingsWrapper!
      }

      type MdAppSettingsErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      type MdAppSettingsVersionsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: [String!]!
      }

      type MdAppSettingsVersionsErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union MdAppSettingsResult = MdAppSettingsResponse | MdAppSettingsErrorResponse
      union MdAppSettingsVersionResult = MdAppSettingsVersionsResponse | MdAppSettingsVersionsErrorResponse

      input ThemeSchemaInput {
        backgroundColor: String!
        primaryColor: String!
        secondaryColor: String!
        textColor: String!
      }

      input ThemeInput {
        light: ThemeSchemaInput!
        dark: ThemeSchemaInput!
      }

      input UserDashboardTabsInput {
        home: ID!
        myEvents: ID!
        publicEvents: ID!
      }

      input HostEventDashboardTabsInput {
        eventsDb: ID!
        tasks: ID!
        guests: ID!
        apps: ID!
        messages: ID!
      }

      input UserDashboardInput {
        tabs: UserDashboardTabsInput!
      }

      input HostEventDashboardInput {
        tabs: HostEventDashboardTabsInput!
      }

      input NavigationBarInput {
        userDashboard: UserDashboardInput!
        hostEventDashboard: HostEventDashboardInput!
      }

      input MdAppSettingsInput {
        version: String!
        themes: ThemeInput!
        navBars: NavigationBarInput!
      }

      type Query {
        getMdAppSettingsByVersion(version: String!): MdAppSettingsResult!
        getMdAppSettings: MdAppSettingsResult!
        getMdAppSettingsVersions: MdAppSettingsVersionResult!
      }

      type Mutation {
        createMdAppSettings(input: MdAppSettingsInput!): MdAppSettingsResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(mdAppSettingsTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Guest = require('../../../src/models/Guest');
const { User } = require('../../../src/models/User');
const Party = require('../../../src/models/Party');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Guest Model Test', () => {
    it('should create and save a Guest successfully', async () => {
        const mockUser = new User({
            role: ['guest'],
            firstName: '<PERSON>',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
        });
        await mockUser.save();

        const mockParty = new Party({
            name: 'Test Party',
            partyType: new mongoose.Types.ObjectId(),
            time: new Date(),
            expectedGuestCount: 50,
            totalBudget: 1000
        });
        await mockParty.save();

        const validGuest = new Guest({
            user: mockUser._id,
            party: mockParty._id
        });

        const savedGuest = await validGuest.save();

        expect(savedGuest._id).toBeDefined();
        expect(savedGuest.user).toEqual(mockUser._id);
        expect(savedGuest.party).toEqual(mockParty._id);
        expect(savedGuest.createdAt).toBeDefined();
        expect(savedGuest.updatedAt).toBeDefined();
    });

    it('should fail to create a Guest without required fields', async () => {
        const guest = new Guest();

        let err;
        try {
            await guest.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.user).toBeDefined();
        expect(err.errors.party).toBeDefined();
    });

    it('should fail to create a Guest with invalid reference IDs', async () => {
        const invalidGuest = new Guest({
            user: 'invalid_id',
            party: 'invalid_id'
        });

        let err;
        try {
            await invalidGuest.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.user).toBeDefined();
        expect(err.errors.party).toBeDefined();
    });
}); 
const mongoose = require('mongoose');
const Vendor = require('../../../../../src/models/Vendor');
const findReferences = require('../../../../../src/graphql/resolvers/references/vendorService.references');

jest.mock('../../../../../src/models/Vendor');

describe('vendorService References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references exist', async () => {
        Vendor.find.mockResolvedValue([]);

        const result = await findReferences('serviceId');

        expect(Vendor.find).toHaveBeenCalledWith({ 'servicesProvided': 'serviceId' });
        expect(result).toEqual([]);
    });

    it('should return array with "Vendor" when vendor references exist', async () => {
        const mockVendors = [
            { _id: 'vendor1', servicesProvided: ['serviceId'] },
            { _id: 'vendor2', servicesProvided: ['serviceId'] }
        ];
        Vendor.find.mockResolvedValue(mockVendors);

        const result = await findReferences('serviceId');

        expect(Vendor.find).toHaveBeenCalledWith({ 'servicesProvided': 'serviceId' });
        expect(result).toEqual(['Vendor']);
    });

    it('should handle database errors gracefully', async () => {
        const error = new Error('Database error');
        Vendor.find.mockRejectedValue(error);

        await expect(findReferences('serviceId')).rejects.toThrow('Database error');
        expect(Vendor.find).toHaveBeenCalledWith({ 'servicesProvided': 'serviceId' });
    });
});

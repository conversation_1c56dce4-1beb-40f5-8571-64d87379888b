const mongoose = require('mongoose');

const buildDocumentQuery = (filters) => {
    const pipeline = [];

    if (filters) {
        const matchConditions = {};

        if (filters.name) {
            matchConditions.name = { $regex: new RegExp(filters.name, 'i') };
        }

        if (filters.documentType) {
            matchConditions.documentType = filters.documentType;
        }

        if (filters.createdBy) {
            if (mongoose.Types.ObjectId.isValid(filters.createdBy)) {
                matchConditions.createdBy = new mongoose.Types.ObjectId(filters.createdBy);
            } else {
                throw new Error('Invalid createdBy ID provided');
            }
        }

        if ((filters.creatorFirstName || filters.creatorLastName) && !filters.createdBy) {
            pipeline.push({
                $lookup: {
                    from: 'users',
                    localField: 'createdBy',
                    foreignField: '_id',
                    as: 'creator'
                }
            });

            pipeline.push({ $unwind: '$creator' });

            if (filters.creatorFirstName) {
                matchConditions['creator.firstName'] = { $regex: new RegExp(filters.creatorFirstName, 'i') };
            }
            if (filters.creatorLastName) {
                matchConditions['creator.lastName'] = { $regex: new RegExp(filters.creatorLastName, 'i') };
            }
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }
    }

    pipeline.push({
        $project: {
            _id: 1,
            name: 1,
            documentType: 1,
            documentUrl: 1,
            description: 1,
            createdBy: 1,
            createdAt: 1,
            updatedAt: 1,
            creator: 1,
            ...Object.fromEntries(Object.keys(require('../../../models/Document').schema.paths).map(field => [field, 1]))
        }
    });

    return pipeline;
};

module.exports = buildDocumentQuery;
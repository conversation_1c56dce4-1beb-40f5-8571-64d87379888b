const MdChecklist = require('../models/MdChecklist');
const Task = require('../models/Task');
const Event = require('../models/Event');
const Host = require('../models/Host');

const createTasksFromChecklist = async (partyId, partyTypeId, eventId) => {
    try {
        const checklists = await MdChecklist.find({ partyType: partyTypeId });
        
        if (!checklists.length) {
            return { success: true };
        }

        const event = await Event.findById(eventId);
        if (!event?.mainHost) {
            return {
                success: false,
                error: { field: 'eventId', message: 'Event main host not found' }
            };
        }

        const host = await Host.findById(event.mainHost);
        if (!host?.userId) {
            return {
                success: false,
                error: { field: 'eventId', message: 'Host user not found' }
            };
        }

        const tasks = [];
        for (const checklist of checklists) {
            for (const item of checklist.items) {
                const task = new Task({
                    title: item.title,
                    description: item.description,
                    type: 'PRE_PARTY',
                    party: partyId,
                    createdBy: host.userId,
                    order: item.order
                });
                tasks.push(task);
            }
        }

        await Task.insertMany(tasks);
        return { success: true };
    } catch (error) {
        console.error('Error creating tasks from checklist:', error);
        return {
            success: false,
            error: { field: 'tasks', message: 'Failed to create tasks from checklist' }
        };
    }
};

const deleteTasksByPartyId = async (partyId) => {
    try {
        await Task.deleteMany({ party: partyId });
        return { success: true };
    } catch (error) {
        console.error('Error deleting tasks:', error);
        return {
            success: false,
            error: { field: 'tasks', message: 'Failed to delete tasks' }
        };
    }
};

module.exports = {
    createTasksFromChecklist,
    deleteTasksByPartyId
}; 
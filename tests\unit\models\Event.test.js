const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const Event = require('../../../src/models/Event');
const MdEventType = require('../../../src/models/MdEventType');
const Host = require('../../../src/models/Host');
const Guest = require('../../../src/models/Guest');
const Address = require('../../../src/models/Address');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('Event Model Test', () => {
    it('should create and save an Event successfully with all fields including dates', async () => {
        const mockEventType = new MdEventType({
            name: 'Birthday',
            description: 'Birthday celebration',
            duration: 'ONE_DAY'
        });
        await mockEventType.save();

        const mockHost = new Host({
            userId: new mongoose.Types.ObjectId()
        });
        await mockHost.save();

        const mockGuest = new Guest({
            user: new mongoose.Types.ObjectId(),
            party: new mongoose.Types.ObjectId()
        });
        await mockGuest.save();

        const mockAddress = new Address({
            addressType: 'HOME',
            street1: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            postalCode: '12345',
            country: 'Test Country',
            isPrimary: true
        });
        await mockAddress.save();

        const startDate = new Date('2024-01-01');
        const endDate = new Date('2024-01-02');

        const validEvent = new Event({
            name: 'Test Event',
            description: 'A test event description',
            eventType: mockEventType._id,
            mainHost: mockHost._id,
            coHosts: [mockHost._id],
            guests: [mockGuest._id],
            location: mockAddress._id,
            startDate: startDate,
            endDate: endDate
        });

        const savedEvent = await validEvent.save();

        expect(savedEvent._id).toBeDefined();
        expect(savedEvent.name).toBe('Test Event');
        expect(savedEvent.description).toBe('A test event description');
        expect(savedEvent.eventType).toEqual(mockEventType._id);
        expect(savedEvent.mainHost).toEqual(mockHost._id);
        expect(savedEvent.coHosts).toContainEqual(mockHost._id);
        expect(savedEvent.guests).toContainEqual(mockGuest._id);
        expect(savedEvent.location).toEqual(mockAddress._id);
        expect(savedEvent.startDate).toEqual(startDate);
        expect(savedEvent.endDate).toEqual(endDate);
        expect(savedEvent.createdAt).toBeDefined();
        expect(savedEvent.updatedAt).toBeDefined();
    });

    it('should create an Event with only required fields and optional dates', async () => {
        const mockHost = new Host({
            userId: new mongoose.Types.ObjectId()
        });
        await mockHost.save();

        const mockAddress = new Address({
            addressType: 'HOME',
            street1: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            postalCode: '12345',
            country: 'Test Country',
            isPrimary: true
        });
        await mockAddress.save();

        const minimalEvent = new Event({
            name: 'Minimal Event',
            description: 'Minimal event description',
            mainHost: mockHost._id,
            location: mockAddress._id
        });

        const savedEvent = await minimalEvent.save();

        expect(savedEvent._id).toBeDefined();
        expect(savedEvent.name).toBe('Minimal Event');
        expect(savedEvent.description).toBe('Minimal event description');
        expect(savedEvent.mainHost).toEqual(mockHost._id);
        expect(savedEvent.location).toEqual(mockAddress._id);
        expect(savedEvent.eventType).toBeUndefined();
        expect(savedEvent.coHosts).toEqual([]);
        expect(savedEvent.guests).toEqual([]);
    });

    it('should fail to create an Event with invalid reference IDs', async () => {
        const invalidEvent = new Event({
            name: 'Invalid Event',
            description: 'Invalid event description',
            eventType: 'invalid_id',
            mainHost: 'invalid_id',
            coHosts: ['invalid_id'],
            guests: ['invalid_id'],
            location: 'invalid_id'
        });

        let err;
        try {
            await invalidEvent.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.eventType).toBeDefined();
        expect(err.errors.mainHost).toBeDefined();
        expect(err.errors['coHosts.0']).toBeDefined();
        expect(err.errors['guests.0']).toBeDefined();
        expect(err.errors.location).toBeDefined();
    });
}); 
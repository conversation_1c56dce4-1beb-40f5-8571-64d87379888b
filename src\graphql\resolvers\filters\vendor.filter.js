const { isValidObjectId } = require("../../../utils/validation.util");
const buildVendorUserQuery = require("./vendorUser.filter");
const buildAddressQuery = require("./address.filter");
const buildServiceLocationQuery = require("./mdServiceLocation.filter");
const buildVendorServiceQuery = require("./vendorService.filter");
const VendorUser = require("../../../models/VendorUser");
const Address = require("../../../models/Address");
const MdServiceLocation = require("../../../models/MdServiceLocation");
const VendorService = require("../../../models/VendorService");

const buildVendorQuery = async (filters) => {
    const pipeline = [];
    const matchConditions = {};

    if (filters) {
        if (filters.name) {
            matchConditions.name = { $regex: new RegExp(filters.name, 'i') };
        }

        if (filters.primaryContact) {
            const vendorUserPipeline = buildVendorUserQuery(filters.primaryContact);
            if (vendorUserPipeline.length > 0) {
                const matchingContacts = await VendorUser.aggregate(vendorUserPipeline);
                if (matchingContacts.length > 0) {
                    matchConditions.primaryContact = { $in: matchingContacts.map(contact => contact._id) };
                } else {
                    matchConditions.primaryContact = { $in: [] };
                }
            }
        }

        if (filters.contacts && filters.contacts.length > 0) {
            const contactQueries = await Promise.all(
                filters.contacts.map(async (contactFilter) => {
                    const vendorUserPipeline = buildVendorUserQuery(contactFilter);
                    if (vendorUserPipeline.length > 0) {
                        const matchingContacts = await VendorUser.aggregate(vendorUserPipeline);
                        return matchingContacts.map(contact => contact._id);
                    }
                    return [];
                })
            );

            const flattenedContacts = contactQueries.flat().filter(Boolean);
            if (flattenedContacts.length > 0) {
                matchConditions.contacts = { $all: flattenedContacts };
            } else {
                matchConditions.contacts = { $in: [] };
            }
        }

        if (filters.businessAddress) {
            const addressQuery = buildAddressQuery(filters.businessAddress);
            if (Object.keys(addressQuery).length > 0) {
                const matchingAddresses = await Address.find(addressQuery).select('_id');
                if (matchingAddresses.length > 0) {
                    matchConditions.businessAddress = { $in: matchingAddresses.map(addr => addr._id) };
                } else {
                    matchConditions.businessAddress = { $in: [] };
                }
            }
        }

        if (filters.serviceLocations && filters.serviceLocations.length > 0) {
            const locationQueries = await Promise.all(
                filters.serviceLocations.map(async (locationFilter) => {
                    const locationQuery = buildServiceLocationQuery(locationFilter);
                    if (Object.keys(locationQuery).length > 0) {
                        const matchingLocations = await MdServiceLocation.find(locationQuery).select('_id');
                        return matchingLocations.map(loc => loc._id);
                    }
                    return [];
                })
            );

            const flattenedLocations = locationQueries.flat().filter(Boolean);
            if (flattenedLocations.length > 0) {
                matchConditions.serviceLocations = { $all: flattenedLocations };
            } else {
                matchConditions.serviceLocations = { $in: [] };
            }
        }

        if (filters.servicesProvided && filters.servicesProvided.length > 0) {
            const serviceQueries = await Promise.all(
                filters.servicesProvided.map(async (serviceFilter) => {
                    const serviceQuery = await buildVendorServiceQuery(serviceFilter);
                    if (Object.keys(serviceQuery).length > 0) {
                        const matchingServices = await VendorService.find(serviceQuery).select('_id');
                        return matchingServices.map(service => service._id);
                    }
                    return [];
                })
            );

            const flattenedServices = serviceQueries.flat().filter(Boolean);
            if (flattenedServices.length > 0) {
                matchConditions.servicesProvided = { $all: flattenedServices };
            } else {
                matchConditions.servicesProvided = { $in: [] };
            }
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }

        if (filters.minRating !== undefined || filters.minTotalReviews !== undefined) {
            pipeline.push({
                $lookup: {
                    from: 'vendor_ratings',
                    localField: '_id',
                    foreignField: 'vendor',
                    as: 'ratings'
                }
            });

            pipeline.push({
                $addFields: {
                    ratingStats: {
                        totalRating: { $size: '$ratings' },
                        averageRating: {
                            $cond: {
                                if: { $eq: [{ $size: '$ratings' }, 0] },
                                then: 0,
                                else: {
                                    $round: [{ $avg: '$ratings.rating' }, 1]
                                }
                            }
                        }
                    }
                }
            });

            const ratingMatchConditions = {};
            
            if (filters.minRating !== undefined) {
                ratingMatchConditions['ratingStats.averageRating'] = { $gte: filters.minRating };
            }

            if (filters.minTotalReviews !== undefined) {
                ratingMatchConditions['ratingStats.totalRating'] = { $gte: filters.minTotalReviews };
            }

            if (Object.keys(ratingMatchConditions).length > 0) {
                pipeline.push({ $match: ratingMatchConditions });
            }

            pipeline.push({
                $addFields: {
                    ratingAggregates: {
                        averageRating: '$ratingStats.averageRating',
                        totalRating: '$ratingStats.totalRating'
                    }
                }
            });
        }
    }

    return pipeline;
};

module.exports = buildVendorQuery;


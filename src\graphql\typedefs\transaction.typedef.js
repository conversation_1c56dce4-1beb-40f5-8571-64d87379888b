const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    enum TransactionType {
        CREDIT
        DEBIT
    }

    enum TransactionMethod {
        CASH
        UPI
        CREDIT_CARD
        DEBIT_CARD
    }

    type Transaction {
        id: ID!
        description: String!
        amount: Float!
        type: TransactionType!
        method: TransactionMethod!
        timeStamp: DateTime
        documents: [Document!]
    }

    input TransactionFilterInput {
        description: String
        type: TransactionType
        method: TransactionMethod
        timeStamp: DateTimeRangeInput
        minAmount: Float
        maxAmount: Float
    }

    type TransactionWrapper {
        transaction: Transaction!
    }

    type TransactionsWrapper {
        transactions: [Transaction]!
    }

    type TransactionResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TransactionWrapper!
    }

    type TransactionsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: TransactionsWrapper!
        pagination: PaginationInfo!
    }

    type TransactionErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union TransactionResult = TransactionResponse | TransactionErrorResponse
    union TransactionsResult = TransactionsResponse | TransactionErrorResponse

    type Query {
        getTransactionById(id: ID!): TransactionResult!
        getTransactions(filter: TransactionFilterInput, pagination: PaginationInput): TransactionsResult!
    }

    input TransactionInput {
        description: String!
        amount: Float!
        type: TransactionType!
        method: TransactionMethod!
        timeStamp: DateTime
        documents: [ID!]
    }

    type Mutation {
        createTransaction(input: TransactionInput!): TransactionResult!
        deleteTransaction(id: ID!): TransactionResult!
    }
`;
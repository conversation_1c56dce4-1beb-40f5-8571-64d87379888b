const { createResponse } = require("../../utils/response.util");
const MdNotificationConfig = require("../../models/MdNotificationConfig");
const { getByIdCache, setCache, clearCacheById, setCachePermanent } = require("../../utils/cache.util");

const ALL_CONFIGS_CACHE_KEY = 'all_notification_configs';

const mdNotificationConfigResolvers = {
    Query: {
        getMdNotificationConfig: async (_, { id }) => {
            try {
                const notificationConfig = await MdNotificationConfig.findById(id);
                
                if (!notificationConfig) {
                    return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Notification config not found', {
                        errors: [{ field: 'id', message: 'Notification config not found' }]
                    });
                }

                return createResponse('MdNotificationConfigResponse', 'SUCCESS', 'Notification config retrieved successfully', {
                    result: { notificationConfig }
                });
            } catch (error) {
                console.error('Error in getMdNotificationConfig:', error);
                return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Error retrieving notification config', {
                    errors: [{ field: 'getMdNotificationConfig', message: error.message }]
                });
            }
        },

        getMdNotificationConfigs: async () => {
            try {
                const notificationConfigs = await MdNotificationConfig.find().sort({ createdAt: -1 });
                
                if (notificationConfigs.length === 0) {
                    return createResponse('MdNotificationConfigsResponse', 'FAILURE', 'No notification configs found', {
                        result: { notificationConfigs: [] }
                    });
                }

                await setCache(ALL_CONFIGS_CACHE_KEY, notificationConfigs);

                return createResponse('MdNotificationConfigsResponse', 'SUCCESS', 'Notification configs fetched successfully', {
                    result: { notificationConfigs }
                });
            } catch (error) {
                console.error(error);
                return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Error fetching notification configs', {
                    errors: [{ field: 'getMdNotificationConfigs', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createMdNotificationConfig: async (_, { input }) => {
            try {
                if (!input.config.receivers || input.config.receivers.length === 0) {
                    return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'At least one receiver is required', {
                        errors: [{ field: 'receivers', message: 'At least one receiver is required' }]
                    });
                }

                if (input.config.requiredData) {
                    delete input.config.requiredData;
                }

                const notificationConfig = new MdNotificationConfig(input);
                await notificationConfig.save();
                
                await clearCacheById(ALL_CONFIGS_CACHE_KEY);
                
                const allConfigs = await MdNotificationConfig.find();
                await setCachePermanent(ALL_CONFIGS_CACHE_KEY, allConfigs);

                return createResponse('MdNotificationConfigResponse', 'SUCCESS', 'Notification config created successfully', {
                    result: { notificationConfig }
                });
            } catch (error) {
                console.error('Error in createMdNotificationConfig:', error);
                return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Error creating notification config', {
                    errors: [{ field: 'createMdNotificationConfig', message: error.message }]
                });
            }
        },

        updateMdNotificationConfig: async (_, { id, input }) => {
            try {
                const existingConfig = await MdNotificationConfig.findById(id);
                if (!existingConfig) {
                    return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Notification config not found', {
                        errors: [{ field: 'id', message: 'Notification config not found' }]
                    });
                }

                if (input.config?.receivers && input.config.receivers.length === 0) {
                    return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'At least one receiver is required', {
                        errors: [{ field: 'receivers', message: 'At least one receiver is required' }]
                    });
                }

                const notificationConfig = await MdNotificationConfig.findByIdAndUpdate(
                    id,
                    input,
                    { new: true }
                );

                await clearCacheById(ALL_CONFIGS_CACHE_KEY);
                
                const allConfigs = await MdNotificationConfig.find();
                await setCachePermanent(ALL_CONFIGS_CACHE_KEY, allConfigs);

                return createResponse('MdNotificationConfigResponse', 'SUCCESS', 'Notification config updated successfully', {
                    result: { notificationConfig }
                });
            } catch (error) {
                console.error('Error in updateMdNotificationConfig:', error);
                return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Error updating notification config', {
                    errors: [{ field: 'updateMdNotificationConfig', message: error.message }]
                });
            }
        },

        deleteMdNotificationConfig: async (_, { id }) => {
            try {
                const notificationConfig = await MdNotificationConfig.findByIdAndDelete(id);
                
                if (!notificationConfig) {
                    return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Notification config not found', {
                        errors: [{ field: 'id', message: 'Notification config not found' }]
                    });
                }

                await clearCacheById(ALL_CONFIGS_CACHE_KEY);

                const allConfigs = await MdNotificationConfig.find();
                await setCachePermanent(ALL_CONFIGS_CACHE_KEY, allConfigs);

                return createResponse('MdNotificationConfigResponse', 'SUCCESS', 'Notification config deleted successfully', {
                    result: { notificationConfig }
                });
            } catch (error) {
                console.error('Error in deleteMdNotificationConfig:', error);
                return createResponse('MdNotificationConfigErrorResponse', 'FAILURE', 'Error deleting notification config', {
                    errors: [{ field: 'deleteMdNotificationConfig', message: error.message }]
                });
            }
        }
    }
};

module.exports = mdNotificationConfigResolvers;
const VendorService = require('../../models/VendorService');
const buildVendorServiceQuery = require('./filters/vendorService.filter');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const MdVendorType = require('../../models/MdVendorType');
const MdFeature = require('../../models/MdFeature');
const Media = require('../../models/Media');
const { validateReferences } = require('../../utils/validation.util');
const Vendor = require('../../models/Vendor');
const { findReferences } = require('../../utils/referenceCheck.util');

const baseVendorServiceIdSchema = {
    vendorType: { type: 'single', model: MdVendorType },
    vendor: { type: 'single', model: Vendor },
    features: { type: 'array', required: false, model: MdFeature },
    media: { type: 'array', required: false, model: Media }
};

const vendorServiceIdSchema = { ...baseVendorServiceIdSchema };

const createVendorServiceIdSchema = {
    ...baseVendorServiceIdSchema,
    vendorType: { ...baseVendorServiceIdSchema.vendorType, required: true },
    vendor: { ...baseVendorServiceIdSchema.vendor, required: true }
};

const vendorServiceResolvers = {
    VendorService: {
        vendorType: async (vendorService) => {
            try {
                return await MdVendorType.findById(vendorService.vendorType);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor type');
            }
        },
        vendor: async (vendorService) => {
            try {
                return await Vendor.findById(vendorService.vendor);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor');
            }
        },
        features: async (vendorService) => {
            try {
                return await MdFeature.find({ _id: { $in: vendorService.features } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting features');
            }
        },
        media: async (vendorService) => {
            try {
                return await Media.find({ _id: { $in: vendorService.media } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting media');
            }
        }
    },
    Query: {
        getVendorServiceById: async (_, { id }) => {
            try {
                let vendorService = await getByIdCache(id);
                if (!vendorService) {
                    vendorService = await VendorService.findById(id)
                        .populate('vendorType')
                        .populate('vendor')
                        .populate('features')
                        .populate('media');
                    if (!vendorService) {
                        return createResponse('VendorServiceErrorResponse', 'FAILURE', 'VendorService not found', {
                            errors: [{ field: 'getVendorServiceById', message: 'VendorService not found' }]
                        });
                    }
                }

                await setCache(id, vendorService);
                return createResponse('VendorServiceResponse', 'SUCCESS', 'VendorService fetched successfully', {
                    result: { vendorService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error getting VendorService', {
                    errors: [{ field: 'getVendorServiceById', message: error.message }]
                });
            }
        },

        getVendorServices: async (_, { filter, pagination }) => {
            try {
                const query = await buildVendorServiceQuery(filter);

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(VendorService, query, limit, skip);

                const vendorServices = await VendorService.find(query)
                    .populate('vendorType')
                    .populate('vendor')
                    .populate('features')
                    .populate('media')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (vendorServices.length === 0) {
                    return createResponse('VendorServicesResponse', 'FAILURE', 'No vendor services found', {
                        result: { vendorServices },
                        pagination: paginationInfo
                    });
                }

                return createResponse('VendorServicesResponse', 'SUCCESS', 'Vendor services fetched successfully', {
                    result: { vendorServices },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error getting vendor services', {
                    errors: [{ field: 'getVendorServices', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createVendorService: async (_, { input }) => {
            try {
                const validationError = await validateReferences(input, createVendorServiceIdSchema, 'VendorService');
                if (validationError) {
                    return createResponse('VendorServiceErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }   

                const vendorService = new VendorService(input);
                await vendorService.save();

                await Vendor.findByIdAndUpdate(
                    input.vendor,
                    { $push: { servicesProvided: vendorService._id } }
                );

                const populatedVendorService = await VendorService.findById(vendorService._id)
                    .populate('vendorType')
                    .populate('vendor')
                    .populate('features')
                    .populate('media');

                return createResponse('VendorServiceResponse', 'SUCCESS', 'Vendor service created successfully', {
                    result: { vendorService: populatedVendorService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error creating vendor service', {
                    errors: [{ field: 'createVendorService', message: error.message }]
                });
            }
        },

        updateVendorService: async (_, { id, input }) => {
            try {
                const validationError = await validateReferences(input, vendorServiceIdSchema, 'VendorService');
                if (validationError) {
                    return createResponse('VendorServiceErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const vendorService = await VendorService.findByIdAndUpdate(
                    id,
                    { $set: input },
                    { new: true }
                )
                .populate('vendorType')
                .populate('vendor')
                .populate('features')
                .populate('media');

                if (!vendorService) {
                    return createResponse('VendorServiceErrorResponse', 'FAILURE', 'VendorService not found', {
                        errors: [{ field: 'updateVendorService', message: 'VendorService not found' }]
                    });
                }

                await clearCacheById(id);
                return createResponse('VendorServiceResponse', 'SUCCESS', 'Vendor service updated successfully', {
                    result: { vendorService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error updating vendor service', {
                    errors: [{ field: 'updateVendorService', message: error.message }]
                });
            }
        },

        deleteVendorService: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'VendorService');
                if (references.length > 0) {
                    return createResponse('VendorServiceErrorResponse', 'FAILURE', 'VendorService cannot be deleted', {
                        errors: [{ field: 'deleteVendorService', message: `VendorService cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const vendorService = await VendorService.findById(id);
                
                if (!vendorService) {
                    return createResponse('VendorServiceErrorResponse', 'FAILURE', 'VendorService not found', {
                        errors: [{ field: 'deleteVendorService', message: 'VendorService not found' }]
                    });
                }

                await Vendor.findByIdAndUpdate(
                    vendorService.vendor,
                    { $pull: { servicesProvided: id } }
                );

                await vendorService.deleteOne();

                await clearCacheById(id);
                return createResponse('VendorServiceResponse', 'SUCCESS', 'Vendor service deleted successfully', {
                    result: { vendorService }
                });
            } catch (error) {
                console.error(error);
                return createResponse('VendorServiceErrorResponse', 'FAILURE', 'Error deleting vendor service', {
                    errors: [{ field: 'deleteVendorService', message: error.message }]
                });
            }
        }
    }
};

module.exports = vendorServiceResolvers;
const { isValidObjectId } = require("../../../utils/validation.util");
const buildUserQuery = require("./user.filter");
const User = require("../../../models/User");
const Event = require("../../../models/Event");
const Party = require("../../../models/Party");

const buildHostQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.eventId) {
            if (isValidObjectId(filter.eventId)) {
                const event = await Event.findById(filter.eventId);
                if (!event) {
                    throw new Error('Event not found');
                }

                const parties = await Party.find({ eventId: filter.eventId });
                
                const hostIds = new Set();
                
                hostIds.add(event.mainHost.toString());
                
                if (event.coHosts && event.coHosts.length > 0) {
                    event.coHosts.forEach(hostId => hostIds.add(hostId.toString()));
                }
                
                parties.forEach(party => {
                    if (party.coHosts && party.coHosts.length > 0) {
                        party.coHosts.forEach(hostId => hostIds.add(hostId.toString()));
                    }
                });

                if (hostIds.size > 0) {
                    query._id = { $in: Array.from(hostIds) };
                } else {
                    query._id = { $in: [] };
                }
            } else {
                throw new Error('Invalid event ID provided');
            }
        }

        if (filter.userId) {
            if (typeof filter.userId === 'string') {
                if (isValidObjectId(filter.userId)) {
                    query.userId = filter.userId;
                } else {
                    throw new Error('Invalid user ID provided');
                }
            } else if (typeof filter.userId === 'object') {
                const userQuery = await buildUserQuery(filter.userId);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.userId = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.userId = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildHostQuery; 
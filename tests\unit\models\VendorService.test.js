const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const VendorService = require('../../../src/models/VendorService');
const MdVendorType = require('../../../src/models/MdVendorType');
const MdFeature = require('../../../src/models/MdFeature');
const Media = require('../../../src/models/Media');

let mongoServer;

beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
});

afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
});

describe('VendorService Model Test', () => {
    it('should create and save a VendorService successfully', async () => {
        const mockVendorType = new MdVendorType({
            name: 'Test Vendor Type',
            description: 'Test Description'
        });
        await mockVendorType.save();

        const mockFeature = new MdFeature({
            name: 'Test Feature'
        });
        await mockFeature.save();

        const mockMedia = new mongoose.Types.ObjectId();

        const validVendorService = new VendorService({
            vendorType: mockVendorType._id,
            title: 'Test Service',
            description: 'Test Service Description',
            features: [mockFeature._id],
            priceRange: '$100-$200',
            specialities: 'Test Specialities',
            media: [mockMedia]
        });

        const savedVendorService = await validVendorService.save();

        expect(savedVendorService._id).toBeDefined();
        expect(savedVendorService.vendorType).toEqual(mockVendorType._id);
        expect(savedVendorService.title).toBe('Test Service');
        expect(savedVendorService.description).toBe('Test Service Description');
        expect(savedVendorService.features[0]).toEqual(mockFeature._id);
        expect(savedVendorService.priceRange).toBe('$100-$200');
        expect(savedVendorService.specialities).toBe('Test Specialities');
        expect(savedVendorService.media[0]).toEqual(mockMedia);
        expect(savedVendorService.createdAt).toBeDefined();
        expect(savedVendorService.updatedAt).toBeDefined();
    });

    it('should fail to create a VendorService without required fields', async () => {
        const vendorService = new VendorService();

        let err;
        try {
            await vendorService.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.vendorType).toBeDefined();
        expect(err.errors.title).toBeDefined();
        expect(err.errors.priceRange).toBeDefined();
    });

    it('should create a VendorService with minimal required fields', async () => {
        const mockVendorType = new MdVendorType({
            name: 'Minimal Vendor Type',
            description: 'Minimal Description'
        });
        await mockVendorType.save();

        const minimalVendorService = new VendorService({
            vendorType: mockVendorType._id,
            title: 'Minimal Service',
            priceRange: '$50-$100'
        });

        const savedMinimalVendorService = await minimalVendorService.save();

        expect(savedMinimalVendorService._id).toBeDefined();
        expect(savedMinimalVendorService.vendorType).toEqual(mockVendorType._id);
        expect(savedMinimalVendorService.title).toBe('Minimal Service');
        expect(savedMinimalVendorService.priceRange).toBe('$50-$100');
        expect(savedMinimalVendorService.features).toEqual([]);
        expect(savedMinimalVendorService.media).toEqual([]);
    });

    it('should fail to create a VendorService with invalid references', async () => {
        const invalidVendorService = new VendorService({
            vendorType: 'invalid_id',
            title: 'Invalid Service',
            priceRange: '$50-$100',
            features: ['invalid_id'],
            media: ['invalid_id']
        });

        let err;
        try {
            await invalidVendorService.validate();
        } catch (error) {
            err = error;
        }

        expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
        expect(err.errors.vendorType).toBeDefined();
        expect(err.errors['features.0']).toBeDefined();
        expect(err.errors['media.0']).toBeDefined();
    });
});
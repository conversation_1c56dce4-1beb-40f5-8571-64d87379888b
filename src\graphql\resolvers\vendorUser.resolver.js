const VendorUser = require('../../models/VendorUser');
const { User } = require('../../models/User');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { createResponse } = require('../../utils/response.util');
const buildVendorUserQuery = require('./filters/vendorUser.filter');
const findReferences = require('./references/vendorUser.references');

const vendorUserResolvers = {
    VendorUser: {
        user: async (parent) => {
            try {
                return await User.findById(parent.user);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        }
    },

    Query: {
        getVendorUserById: async (_, { id }) => {
            try {
                let vendorUser = await getByIdCache(id) || await VendorUser.findById(id).populate('user');
                if (vendorUser) {
                    await setCache(id, vendorUser);
                } else {
                    return createResponse('VendorUserErrorResponse', 'FAILURE', 'VendorUser not found', { errors: [{ field: 'id', message: 'VendorUser not found' }] });
                }
                return createResponse('VendorUserResponse', 'SUCCESS', 'VendorUser fetched successfully', { result: { vendorUser } });
            } catch (error) {
                console.error(error);
                return createResponse('VendorUserErrorResponse', 'FAILURE', 'Error getting VendorUser', { errors: [{ field: 'getVendorUserById', message: error.message }] });
            }
        },

        getVendorUsers: async (_, { filter, pagination }) => {
            try {
                const pipeline = buildVendorUserQuery(filter);
        
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
        
                const countPipeline = [...pipeline, { $count: 'total' }];
                const countResult = await VendorUser.aggregate(countPipeline);
                const totalCount = countResult[0]?.total || 0;
        
                pipeline.push({ $skip: skip });
                pipeline.push({ $limit: limit });
        
                const vendorUsers = await VendorUser.aggregate(pipeline);
        
                const paginationInfo = {
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    currentPage: Math.floor(skip / limit) + 1,
                    pageSize: limit,
                    hasNextPage: skip + limit < totalCount,
                    hasPreviousPage: skip > 0
                };

                const mappedVendorUsers = vendorUsers.map(user => ({
                    ...user,
                    id: user._id.toString(),
                    userDetails: user.userDetails ? {
                        ...user.userDetails,
                        id: user.userDetails._id.toString()
                    } : null
                }));
        
                if (mappedVendorUsers.length === 0) {
                    return createResponse('VendorUsersResponse', 'FAILURE', 'No VendorUsers found', { result: { vendorUsers: mappedVendorUsers }, pagination: paginationInfo });
                }
                return createResponse('VendorUsersResponse', 'SUCCESS', 'VendorUsers fetched successfully', { result: { vendorUsers: mappedVendorUsers }, pagination: paginationInfo });
            } catch (error) {
                console.error(error);
                return createResponse('VendorUserErrorResponse', 'FAILURE', 'Error getting VendorUsers', { errors: [{ field: 'getVendorUsers', message: error.message }] });
            }
        }
    },

    Mutation: {
        createVendorUser: async (_, { userId }) => {
            try {
                const vendorUser = new VendorUser({ user: userId });
                await vendorUser.save();
                return createResponse('VendorUserResponse', 'SUCCESS', 'VendorUser created successfully', { result: { vendorUser } });
            } catch (error) {
                console.error(error);
                return createResponse('VendorUserErrorResponse', 'FAILURE', 'Error creating VendorUser', { errors: [{ field: 'createVendorUser', message: error.message }] });
            }
        },

        deleteVendorUser: async (_, { id }) => {
            try {
                const references = await findReferences(id);
                if (references.length > 0) {
                    return createResponse('VendorUserErrorResponse', 'FAILURE', 'VendorUser cannot be deleted', { errors: [{ field: 'id', message: `VendorUser cannot be deleted as it has references in the following collections: ${references.join(', ')}` }] });
                }

                const vendorUser = await VendorUser.findByIdAndDelete(id);
                if (!vendorUser) {
                    return createResponse('VendorUserErrorResponse', 'FAILURE', 'VendorUser not found', { errors: [{ field: 'id', message: 'VendorUser not found' }] });
                }
                await clearCacheById(id);
                return createResponse('VendorUserResponse', 'SUCCESS', 'VendorUser deleted successfully', { result: { vendorUser } });
            } catch (error) {
                console.error(error);
                return createResponse('VendorUserErrorResponse', 'FAILURE', 'Error deleting VendorUser', { errors: [{ field: 'deleteVendorUser', message: error.message }] });
            }
        }
    }
};

module.exports = vendorUserResolvers;
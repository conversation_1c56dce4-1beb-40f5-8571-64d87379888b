const mongoose = require('mongoose');
const findReferences = require('../../../../../src/graphql/resolvers/references/vendorUser.references');
const Vendor = require('../../../../../src/models/Vendor');

jest.mock('../../../../../src/models/Vendor');

describe('vendorUser References', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty array when no references found', async () => {
        Vendor.find.mockResolvedValue([]);

        const userId = new mongoose.Types.ObjectId().toString();
        const result = await findReferences(userId);

        expect(Vendor.find).toHaveBeenCalledWith({
            $or: [
                { 'primaryContact': userId },
                { 'contacts': userId }
            ]
        });
        expect(result).toEqual([]);
    });

    it('should return ["Vendor"] when user is referenced as primaryContact', async () => {
        const userId = new mongoose.Types.ObjectId().toString();
        const mockVendor = {
            _id: new mongoose.Types.ObjectId(),
            primaryContact: userId
        };

        Vendor.find.mockResolvedValue([mockVendor]);

        const result = await findReferences(userId);

        expect(Vendor.find).toHaveBeenCalledWith({
            $or: [
                { 'primaryContact': userId },
                { 'contacts': userId }
            ]
        });
        expect(result).toEqual(['Vendor']);
    });

    it('should return ["Vendor"] when user is referenced in contacts', async () => {
        const userId = new mongoose.Types.ObjectId().toString();
        const mockVendor = {
            _id: new mongoose.Types.ObjectId(),
            contacts: [userId]
        };

        Vendor.find.mockResolvedValue([mockVendor]);

        const result = await findReferences(userId);

        expect(Vendor.find).toHaveBeenCalledWith({
            $or: [
                { 'primaryContact': userId },
                { 'contacts': userId }
            ]
        });
        expect(result).toEqual(['Vendor']);
    });

    it('should handle database errors gracefully', async () => {
        const userId = new mongoose.Types.ObjectId().toString();
        const error = new Error('Database error');

        Vendor.find.mockRejectedValue(error);

        await expect(findReferences(userId)).rejects.toThrow('Database error');
    });
});

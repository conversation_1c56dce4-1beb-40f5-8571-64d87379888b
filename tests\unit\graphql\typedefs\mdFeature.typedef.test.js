const { gql } = require('graphql-tag');
const mdFeatureTypeDef = require('../../../../src/graphql/typedefs/mdFeature.typedef');
const sharedTypeDef = require('../../../../src/graphql/typedefs/shared.typedef');

describe('mdFeatureTypeDef', () => {
  it('should contain the MdFeature type definitions', () => {
    const expectedTypeDefs = gql`
      ${sharedTypeDef}

      type MdFeature {
        id: ID!
        name: String!
        icon: MdIcon
        createdAt: DateTime
        updatedAt: DateTime
      }

      input MdFeatureFilterInput {
        id: String!
        name: String
      }

      type MdFeatureWrapper {
        mdFeature: MdFeature!
      }

      type MdFeaturesWrapper {
        mdFeatures: [MdFeature]!
      }

      type MdFeaturesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdFeaturesWrapper!
        pagination: PaginationInfo!
      }

      type MdFeatureResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdFeatureWrapper!
      }

      type MdFeatureErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
      }

      union MdFeatureResult = MdFeatureResponse | MdFeatureErrorResponse
      union MdFeaturesResult = MdFeaturesResponse | MdFeatureErrorResponse

      type Query {
        getMdFeatureById(id: ID!): MdFeatureResult!
        getMdFeatures(filter: MdFeatureFilterInput, pagination: PaginationInput): MdFeaturesResult!
      }

      input MdFeatureInput {
        name: String!
        icon: ID
      }

      input MdFeatureUpdateInput {
        name: String
        icon: ID
      }

      type Mutation {
        createMdFeature(input: MdFeatureInput!): MdFeatureResult!
        updateMdFeature(id: ID!, input: MdFeatureUpdateInput!): MdFeatureResult!
        deleteMdFeature(id: ID!): MdFeatureResult!
      }
    `;

    const normalize = str => str.replace(/\s+/g, ' ').trim();

    expect(normalize(mdFeatureTypeDef.loc.source.body)).toEqual(normalize(expectedTypeDefs.loc.source.body));
  });
});
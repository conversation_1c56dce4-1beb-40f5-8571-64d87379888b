const mongoose = require('mongoose');
const Reaction = require('../../../../src/models/Reaction');
const { User } = require('../../../../src/models/User');
const MdReaction = require('../../../../src/models/MdReaction');
const { createResponse } = require('../../../../src/utils/response.util');
const { getPaginationInfo } = require('../../../../src/utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../../../src/utils/cache.util');
const buildReactionQuery = require('../../../../src/graphql/resolvers/filters/reaction.filter');
const { findReferences } = require('../../../../src/utils/referenceCheck.util');
const { validateReferences } = require('../../../../src/utils/validation.util');
const reactionResolvers = require('../../../../src/graphql/resolvers/reaction.resolver');

const createReactionIdSchema = {
    type: { type: 'single', model: MdReaction, required: true },
    user: { type: 'single', model: User, required: true }
};

jest.mock('../../../../src/models/Reaction');
jest.mock('../../../../src/models/User');
jest.mock('../../../../src/models/MdReaction');
jest.mock('../../../../src/utils/response.util');
jest.mock('../../../../src/utils/paginationInfo.util');
jest.mock('../../../../src/utils/cache.util');
jest.mock('../../../../src/graphql/resolvers/filters/reaction.filter');
jest.mock('../../../../src/utils/referenceCheck.util');
jest.mock('../../../../src/utils/validation.util');

describe('reactionResolvers', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        console.error = jest.fn();
    });

    describe('Reaction Field Resolvers', () => {
        describe('type', () => {
            it('should resolve reaction type successfully', async () => {
                const parent = { type: 'typeId' };
                const mockType = { _id: 'typeId', name: 'Like' };
                
                MdReaction.findById.mockResolvedValue(mockType);
                
                const result = await reactionResolvers.Reaction.type(parent);
                
                expect(MdReaction.findById).toHaveBeenCalledWith(parent.type);
                expect(result).toEqual(mockType);
            });

            it('should handle error when resolving type', async () => {
                const parent = { type: 'typeId' };
                const error = new Error('Database error');
                
                MdReaction.findById.mockRejectedValue(error);
                
                await expect(reactionResolvers.Reaction.type(parent))
                    .rejects.toThrow('Error getting reaction type');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });

        describe('user', () => {
            it('should resolve user successfully', async () => {
                const parent = { user: 'userId' };
                const mockUser = { _id: 'userId', name: 'John Doe' };
                
                User.findById.mockResolvedValue(mockUser);
                
                const result = await reactionResolvers.Reaction.user(parent);
                
                expect(User.findById).toHaveBeenCalledWith(parent.user);
                expect(result).toEqual(mockUser);
            });

            it('should handle error when resolving user', async () => {
                const parent = { user: 'userId' };
                const error = new Error('Database error');
                
                User.findById.mockRejectedValue(error);
                
                await expect(reactionResolvers.Reaction.user(parent))
                    .rejects.toThrow('Error getting user');
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    });

    describe('Query Resolvers', () => {
        describe('getReactionById', () => {
            it('should return cached reaction if available', async () => {
                const id = 'reactionId';
                const cachedReaction = { _id: id, type: 'typeId', user: 'userId' };
                
                getByIdCache.mockResolvedValue(cachedReaction);
                createResponse.mockReturnValue('successResponse');
                
                const result = await reactionResolvers.Query.getReactionById(null, { id });
                
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionResponse',
                    'SUCCESS',
                    'Reaction retrieved successfully from cache',
                    { result: { reaction: cachedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should fetch and cache reaction if not in cache', async () => {
                const id = 'reactionId';
                const reaction = { _id: id, type: 'typeId', user: 'userId' };
                
                getByIdCache.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(reaction);
                Reaction.findById.mockReturnValue({ populate: mockPopulate });
                createResponse.mockReturnValue('successResponse');
                
                const result = await reactionResolvers.Query.getReactionById(null, { id });
                
                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Reaction.findById).toHaveBeenCalledWith(id);
                expect(mockPopulate).toHaveBeenCalledWith(['type', 'user']);
                expect(setCache).toHaveBeenCalledWith(id, reaction);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionResponse',
                    'SUCCESS',
                    'Reaction retrieved successfully',
                    { result: { reaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle non-existent reaction', async () => {
                const id = 'nonExistentId';
                
                getByIdCache.mockResolvedValue(null);
                const mockPopulate = jest.fn().mockResolvedValue(null);
                Reaction.findById.mockReturnValue({ populate: mockPopulate });
                createResponse.mockReturnValue('errorResponse');
                
                const result = await reactionResolvers.Query.getReactionById(null, { id });
                
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Reaction not found',
                    { errors: [{ field: 'id', message: 'Reaction not found' }] }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle errors when retrieving reaction', async () => {
                const id = 'testId';
                const error = new Error('Database error');
                
                getByIdCache.mockResolvedValue(null);
                
                Reaction.findById.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await reactionResolvers.Query.getReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Reaction.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Error retrieving reaction',
                    {
                        errors: [{ field: 'id', message: 'Database error' }]
                    }
                );
                expect(result).toBe('errorResponse');
                expect(console.error).toHaveBeenCalledWith(error);
            });

            it('should return error response when reaction is not found', async () => {
                const id = 'nonexistentId';
                
                getByIdCache.mockResolvedValue(null);
                
                Reaction.findById.mockReturnValue({
                    populate: jest.fn().mockResolvedValue(null)
                });

                createResponse.mockReturnValue('errorResponse');

                const result = await reactionResolvers.Query.getReactionById(null, { id });

                expect(getByIdCache).toHaveBeenCalledWith(id);
                expect(Reaction.findById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Reaction not found',
                    {
                        errors: [{ field: 'id', message: 'Reaction not found' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });
        });

        describe('getReactions', () => {
            it('should return reactions successfully', async () => {
                const filter = { type: 'typeId' };
                const pagination = { limit: 10, skip: 0 };
                const query = { type: 'typeId' };
                const reactions = [{ _id: 'reaction1' }, { _id: 'reaction2' }];
                const paginationInfo = { totalPages: 1, totalItems: 2 };
                
                buildReactionQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);
                
                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(reactions)
                };
                
                Reaction.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('successResponse');
                
                const result = await reactionResolvers.Query.getReactions(null, { filter, pagination });
                
                expect(buildReactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(Reaction, query, pagination.limit, pagination.skip);
                expect(Reaction.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith(['type', 'user']);
                expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
                expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
                expect(mockFind.sort).toHaveBeenCalledWith({ timestamp: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionsResponse',
                    'SUCCESS',
                    'Reactions fetched successfully',
                    {
                        result: { reactions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle errors when retrieving reactions', async () => {
                const filter = { type: 'typeId' };
                const pagination = { limit: 10, skip: 0 };
                const error = new Error('Database error');

                buildReactionQuery.mockResolvedValue({ type: 'typeId' });
                getPaginationInfo.mockRejectedValue(error);
                createResponse.mockReturnValue('errorResponse');

                const result = await reactionResolvers.Query.getReactions(null, { filter, pagination });

                expect(buildReactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(
                    Reaction,
                    { type: 'typeId' },
                    pagination.limit,
                    pagination.skip
                );
                expect(console.error).toHaveBeenCalledWith(error);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Error retrieving reactions',
                    {
                        errors: [{ field: 'getReactions', message: 'Database error' }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should return failure response when no reactions found', async () => {
                const filter = { type: 'nonexistentType' };
                const pagination = { limit: 10, skip: 0 };
                const query = { type: 'nonexistentType' };
                const paginationInfo = { total: 0, limit: 10, skip: 0 };
                const reactions = [];

                buildReactionQuery.mockResolvedValue(query);
                getPaginationInfo.mockResolvedValue(paginationInfo);

                const mockFind = {
                    populate: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockReturnThis(),
                    skip: jest.fn().mockReturnThis(),
                    sort: jest.fn().mockResolvedValue(reactions),
                };

                Reaction.find.mockReturnValue(mockFind);
                createResponse.mockReturnValue('failureResponse');

                const result = await reactionResolvers.Query.getReactions(null, { filter, pagination });

                expect(buildReactionQuery).toHaveBeenCalledWith(filter);
                expect(getPaginationInfo).toHaveBeenCalledWith(Reaction, query, pagination.limit, pagination.skip);
                expect(Reaction.find).toHaveBeenCalledWith(query);
                expect(mockFind.populate).toHaveBeenCalledWith(['type', 'user']);
                expect(mockFind.limit).toHaveBeenCalledWith(pagination.limit);
                expect(mockFind.skip).toHaveBeenCalledWith(pagination.skip);
                expect(mockFind.sort).toHaveBeenCalledWith({ timestamp: -1 });
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionsResponse',
                    'FAILURE',
                    'No reactions found',
                    {
                        result: { reactions },
                        pagination: paginationInfo
                    }
                );
                expect(result).toBe('failureResponse');
            });
        });
    });

    describe('Mutation Resolvers', () => {
        describe('createReaction', () => {
            it('should create reaction successfully', async () => {
                const input = {
                    type: 'typeId',
                    user: 'userId',
                    timestamp: new Date()
                };
                
                validateReferences.mockResolvedValue(null);
                
                const savedReaction = { _id: 'newId', ...input };
                const populatedReaction = { ...savedReaction, type: { _id: 'typeId' }, user: { _id: 'userId' } };
                
                Reaction.mockImplementation(() => ({
                    save: jest.fn().mockResolvedValue(savedReaction)
                }));
                
                const mockPopulate = jest.fn().mockResolvedValue(populatedReaction);
                Reaction.findById.mockReturnValue({ populate: mockPopulate });
                
                createResponse.mockReturnValue('successResponse');
                
                const result = await reactionResolvers.Mutation.createReaction(null, { input });
                
                expect(validateReferences).toHaveBeenCalledWith(input, expect.any(Object), 'Reaction');
                expect(Reaction).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionResponse',
                    'SUCCESS',
                    'Reaction created successfully',
                    { result: { reaction: populatedReaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle error when creating reaction fails', async () => {
                const input = {
                    type: 'typeId',
                    user: 'userId',
                    timestamp: new Date()
                };
                
                const error = new Error('Database error');
                
                validateReferences.mockResolvedValue(null);
                
                Reaction.mockImplementation(() => ({
                    save: jest.fn().mockRejectedValue(error)
                }));

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error creating reaction',
                    errors: [{ field: 'createReaction', message: error.message }]
                });

                const result = await reactionResolvers.Mutation.createReaction(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, createReactionIdSchema, 'Reaction');
                expect(Reaction).toHaveBeenCalledWith(input);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Error creating reaction',
                    {
                        errors: [{ field: 'createReaction', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error creating reaction',
                    errors: [{ field: 'createReaction', message: error.message }]
                });
                expect(console.error).toHaveBeenCalledWith(error);
            });

            it('should return validation error response when validation fails', async () => {
                const input = {
                    type: 'invalidTypeId',
                    user: 'invalidUserId'
                };
                const validationError = {
                    message: 'Validation failed',
                    errors: [
                        { field: 'type', message: 'Invalid type reference' },
                        { field: 'user', message: 'Invalid user reference' }
                    ]
                };

                validateReferences.mockResolvedValue(validationError);
                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });

                const result = await reactionResolvers.Mutation.createReaction(null, { input });

                expect(validateReferences).toHaveBeenCalledWith(input, createReactionIdSchema, 'Reaction');
                expect(Reaction.prototype.save).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    validationError.message,
                    { errors: validationError.errors }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: validationError.message,
                    errors: validationError.errors
                });
            });
        });

        describe('deleteReaction', () => {
            beforeEach(() => {
                jest.clearAllMocks();
            });

            it('should delete reaction successfully', async () => {
                const id = 'reactionId';
                const reaction = { _id: id, type: 'typeId', user: 'userId' };
                
                findReferences.mockResolvedValue([]);
                const mockPopulate = jest.fn().mockResolvedValue(reaction);
                Reaction.findByIdAndDelete.mockReturnValue({ populate: mockPopulate });
                createResponse.mockReturnValue('successResponse');
                
                const result = await reactionResolvers.Mutation.deleteReaction(null, { id });
                
                expect(findReferences).toHaveBeenCalledWith(id, 'Reaction');
                expect(Reaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(mockPopulate).toHaveBeenCalledWith(['type', 'user']);
                expect(clearCacheById).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionResponse',
                    'SUCCESS',
                    'Reaction deleted successfully',
                    { result: { reaction } }
                );
                expect(result).toBe('successResponse');
            });

            it('should handle reaction with references', async () => {
                const id = 'reactionId';
                const references = ['Document1', 'Document2'];
                
                findReferences.mockResolvedValue(references);
                createResponse.mockReturnValue('errorResponse');
                
                const result = await reactionResolvers.Mutation.deleteReaction(null, { id });
                
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Reaction cannot be deleted',
                    {
                        errors: [{
                            field: 'id',
                            message: `Reaction cannot be deleted as it is being used in: ${references.join(', ')}`
                        }]
                    }
                );
                expect(result).toBe('errorResponse');
            });

            it('should handle error when deleting reaction fails', async () => {
                const id = 'testReactionId';
                const error = new Error('Database error');
                
                findReferences.mockResolvedValue([]);
                
                Reaction.findByIdAndDelete.mockReturnValue({
                    populate: jest.fn().mockRejectedValue(error)
                });

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting reaction',
                    errors: [{ field: 'id', message: error.message }]
                });

                const result = await reactionResolvers.Mutation.deleteReaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Reaction');
                expect(Reaction.findByIdAndDelete).toHaveBeenCalledWith(id);
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Error deleting reaction',
                    {
                        errors: [{ field: 'id', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting reaction',
                    errors: [{ field: 'id', message: error.message }]
                });
                expect(console.error).toHaveBeenCalledWith(error);
            });

            it('should handle error when checking references fails', async () => {
                const id = 'testReactionId';
                const error = new Error('Reference check failed');
                
                findReferences.mockRejectedValue(error);

                createResponse.mockReturnValue({
                    status: 'FAILURE',
                    message: 'Error deleting reaction',
                    errors: [{ field: 'id', message: error.message }]
                });

                const result = await reactionResolvers.Mutation.deleteReaction(null, { id });

                expect(findReferences).toHaveBeenCalledWith(id, 'Reaction');
                expect(Reaction.findByIdAndDelete).not.toHaveBeenCalled();
                expect(createResponse).toHaveBeenCalledWith(
                    'ReactionErrorResponse',
                    'FAILURE',
                    'Error deleting reaction',
                    {
                        errors: [{ field: 'id', message: error.message }]
                    }
                );
                expect(result).toEqual({
                    status: 'FAILURE',
                    message: 'Error deleting reaction',
                    errors: [{ field: 'id', message: error.message }]
                });
                expect(console.error).toHaveBeenCalledWith(error);
            });
        });
    });
});
const TaskCollaborator = require('../../models/TaskCollaborator');
const { User } = require('../../models/User');
const Task = require('../../models/Task');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { validateReferences } = require('../../utils/validation.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const Party = require('../../models/Party');
const Event = require('../../models/Event');
const Host = require('../../models/Host');
const Guest = require('../../models/Guest');
const { hasEventLevelAccess } = require('../../utils/auth/accessLevels.util');
const { 
    notifyTaskCollaboratorAdded 
} = require('../../notification/taskNotification');

const taskCollaboratorIdSchema = {
    taskId: { type: 'single', required: true, model: Task },
    userIds: { type: 'array', required: true, model: User }
};

const getEventIdFromTaskId = async (taskId) => {
    const task = await Task.findById(taskId);
    if (!task || !task.party) return null;
    
    const party = await Party.findById(task.party);
    if (!party || !party.eventId) return null;
    
    return party.eventId;
};

const taskCollaboratorResolvers = {
    TaskCollaborator: {
        user: async (parent) => {
            try {
                return await User.findById(parent.user);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        },
        task: async (parent) => {
            try {
                return await Task.findById(parent.task);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting task');
            }
        }
    },

    Query: {
        getTaskCollaboratorById: async (_, { id }) => {
            try {
                const cachedCollaborator = await getByIdCache(id);
                if (cachedCollaborator) {
                    return createResponse('TaskCollaboratorResponse', 'SUCCESS', 'Task collaborator retrieved successfully from cache', {
                        result: { taskCollaborator: cachedCollaborator }
                    });
                }

                const taskCollaborator = await TaskCollaborator.findById(id);
                if (!taskCollaborator) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Task collaborator not found', {
                        errors: [{ field: 'id', message: 'Task collaborator not found' }]
                    });
                }

                await setCache(id, taskCollaborator);
                return createResponse('TaskCollaboratorResponse', 'SUCCESS', 'Task collaborator retrieved successfully', {
                    result: { taskCollaborator }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Error retrieving task collaborator', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        },

        getTaskCollaborators: async (_, { filter, pagination }) => {
            try {
                const query = filter ? {
                    ...(filter.taskId && { task: filter.taskId }),
                    ...(filter.userId && { user: filter.userId }),
                    ...(filter.assignedOn && {
                        assignedOn: {
                            ...(filter.assignedOn.gte && { $gte: filter.assignedOn.gte }),
                            ...(filter.assignedOn.lte && { $lte: filter.assignedOn.lte })
                        }
                    })
                } : {};

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(TaskCollaborator, query, limit, skip);

                const taskCollaborators = await TaskCollaborator.find(query)
                    .populate('user')
                    .populate('task')
                    .limit(limit)
                    .skip(skip)
                    .sort({ assignedOn: -1 });

                if (taskCollaborators.length === 0) {
                    return createResponse('TaskCollaboratorsResponse', 'FAILURE', 'No task collaborators found', {
                        result: { taskCollaborators },
                        pagination: paginationInfo
                    });
                }

                return createResponse('TaskCollaboratorsResponse', 'SUCCESS', 'Task collaborators fetched successfully', {
                    result: { taskCollaborators },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Error retrieving task collaborators', {
                    errors: [{ field: 'getTaskCollaborators', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createTaskCollaborator: async (_, { taskId, userIds }, context) => {
            try {
                const eventId = await getEventIdFromTaskId(taskId);
                if (!eventId) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Invalid task', {
                        errors: [{ field: 'taskId', message: 'Could not find associated event' }]
                    });
                }

                if (!await hasEventLevelAccess(context.user._id, eventId)) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const validationError = await validateReferences(
                    { taskId, userIds }, 
                    taskCollaboratorIdSchema, 
                    'TaskCollaborator'
                );
                if (validationError) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const task = await Task.findById(taskId);
                if (!task) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Task not found', {
                        errors: [{ field: 'taskId', message: 'Task not found' }]
                    });
                }

                const party = await Party.findById(task.party);
                if (!party) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'taskId', message: 'Associated party not found' }]
                    });
                }

                const event = await Event.findById(party.eventId);
                if (!event) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Event not found', {
                        errors: [{ field: 'taskId', message: 'Associated event not found' }]
                    });
                }

                const collaborators = [];
                for (const userId of userIds) {
                    const isMainHost = await Host.exists({ _id: event.mainHost, userId });

                    const parties = await Party.find({ eventId: event._id });
                    const allCoHostIds = [
                        ...(event.coHosts || []),
                        ...parties.reduce((ids, party) => [...ids, ...(party.coHosts || [])], [])
                    ];

                    const isCoHost = allCoHostIds.length > 0 && await Host.exists({ 
                        _id: { $in: allCoHostIds }, 
                        userId 
                    });

                    const isGuest = await Guest.exists({
                        party: { $in: parties.map(p => p._id) },
                        user: userId
                    });

                    if (!isMainHost && !isCoHost && !isGuest) {
                        return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'User is not an event collaborator', {
                            errors: [{ 
                                field: 'userId', 
                                message: `User ${userId} must be a collaborator of the event to be added to the task` 
                            }]
                        });
                    }

                    const taskCollaborator = new TaskCollaborator({
                        task: taskId,
                        user: userId,
                        assignedOn: new Date()
                    });
                    await taskCollaborator.save();
                    collaborators.push(taskCollaborator);
                }

                await Task.findByIdAndUpdate(taskId, {
                    $push: { collaborators: { $each: collaborators.map(c => c._id) } }
                });

                await clearCacheById(taskId);

                // Populate the collaborators with user information for notification
                const populatedCollaborators = await TaskCollaborator.find({
                    _id: { $in: collaborators.map(c => c._id) }
                }).populate('user');

                // Send notification to new collaborators
                await notifyTaskCollaboratorAdded(task, party, populatedCollaborators, context.user._id);

                const populatedCollaborator = await TaskCollaborator.findById(collaborators[0]._id)
                    .populate('user')
                    .populate('task');

                return createResponse('TaskCollaboratorResponse', 'SUCCESS', 'Task collaborators created successfully', {
                    result: { taskCollaborator: populatedCollaborator }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Error creating task collaborator', {
                    errors: [{ field: 'createTaskCollaborator', message: error.message }]
                });
            }
        },

        deleteTaskCollaborator: async (_, { id }, context) => {
            try {
                const taskCollaborator = await TaskCollaborator.findById(id);
                if (!taskCollaborator) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Task collaborator not found', {
                        errors: [{ field: 'id', message: 'Task collaborator not found' }]
                    });
                }

                const eventId = await getEventIdFromTaskId(taskCollaborator.task);
                if (!eventId) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Task is not associated with an event', {
                        errors: [{ field: 'event', message: 'Task is not associated with an event' }]
                    });
                }
                
                if (!await hasEventLevelAccess(context.user._id, eventId)) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }
                
                const references = await findReferences(id, 'TaskCollaborator');
                if (references.length > 0) {
                    return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Task collaborator cannot be deleted', {
                        errors: [{ field: 'id', message: `Task collaborator cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                await Task.findByIdAndUpdate(taskCollaborator.task, {
                    $pull: { collaborators: id }
                });

                await TaskCollaborator.findByIdAndDelete(id);
                await clearCacheById(id);

                return createResponse('TaskCollaboratorResponse', 'SUCCESS', 'Task collaborator deleted successfully', {
                    result: { taskCollaborator }
                });
            } catch (error) {
                console.error(error);
                return createResponse('TaskCollaboratorErrorResponse', 'FAILURE', 'Error deleting task collaborator', {
                    errors: [{ field: 'deleteTaskCollaborator', message: error.message }]
                });
            }
        }
    }
};

module.exports = taskCollaboratorResolvers; 
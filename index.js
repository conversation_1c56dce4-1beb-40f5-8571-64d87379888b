const express = require("express");
const path = require("path");

require("dotenv").config();

const typeDefs = require("./src/graphql/typeDefs");
const resolvers = require("./src/graphql/resolvers");
const webhookRouter = require("./src/routes/webhook");
const events = require("./src/routes/events");
const bodyParser = require('body-parser');
const { configureMiddleware, createServer, connectToDbAndRunApp } = require('./src/utils/serverSetup.util');

const startServer = async () => {
  const app = express();

  app.use(bodyParser.json());
  app.use("/webhook", webhookRouter);
  app.use("/activity_events", events);

  app.use(express.static(path.join(__dirname, 'public')));


  const server = await createServer(app, typeDefs, resolvers);

  // Middleware to set HTTP status code based on GraphQL response
  configureMiddleware(app);

  connectToDbAndRunApp(app, server);
};

startServer();
const buildVendorServiceQuery = require('../../../../../src/graphql/resolvers/filters/vendorService.filter');
const mongoose = require('mongoose');

describe('buildVendorServiceQuery', () => {
    it('should return empty query object when no filters provided', () => {
        const query = buildVendorServiceQuery();
        expect(query).toEqual({});
    });

    it('should build query with valid id filter', () => {
        const validId = new mongoose.Types.ObjectId().toString();
        const filters = { id: validId };
        const query = buildVendorServiceQuery(filters);
        expect(query).toEqual({ _id: validId });
    });

    it('should throw error with invalid id filter', () => {
        const filters = { id: 'invalid-id' };
        expect(() => buildVendorServiceQuery(filters)).toThrow('Invalid id provided');
    });

    it('should build query with title filter', () => {
        const filters = { title: 'Test Service' };
        const query = buildVendorServiceQuery(filters);
        expect(query).toEqual({
            title: { $regex: 'Test Service', $options: 'i' }
        });
    });

    it('should build query with priceRange filter', () => {
        const filters = { priceRange: '$100-$200' };
        const query = buildVendorServiceQuery(filters);
        expect(query).toEqual({
            priceRange: { $regex: '$100-$200', $options: 'i' }
        });
    });

    it('should build query with multiple filters', () => {
        const validId = new mongoose.Types.ObjectId().toString();
        const filters = {
            id: validId,
            title: 'Test Service',
            priceRange: '$100-$200'
        };
        const query = buildVendorServiceQuery(filters);
        expect(query).toEqual({
            _id: validId,
            title: { $regex: 'Test Service', $options: 'i' },
            priceRange: { $regex: '$100-$200', $options: 'i' }
        });
    });

    it('should ignore undefined filters', () => {
        const filters = {
            title: undefined,
            priceRange: null
        };
        const query = buildVendorServiceQuery(filters);
        expect(query).toEqual({});
    });
});